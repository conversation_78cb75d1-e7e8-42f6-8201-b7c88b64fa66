-- StarterPlayerScripts/GUI/ModernBuildGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Bau-GUI für Infrastruktur mit Kategorien und Vorschau

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetBuildableItemsFunction = Events:WaitForChild("GetBuildableItemsFunction")
local PlaceBuildingEvent = Events:WaitForChild("PlaceBuildingEvent")
local GetTerrainInfoFunction = Events:WaitForChild("GetTerrainInfoFunction")

local ModernBuildGUI = {}
ModernBuildGUI.__index = ModernBuildGUI

-- Konstruktor
function ModernBuildGUI.new()
    local self = setmetatable({}, ModernBuildGUI)
    
    self.isOpen = false
    self.isDocked = true -- Standardmäßig gedockt
    self.currentCategory = "TRACKS" -- TRACKS, ROADS, STATIONS, BUILDINGS, TERRAIN
    self.selectedItem = nil
    self.buildMode = false
    self.previewModel = nil
    
    return self
end

-- GUI erstellen
function ModernBuildGUI:CreateGUI()
    if self.screenGui then return end
    
    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernBuildGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Hauptfenster (standardmäßig gedockt)
    local mainWindow = Instance.new("Frame")
    mainWindow.Size = UDim2.new(0, 350, 1, -100)
    mainWindow.Position = UDim2.new(1, -360, 0, 50)
    mainWindow.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainWindow.BorderSizePixel = 0
    mainWindow.Visible = false
    mainWindow.Parent = self.screenGui
    
    local windowCorner = Instance.new("UICorner")
    windowCorner.CornerRadius = UDim.new(0, 15)
    windowCorner.Parent = mainWindow
    
    -- Fenster-Gradient
    local windowGradient = Instance.new("UIGradient")
    windowGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(20, 25, 30)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(10, 15, 20))
    }
    windowGradient.Rotation = 90
    windowGradient.Parent = mainWindow
    
    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainWindow
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 15)
    titleCorner.Parent = titleBar
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -100, 1, 0)
    title.Position = UDim2.new(0, 15, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "🏗️ BAU-MODUS"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Dock-Button
    local dockButton = Instance.new("TextButton")
    dockButton.Size = UDim2.new(0, 40, 0, 40)
    dockButton.Position = UDim2.new(1, -85, 0, 5)
    dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    dockButton.Text = "🔓"
    dockButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    dockButton.TextScaled = true
    dockButton.Font = Enum.Font.SourceSans
    dockButton.BorderSizePixel = 0
    dockButton.Parent = titleBar
    
    local dockCorner = Instance.new("UICorner")
    dockCorner.CornerRadius = UDim.new(0, 8)
    dockCorner.Parent = dockButton
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -40, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Kategorie-Navigation
    local categoryContainer = Instance.new("Frame")
    categoryContainer.Size = UDim2.new(1, -20, 0, 80)
    categoryContainer.Position = UDim2.new(0, 10, 0, 60)
    categoryContainer.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    categoryContainer.BorderSizePixel = 0
    categoryContainer.Parent = mainWindow
    
    local categoryCorner = Instance.new("UICorner")
    categoryCorner.CornerRadius = UDim.new(0, 10)
    categoryCorner.Parent = categoryContainer
    
    -- Kategorie-Buttons
    local categoryButtonContainer = Instance.new("Frame")
    categoryButtonContainer.Size = UDim2.new(1, -20, 1, -20)
    categoryButtonContainer.Position = UDim2.new(0, 10, 0, 10)
    categoryButtonContainer.BackgroundTransparency = 1
    categoryButtonContainer.Parent = categoryContainer
    
    local categoryLayout = Instance.new("UIGridLayout")
    categoryLayout.CellSize = UDim2.new(0, 60, 0, 25)
    categoryLayout.CellPadding = UDim2.new(0, 5, 0, 5)
    categoryLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    categoryLayout.VerticalAlignment = Enum.VerticalAlignment.Top
    categoryLayout.Parent = categoryButtonContainer
    
    -- Kategorien definieren
    local categories = {
        {text = "🛤️ GLEISE", id = "TRACKS", color = Color3.fromRGB(100, 150, 255)},
        {text = "🛣️ STRASSEN", id = "ROADS", color = Color3.fromRGB(150, 150, 150)},
        {text = "🚉 STATIONEN", id = "STATIONS", color = Color3.fromRGB(100, 255, 100)},
        {text = "🏢 GEBÄUDE", id = "BUILDINGS", color = Color3.fromRGB(255, 200, 100)},
        {text = "🏔️ TERRAIN", id = "TERRAIN", color = Color3.fromRGB(150, 255, 150)}
    }
    
    self.categoryButtons = {}
    
    for _, categoryData in ipairs(categories) do
        local categoryButton = Instance.new("TextButton")
        categoryButton.Size = UDim2.new(0, 60, 0, 25)
        categoryButton.BackgroundColor3 = categoryData.color
        categoryButton.Text = categoryData.text
        categoryButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        categoryButton.TextScaled = true
        categoryButton.Font = Enum.Font.SourceSansBold
        categoryButton.BorderSizePixel = 0
        categoryButton.Parent = categoryButtonContainer
        
        local categoryButtonCorner = Instance.new("UICorner")
        categoryButtonCorner.CornerRadius = UDim.new(0, 6)
        categoryButtonCorner.Parent = categoryButton
        
        -- Click-Handler
        categoryButton.MouseButton1Click:Connect(function()
            self:SwitchCategory(categoryData.id)
        end)
        
        self.categoryButtons[categoryData.id] = {button = categoryButton, color = categoryData.color}
    end
    
    -- Items-Liste
    local itemsContainer = Instance.new("ScrollingFrame")
    itemsContainer.Size = UDim2.new(1, -20, 1, -220)
    itemsContainer.Position = UDim2.new(0, 10, 0, 150)
    itemsContainer.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    itemsContainer.BorderSizePixel = 0
    itemsContainer.ScrollBarThickness = 8
    itemsContainer.Parent = mainWindow
    
    local itemsCorner = Instance.new("UICorner")
    itemsCorner.CornerRadius = UDim.new(0, 10)
    itemsCorner.Parent = itemsContainer
    
    local itemsLayout = Instance.new("UIListLayout")
    itemsLayout.SortOrder = Enum.SortOrder.LayoutOrder
    itemsLayout.Padding = UDim.new(0, 5)
    itemsLayout.Parent = itemsContainer
    
    local itemsPadding = Instance.new("UIPadding")
    itemsPadding.PaddingAll = UDim.new(0, 10)
    itemsPadding.Parent = itemsContainer
    
    -- Info-Bereich
    local infoContainer = Instance.new("Frame")
    infoContainer.Size = UDim2.new(1, -20, 0, 60)
    infoContainer.Position = UDim2.new(0, 10, 1, -70)
    infoContainer.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    infoContainer.BorderSizePixel = 0
    infoContainer.Parent = mainWindow
    
    local infoCorner = Instance.new("UICorner")
    infoCorner.CornerRadius = UDim.new(0, 10)
    infoCorner.Parent = infoContainer
    
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Size = UDim2.new(1, -20, 1, -20)
    infoLabel.Position = UDim2.new(0, 10, 0, 10)
    infoLabel.BackgroundTransparency = 1
    infoLabel.Text = "💡 Wähle eine Kategorie und ein Element zum Bauen"
    infoLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.SourceSans
    infoLabel.TextWrapped = true
    infoLabel.Parent = infoContainer
    
    -- Referenzen speichern
    self.mainWindow = mainWindow
    self.titleBar = titleBar
    self.dockButton = dockButton
    self.closeButton = closeButton
    self.itemsContainer = itemsContainer
    self.infoLabel = infoLabel
    
    -- Event-Handler
    dockButton.MouseButton1Click:Connect(function()
        self:ToggleDock()
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Drag-Funktionalität
    self:SetupDragging()
    
    return self.screenGui
end

-- Kategorie wechseln
function ModernBuildGUI:SwitchCategory(categoryId)
    if self.currentCategory == categoryId then return end
    
    -- Alten Button deaktivieren
    if self.categoryButtons[self.currentCategory] then
        local oldButton = self.categoryButtons[self.currentCategory].button
        oldButton.BackgroundTransparency = 0.3
    end
    
    -- Neuen Button aktivieren
    if self.categoryButtons[categoryId] then
        local newButton = self.categoryButtons[categoryId].button
        newButton.BackgroundTransparency = 0
    end
    
    self.currentCategory = categoryId
    self:LoadCategoryItems()
end

-- Kategorie-Items laden
function ModernBuildGUI:LoadCategoryItems()
    -- Bestehende Items löschen
    for _, child in pairs(self.itemsContainer:GetChildren()) do
        if not child:IsA("UIListLayout") and not child:IsA("UIPadding") then
            child:Destroy()
        end
    end
    
    -- Items basierend auf Kategorie laden
    local items = self:GetItemsForCategory(self.currentCategory)
    
    for i, itemData in ipairs(items) do
        self:CreateItemEntry(itemData, i)
    end
    
    -- Canvas-Größe setzen
    local totalHeight = (#items * 70) + ((#items - 1) * 5) + 20
    self.itemsContainer.CanvasSize = UDim2.new(0, 0, 0, totalHeight)
end

-- Items für Kategorie abrufen
function ModernBuildGUI:GetItemsForCategory(category)
    local items = {}
    
    if category == "TRACKS" then
        items = {
            {name = "Einfache Gleise", cost = 1000, icon = "🛤️", description = "Standard-Eisenbahngleise"},
            {name = "Elektrifizierte Gleise", cost = 2500, icon = "⚡", description = "Gleise mit Oberleitung"},
            {name = "Hochgeschwindigkeits-Gleise", cost = 5000, icon = "🚄", description = "Für schnelle Züge"},
            {name = "Brücken-Gleise", cost = 8000, icon = "🌉", description = "Gleise für Brücken"}
        }
    elseif category == "ROADS" then
        items = {
            {name = "Landstraße", cost = 500, icon = "🛣️", description = "Einfache Straße"},
            {name = "Autobahn", cost = 2000, icon = "🛣️", description = "Mehrspurige Autobahn"},
            {name = "Stadtstraße", cost = 1000, icon = "🏙️", description = "Straße in der Stadt"},
            {name = "Brücken-Straße", cost = 4000, icon = "🌉", description = "Straße für Brücken"}
        }
    elseif category == "STATIONS" then
        items = {
            {name = "Bahnhof", cost = 25000, icon = "🚉", description = "Passagier-Bahnhof"},
            {name = "Güterbahnhof", cost = 30000, icon = "🚛", description = "Fracht-Bahnhof"},
            {name = "Bushaltestelle", cost = 5000, icon = "🚌", description = "Bus-Station"},
            {name = "Flughafen", cost = 100000, icon = "✈️", description = "Großer Flughafen"}
        }
    elseif category == "BUILDINGS" then
        items = {
            {name = "Depot", cost = 50000, icon = "🏭", description = "Fahrzeug-Depot"},
            {name = "Wartungshalle", cost = 75000, icon = "🔧", description = "Fahrzeug-Wartung"},
            {name = "Kraftwerk", cost = 200000, icon = "⚡", description = "Strom-Erzeugung"},
            {name = "Bürogebäude", cost = 150000, icon = "🏢", description = "Verwaltung"}
        }
    elseif category == "TERRAIN" then
        items = {
            {name = "Terrain Anheben", cost = 100, icon = "⬆️", description = "Gelände erhöhen"},
            {name = "Terrain Absenken", cost = 100, icon = "⬇️", description = "Gelände senken"},
            {name = "Terrain Glätten", cost = 50, icon = "📏", description = "Gelände ebnen"},
            {name = "Wasser Hinzufügen", cost = 200, icon = "💧", description = "Gewässer erstellen"}
        }
    end
    
    return items
end

-- Item-Eintrag erstellen
function ModernBuildGUI:CreateItemEntry(itemData, index)
    local entry = Instance.new("Frame")
    entry.Size = UDim2.new(1, -20, 0, 60)
    entry.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    entry.BorderSizePixel = 0
    entry.LayoutOrder = index
    entry.Parent = self.itemsContainer
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 8)
    entryCorner.Parent = entry
    
    -- Item-Icon
    local icon = Instance.new("TextLabel")
    icon.Size = UDim2.new(0, 40, 0, 40)
    icon.Position = UDim2.new(0, 10, 0, 10)
    icon.BackgroundTransparency = 1
    icon.Text = itemData.icon
    icon.TextColor3 = Color3.fromRGB(255, 255, 255)
    icon.TextScaled = true
    icon.Font = Enum.Font.SourceSans
    icon.Parent = entry
    
    -- Item-Name
    local name = Instance.new("TextLabel")
    name.Size = UDim2.new(1, -120, 0, 25)
    name.Position = UDim2.new(0, 60, 0, 5)
    name.BackgroundTransparency = 1
    name.Text = itemData.name
    name.TextColor3 = Color3.fromRGB(255, 255, 255)
    name.TextScaled = true
    name.Font = Enum.Font.SourceSansBold
    name.TextXAlignment = Enum.TextXAlignment.Left
    name.Parent = entry
    
    -- Item-Beschreibung
    local description = Instance.new("TextLabel")
    description.Size = UDim2.new(1, -120, 0, 20)
    description.Position = UDim2.new(0, 60, 0, 25)
    description.BackgroundTransparency = 1
    description.Text = itemData.description
    description.TextColor3 = Color3.fromRGB(150, 150, 150)
    description.TextScaled = true
    description.Font = Enum.Font.SourceSans
    description.TextXAlignment = Enum.TextXAlignment.Left
    description.Parent = entry
    
    -- Kosten
    local cost = Instance.new("TextLabel")
    cost.Size = UDim2.new(0, 80, 0, 30)
    cost.Position = UDim2.new(1, -90, 0, 15)
    cost.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    cost.Text = self:FormatMoney(itemData.cost)
    cost.TextColor3 = Color3.fromRGB(255, 255, 255)
    cost.TextScaled = true
    cost.Font = Enum.Font.SourceSansBold
    cost.BorderSizePixel = 0
    cost.Parent = entry
    
    local costCorner = Instance.new("UICorner")
    costCorner.CornerRadius = UDim.new(0, 6)
    costCorner.Parent = cost
    
    -- Click-Handler
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(1, 0, 1, 0)
    button.BackgroundTransparency = 1
    button.Text = ""
    button.Parent = entry
    
    button.MouseButton1Click:Connect(function()
        self:SelectItem(itemData)
    end)
    
    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        TweenService:Create(entry, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(30, 35, 40)
        }):Play()
    end)
    
    button.MouseLeave:Connect(function()
        if self.selectedItem ~= itemData then
            TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(20, 25, 30)
            }):Play()
        end
    end)
end

-- Item auswählen
function ModernBuildGUI:SelectItem(itemData)
    self.selectedItem = itemData
    self.infoLabel.Text = string.format("🔨 %s ausgewählt - Klicke in die Welt zum Platzieren\nKosten: %s", 
        itemData.name, self:FormatMoney(itemData.cost))
    
    -- Bau-Modus aktivieren
    self.buildMode = true
    print("🔨 Bau-Modus aktiviert:", itemData.name)
end

-- Geld formatieren
function ModernBuildGUI:FormatMoney(amount)
    if amount >= 1000000 then
        return string.format("%.1fM $", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.1fK $", amount / 1000)
    else
        return string.format("%d $", amount)
    end
end

-- Docking umschalten
function ModernBuildGUI:ToggleDock()
    self.isDocked = not self.isDocked

    if self.isDocked then
        -- An rechte Seite andocken
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 350, 1, -100),
            Position = UDim2.new(1, -360, 0, 50)
        }):Play()
        self.dockButton.Text = "🔓"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    else
        -- Floating
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 400, 0, 600),
            Position = UDim2.new(0.5, -200, 0.5, -300)
        }):Play()
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    end
end

-- Dragging Setup
function ModernBuildGUI:SetupDragging()
    local dragging = false
    local dragStart = nil
    local startPos = nil

    self.titleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 and not self.isDocked then
            dragging = true
            dragStart = input.Position
            startPos = self.mainWindow.Position
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            self.mainWindow.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)

    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
end

-- GUI öffnen
function ModernBuildGUI:OpenGUI()
    if not self.screenGui then
        self:CreateGUI()
    end

    self.mainWindow.Visible = true
    self.isOpen = true

    -- Smooth fade-in
    self.mainWindow.BackgroundTransparency = 1
    TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    }):Play()

    -- Standard-Kategorie setzen
    self:SwitchCategory("TRACKS")
end

-- GUI schließen
function ModernBuildGUI:CloseGUI()
    if self.mainWindow then
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        }):Play()

        wait(0.3)
        self.mainWindow.Visible = false
        self.isOpen = false

        -- Bau-Modus deaktivieren
        self.buildMode = false
        self.selectedItem = nil

        if self.previewModel then
            self.previewModel:Destroy()
            self.previewModel = nil
        end
    end
end

-- Singleton-Instanz
local ModernBuildGUIInstance = ModernBuildGUI.new()

return ModernBuildGUIInstance
