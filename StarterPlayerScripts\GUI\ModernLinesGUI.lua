-- StarterPlayerScripts/GUI/ModernLinesGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Linien-Management-GUI mit Docking-Unterstützung

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetTransportLinesFunction = Events:WaitForChild("GetTransportLinesFunction")
local CreateLineEvent = Events:WaitForChild("CreateLineEvent")
local ModifyLineEvent = Events:WaitForChild("ModifyLineEvent")
local DeleteLineEvent = Events:WaitForChild("DeleteLineEvent")

local ModernLinesGUI = {}
ModernLinesGUI.__index = ModernLinesGUI

-- Konstruktor
function ModernLinesGUI.new()
    local self = setmetatable({}, ModernLinesGUI)
    
    self.isOpen = false
    self.isDocked = false
    self.currentFilter = "ALL" -- ALL, TRAINS, TRUCKS, SHIPS, PLANES
    self.selectedLine = nil
    self.lines = {}
    
    return self
end

-- GUI erstellen
function ModernLinesGUI:CreateGUI()
    if self.screenGui then return end
    
    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernLinesGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Hauptfenster
    local mainWindow = Instance.new("Frame")
    mainWindow.Size = UDim2.new(0, 1000, 0, 700)
    mainWindow.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainWindow.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainWindow.BorderSizePixel = 0
    mainWindow.Visible = false
    mainWindow.Parent = self.screenGui
    
    local windowCorner = Instance.new("UICorner")
    windowCorner.CornerRadius = UDim.new(0, 15)
    windowCorner.Parent = mainWindow
    
    -- Fenster-Gradient
    local windowGradient = Instance.new("UIGradient")
    windowGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 30, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 20, 25))
    }
    windowGradient.Rotation = 45
    windowGradient.Parent = mainWindow
    
    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainWindow
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 15)
    titleCorner.Parent = titleBar
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -200, 1, 0)
    title.Position = UDim2.new(0, 15, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "🚂 LINIEN-MANAGEMENT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Dock-Button
    local dockButton = Instance.new("TextButton")
    dockButton.Size = UDim2.new(0, 40, 0, 40)
    dockButton.Position = UDim2.new(1, -90, 0, 5)
    dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    dockButton.Text = "📌"
    dockButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    dockButton.TextScaled = true
    dockButton.Font = Enum.Font.SourceSans
    dockButton.BorderSizePixel = 0
    dockButton.Parent = titleBar
    
    local dockCorner = Instance.new("UICorner")
    dockCorner.CornerRadius = UDim.new(0, 8)
    dockCorner.Parent = dockButton
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -45, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Toolbar
    local toolbar = Instance.new("Frame")
    toolbar.Size = UDim2.new(1, -20, 0, 60)
    toolbar.Position = UDim2.new(0, 10, 0, 60)
    toolbar.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    toolbar.BorderSizePixel = 0
    toolbar.Parent = mainWindow
    
    local toolbarCorner = Instance.new("UICorner")
    toolbarCorner.CornerRadius = UDim.new(0, 10)
    toolbarCorner.Parent = toolbar
    
    -- Filter-Buttons
    local filterContainer = Instance.new("Frame")
    filterContainer.Size = UDim2.new(1, -20, 1, -20)
    filterContainer.Position = UDim2.new(0, 10, 0, 10)
    filterContainer.BackgroundTransparency = 1
    filterContainer.Parent = toolbar
    
    local filterLayout = Instance.new("UIListLayout")
    filterLayout.FillDirection = Enum.FillDirection.Horizontal
    filterLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    filterLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    filterLayout.Padding = UDim.new(0, 10)
    filterLayout.Parent = filterContainer
    
    -- Filter-Buttons erstellen
    local filters = {
        {text = "🚀 ALLE", id = "ALL", color = Color3.fromRGB(100, 150, 255)},
        {text = "🚂 ZÜGE", id = "TRAINS", color = Color3.fromRGB(100, 255, 100)},
        {text = "🚛 LKW", id = "TRUCKS", color = Color3.fromRGB(255, 150, 100)},
        {text = "🚢 SCHIFFE", id = "SHIPS", color = Color3.fromRGB(100, 200, 255)},
        {text = "✈️ FLUGZEUGE", id = "PLANES", color = Color3.fromRGB(255, 200, 100)}
    }
    
    self.filterButtons = {}
    
    for _, filterData in ipairs(filters) do
        local filterButton = Instance.new("TextButton")
        filterButton.Size = UDim2.new(0, 120, 1, 0)
        filterButton.BackgroundColor3 = filterData.color
        filterButton.Text = filterData.text
        filterButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        filterButton.TextScaled = true
        filterButton.Font = Enum.Font.SourceSansBold
        filterButton.BorderSizePixel = 0
        filterButton.Parent = filterContainer
        
        local filterCorner = Instance.new("UICorner")
        filterCorner.CornerRadius = UDim.new(0, 8)
        filterCorner.Parent = filterButton
        
        -- Click-Handler
        filterButton.MouseButton1Click:Connect(function()
            self:SetFilter(filterData.id)
        end)
        
        self.filterButtons[filterData.id] = filterButton
    end
    
    -- Neue Linie Button
    local newLineButton = Instance.new("TextButton")
    newLineButton.Size = UDim2.new(0, 150, 1, 0)
    newLineButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
    newLineButton.Text = "➕ NEUE LINIE"
    newLineButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    newLineButton.TextScaled = true
    newLineButton.Font = Enum.Font.SourceSansBold
    newLineButton.BorderSizePixel = 0
    newLineButton.Parent = filterContainer
    
    local newLineCorner = Instance.new("UICorner")
    newLineCorner.CornerRadius = UDim.new(0, 8)
    newLineCorner.Parent = newLineButton
    
    -- Content-Bereich
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 1, -140)
    contentFrame.Position = UDim2.new(0, 10, 0, 130)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = mainWindow
    
    -- Linien-Liste (links)
    local linesList = Instance.new("ScrollingFrame")
    linesList.Size = UDim2.new(0.4, -10, 1, 0)
    linesList.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    linesList.BorderSizePixel = 0
    linesList.ScrollBarThickness = 8
    linesList.Parent = contentFrame
    
    local linesCorner = Instance.new("UICorner")
    linesCorner.CornerRadius = UDim.new(0, 10)
    linesCorner.Parent = linesList
    
    local linesLayout = Instance.new("UIListLayout")
    linesLayout.SortOrder = Enum.SortOrder.LayoutOrder
    linesLayout.Padding = UDim.new(0, 5)
    linesLayout.Parent = linesList
    
    local linesPadding = Instance.new("UIPadding")
    linesPadding.PaddingAll = UDim.new(0, 10)
    linesPadding.Parent = linesList
    
    -- Details-Bereich (rechts)
    local detailsFrame = Instance.new("Frame")
    detailsFrame.Size = UDim2.new(0.6, -10, 1, 0)
    detailsFrame.Position = UDim2.new(0.4, 10, 0, 0)
    detailsFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    detailsFrame.BorderSizePixel = 0
    detailsFrame.Parent = contentFrame
    
    local detailsCorner = Instance.new("UICorner")
    detailsCorner.CornerRadius = UDim.new(0, 10)
    detailsCorner.Parent = detailsFrame
    
    -- Referenzen speichern
    self.mainWindow = mainWindow
    self.titleBar = titleBar
    self.dockButton = dockButton
    self.closeButton = closeButton
    self.linesList = linesList
    self.detailsFrame = detailsFrame
    self.newLineButton = newLineButton
    
    -- Event-Handler
    dockButton.MouseButton1Click:Connect(function()
        self:ToggleDock()
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    newLineButton.MouseButton1Click:Connect(function()
        self:ShowNewLineDialog()
    end)
    
    -- Drag-Funktionalität für undocked Fenster
    self:SetupDragging()
    
    return self.screenGui
end

-- Filter setzen
function ModernLinesGUI:SetFilter(filterId)
    self.currentFilter = filterId
    
    -- Button-Status aktualisieren
    for id, button in pairs(self.filterButtons) do
        if id == filterId then
            button.BackgroundTransparency = 0
        else
            button.BackgroundTransparency = 0.3
        end
    end
    
    -- Linien-Liste aktualisieren
    self:RefreshLinesList()
end

-- Linien-Liste aktualisieren
function ModernLinesGUI:RefreshLinesList()
    -- Bestehende Einträge löschen
    for _, child in pairs(self.linesList:GetChildren()) do
        if not child:IsA("UIListLayout") and not child:IsA("UIPadding") then
            child:Destroy()
        end
    end
    
    -- Linien vom Server abrufen
    local success, lines = pcall(function()
        return GetTransportLinesFunction:InvokeServer()
    end)
    
    if success and lines then
        self.lines = lines
        
        for i, lineData in ipairs(lines) do
            if self:ShouldShowLine(lineData) then
                self:CreateLineEntry(lineData, i)
            end
        end
        
        -- Canvas-Größe setzen
        local visibleLines = 0
        for _, lineData in ipairs(lines) do
            if self:ShouldShowLine(lineData) then
                visibleLines = visibleLines + 1
            end
        end
        
        local totalHeight = (visibleLines * 80) + ((visibleLines - 1) * 5) + 20
        self.linesList.CanvasSize = UDim2.new(0, 0, 0, totalHeight)
    else
        warn("❌ Fehler beim Laden der Linien:", lines)
    end
end

-- Prüfen ob Linie angezeigt werden soll
function ModernLinesGUI:ShouldShowLine(lineData)
    if self.currentFilter == "ALL" then
        return true
    elseif self.currentFilter == "TRAINS" then
        return lineData.vehicleType == "TRAIN"
    elseif self.currentFilter == "TRUCKS" then
        return lineData.vehicleType == "TRUCK"
    elseif self.currentFilter == "SHIPS" then
        return lineData.vehicleType == "SHIP"
    elseif self.currentFilter == "PLANES" then
        return lineData.vehicleType == "PLANE"
    end
    
    return false
end

-- Linien-Eintrag erstellen
function ModernLinesGUI:CreateLineEntry(lineData, index)
    local entry = Instance.new("Frame")
    entry.Size = UDim2.new(1, -20, 0, 70)
    entry.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    entry.BorderSizePixel = 0
    entry.LayoutOrder = index
    entry.Parent = self.linesList
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 8)
    entryCorner.Parent = entry
    
    -- Linien-Icon
    local icon = Instance.new("TextLabel")
    icon.Size = UDim2.new(0, 40, 0, 40)
    icon.Position = UDim2.new(0, 10, 0, 15)
    icon.BackgroundTransparency = 1
    icon.Text = self:GetVehicleIcon(lineData.vehicleType)
    icon.TextColor3 = self:GetVehicleColor(lineData.vehicleType)
    icon.TextScaled = true
    icon.Font = Enum.Font.SourceSans
    icon.Parent = entry
    
    -- Linien-Name
    local name = Instance.new("TextLabel")
    name.Size = UDim2.new(1, -120, 0, 25)
    name.Position = UDim2.new(0, 60, 0, 10)
    name.BackgroundTransparency = 1
    name.Text = lineData.name or ("Linie " .. index)
    name.TextColor3 = Color3.fromRGB(255, 255, 255)
    name.TextScaled = true
    name.Font = Enum.Font.SourceSansBold
    name.TextXAlignment = Enum.TextXAlignment.Left
    name.Parent = entry
    
    -- Linien-Info
    local info = Instance.new("TextLabel")
    info.Size = UDim2.new(1, -120, 0, 20)
    info.Position = UDim2.new(0, 60, 0, 35)
    info.BackgroundTransparency = 1
    info.Text = string.format("%d Fahrzeuge | %d Stationen | %s", 
        lineData.vehicleCount or 0,
        lineData.stationCount or 0,
        lineData.status or "Aktiv")
    info.TextColor3 = Color3.fromRGB(180, 180, 180)
    info.TextScaled = true
    info.Font = Enum.Font.SourceSans
    info.TextXAlignment = Enum.TextXAlignment.Left
    info.Parent = entry
    
    -- Gewinn-Anzeige
    local profit = Instance.new("TextLabel")
    profit.Size = UDim2.new(0, 80, 0, 30)
    profit.Position = UDim2.new(1, -90, 0, 20)
    profit.BackgroundTransparency = 1
    profit.Text = self:FormatMoney(lineData.monthlyProfit or 0)
    profit.TextColor3 = (lineData.monthlyProfit or 0) >= 0 and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(255, 100, 100)
    profit.TextScaled = true
    profit.Font = Enum.Font.SourceSansBold
    profit.Parent = entry
    
    -- Click-Handler
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(1, 0, 1, 0)
    button.BackgroundTransparency = 1
    button.Text = ""
    button.Parent = entry
    
    button.MouseButton1Click:Connect(function()
        self:SelectLine(lineData)
    end)
    
    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        TweenService:Create(entry, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(35, 40, 45)
        }):Play()
    end)
    
    button.MouseLeave:Connect(function()
        if self.selectedLine ~= lineData then
            TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(25, 30, 35)
            }):Play()
        end
    end)
end

-- Fahrzeug-Icon abrufen
function ModernLinesGUI:GetVehicleIcon(vehicleType)
    local icons = {
        TRAIN = "🚂",
        TRUCK = "🚛",
        SHIP = "🚢",
        PLANE = "✈️"
    }
    return icons[vehicleType] or "🚀"
end

-- Fahrzeug-Farbe abrufen
function ModernLinesGUI:GetVehicleColor(vehicleType)
    local colors = {
        TRAIN = Color3.fromRGB(100, 255, 100),
        TRUCK = Color3.fromRGB(255, 150, 100),
        SHIP = Color3.fromRGB(100, 200, 255),
        PLANE = Color3.fromRGB(255, 200, 100)
    }
    return colors[vehicleType] or Color3.fromRGB(255, 255, 255)
end

-- Geld formatieren
function ModernLinesGUI:FormatMoney(amount)
    if amount >= 1000000 then
        return string.format("%.1fM $", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.1fK $", amount / 1000)
    else
        return string.format("%d $", amount)
    end
end

-- Linie auswählen
function ModernLinesGUI:SelectLine(lineData)
    self.selectedLine = lineData
    self:ShowLineDetails(lineData)
end

-- Linien-Details anzeigen
function ModernLinesGUI:ShowLineDetails(lineData)
    -- Details-Bereich leeren
    for _, child in pairs(self.detailsFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    -- Details-Inhalt erstellen
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -20, 0, 40)
    title.Position = UDim2.new(0, 10, 0, 10)
    title.BackgroundTransparency = 1
    title.Text = "📋 LINIEN-DETAILS"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.detailsFrame
    
    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -20, 1, -60)
    placeholder.Position = UDim2.new(0, 10, 0, 50)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = string.format("🚧 Details für: %s\n\nFahrzeugtyp: %s\nFahrzeuge: %d\nStationen: %d\nStatus: %s\nMonatlicher Gewinn: %s", 
        lineData.name or "Unbenannt",
        lineData.vehicleType or "Unbekannt",
        lineData.vehicleCount or 0,
        lineData.stationCount or 0,
        lineData.status or "Unbekannt",
        self:FormatMoney(lineData.monthlyProfit or 0))
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.detailsFrame
end

-- Neue Linie Dialog
function ModernLinesGUI:ShowNewLineDialog()
    print("➕ Neue Linie erstellen")
    -- Hier würde ein Dialog zur Linienerstellung angezeigt
end

-- Docking umschalten
function ModernLinesGUI:ToggleDock()
    self.isDocked = not self.isDocked
    
    if self.isDocked then
        -- An rechte Seite andocken
        self.mainWindow.Size = UDim2.new(0, 400, 1, -100)
        self.mainWindow.Position = UDim2.new(1, -410, 0, 50)
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    else
        -- Floating
        self.mainWindow.Size = UDim2.new(0, 1000, 0, 700)
        self.mainWindow.Position = UDim2.new(0.5, -500, 0.5, -350)
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    end
end

-- Dragging Setup
function ModernLinesGUI:SetupDragging()
    local dragging = false
    local dragStart = nil
    local startPos = nil
    
    self.titleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 and not self.isDocked then
            dragging = true
            dragStart = input.Position
            startPos = self.mainWindow.Position
        end
    end)
    
    UserInputService.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            self.mainWindow.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)
    
    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
end

-- GUI öffnen
function ModernLinesGUI:OpenGUI()
    if not self.screenGui then
        self:CreateGUI()
    end
    
    self.mainWindow.Visible = true
    self.isOpen = true
    
    -- Smooth fade-in
    self.mainWindow.BackgroundTransparency = 1
    TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    }):Play()
    
    -- Standard-Filter setzen und Daten laden
    self:SetFilter("ALL")
end

-- GUI schließen
function ModernLinesGUI:CloseGUI()
    if self.mainWindow then
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        }):Play()
        
        wait(0.3)
        self.mainWindow.Visible = false
        self.isOpen = false
    end
end

-- Singleton-Instanz
local ModernLinesGUIInstance = ModernLinesGUI.new()

return ModernLinesGUIInstance
