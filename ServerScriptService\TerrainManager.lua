-- ServerScriptService/TerrainManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Prozedurale Welt-Generation mit Terrain, Gewässern und Vegetation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")

local TerrainManager = {}
TerrainManager.__index = TerrainManager

function TerrainManager.new()
    local self = setmetatable({}, TerrainManager)
    
    -- Terrain settings
    self.mapSize = 2048 -- Map size in studs
    self.heightScale = 100 -- Maximum height variation
    self.seaLevel = 20 -- Sea level height
    
    -- Biome definitions
    self.biomes = {
        ocean = {color = Color3.fromRGB(0, 100, 200), material = Enum.Material.Water, minHeight = 0, maxHeight = 15},
        beach = {color = Color3.fromRGB(255, 255, 150), material = Enum.Material.Sand, minHeight = 15, maxHeight = 25},
        plains = {color = Color3.fromRGB(100, 200, 100), material = Enum.Material.Grass, minHeight = 25, maxHeight = 60},
        forest = {color = Color3.fromRGB(50, 150, 50), material = Enum.Material.LeafyGrass, minHeight = 30, maxHeight = 80},
        hills = {color = Color3.fromRGB(150, 150, 100), material = Enum.Material.Rock, minHeight = 60, maxHeight = 120},
        mountains = {color = Color3.fromRGB(100, 100, 100), material = Enum.Material.Rock, minHeight = 120, maxHeight = 200},
        snow = {color = Color3.fromRGB(255, 255, 255), material = Enum.Material.Snow, minHeight = 150, maxHeight = 300}
    }
    
    -- Climate zones
    self.climateZones = {
        tropical = {temperature = 30, humidity = 80, rainfall = 200},
        temperate = {temperature = 15, humidity = 60, rainfall = 100},
        continental = {temperature = 5, humidity = 50, rainfall = 80},
        arctic = {temperature = -10, humidity = 40, rainfall = 30}
    }
    
    -- Generated world data
    self.heightMap = {}
    self.biomeMap = {}
    self.climateMap = {}
    self.cities = {}
    self.resources = {}
    
    -- Terrain modification costs
    self.terraformCosts = {
        raise = 100, -- per cubic stud
        lower = 80,
        flatten = 50,
        smooth = 30
    }
    
    -- Setup terrain folder
    self.terrainFolder = Workspace:FindFirstChild("GeneratedTerrain") or Instance.new("Folder")
    self.terrainFolder.Name = "GeneratedTerrain"
    self.terrainFolder.Parent = Workspace
    
    -- Initialize terrain
    self:InitializeTerrain()
    
    return self
end

-- Initialize and generate terrain
function TerrainManager:InitializeTerrain()
    print("🌍 Initializing terrain generation...")
    
    -- Generate height map using Perlin noise
    self:GenerateHeightMap()
    
    -- Generate biome map
    self:GenerateBiomeMap()
    
    -- Generate climate zones
    self:GenerateClimateMap()
    
    -- Place cities
    self:GenerateCities()
    
    -- Place natural resources
    self:GenerateResources()
    
    -- Create 3D terrain
    self:CreateTerrain()
    
    -- Add vegetation
    self:AddVegetation()
    
    -- Add water bodies
    self:AddWaterBodies()
    
    print("✅ Terrain generation completed!")
end

-- Generate height map using Perlin noise
function TerrainManager:GenerateHeightMap()
    local resolution = 256 -- Height map resolution
    self.heightMap = {}
    
    for x = 1, resolution do
        self.heightMap[x] = {}
        for z = 1, resolution do
            -- Multi-octave Perlin noise for realistic terrain
            local height = 0
            local amplitude = 1
            local frequency = 0.01
            
            -- Add multiple octaves for detail
            for octave = 1, 6 do
                height = height + (math.noise(x * frequency, z * frequency, 0) * amplitude)
                amplitude = amplitude * 0.5
                frequency = frequency * 2
            end
            
            -- Normalize and scale height
            height = (height + 1) / 2 -- Normalize to 0-1
            height = height * self.heightScale
            
            -- Add some randomness
            height = height + (math.random() - 0.5) * 10
            
            -- Ensure minimum height
            height = math.max(0, height)
            
            self.heightMap[x][z] = height
        end
    end
    
    print("🏔️ Height map generated")
end

-- Generate biome map based on height and climate
function TerrainManager:GenerateBiomeMap()
    local resolution = 256
    self.biomeMap = {}
    
    for x = 1, resolution do
        self.biomeMap[x] = {}
        for z = 1, resolution do
            local height = self.heightMap[x][z]
            local biome = "plains" -- Default biome
            
            -- Determine biome based on height
            if height <= 15 then
                biome = "ocean"
            elseif height <= 25 then
                biome = "beach"
            elseif height <= 60 then
                -- Use noise to vary between plains and forest
                local forestNoise = math.noise(x * 0.02, z * 0.02, 100)
                biome = forestNoise > 0.2 and "forest" or "plains"
            elseif height <= 120 then
                biome = "hills"
            elseif height <= 150 then
                biome = "mountains"
            else
                biome = "snow"
            end
            
            self.biomeMap[x][z] = biome
        end
    end
    
    print("🌲 Biome map generated")
end

-- Generate climate zones
function TerrainManager:GenerateClimateMap()
    local resolution = 256
    self.climateMap = {}
    
    for x = 1, resolution do
        self.climateMap[x] = {}
        for z = 1, resolution do
            -- Climate based on latitude (z-coordinate)
            local latitude = z / resolution
            local climate = "temperate" -- Default
            
            if latitude < 0.2 then
                climate = "tropical"
            elseif latitude < 0.4 then
                climate = "temperate"
            elseif latitude < 0.8 then
                climate = "continental"
            else
                climate = "arctic"
            end
            
            -- Add some variation with noise
            local climateNoise = math.noise(x * 0.005, z * 0.005, 200)
            if climateNoise > 0.3 then
                -- Shift climate zone slightly
                local climates = {"tropical", "temperate", "continental", "arctic"}
                local currentIndex = 1
                for i, c in ipairs(climates) do
                    if c == climate then
                        currentIndex = i
                        break
                    end
                end
                
                local newIndex = math.min(#climates, currentIndex + 1)
                climate = climates[newIndex]
            end
            
            self.climateMap[x][z] = climate
        end
    end
    
    print("🌡️ Climate map generated")
end

-- Generate cities in suitable locations
function TerrainManager:GenerateCities()
    local numCities = math.random(15, 25)
    self.cities = {}
    
    for i = 1, numCities do
        local attempts = 0
        local cityPlaced = false
        
        while not cityPlaced and attempts < 100 do
            local x = math.random(20, 236) -- Avoid edges
            local z = math.random(20, 236)
            local height = self.heightMap[x][z]
            local biome = self.biomeMap[x][z]
            
            -- Cities prefer plains, forest edges, and coastal areas
            local suitable = false
            if biome == "plains" or biome == "forest" then
                suitable = true
            elseif biome == "beach" and height > 20 then
                suitable = true -- Coastal city
            end
            
            -- Check distance from other cities
            local tooClose = false
            for _, city in pairs(self.cities) do
                local distance = math.sqrt((x - city.x)^2 + (z - city.z)^2)
                if distance < 30 then -- Minimum distance between cities
                    tooClose = true
                    break
                end
            end
            
            if suitable and not tooClose then
                local cityId = HttpService:GenerateGUID(false)
                local city = {
                    id = cityId,
                    name = self:GenerateCityName(),
                    x = x,
                    z = z,
                    height = height,
                    biome = biome,
                    climate = self.climateMap[x][z],
                    population = math.random(5000, 50000),
                    size = math.random(1, 3), -- 1=small, 2=medium, 3=large
                    industries = {},
                    demands = {
                        passengers = math.random(100, 500),
                        mail = math.random(50, 200),
                        goods = math.random(200, 800)
                    },
                    created = os.time()
                }
                
                -- Add industries based on biome and climate
                self:GenerateCityIndustries(city)
                
                self.cities[cityId] = city
                cityPlaced = true
                
                print("🏙️ City generated:", city.name, "Population:", city.population)
            end
            
            attempts = attempts + 1
        end
    end
    
    print("🏘️ Generated", self:CountCities(), "cities")
end

-- Generate city industries
function TerrainManager:GenerateCityIndustries(city)
    local industries = {}
    
    -- Base industries for all cities
    table.insert(industries, {type = "residential", level = 1, production = 0, demand = city.population / 100})
    table.insert(industries, {type = "commercial", level = 1, production = city.population / 50, demand = city.population / 200})
    
    -- Biome-specific industries
    if city.biome == "forest" then
        table.insert(industries, {type = "lumber_mill", level = 1, production = 100, demand = 0})
    elseif city.biome == "hills" or city.biome == "mountains" then
        table.insert(industries, {type = "quarry", level = 1, production = 150, demand = 0})
        if math.random() > 0.5 then
            table.insert(industries, {type = "iron_mine", level = 1, production = 80, demand = 0})
        end
    elseif city.biome == "plains" then
        table.insert(industries, {type = "farm", level = 1, production = 200, demand = 0})
        if math.random() > 0.7 then
            table.insert(industries, {type = "livestock", level = 1, production = 120, demand = 50})
        end
    end
    
    -- Climate-specific industries
    if city.climate == "continental" or city.climate == "arctic" then
        table.insert(industries, {type = "coal_mine", level = 1, production = 100, demand = 0})
    end
    
    city.industries = industries
end

-- Generate natural resources
function TerrainManager:GenerateResources()
    self.resources = {}
    
    -- Coal deposits in hills/mountains
    for i = 1, math.random(10, 20) do
        local x = math.random(1, 256)
        local z = math.random(1, 256)
        local biome = self.biomeMap[x][z]
        
        if biome == "hills" or biome == "mountains" then
            local resourceId = HttpService:GenerateGUID(false)
            self.resources[resourceId] = {
                id = resourceId,
                type = "coal",
                x = x,
                z = z,
                amount = math.random(10000, 50000),
                quality = math.random(60, 100)
            }
        end
    end
    
    -- Iron ore deposits
    for i = 1, math.random(5, 15) do
        local x = math.random(1, 256)
        local z = math.random(1, 256)
        local biome = self.biomeMap[x][z]
        
        if biome == "hills" or biome == "mountains" then
            local resourceId = HttpService:GenerateGUID(false)
            self.resources[resourceId] = {
                id = resourceId,
                type = "iron",
                x = x,
                z = z,
                amount = math.random(5000, 25000),
                quality = math.random(70, 95)
            }
        end
    end
    
    -- Oil deposits
    for i = 1, math.random(3, 8) do
        local x = math.random(1, 256)
        local z = math.random(1, 256)
        local biome = self.biomeMap[x][z]
        
        if biome == "ocean" or biome == "plains" then
            local resourceId = HttpService:GenerateGUID(false)
            self.resources[resourceId] = {
                id = resourceId,
                type = "oil",
                x = x,
                z = z,
                amount = math.random(20000, 100000),
                quality = math.random(80, 100)
            }
        end
    end
    
    print("⛏️ Generated", self:CountResources(), "resource deposits")
end

-- Create 3D terrain in workspace
function TerrainManager:CreateTerrain()
    local terrain = Workspace.Terrain
    local region = Region3.new(Vector3.new(-self.mapSize/2, 0, -self.mapSize/2), Vector3.new(self.mapSize/2, self.heightScale + 50, self.mapSize/2))
    
    -- Clear existing terrain
    terrain:FillRegion(region, 4, Enum.Material.Air)
    
    -- Create terrain based on height and biome maps
    local resolution = 256
    local cellSize = self.mapSize / resolution
    
    for x = 1, resolution - 1 do
        for z = 1, resolution - 1 do
            local worldX = (x - resolution/2) * cellSize
            local worldZ = (z - resolution/2) * cellSize
            
            local height = self.heightMap[x][z]
            local biome = self.biomeMap[x][z]
            local biomeData = self.biomes[biome]
            
            if height > 0 then
                local cellRegion = Region3.new(
                    Vector3.new(worldX, 0, worldZ),
                    Vector3.new(worldX + cellSize, height, worldZ + cellSize)
                )
                
                terrain:FillRegion(cellRegion, 4, biomeData.material)
            end
        end
    end
    
    print("🌍 3D terrain created")
end

-- Add vegetation to terrain
function TerrainManager:AddVegetation()
    local vegetationFolder = self.terrainFolder:FindFirstChild("Vegetation") or Instance.new("Folder")
    vegetationFolder.Name = "Vegetation"
    vegetationFolder.Parent = self.terrainFolder
    
    local treeCount = 0
    local resolution = 256
    local cellSize = self.mapSize / resolution
    
    for x = 1, resolution, 4 do -- Sample every 4th cell for performance
        for z = 1, resolution, 4 do
            local biome = self.biomeMap[x][z]
            local height = self.heightMap[x][z]
            
            if biome == "forest" and height > 25 and height < 100 then
                -- Add trees with some randomness
                if math.random() > 0.3 then -- 70% chance for tree
                    local worldX = (x - resolution/2) * cellSize
                    local worldZ = (z - resolution/2) * cellSize
                    
                    self:CreateTree(Vector3.new(worldX, height, worldZ), vegetationFolder)
                    treeCount = treeCount + 1
                end
            elseif biome == "plains" and math.random() > 0.9 then -- Sparse trees in plains
                local worldX = (x - resolution/2) * cellSize
                local worldZ = (z - resolution/2) * cellSize
                
                self:CreateTree(Vector3.new(worldX, height, worldZ), vegetationFolder)
                treeCount = treeCount + 1
            end
        end
    end
    
    print("🌳 Added", treeCount, "trees")
end

-- Create a tree model
function TerrainManager:CreateTree(position, parent)
    local tree = Instance.new("Model")
    tree.Name = "Tree"
    tree.Parent = parent
    
    -- Tree trunk
    local trunk = Instance.new("Part")
    trunk.Name = "Trunk"
    trunk.Size = Vector3.new(1, math.random(8, 15), 1)
    trunk.Position = position + Vector3.new(0, trunk.Size.Y/2, 0)
    trunk.Material = Enum.Material.Wood
    trunk.BrickColor = BrickColor.new("Brown")
    trunk.Anchored = true
    trunk.CanCollide = true
    trunk.Parent = tree
    
    -- Tree leaves
    local leaves = Instance.new("Part")
    leaves.Name = "Leaves"
    leaves.Size = Vector3.new(math.random(6, 10), math.random(6, 10), math.random(6, 10))
    leaves.Position = position + Vector3.new(0, trunk.Size.Y + leaves.Size.Y/2 - 2, 0)
    leaves.Material = Enum.Material.LeafyGrass
    leaves.BrickColor = BrickColor.new("Bright green")
    leaves.Shape = Enum.PartType.Ball
    leaves.Anchored = true
    leaves.CanCollide = false
    leaves.Parent = tree
    
    tree.PrimaryPart = trunk
end

-- Add water bodies
function TerrainManager:AddWaterBodies()
    local waterFolder = self.terrainFolder:FindFirstChild("Water") or Instance.new("Folder")
    waterFolder.Name = "Water"
    waterFolder.Parent = self.terrainFolder
    
    -- Create rivers and lakes
    self:CreateRivers(waterFolder)
    self:CreateLakes(waterFolder)
    
    print("💧 Water bodies created")
end

-- Create rivers
function TerrainManager:CreateRivers(parent)
    local numRivers = math.random(3, 8)
    
    for i = 1, numRivers do
        local startX = math.random(50, 206)
        local startZ = math.random(50, 206)
        local riverLength = math.random(50, 150)
        
        local currentX, currentZ = startX, startZ
        
        for segment = 1, riverLength do
            -- River flows towards lower elevation
            local bestX, bestZ = currentX, currentZ
            local lowestHeight = self.heightMap[currentX][currentZ]
            
            -- Check surrounding cells for lowest elevation
            for dx = -1, 1 do
                for dz = -1, 1 do
                    local checkX = math.max(1, math.min(256, currentX + dx))
                    local checkZ = math.max(1, math.min(256, currentZ + dz))
                    
                    if self.heightMap[checkX][checkZ] < lowestHeight then
                        lowestHeight = self.heightMap[checkX][checkZ]
                        bestX, bestZ = checkX, checkZ
                    end
                end
            end
            
            -- Create river segment
            local worldX = (currentX - 128) * (self.mapSize / 256)
            local worldZ = (currentZ - 128) * (self.mapSize / 256)
            
            local riverSegment = Instance.new("Part")
            riverSegment.Name = "RiverSegment"
            riverSegment.Size = Vector3.new(4, 1, 4)
            riverSegment.Position = Vector3.new(worldX, self.seaLevel, worldZ)
            riverSegment.Material = Enum.Material.Water
            riverSegment.BrickColor = BrickColor.new("Bright blue")
            riverSegment.Anchored = true
            riverSegment.CanCollide = false
            riverSegment.Transparency = 0.3
            riverSegment.Parent = parent
            
            currentX, currentZ = bestX, bestZ
            
            -- Stop if reached ocean
            if lowestHeight <= 15 then
                break
            end
        end
    end
end

-- Create lakes
function TerrainManager:CreateLakes(parent)
    local numLakes = math.random(5, 12)
    
    for i = 1, numLakes do
        local centerX = math.random(30, 226)
        local centerZ = math.random(30, 226)
        local lakeSize = math.random(5, 15)
        
        -- Only create lakes in suitable locations
        local height = self.heightMap[centerX][centerZ]
        if height > 30 and height < 80 then
            for x = centerX - lakeSize, centerX + lakeSize do
                for z = centerZ - lakeSize, centerZ + lakeSize do
                    if x >= 1 and x <= 256 and z >= 1 and z <= 256 then
                        local distance = math.sqrt((x - centerX)^2 + (z - centerZ)^2)
                        if distance <= lakeSize then
                            local worldX = (x - 128) * (self.mapSize / 256)
                            local worldZ = (z - 128) * (self.mapSize / 256)
                            
                            local water = Instance.new("Part")
                            water.Name = "Lake"
                            water.Size = Vector3.new(8, 2, 8)
                            water.Position = Vector3.new(worldX, height + 1, worldZ)
                            water.Material = Enum.Material.Water
                            water.BrickColor = BrickColor.new("Bright blue")
                            water.Anchored = true
                            water.CanCollide = false
                            water.Transparency = 0.2
                            water.Parent = parent
                        end
                    end
                end
            end
        end
    end
end

-- Terrain modification
function TerrainManager:ModifyTerrain(player, operation, position, size, strength)
    local playerData = self:GetPlayerData(player)
    local cost = self:CalculateTerraformCost(operation, size, strength)
    
    if playerData.money < cost then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Nicht genügend Geld für Terraforming!", "error")
        return
    end
    
    -- Apply terrain modification
    local terrain = Workspace.Terrain
    local region = Region3.new(position - size/2, position + size/2)
    
    if operation == "raise" then
        terrain:FillRegion(region, 4, Enum.Material.Rock)
    elseif operation == "lower" then
        terrain:FillRegion(region, 4, Enum.Material.Air)
    elseif operation == "flatten" then
        terrain:FillRegion(region, 4, Enum.Material.Air)
        local flatRegion = Region3.new(Vector3.new(region.Min.X, position.Y - 2, region.Min.Z), Vector3.new(region.Max.X, position.Y, region.Max.Z))
        terrain:FillRegion(flatRegion, 4, Enum.Material.Ground)
    end
    
    -- Deduct cost
    self:GetEconomyManager():AddTransaction(player, -cost, "Terraforming: " .. operation, "expense")
    
    -- Notify client
    ReplicatedStorage.Events.TerrainModifiedEvent:FireClient(player, operation, position, size)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Terrain modifiziert für " .. cost .. "$", "success")
    
    print("🏔️ Terrain modified:", player.Name, operation, "Cost:", cost)
end

-- Calculate terraforming cost
function TerrainManager:CalculateTerraformCost(operation, size, strength)
    local volume = size.X * size.Y * size.Z
    local baseCost = self.terraformCosts[operation] or 100
    return math.floor(volume * baseCost * strength)
end

-- Check if building can be placed
function TerrainManager:CanPlaceBuilding(position, buildingSize)
    -- Check terrain height variation
    local terrain = Workspace.Terrain
    local region = Region3.new(position - buildingSize/2, position + buildingSize/2)
    
    -- Simple check - ensure area is relatively flat
    local material, occupancy = terrain:ReadVoxels(region, 4)
    
    -- Check if area has suitable ground
    local hasGround = false
    for x = 1, material.Size.X do
        for y = 1, material.Size.Y do
            for z = 1, material.Size.Z do
                local mat = material[x][y][z]
                if mat == Enum.Material.Grass or mat == Enum.Material.Ground or mat == Enum.Material.Rock then
                    hasGround = true
                    break
                end
            end
        end
    end
    
    return hasGround
end

-- Get terrain info at position
function TerrainManager:GetTerrainInfo(position)
    local terrain = Workspace.Terrain
    local region = Region3.new(position - Vector3.new(2, 2, 2), position + Vector3.new(2, 2, 2))
    local material, occupancy = terrain:ReadVoxels(region, 4)
    
    -- Find dominant material
    local materialCounts = {}
    for x = 1, material.Size.X do
        for y = 1, material.Size.Y do
            for z = 1, material.Size.Z do
                local mat = material[x][y][z]
                materialCounts[mat] = (materialCounts[mat] or 0) + 1
            end
        end
    end
    
    local dominantMaterial = Enum.Material.Air
    local maxCount = 0
    for mat, count in pairs(materialCounts) do
        if count > maxCount then
            maxCount = count
            dominantMaterial = mat
        end
    end
    
    return {
        material = dominantMaterial,
        height = position.Y,
        buildable = self:CanPlaceBuilding(position, Vector3.new(10, 5, 10))
    }
end

-- Generate city name
function TerrainManager:GenerateCityName()
    local prefixes = {"New", "Old", "North", "South", "East", "West", "Upper", "Lower", "Great", "Little"}
    local bases = {"Springfield", "Riverside", "Hillview", "Oakwood", "Pineville", "Fairfield", "Greenwood", "Brookside", "Lakeside", "Mountainview"}
    local suffixes = {"ton", "ville", "burg", "ford", "field", "wood", "dale", "port", "haven", "ridge"}
    
    if math.random() > 0.5 then
        return prefixes[math.random(#prefixes)] .. " " .. bases[math.random(#bases)]
    else
        return bases[math.random(#bases)] .. suffixes[math.random(#suffixes)]
    end
end

-- Helper functions
function TerrainManager:CountResources()
    local count = 0
    for _ in pairs(self.resources) do
        count = count + 1
    end
    return count
end

function TerrainManager:CountCities()
    local count = 0
    for _ in pairs(self.cities) do
        count = count + 1
    end
    return count
end

function TerrainManager:GetPlayerData(player)
    local GameManager = require(script.Parent.GameManager)
    return GameManager:GetPlayerData(player)
end

function TerrainManager:GetEconomyManager()
    local EconomyManager = require(script.Parent.EconomyManager)
    return EconomyManager.new()
end

return TerrainManager
