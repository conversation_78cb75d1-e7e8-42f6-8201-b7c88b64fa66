-- ServerScriptService/Managers/StationManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Verwaltet Stationen, Routen und Linien

local StationManager = {}
StationManager.__index = StationManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")

-- Station-Typen
local STATION_TYPES = {
    TRAIN_STATION = {
        name = "Bahnhof",
        cost = 10000,
        capacity = 500,
        size = Vector3.new(20, 8, 15),
        color = Color3.fromRGB(120, 80, 60),
        vehicleTypes = {"TRAIN"},
        cargoTypes = {"Coal", "Iron", "Steel", "Passengers"}
    },
    TRUCK_DEPOT = {
        name = "LKW-Depot",
        cost = 5000,
        capacity = 100,
        size = Vector3.new(15, 6, 12),
        color = Color3.fromRGB(100, 100, 100),
        vehicleTypes = {"TRUCK"},
        cargoTypes = {"Food", "Goods", "Mail"}
    },
    HARBOR = {
        name = "Hafen",
        cost = 25000,
        capacity = 1000,
        size = Vector3.new(30, 10, 25),
        color = Color3.fromRGB(60, 100, 140),
        vehicleTypes = {"SHIP"},
        cargoTypes = {"Coal", "Iron", "Oil", "Passengers"}
    },
    BUS_STOP = {
        name = "Bushaltestelle",
        cost = 2000,
        capacity = 50,
        size = Vector3.new(8, 4, 6),
        color = Color3.fromRGB(200, 200, 50),
        vehicleTypes = {"BUS"},
        cargoTypes = {"Passengers"}
    }
}

-- Konstruktor
function StationManager.new()
    local self = setmetatable({}, StationManager)
    
    self.stations = {}
    self.routes = {}
    self.lines = {}
    self.nextStationId = 1
    self.nextRouteId = 1
    self.nextLineId = 1
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function StationManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Station-Events
    if not Events:FindFirstChild("BuildStationEvent") then
        local buildStationEvent = Instance.new("RemoteEvent")
        buildStationEvent.Name = "BuildStationEvent"
        buildStationEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("CreateRouteEvent") then
        local createRouteEvent = Instance.new("RemoteEvent")
        createRouteEvent.Name = "CreateRouteEvent"
        createRouteEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("CreateLineEvent") then
        local createLineEvent = Instance.new("RemoteEvent")
        createLineEvent.Name = "CreateLineEvent"
        createLineEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("GetStationDataFunction") then
        local getStationDataFunction = Instance.new("RemoteFunction")
        getStationDataFunction.Name = "GetStationDataFunction"
        getStationDataFunction.Parent = Events
    end
    
    -- Event-Handler
    Events.BuildStationEvent.OnServerEvent:Connect(function(player, stationType, position)
        self:BuildStation(player, stationType, position)
    end)
    
    Events.CreateRouteEvent.OnServerEvent:Connect(function(player, stationIds)
        self:CreateRoute(player, stationIds)
    end)
    
    Events.CreateLineEvent.OnServerEvent:Connect(function(player, routeId, vehicleType, frequency)
        self:CreateLine(player, routeId, vehicleType, frequency)
    end)
    
    Events.GetStationDataFunction.OnServerInvoke = function(player)
        return self:GetStationData(player)
    end
end

-- Station bauen
function StationManager:BuildStation(player, stationType, position)
    local stationConfig = STATION_TYPES[stationType]
    if not stationConfig then
        warn("Unbekannter Stationstyp:", stationType)
        return false
    end
    
    -- Kosten prüfen (Integration mit EconomyManager)
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager and not economyManager:CanPlayerAfford(player.UserId, stationConfig.cost) then
        return false
    end
    
    -- Station erstellen
    local stationId = "station_" .. self.nextStationId
    self.nextStationId = self.nextStationId + 1
    
    local station = {
        id = stationId,
        type = stationType,
        name = stationConfig.name .. " " .. self.nextStationId,
        position = position,
        owner = player.UserId,
        capacity = stationConfig.capacity,
        currentCargo = {},
        waitingPassengers = 0,
        connectedRoutes = {},
        isActive = true,
        buildTime = tick()
    }
    
    -- 3D-Modell erstellen
    local stationModel = self:CreateStationModel(station, stationConfig)
    station.model = stationModel
    
    self.stations[stationId] = station
    
    -- Kosten abziehen
    if economyManager then
        economyManager:DeductPlayerMoney(player.UserId, stationConfig.cost)
    end
    
    print("🚉 Station gebaut:", station.name, "an Position", position)
    return true
end

-- Station-Modell erstellen
function StationManager:CreateStationModel(station, config)
    local model = Instance.new("Model")
    model.Name = station.name
    model.Parent = Workspace
    
    -- Hauptgebäude
    local building = Instance.new("Part")
    building.Name = "Building"
    building.Size = config.size
    building.Position = station.position
    building.BrickColor = BrickColor.new(config.color)
    building.Material = Enum.Material.Brick
    building.Anchored = true
    building.Parent = model
    
    -- Dach
    local roof = Instance.new("Part")
    roof.Name = "Roof"
    roof.Size = Vector3.new(config.size.X + 2, 2, config.size.Z + 2)
    roof.Position = station.position + Vector3.new(0, config.size.Y/2 + 1, 0)
    roof.BrickColor = BrickColor.new("Dark red")
    roof.Material = Enum.Material.Slate
    roof.Anchored = true
    roof.Parent = model
    
    -- Schild
    local sign = Instance.new("Part")
    sign.Name = "Sign"
    sign.Size = Vector3.new(8, 3, 0.5)
    sign.Position = station.position + Vector3.new(0, config.size.Y/2 + 3, config.size.Z/2 + 1)
    sign.BrickColor = BrickColor.new("White")
    sign.Material = Enum.Material.Neon
    sign.Anchored = true
    sign.Parent = model
    
    -- Text auf Schild
    local surfaceGui = Instance.new("SurfaceGui")
    surfaceGui.Face = Enum.NormalId.Front
    surfaceGui.Parent = sign
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = station.name
    textLabel.TextColor3 = Color3.fromRGB(0, 0, 0)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = surfaceGui
    
    -- Plattform/Ladezone
    local platform = Instance.new("Part")
    platform.Name = "Platform"
    platform.Size = Vector3.new(config.size.X + 10, 1, config.size.Z + 10)
    platform.Position = station.position - Vector3.new(0, config.size.Y/2, 0)
    platform.BrickColor = BrickColor.new("Medium stone grey")
    platform.Material = Enum.Material.Concrete
    platform.Anchored = true
    platform.Parent = model
    
    return model
end

-- Route erstellen
function StationManager:CreateRoute(player, stationIds)
    if #stationIds < 2 then
        warn("Route benötigt mindestens 2 Stationen")
        return false
    end
    
    -- Prüfen ob alle Stationen existieren
    for _, stationId in ipairs(stationIds) do
        if not self.stations[stationId] then
            warn("Station nicht gefunden:", stationId)
            return false
        end
    end
    
    local routeId = "route_" .. self.nextRouteId
    self.nextRouteId = self.nextRouteId + 1
    
    local route = {
        id = routeId,
        name = "Route " .. self.nextRouteId,
        stations = stationIds,
        owner = player.UserId,
        distance = self:CalculateRouteDistance(stationIds),
        isActive = true,
        lines = {},
        createTime = tick()
    }
    
    self.routes[routeId] = route
    
    -- Stationen mit Route verbinden
    for _, stationId in ipairs(stationIds) do
        table.insert(self.stations[stationId].connectedRoutes, routeId)
    end
    
    print("🛤️ Route erstellt:", route.name, "mit", #stationIds, "Stationen")
    return routeId
end

-- Linie erstellen
function StationManager:CreateLine(player, routeId, vehicleType, frequency)
    local route = self.routes[routeId]
    if not route then
        warn("Route nicht gefunden:", routeId)
        return false
    end
    
    local lineId = "line_" .. self.nextLineId
    self.nextLineId = self.nextLineId + 1
    
    local line = {
        id = lineId,
        name = "Linie " .. self.nextLineId,
        route = routeId,
        vehicleType = vehicleType,
        frequency = frequency, -- Minuten zwischen Fahrzeugen
        owner = player.UserId,
        vehicles = {},
        isActive = true,
        profit = 0,
        passengers = 0,
        cargo = 0,
        createTime = tick()
    }
    
    self.lines[lineId] = line
    table.insert(route.lines, lineId)
    
    print("🚌 Linie erstellt:", line.name, "auf Route", route.name)
    return lineId
end

-- Route-Distanz berechnen
function StationManager:CalculateRouteDistance(stationIds)
    local totalDistance = 0
    
    for i = 1, #stationIds - 1 do
        local station1 = self.stations[stationIds[i]]
        local station2 = self.stations[stationIds[i + 1]]
        
        if station1 and station2 then
            local distance = (station1.position - station2.position).Magnitude
            totalDistance = totalDistance + distance
        end
    end
    
    return totalDistance
end

-- Station-Daten abrufen
function StationManager:GetStationData(player)
    local playerStations = {}
    local playerRoutes = {}
    local playerLines = {}
    
    -- Spieler-Stationen filtern
    for stationId, station in pairs(self.stations) do
        if station.owner == player.UserId then
            playerStations[stationId] = {
                id = station.id,
                name = station.name,
                type = station.type,
                position = station.position,
                capacity = station.capacity,
                currentCargo = station.currentCargo,
                waitingPassengers = station.waitingPassengers,
                connectedRoutes = station.connectedRoutes,
                isActive = station.isActive
            }
        end
    end
    
    -- Spieler-Routen filtern
    for routeId, route in pairs(self.routes) do
        if route.owner == player.UserId then
            playerRoutes[routeId] = {
                id = route.id,
                name = route.name,
                stations = route.stations,
                distance = route.distance,
                lines = route.lines,
                isActive = route.isActive
            }
        end
    end
    
    -- Spieler-Linien filtern
    for lineId, line in pairs(self.lines) do
        if line.owner == player.UserId then
            playerLines[lineId] = {
                id = line.id,
                name = line.name,
                route = line.route,
                vehicleType = line.vehicleType,
                frequency = line.frequency,
                vehicles = line.vehicles,
                isActive = line.isActive,
                profit = line.profit,
                passengers = line.passengers,
                cargo = line.cargo
            }
        end
    end
    
    return {
        stations = playerStations,
        routes = playerRoutes,
        lines = playerLines,
        stationTypes = STATION_TYPES
    }
end

-- Update-Funktion
function StationManager:Update(deltaTime)
    -- Cargo-Verarbeitung in Stationen
    for stationId, station in pairs(self.stations) do
        if station.isActive then
            self:ProcessStationCargo(station, deltaTime)
        end
    end
    
    -- Linien-Simulation
    for lineId, line in pairs(self.lines) do
        if line.isActive then
            self:UpdateLine(line, deltaTime)
        end
    end
end

-- Station-Cargo verarbeiten
function StationManager:ProcessStationCargo(station, deltaTime)
    -- Passagiere generieren
    if math.random() < 0.1 * deltaTime then
        station.waitingPassengers = station.waitingPassengers + math.random(1, 5)
    end
    
    -- Cargo-Verfall simulieren
    for cargoType, amount in pairs(station.currentCargo) do
        if math.random() < 0.01 * deltaTime then
            station.currentCargo[cargoType] = math.max(0, amount - 1)
        end
    end
end

-- Linie aktualisieren
function StationManager:UpdateLine(line, deltaTime)
    -- Vereinfachte Linien-Simulation
    if math.random() < 0.05 * deltaTime then
        line.profit = line.profit + math.random(100, 500)
        line.passengers = line.passengers + math.random(5, 20)
        line.cargo = line.cargo + math.random(10, 50)
    end
end

return StationManager
