-- StarterPlayerScripts/GUI/VehiclePurchaseGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Fahrzeug-<PERSON><PERSON> und Verwaltungs-GUI

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local BuyVehicleEvent = Events:WaitForChild("BuyVehicleEvent")
local SellVehicleEvent = Events:WaitForChild("SellVehicleEvent")
local GetVehicleDataFunction = Events:WaitForChild("GetVehicleDataFunction")
local CreateDepotEvent = Events:WaitForChild("CreateDepotEvent")

local VehiclePurchaseGUI = {}
VehiclePurchaseGUI.IsOpen = false
VehiclePurchaseGUI.CurrentTab = "PURCHASE" -- PURCHASE, MANAGE, DEPOTS
VehiclePurchaseGUI.SelectedCategory = "TRAIN" -- TRAIN, TRUCK, SHIP, BUS
VehiclePurchaseGUI.VehicleData = {}
VehiclePurchaseGUI.PlayerMoney = 1000000

-- Fahrzeug-Kategorien
local VEHICLE_CATEGORIES = {
    TRAIN = {name = "🚂 Züge", icon = "🚂", color = Color3.fromRGB(100, 150, 255)},
    TRUCK = {name = "🚛 LKWs", icon = "🚛", color = Color3.fromRGB(255, 150, 100)},
    SHIP = {name = "🚢 Schiffe", icon = "🚢", color = Color3.fromRGB(100, 255, 150)},
    BUS = {name = "🚌 Busse", icon = "🚌", color = Color3.fromRGB(255, 100, 150)}
}

-- GUI erstellen
function VehiclePurchaseGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "VehiclePurchaseGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1400, 0, 900)
    mainFrame.Position = UDim2.new(0.5, -700, 0.5, -450)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 60)
    title.BackgroundTransparency = 1
    title.Text = "🚗 FAHRZEUG-ZENTRALE"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Geld-Anzeige
    local moneyFrame = Instance.new("Frame")
    moneyFrame.Size = UDim2.new(0, 300, 0, 40)
    moneyFrame.Position = UDim2.new(1, -320, 0, 10)
    moneyFrame.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
    moneyFrame.BorderSizePixel = 0
    moneyFrame.Parent = mainFrame
    
    local moneyCorner = Instance.new("UICorner")
    moneyCorner.CornerRadius = UDim.new(0, 8)
    moneyCorner.Parent = moneyFrame
    
    local moneyLabel = Instance.new("TextLabel")
    moneyLabel.Size = UDim2.new(1, -10, 1, 0)
    moneyLabel.Position = UDim2.new(0, 5, 0, 0)
    moneyLabel.BackgroundTransparency = 1
    moneyLabel.Text = "💰 $" .. string.format("%,d", self.PlayerMoney)
    moneyLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    moneyLabel.TextScaled = true
    moneyLabel.Font = Enum.Font.SourceSansBold
    moneyLabel.Parent = moneyFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 70)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "PURCHASE", text = "🛒 Fahrzeuge kaufen", icon = "🛒"},
        {name = "MANAGE", text = "🔧 Fahrzeuge verwalten", icon = "🔧"},
        {name = "DEPOTS", text = "🏭 Depots", icon = "🏭"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 760)
    contentFrame.Position = UDim2.new(0, 10, 0, 130)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    self.MoneyLabel = moneyLabel
    
    return screenGui
end

-- Tab wechseln
function VehiclePurchaseGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function VehiclePurchaseGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        end
    end
end

-- Content aktualisieren
function VehiclePurchaseGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "PURCHASE" then
        self:CreatePurchaseContent()
    elseif self.CurrentTab == "MANAGE" then
        self:CreateManageContent()
    elseif self.CurrentTab == "DEPOTS" then
        self:CreateDepotsContent()
    end
end

-- Kauf-Content erstellen
function VehiclePurchaseGUI:CreatePurchaseContent()
    -- Kategorie-Auswahl
    local categoryFrame = Instance.new("Frame")
    categoryFrame.Size = UDim2.new(1, -20, 0, 60)
    categoryFrame.Position = UDim2.new(0, 10, 0, 10)
    categoryFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    categoryFrame.BorderSizePixel = 0
    categoryFrame.Parent = self.ContentFrame
    
    local categoryCorner = Instance.new("UICorner")
    categoryCorner.CornerRadius = UDim.new(0, 8)
    categoryCorner.Parent = categoryFrame
    
    local categoryButtons = {}
    local i = 0
    for categoryKey, categoryData in pairs(VEHICLE_CATEGORIES) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(0.24, 0, 0, 40)
        button.Position = UDim2.new(i * 0.25, 10, 0, 10)
        button.BackgroundColor3 = categoryKey == self.SelectedCategory and categoryData.color or Color3.fromRGB(60, 65, 70)
        button.Text = categoryData.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = categoryFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self.SelectedCategory = categoryKey
            self:UpdateContent()
        end)
        
        categoryButtons[categoryKey] = button
        i = i + 1
    end
    
    -- Fahrzeug-Liste
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 0, 670)
    scrollFrame.Position = UDim2.new(0, 10, 0, 80)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 12
    scrollFrame.Parent = self.ContentFrame
    
    self:CreateVehicleList(scrollFrame)
end

-- Fahrzeug-Liste erstellen
function VehiclePurchaseGUI:CreateVehicleList(parent)
    -- Beispiel-Fahrzeuge für die gewählte Kategorie
    local vehicles = self:GetVehiclesForCategory(self.SelectedCategory)
    
    local yPos = 10
    for i, vehicle in ipairs(vehicles) do
        local vehicleFrame = Instance.new("Frame")
        vehicleFrame.Size = UDim2.new(1, -20, 0, 120)
        vehicleFrame.Position = UDim2.new(0, 10, 0, yPos)
        vehicleFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        vehicleFrame.BorderSizePixel = 0
        vehicleFrame.Parent = parent
        
        local vehicleCorner = Instance.new("UICorner")
        vehicleCorner.CornerRadius = UDim.new(0, 8)
        vehicleCorner.Parent = vehicleFrame
        
        -- Fahrzeug-Info
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(0.4, 0, 0, 30)
        nameLabel.Position = UDim2.new(0, 15, 0, 10)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = vehicle.name
        nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        nameLabel.TextScaled = true
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Parent = vehicleFrame
        
        local descLabel = Instance.new("TextLabel")
        descLabel.Size = UDim2.new(0.4, 0, 0, 20)
        descLabel.Position = UDim2.new(0, 15, 0, 40)
        descLabel.BackgroundTransparency = 1
        descLabel.Text = vehicle.description
        descLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        descLabel.TextScaled = true
        descLabel.Font = Enum.Font.SourceSans
        descLabel.TextXAlignment = Enum.TextXAlignment.Left
        descLabel.Parent = vehicleFrame
        
        -- Statistiken
        local statsText = string.format("⚡ %d PS | 🏃 %d km/h | 👥 %d | 📦 %dt", 
            vehicle.power, vehicle.maxSpeed, vehicle.capacity.passengers, vehicle.capacity.cargo)
        
        local statsLabel = Instance.new("TextLabel")
        statsLabel.Size = UDim2.new(0.4, 0, 0, 20)
        statsLabel.Position = UDim2.new(0, 15, 0, 65)
        statsLabel.BackgroundTransparency = 1
        statsLabel.Text = statsText
        statsLabel.TextColor3 = Color3.fromRGB(150, 200, 255)
        statsLabel.TextScaled = true
        statsLabel.Font = Enum.Font.SourceSans
        statsLabel.TextXAlignment = Enum.TextXAlignment.Left
        statsLabel.Parent = vehicleFrame
        
        -- Preis
        local priceLabel = Instance.new("TextLabel")
        priceLabel.Size = UDim2.new(0.2, 0, 0, 30)
        priceLabel.Position = UDim2.new(0.5, 0, 0, 10)
        priceLabel.BackgroundTransparency = 1
        priceLabel.Text = "💰 $" .. string.format("%,d", vehicle.cost)
        priceLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
        priceLabel.TextScaled = true
        priceLabel.Font = Enum.Font.SourceSansBold
        priceLabel.Parent = vehicleFrame
        
        -- Kauf-Button
        local buyButton = Instance.new("TextButton")
        buyButton.Size = UDim2.new(0.15, 0, 0, 40)
        buyButton.Position = UDim2.new(0.8, 0, 0, 40)
        buyButton.BackgroundColor3 = vehicle.cost <= self.PlayerMoney and Color3.fromRGB(0, 150, 0) or Color3.fromRGB(100, 100, 100)
        buyButton.Text = "🛒 KAUFEN"
        buyButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        buyButton.TextScaled = true
        buyButton.Font = Enum.Font.SourceSansBold
        buyButton.BorderSizePixel = 0
        buyButton.Active = vehicle.cost <= self.PlayerMoney
        buyButton.Parent = vehicleFrame
        
        local buyCorner = Instance.new("UICorner")
        buyCorner.CornerRadius = UDim.new(0, 5)
        buyCorner.Parent = buyButton
        
        if vehicle.cost <= self.PlayerMoney then
            buyButton.MouseButton1Click:Connect(function()
                self:BuyVehicle(vehicle.id)
            end)
        end
        
        yPos = yPos + 130
    end
    
    parent.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Fahrzeuge für Kategorie abrufen
function VehiclePurchaseGUI:GetVehiclesForCategory(category)
    -- Beispiel-Daten - in der echten Implementierung vom Server abrufen
    local vehicles = {}
    
    if category == "TRAIN" then
        vehicles = {
            {id = "STEAM_LOCOMOTIVE", name = "Dampflok 'Pioneer'", cost = 50000, power = 800, maxSpeed = 60, capacity = {passengers = 0, cargo = 0}, description = "Klassische Dampflokomotive"},
            {id = "DIESEL_LOCOMOTIVE", name = "Diesellok 'Powerhouse'", cost = 120000, power = 2000, maxSpeed = 120, capacity = {passengers = 0, cargo = 0}, description = "Moderne Diesellokomotive"},
            {id = "PASSENGER_CAR_BASIC", name = "Personenwagen Standard", cost = 15000, power = 0, maxSpeed = 120, capacity = {passengers = 80, cargo = 0}, description = "Komfortabler Personenwagen"}
        }
    elseif category == "TRUCK" then
        vehicles = {
            {id = "SMALL_TRUCK", name = "Kleiner LKW 'Courier'", cost = 25000, power = 150, maxSpeed = 80, capacity = {passengers = 2, cargo = 5}, description = "Wendiger LKW für kurze Strecken"},
            {id = "HEAVY_TRUCK", name = "Schwerer LKW 'Titan'", cost = 80000, power = 400, maxSpeed = 90, capacity = {passengers = 2, cargo = 40}, description = "Leistungsstarker LKW"}
        }
    end
    
    return vehicles
end

-- Fahrzeug kaufen
function VehiclePurchaseGUI:BuyVehicle(vehicleId)
    -- Depot auswählen (vereinfacht - erstes verfügbares Depot)
    local depotId = 1
    
    BuyVehicleEvent:FireServer(vehicleId, depotId)
    print("🛒 Fahrzeug-Kauf angefordert:", vehicleId)
end

-- GUI öffnen
function VehiclePurchaseGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadVehicleData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function VehiclePurchaseGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Fahrzeug-Daten laden
function VehiclePurchaseGUI:LoadVehicleData()
    local success, data = pcall(function()
        return GetVehicleDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.VehicleData = data.vehicles or {}
        self.PlayerMoney = data.playerMoney or 1000000
        
        if self.MoneyLabel then
            self.MoneyLabel.Text = "💰 $" .. string.format("%,d", self.PlayerMoney)
        end
        
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Fahrzeug-Daten")
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.V then
        if VehiclePurchaseGUI.IsOpen then
            VehiclePurchaseGUI:CloseGUI()
        else
            VehiclePurchaseGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function VehiclePurchaseGUI:Initialize()
    print("🚗 VehiclePurchaseGUI initialisiert - Drücke 'V' zum Öffnen")
end

-- Auto-Start
VehiclePurchaseGUI:Initialize()

return VehiclePurchaseGUI
