-- ServerScriptService/Managers/MarketManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- <PERSON><PERSON><PERSON><PERSON><PERSON> Markt-Simulation mit dynamischen Preisen und Produktionsketten

local MarketManager = {}
MarketManager.__index = MarketManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Erweiterte Ressourcen-System
local RESOURCES = {
    -- Rohstoffe
    Coal = {type = "RAW", basePrice = 10, volatility = 0.2},
    Iron = {type = "RAW", basePrice = 15, volatility = 0.3},
    Wood = {type = "RAW", basePrice = 8, volatility = 0.15},
    Oil = {type = "RAW", basePrice = 25, volatility = 0.4},
    Stone = {type = "RAW", basePrice = 5, volatility = 0.1},
    Sand = {type = "RAW", basePrice = 3, volatility = 0.1},
    Grain = {type = "RAW", basePrice = 12, volatility = 0.25},
    Livestock = {type = "RAW", basePrice = 20, volatility = 0.3},
    
    -- Verarbeitete Güter
    Steel = {type = "PROCESSED", basePrice = 50, volatility = 0.3},
    Tools = {type = "PROCESSED", basePrice = 80, volatility = 0.25},
    Machinery = {type = "PROCESSED", basePrice = 200, volatility = 0.35},
    Food = {type = "PROCESSED", basePrice = 30, volatility = 0.2},
    Goods = {type = "PROCESSED", basePrice = 60, volatility = 0.3},
    Fuel = {type = "PROCESSED", basePrice = 40, volatility = 0.4},
    Chemicals = {type = "PROCESSED", basePrice = 100, volatility = 0.35},
    
    -- Spezial-Güter
    Passengers = {type = "SPECIAL", basePrice = 5, volatility = 0.1},
    Mail = {type = "SPECIAL", basePrice = 2, volatility = 0.05},
    Luxury_Goods = {type = "SPECIAL", basePrice = 300, volatility = 0.5},
    Electronics = {type = "SPECIAL", basePrice = 500, volatility = 0.6}
}

-- Produktionsketten
local PRODUCTION_CHAINS = {
    Steel = {
        inputs = {Coal = 2, Iron = 3},
        output = 1,
        time = 5, -- Minuten
        building = "SteelMill",
        efficiency = 1.0
    },
    Tools = {
        inputs = {Steel = 2, Wood = 1},
        output = 1,
        time = 8,
        building = "ToolFactory",
        efficiency = 1.0
    },
    Machinery = {
        inputs = {Steel = 3, Tools = 2},
        output = 1,
        time = 12,
        building = "MachineryFactory",
        efficiency = 1.0
    },
    Food = {
        inputs = {Grain = 2, Livestock = 1},
        output = 3,
        time = 3,
        building = "FoodProcessing",
        efficiency = 1.0
    },
    Goods = {
        inputs = {Wood = 1, Tools = 1},
        output = 2,
        time = 6,
        building = "GoodsFactory",
        efficiency = 1.0
    },
    Fuel = {
        inputs = {Oil = 2},
        output = 1,
        time = 4,
        building = "Refinery",
        efficiency = 1.0
    },
    Chemicals = {
        inputs = {Oil = 1, Coal = 1},
        output = 1,
        time = 10,
        building = "ChemicalPlant",
        efficiency = 1.0
    },
    Electronics = {
        inputs = {Steel = 1, Chemicals = 2, Tools = 1},
        output = 1,
        time = 15,
        building = "ElectronicsFactory",
        efficiency = 1.0
    }
}

-- Konstruktor
function MarketManager.new()
    local self = setmetatable({}, MarketManager)
    
    self.currentPrices = {}
    self.priceHistory = {}
    self.supply = {}
    self.demand = {}
    self.marketTrends = {}
    self.productionData = {}
    
    self:InitializePrices()
    self:InitializeEvents()
    
    return self
end

-- Preise initialisieren
function MarketManager:InitializePrices()
    for resource, data in pairs(RESOURCES) do
        self.currentPrices[resource] = data.basePrice
        self.priceHistory[resource] = {data.basePrice}
        self.supply[resource] = 1000 -- Basis-Angebot
        self.demand[resource] = 1000 -- Basis-Nachfrage
        self.marketTrends[resource] = 0 -- Neutral
    end
    
    print("💰 Marktpreise initialisiert")
end

-- Events initialisieren
function MarketManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetMarketDataFunction") then
        local getMarketDataFunction = Instance.new("RemoteFunction")
        getMarketDataFunction.Name = "GetMarketDataFunction"
        getMarketDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("TradeResourceEvent") then
        local tradeResourceEvent = Instance.new("RemoteEvent")
        tradeResourceEvent.Name = "TradeResourceEvent"
        tradeResourceEvent.Parent = Events
    end
    
    -- Event-Handler
    Events.GetMarketDataFunction.OnServerInvoke = function(player)
        return self:GetMarketData(player)
    end
    
    Events.TradeResourceEvent.OnServerEvent:Connect(function(player, action, resource, amount, price)
        self:ProcessTrade(player, action, resource, amount, price)
    end)
end

-- Markt-Update
function MarketManager:UpdateMarket(deltaTime)
    for resource, _ in pairs(RESOURCES) do
        self:UpdateResourcePrice(resource, deltaTime)
        self:UpdateSupplyDemand(resource, deltaTime)
    end
    
    -- Produktionsketten verarbeiten
    self:ProcessProductionChains(deltaTime)
    
    -- Preishistorie aktualisieren (alle 5 Minuten)
    if math.random() < 0.01 * deltaTime then
        self:UpdatePriceHistory()
    end
end

-- Ressourcen-Preis aktualisieren
function MarketManager:UpdateResourcePrice(resource, deltaTime)
    local resourceData = RESOURCES[resource]
    if not resourceData then return end
    
    local currentPrice = self.currentPrices[resource]
    local supply = self.supply[resource]
    local demand = self.demand[resource]
    
    -- Angebot/Nachfrage-Verhältnis
    local ratio = supply / demand
    local priceMultiplier = 1.0
    
    if ratio > 1.2 then
        -- Überangebot - Preis sinkt
        priceMultiplier = 0.95
    elseif ratio < 0.8 then
        -- Knappheit - Preis steigt
        priceMultiplier = 1.05
    end
    
    -- Volatilität hinzufügen
    local volatilityFactor = 1.0 + (math.random() - 0.5) * resourceData.volatility * deltaTime
    priceMultiplier = priceMultiplier * volatilityFactor
    
    -- Neuen Preis berechnen
    local newPrice = currentPrice * priceMultiplier
    
    -- Preis-Grenzen
    local minPrice = resourceData.basePrice * 0.3
    local maxPrice = resourceData.basePrice * 3.0
    newPrice = math.max(minPrice, math.min(maxPrice, newPrice))
    
    self.currentPrices[resource] = newPrice
    
    -- Trend berechnen
    local trend = (newPrice - currentPrice) / currentPrice
    self.marketTrends[resource] = trend
end

-- Angebot/Nachfrage aktualisieren
function MarketManager:UpdateSupplyDemand(resource, deltaTime)
    -- Zufällige Schwankungen
    local supplyChange = (math.random() - 0.5) * 100 * deltaTime
    local demandChange = (math.random() - 0.5) * 100 * deltaTime
    
    self.supply[resource] = math.max(100, self.supply[resource] + supplyChange)
    self.demand[resource] = math.max(100, self.demand[resource] + demandChange)
    
    -- Saisonale Effekte (vereinfacht)
    local time = tick()
    local seasonalFactor = math.sin(time / 3600) * 0.1 -- Stündliche Zyklen
    
    if resource == "Food" or resource == "Grain" then
        self.demand[resource] = self.demand[resource] * (1.0 + seasonalFactor)
    elseif resource == "Fuel" or resource == "Oil" then
        self.demand[resource] = self.demand[resource] * (1.0 - seasonalFactor * 0.5)
    end
end

-- Produktionsketten verarbeiten
function MarketManager:ProcessProductionChains(deltaTime)
    for product, chain in pairs(PRODUCTION_CHAINS) do
        -- Prüfen ob genug Input-Materialien vorhanden sind
        local canProduce = true
        for input, amount in pairs(chain.inputs) do
            if self.supply[input] < amount then
                canProduce = false
                break
            end
        end
        
        if canProduce then
            -- Input-Materialien verbrauchen
            for input, amount in pairs(chain.inputs) do
                self.supply[input] = self.supply[input] - amount * chain.efficiency
            end
            
            -- Output produzieren
            local outputAmount = chain.output * chain.efficiency
            self.supply[product] = self.supply[product] + outputAmount
            
            -- Produktions-Statistiken
            if not self.productionData[product] then
                self.productionData[product] = {total = 0, rate = 0}
            end
            self.productionData[product].total = self.productionData[product].total + outputAmount
            self.productionData[product].rate = outputAmount / (chain.time / 60) -- Pro Minute
        end
    end
end

-- Handel verarbeiten
function MarketManager:ProcessTrade(player, action, resource, amount, price)
    if not RESOURCES[resource] then
        warn("Unbekannte Ressource:", resource)
        return false
    end
    
    local currentPrice = self.currentPrices[resource]
    local totalCost = amount * price
    
    if action == "BUY" then
        -- Kaufen
        if self.supply[resource] >= amount then
            self.supply[resource] = self.supply[resource] - amount
            self.demand[resource] = self.demand[resource] + amount * 0.1
            
            -- Spieler-Geld abziehen (Integration mit EconomyManager)
            local economyManager = require(script.Parent.EconomyManager)
            if economyManager then
                economyManager:DeductPlayerMoney(player.UserId, totalCost)
            end
            
            print("💰 Handel:", player.Name, "kauft", amount, resource, "für", totalCost)
            return true
        end
    elseif action == "SELL" then
        -- Verkaufen
        self.supply[resource] = self.supply[resource] + amount
        self.demand[resource] = math.max(0, self.demand[resource] - amount * 0.1)
        
        -- Spieler-Geld hinzufügen
        local economyManager = require(script.Parent.EconomyManager)
        if economyManager then
            economyManager:AddPlayerMoney(player.UserId, totalCost)
        end
        
        print("💰 Handel:", player.Name, "verkauft", amount, resource, "für", totalCost)
        return true
    end
    
    return false
end

-- Preishistorie aktualisieren
function MarketManager:UpdatePriceHistory()
    for resource, price in pairs(self.currentPrices) do
        table.insert(self.priceHistory[resource], price)
        
        -- Nur die letzten 100 Einträge behalten
        if #self.priceHistory[resource] > 100 then
            table.remove(self.priceHistory[resource], 1)
        end
    end
end

-- Markt-Daten abrufen
function MarketManager:GetMarketData(player)
    local marketData = {
        prices = {},
        trends = {},
        supply = {},
        demand = {},
        priceHistory = {},
        productionChains = PRODUCTION_CHAINS,
        resources = RESOURCES
    }
    
    for resource, _ in pairs(RESOURCES) do
        marketData.prices[resource] = math.floor(self.currentPrices[resource] * 100) / 100
        marketData.trends[resource] = math.floor(self.marketTrends[resource] * 10000) / 100 -- Prozent
        marketData.supply[resource] = math.floor(self.supply[resource])
        marketData.demand[resource] = math.floor(self.demand[resource])
        marketData.priceHistory[resource] = self.priceHistory[resource]
    end
    
    return marketData
end

-- Markt-Analyse
function MarketManager:GetMarketAnalysis()
    local analysis = {
        mostVolatile = "",
        mostProfitable = "",
        shortageRisks = {},
        overSupplied = {},
        trends = {}
    }
    
    local maxVolatility = 0
    local maxProfit = 0
    
    for resource, data in pairs(RESOURCES) do
        local currentPrice = self.currentPrices[resource]
        local basePrice = data.basePrice
        local volatility = math.abs(self.marketTrends[resource])
        local profitMargin = (currentPrice - basePrice) / basePrice
        
        -- Volatilität
        if volatility > maxVolatility then
            maxVolatility = volatility
            analysis.mostVolatile = resource
        end
        
        -- Profitabilität
        if profitMargin > maxProfit then
            maxProfit = profitMargin
            analysis.mostProfitable = resource
        end
        
        -- Knappheits-Risiken
        local ratio = self.supply[resource] / self.demand[resource]
        if ratio < 0.5 then
            table.insert(analysis.shortageRisks, resource)
        elseif ratio > 2.0 then
            table.insert(analysis.overSupplied, resource)
        end
        
        -- Trends
        if self.marketTrends[resource] > 0.05 then
            analysis.trends[resource] = "RISING"
        elseif self.marketTrends[resource] < -0.05 then
            analysis.trends[resource] = "FALLING"
        else
            analysis.trends[resource] = "STABLE"
        end
    end
    
    return analysis
end

return MarketManager
