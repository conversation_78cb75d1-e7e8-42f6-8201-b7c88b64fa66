-- ServerScriptService/AchievementManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Erweiterte Achievement-System mit Kategorien und Belohnungen

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local AchievementManager = {}
AchievementManager.__index = AchievementManager

function AchievementManager.new()
    local self = setmetatable({}, AchievementManager)
    
    -- Player achievement data
    self.playerAchievements = {}
    
    -- Achievement categories
    self.categories = {
        financial = {
            name = "Finanziell",
            icon = "💰",
            color = Color3.fromRGB(255, 215, 0)
        },
        transport = {
            name = "Transport",
            icon = "🚂",
            color = Color3.fromRGB(70, 130, 180)
        },
        construction = {
            name = "Bau",
            icon = "🏗️",
            color = Color3.fromRGB(139, 69, 19)
        },
        exploration = {
            name = "Erkundung",
            icon = "🗺️",
            color = Color3.fromRGB(34, 139, 34)
        },
        efficiency = {
            name = "Effizienz",
            icon = "⚡",
            color = Color3.fromRGB(255, 140, 0)
        },
        special = {
            name = "Besonders",
            icon = "🏆",
            color = Color3.fromRGB(218, 165, 32)
        }
    }
    
    -- Comprehensive achievement list
    self.achievements = {
        -- Financial Achievements
        first_thousand = {
            id = "first_thousand",
            name = "Erste Schritte",
            description = "Verdiene deine ersten 1.000 €",
            category = "financial",
            icon = "💵",
            type = "cumulative_revenue",
            target = 1000,
            rarity = "common",
            reward = {experience = 50, money = 500}
        },
        first_million = {
            id = "first_million",
            name = "Millionär",
            description = "Erreiche 1 Million € Bargeld",
            category = "financial",
            icon = "💰",
            type = "cash",
            target = 1000000,
            rarity = "rare",
            reward = {experience = 500, money = 50000}
        },
        billionaire = {
            id = "billionaire",
            name = "Milliardär",
            description = "Erreiche 1 Milliarde € Unternehmenswert",
            category = "financial",
            icon = "💎",
            type = "company_value",
            target = 1000000000,
            rarity = "legendary",
            reward = {experience = 5000, money = 1000000}
        },
        profit_master = {
            id = "profit_master",
            name = "Gewinn-Meister",
            description = "Erziele 12 Monate in Folge Gewinn",
            category = "financial",
            icon = "📈",
            type = "consecutive_profitable_months",
            target = 12,
            rarity = "epic",
            reward = {experience = 1000, money = 100000}
        },
        
        -- Transport Achievements
        first_route = {
            id = "first_route",
            name = "Erste Route",
            description = "Erstelle deine erste Transport-Route",
            category = "transport",
            icon = "🛤️",
            type = "routes_created",
            target = 1,
            rarity = "common",
            reward = {experience = 100}
        },
        route_master = {
            id = "route_master",
            name = "Routen-Meister",
            description = "Betreibe 50 aktive Routen gleichzeitig",
            category = "transport",
            icon = "🗺️",
            type = "active_routes",
            target = 50,
            rarity = "epic",
            reward = {experience = 2000}
        },
        passenger_king = {
            id = "passenger_king",
            name = "Passagier-König",
            description = "Transportiere 10 Millionen Passagiere",
            category = "transport",
            icon = "👥",
            type = "passengers_transported",
            target = 10000000,
            rarity = "legendary",
            reward = {experience = 3000}
        },
        cargo_champion = {
            id = "cargo_champion",
            name = "Fracht-Champion",
            description = "Transportiere 1 Million Tonnen Fracht",
            category = "transport",
            icon = "📦",
            type = "cargo_transported",
            target = 1000000,
            rarity = "epic",
            reward = {experience = 2500}
        },
        speed_demon = {
            id = "speed_demon",
            name = "Geschwindigkeits-Dämon",
            description = "Kaufe ein Fahrzeug mit über 300 km/h",
            category = "transport",
            icon = "🚄",
            type = "max_vehicle_speed",
            target = 300,
            rarity = "rare",
            reward = {experience = 800}
        },
        
        -- Construction Achievements
        first_station = {
            id = "first_station",
            name = "Erste Station",
            description = "Baue deine erste Station",
            category = "construction",
            icon = "🚉",
            type = "stations_built",
            target = 1,
            rarity = "common",
            reward = {experience = 75}
        },
        infrastructure_king = {
            id = "infrastructure_king",
            name = "Infrastruktur-König",
            description = "Baue 1000 km Infrastruktur",
            category = "construction",
            icon = "🏗️",
            type = "infrastructure_length",
            target = 1000000, -- meters
            rarity = "legendary",
            reward = {experience = 4000}
        },
        bridge_builder = {
            id = "bridge_builder",
            name = "Brücken-Baumeister",
            description = "Baue 100 Brücken",
            category = "construction",
            icon = "🌉",
            type = "bridges_built",
            target = 100,
            rarity = "epic",
            reward = {experience = 1500}
        },
        tunnel_master = {
            id = "tunnel_master",
            name = "Tunnel-Meister",
            description = "Baue 50 Tunnel",
            category = "construction",
            icon = "🕳️",
            type = "tunnels_built",
            target = 50,
            rarity = "rare",
            reward = {experience = 1200}
        },
        
        -- Exploration Achievements
        city_connector = {
            id = "city_connector",
            name = "Stadt-Verbinder",
            description = "Verbinde 25 Städte mit deinem Netzwerk",
            category = "exploration",
            icon = "🏙️",
            type = "cities_connected",
            target = 25,
            rarity = "rare",
            reward = {experience = 1000}
        },
        world_explorer = {
            id = "world_explorer",
            name = "Welt-Erkunder",
            description = "Entdecke alle Regionen der Karte",
            category = "exploration",
            icon = "🌍",
            type = "regions_discovered",
            target = 10, -- Assuming 10 regions
            rarity = "epic",
            reward = {experience = 2000}
        },
        resource_finder = {
            id = "resource_finder",
            name = "Ressourcen-Finder",
            description = "Entdecke alle Ressourcen-Vorkommen",
            category = "exploration",
            icon = "⛏️",
            type = "resources_discovered",
            target = 20,
            rarity = "rare",
            reward = {experience = 800}
        },
        
        -- Efficiency Achievements
        efficiency_expert = {
            id = "efficiency_expert",
            name = "Effizienz-Experte",
            description = "Erreiche 95% Effizienz bei 10 Fahrzeugen",
            category = "efficiency",
            icon = "⚡",
            type = "high_efficiency_vehicles",
            target = 10,
            rarity = "epic",
            reward = {experience = 1500}
        },
        punctuality_master = {
            id = "punctuality_master",
            name = "Pünktlichkeits-Meister",
            description = "Erreiche 99% Pünktlichkeit über 6 Monate",
            category = "efficiency",
            icon = "⏰",
            type = "punctuality_streak",
            target = 6,
            rarity = "legendary",
            reward = {experience = 3000}
        },
        fuel_saver = {
            id = "fuel_saver",
            name = "Treibstoff-Sparer",
            description = "Reduziere Treibstoffkosten um 50%",
            category = "efficiency",
            icon = "⛽",
            type = "fuel_efficiency",
            target = 50, -- 50% reduction
            rarity = "rare",
            reward = {experience = 1000}
        },
        
        -- Special Achievements
        early_adopter = {
            id = "early_adopter",
            name = "Früher Anwender",
            description = "Kaufe das erste verfügbare Fahrzeug einer neuen Technologie",
            category = "special",
            icon = "🔬",
            type = "first_tech_vehicle",
            target = 1,
            rarity = "rare",
            reward = {experience = 500}
        },
        disaster_survivor = {
            id = "disaster_survivor",
            name = "Katastrophen-Überlebender",
            description = "Überlebe eine Naturkatastrophe ohne Bankrott",
            category = "special",
            icon = "🌪️",
            type = "disaster_survived",
            target = 1,
            rarity = "epic",
            reward = {experience = 2000}
        },
        competitor_crusher = {
            id = "competitor_crusher",
            name = "Konkurrenz-Vernichter",
            description = "Erreiche 75% Marktanteil",
            category = "special",
            icon = "👑",
            type = "market_share",
            target = 75,
            rarity = "legendary",
            reward = {experience = 5000}
        },
        century_company = {
            id = "century_company",
            name = "Jahrhundert-Unternehmen",
            description = "Führe dein Unternehmen 100 Jahre lang",
            category = "special",
            icon = "🎂",
            type = "company_age",
            target = 100,
            rarity = "legendary",
            reward = {experience = 10000}
        }
    }
    
    return self
end

-- Initialize player achievements
function AchievementManager:InitializePlayer(playerId)
    self.playerAchievements[playerId] = {
        unlockedAchievements = {},
        progress = {},
        totalExperience = 0,
        achievementLevel = 1,
        
        -- Statistics for tracking
        stats = {
            cumulative_revenue = 0,
            cash = 0,
            company_value = 0,
            consecutive_profitable_months = 0,
            routes_created = 0,
            active_routes = 0,
            passengers_transported = 0,
            cargo_transported = 0,
            max_vehicle_speed = 0,
            stations_built = 0,
            infrastructure_length = 0,
            bridges_built = 0,
            tunnels_built = 0,
            cities_connected = 0,
            regions_discovered = 0,
            resources_discovered = 0,
            high_efficiency_vehicles = 0,
            punctuality_streak = 0,
            fuel_efficiency = 0,
            first_tech_vehicle = 0,
            disaster_survived = 0,
            market_share = 0,
            company_age = 0
        }
    }
end

-- Update achievement progress
function AchievementManager:UpdateProgress(playerId, statType, value, isIncrement)
    local playerData = self.playerAchievements[playerId]
    if not playerData then
        self:InitializePlayer(playerId)
        playerData = self.playerAchievements[playerId]
    end
    
    -- Update statistic
    if isIncrement then
        playerData.stats[statType] = (playerData.stats[statType] or 0) + value
    else
        playerData.stats[statType] = value
    end
    
    -- Check for new achievements
    local newAchievements = self:CheckAchievements(playerId)
    
    return newAchievements
end

-- Check achievements
function AchievementManager:CheckAchievements(playerId)
    local playerData = self.playerAchievements[playerId]
    if not playerData then return {} end
    
    local newAchievements = {}
    local stats = playerData.stats
    
    for achievementId, achievement in pairs(self.achievements) do
        if not playerData.unlockedAchievements[achievementId] then
            local currentProgress = stats[achievement.type] or 0
            playerData.progress[achievementId] = currentProgress
            
            -- Check if achievement is unlocked
            if currentProgress >= achievement.target then
                playerData.unlockedAchievements[achievementId] = {
                    unlockedAt = os.time(),
                    achievement = achievement
                }
                
                -- Award experience
                if achievement.reward and achievement.reward.experience then
                    playerData.totalExperience = playerData.totalExperience + achievement.reward.experience
                    
                    -- Check for level up
                    local newLevel = math.floor(playerData.totalExperience / 1000) + 1
                    if newLevel > playerData.achievementLevel then
                        playerData.achievementLevel = newLevel
                    end
                end
                
                table.insert(newAchievements, achievement)
                print("🏆 Achievement unlocked for player", playerId, ":", achievement.name)
            end
        end
    end
    
    return newAchievements
end

-- Get player achievements
function AchievementManager:GetPlayerAchievements(playerId)
    local playerData = self.playerAchievements[playerId]
    if not playerData then
        self:InitializePlayer(playerId)
        playerData = self.playerAchievements[playerId]
    end
    
    return {
        unlockedAchievements = playerData.unlockedAchievements,
        progress = playerData.progress,
        totalExperience = playerData.totalExperience,
        achievementLevel = playerData.achievementLevel,
        stats = playerData.stats
    }
end

-- Get achievement by category
function AchievementManager:GetAchievementsByCategory(category)
    local categoryAchievements = {}
    
    for achievementId, achievement in pairs(self.achievements) do
        if achievement.category == category then
            categoryAchievements[achievementId] = achievement
        end
    end
    
    return categoryAchievements
end

-- Get achievement statistics
function AchievementManager:GetAchievementStats(playerId)
    local playerData = self.playerAchievements[playerId]
    if not playerData then return nil end
    
    local totalAchievements = 0
    local unlockedCount = 0
    local categoryStats = {}
    
    -- Initialize category stats
    for categoryId, category in pairs(self.categories) do
        categoryStats[categoryId] = {
            name = category.name,
            icon = category.icon,
            total = 0,
            unlocked = 0,
            percentage = 0
        }
    end
    
    -- Count achievements
    for achievementId, achievement in pairs(self.achievements) do
        totalAchievements = totalAchievements + 1
        categoryStats[achievement.category].total = categoryStats[achievement.category].total + 1
        
        if playerData.unlockedAchievements[achievementId] then
            unlockedCount = unlockedCount + 1
            categoryStats[achievement.category].unlocked = categoryStats[achievement.category].unlocked + 1
        end
    end
    
    -- Calculate percentages
    for categoryId, stats in pairs(categoryStats) do
        if stats.total > 0 then
            stats.percentage = math.floor((stats.unlocked / stats.total) * 100)
        end
    end
    
    return {
        totalAchievements = totalAchievements,
        unlockedCount = unlockedCount,
        completionPercentage = math.floor((unlockedCount / totalAchievements) * 100),
        categoryStats = categoryStats,
        totalExperience = playerData.totalExperience,
        achievementLevel = playerData.achievementLevel
    }
end

-- Get recent achievements
function AchievementManager:GetRecentAchievements(playerId, limit)
    local playerData = self.playerAchievements[playerId]
    if not playerData then return {} end
    
    local recentAchievements = {}
    
    for achievementId, data in pairs(playerData.unlockedAchievements) do
        table.insert(recentAchievements, {
            achievement = data.achievement,
            unlockedAt = data.unlockedAt
        })
    end
    
    -- Sort by unlock time (most recent first)
    table.sort(recentAchievements, function(a, b)
        return a.unlockedAt > b.unlockedAt
    end)
    
    -- Limit results
    if limit and #recentAchievements > limit then
        local limited = {}
        for i = 1, limit do
            table.insert(limited, recentAchievements[i])
        end
        return limited
    end
    
    return recentAchievements
end

-- Get all achievements (for display)
function AchievementManager:GetAllAchievements()
    return self.achievements
end

-- Get categories
function AchievementManager:GetCategories()
    return self.categories
end

return AchievementManager
