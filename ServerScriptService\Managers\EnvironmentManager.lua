-- ServerScriptService/Managers/EnvironmentManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Umwelt-Impact-System: Verschmutzung, Ökologie, Nachhaltigkeit

local EnvironmentManager = {}
EnvironmentManager.__index = EnvironmentManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Umwelt-Faktoren
local POLLUTION_SOURCES = {
    COAL_POWER = {
        name = "Kohlekraftwerk",
        airPollution = 10,
        waterPollution = 5,
        noisePollution = 8,
        co2Emission = 15
    },
    
    DIESEL_TRAIN = {
        name = "Dieselzug",
        airPollution = 3,
        waterPollution = 1,
        noisePollution = 6,
        co2Emission = 4
    },
    
    ELECTRIC_TRAIN = {
        name = "Elektrozug",
        airPollution = 0,
        waterPollution = 0,
        noisePollution = 4,
        co2Emission = 1 -- Indirekt durch Stromerzeugung
    },
    
    TRUCK = {
        name = "LKW",
        airPollution = 2,
        waterPollution = 0,
        noisePollution = 5,
        co2Emission = 3
    },
    
    SHIP = {
        name = "Schiff",
        airPollution = 4,
        waterPollution = 6,
        noisePollution = 3,
        co2Emission = 5
    },
    
    AIRPLANE = {
        name = "Flugzeug",
        airPollution = 8,
        waterPollution = 0,
        noisePollution = 10,
        co2Emission = 12
    },
    
    INDUSTRY = {
        name = "Industrie",
        airPollution = 6,
        waterPollution = 8,
        noisePollution = 7,
        co2Emission = 8
    },
    
    CONSTRUCTION = {
        name = "Baustelle",
        airPollution = 4,
        waterPollution = 2,
        noisePollution = 9,
        co2Emission = 3
    }
}

-- Umwelt-Verbesserungen
local ENVIRONMENTAL_IMPROVEMENTS = {
    TREE_PLANTING = {
        name = "Aufforstung",
        cost = 1000,
        airPollutionReduction = -2,
        co2Reduction = -3,
        biodiversityBonus = 5,
        touristBonus = 1.1
    },
    
    SOLAR_POWER = {
        name = "Solarenergie",
        cost = 500000,
        airPollutionReduction = -5,
        co2Reduction = -8,
        energyProduction = 100,
        maintenanceCost = 5000
    },
    
    WIND_POWER = {
        name = "Windenergie",
        cost = 800000,
        airPollutionReduction = -3,
        co2Reduction = -10,
        energyProduction = 150,
        noisePollution = 2,
        maintenanceCost = 8000
    },
    
    WATER_TREATMENT = {
        name = "Kläranlage",
        cost = 1000000,
        waterPollutionReduction = -10,
        healthBonus = 1.2,
        maintenanceCost = 15000
    },
    
    NOISE_BARRIERS = {
        name = "Lärmschutzwände",
        cost = 100000,
        noisePollutionReduction = -5,
        constructionCost = 50000 -- pro km
    },
    
    ELECTRIC_INFRASTRUCTURE = {
        name = "Elektrifizierung",
        cost = 2000000,
        airPollutionReduction = -4,
        co2Reduction = -6,
        efficiencyBonus = 1.15
    }
}

-- Umwelt-Zonen
local ENVIRONMENTAL_ZONES = {
    NATURE_RESERVE = {
        name = "Naturschutzgebiet",
        restrictions = {
            noIndustry = true,
            noMining = true,
            limitedConstruction = true
        },
        benefits = {
            biodiversityBonus = 10,
            touristAttraction = 1.5,
            airQualityBonus = 5
        }
    },
    
    LOW_EMISSION_ZONE = {
        name = "Umweltzone",
        restrictions = {
            onlyElectricVehicles = true,
            noCoalPower = true
        },
        benefits = {
            airQualityBonus = 8,
            healthBonus = 1.3,
            propertyValueBonus = 1.2
        }
    },
    
    INDUSTRIAL_ZONE = {
        name = "Industriegebiet",
        restrictions = {
            noResidential = true,
            limitedTourism = true
        },
        effects = {
            pollutionConcentration = 2.0,
            economicBonus = 1.4
        }
    }
}

-- Konstruktor
function EnvironmentManager.new()
    local self = setmetatable({}, EnvironmentManager)
    
    -- Umwelt-Zustand
    self.globalPollution = {
        air = 0,
        water = 0,
        noise = 0,
        co2 = 0
    }
    
    self.regionalPollution = {} -- [regionId] = pollution data
    self.pollutionSources = {} -- [sourceId] = source data
    self.environmentalImprovements = {} -- [improvementId] = improvement data
    self.environmentalZones = {} -- [zoneId] = zone data
    
    -- Statistiken
    self.statistics = {
        totalCO2Emissions = 0,
        totalTreesPlanted = 0,
        renewableEnergyPercentage = 0,
        biodiversityIndex = 100,
        overallEnvironmentalScore = 100
    }
    
    self.nextId = 1
    
    self:InitializeEvents()
    self:StartUpdateLoop()
    
    return self
end

-- Events initialisieren
function EnvironmentManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetEnvironmentDataFunction") then
        local getEnvironmentDataFunction = Instance.new("RemoteFunction")
        getEnvironmentDataFunction.Name = "GetEnvironmentDataFunction"
        getEnvironmentDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("AddEnvironmentalImprovementEvent") then
        local addEnvironmentalImprovementEvent = Instance.new("RemoteEvent")
        addEnvironmentalImprovementEvent.Name = "AddEnvironmentalImprovementEvent"
        addEnvironmentalImprovementEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("CreateEnvironmentalZoneEvent") then
        local createEnvironmentalZoneEvent = Instance.new("RemoteEvent")
        createEnvironmentalZoneEvent.Name = "CreateEnvironmentalZoneEvent"
        createEnvironmentalZoneEvent.Parent = Events
    end
    
    -- Event-Handler
    Events.GetEnvironmentDataFunction.OnServerInvoke = function(player)
        return self:GetEnvironmentData(player)
    end
    
    Events.AddEnvironmentalImprovementEvent.OnServerEvent:Connect(function(player, improvementType, position, options)
        self:AddEnvironmentalImprovement(player, improvementType, position, options)
    end)
    
    Events.CreateEnvironmentalZoneEvent.OnServerEvent:Connect(function(player, zoneType, area, options)
        self:CreateEnvironmentalZone(player, zoneType, area, options)
    end)
end

-- Update-Loop starten
function EnvironmentManager:StartUpdateLoop()
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdatePollution(deltaTime)
        self:UpdateEnvironmentalEffects(deltaTime)
        self:UpdateStatistics(deltaTime)
    end)
end

-- Verschmutzungsquelle hinzufügen
function EnvironmentManager:AddPollutionSource(sourceType, position, intensity, ownerId)
    local sourceData = POLLUTION_SOURCES[sourceType]
    if not sourceData then
        warn("Unbekannte Verschmutzungsquelle:", sourceType)
        return nil
    end
    
    local sourceId = "pollution_" .. self.nextId
    self.nextId = self.nextId + 1
    
    local source = {
        id = sourceId,
        type = sourceType,
        position = position,
        intensity = intensity or 1.0,
        ownerId = ownerId,
        
        -- Verschmutzungswerte
        airPollution = sourceData.airPollution * intensity,
        waterPollution = sourceData.waterPollution * intensity,
        noisePollution = sourceData.noisePollution * intensity,
        co2Emission = sourceData.co2Emission * intensity,
        
        -- Status
        active = true,
        createdAt = tick()
    }
    
    self.pollutionSources[sourceId] = source
    
    print("🏭 Verschmutzungsquelle hinzugefügt:", sourceData.name, "Intensität:", intensity)
    return source
end

-- Verschmutzungsquelle entfernen
function EnvironmentManager:RemovePollutionSource(sourceId)
    if self.pollutionSources[sourceId] then
        self.pollutionSources[sourceId] = nil
        print("✅ Verschmutzungsquelle entfernt:", sourceId)
        return true
    end
    return false
end

-- Umwelt-Verbesserung hinzufügen
function EnvironmentManager:AddEnvironmentalImprovement(player, improvementType, position, options)
    local playerId = player.UserId
    local improvementData = ENVIRONMENTAL_IMPROVEMENTS[improvementType]
    
    if not improvementData then
        warn("Unbekannte Umwelt-Verbesserung:", improvementType)
        return false
    end
    
    -- Kosten prüfen
    local economyManager = require(script.Parent.EconomyManager)
    if not economyManager:CanAfford(playerId, improvementData.cost) then
        print("❌ Nicht genug Geld für:", improvementData.name, "Kosten:", improvementData.cost)
        return false
    end
    
    -- Geld abziehen
    economyManager:SpendMoney(playerId, improvementData.cost)
    
    local improvementId = "improvement_" .. self.nextId
    self.nextId = self.nextId + 1
    
    local improvement = {
        id = improvementId,
        type = improvementType,
        position = position,
        ownerId = playerId,
        
        -- Effekte
        airPollutionReduction = improvementData.airPollutionReduction or 0,
        waterPollutionReduction = improvementData.waterPollutionReduction or 0,
        noisePollutionReduction = improvementData.noisePollutionReduction or 0,
        co2Reduction = improvementData.co2Reduction or 0,
        
        -- Boni
        biodiversityBonus = improvementData.biodiversityBonus or 0,
        touristBonus = improvementData.touristBonus or 1.0,
        healthBonus = improvementData.healthBonus or 1.0,
        efficiencyBonus = improvementData.efficiencyBonus or 1.0,
        
        -- Wartung
        maintenanceCost = improvementData.maintenanceCost or 0,
        lastMaintenancePayment = tick(),
        
        -- Status
        active = true,
        createdAt = tick(),
        options = options or {}
    }
    
    self.environmentalImprovements[improvementId] = improvement
    
    print("🌱 Umwelt-Verbesserung hinzugefügt:", improvementData.name)
    return improvement
end

-- Umwelt-Zone erstellen
function EnvironmentManager:CreateEnvironmentalZone(player, zoneType, area, options)
    local playerId = player.UserId
    local zoneData = ENVIRONMENTAL_ZONES[zoneType]
    
    if not zoneData then
        warn("Unbekannte Umwelt-Zone:", zoneType)
        return false
    end
    
    local zoneId = "zone_" .. self.nextId
    self.nextId = self.nextId + 1
    
    local zone = {
        id = zoneId,
        type = zoneType,
        area = area, -- {center = Vector3, radius = number} oder {corners = {Vector3}}
        ownerId = playerId,
        
        -- Eigenschaften
        restrictions = zoneData.restrictions,
        benefits = zoneData.benefits,
        effects = zoneData.effects,
        
        -- Status
        active = true,
        createdAt = tick(),
        options = options or {}
    }
    
    self.environmentalZones[zoneId] = zone
    
    print("🏞️ Umwelt-Zone erstellt:", zoneData.name)
    return zone
end

-- Verschmutzung aktualisieren
function EnvironmentManager:UpdatePollution(deltaTime)
    -- Globale Verschmutzung zurücksetzen
    self.globalPollution = {
        air = 0,
        water = 0,
        noise = 0,
        co2 = 0
    }
    
    -- Verschmutzung von Quellen addieren
    for sourceId, source in pairs(self.pollutionSources) do
        if source.active then
            self.globalPollution.air = self.globalPollution.air + source.airPollution
            self.globalPollution.water = self.globalPollution.water + source.waterPollution
            self.globalPollution.noise = self.globalPollution.noise + source.noisePollution
            self.globalPollution.co2 = self.globalPollution.co2 + source.co2Emission
        end
    end
    
    -- Verbesserungen abziehen
    for improvementId, improvement in pairs(self.environmentalImprovements) do
        if improvement.active then
            self.globalPollution.air = math.max(0, self.globalPollution.air + improvement.airPollutionReduction)
            self.globalPollution.water = math.max(0, self.globalPollution.water + improvement.waterPollutionReduction)
            self.globalPollution.noise = math.max(0, self.globalPollution.noise + improvement.noisePollutionReduction)
            self.globalPollution.co2 = math.max(0, self.globalPollution.co2 + improvement.co2Reduction)
            
            -- Wartungskosten
            if improvement.maintenanceCost > 0 then
                local daysSinceLastPayment = (tick() - improvement.lastMaintenancePayment) / (24 * 60 * 60)
                
                if daysSinceLastPayment >= 1 then
                    local economyManager = require(script.Parent.EconomyManager)
                    if economyManager and economyManager:CanAfford(improvement.ownerId, improvement.maintenanceCost) then
                        economyManager:SpendMoney(improvement.ownerId, improvement.maintenanceCost)
                        improvement.lastMaintenancePayment = tick()
                    else
                        -- Verbesserung deaktivieren bei fehlender Wartung
                        improvement.active = false
                        print("⚠️ Umwelt-Verbesserung deaktiviert (keine Wartung):", improvement.id)
                    end
                end
            end
        end
    end
end

-- Umwelt-Effekte aktualisieren
function EnvironmentManager:UpdateEnvironmentalEffects(deltaTime)
    -- Auswirkungen auf andere Systeme
    local cityManager = require(script.Parent.CityManager)
    local economyManager = require(script.Parent.EconomyManager)
    
    if cityManager then
        -- Verschmutzung beeinflusst Städte
        for cityId, city in pairs(cityManager.cities or {}) do
            local pollutionImpact = self:CalculatePollutionImpact(city.position)
            
            -- Gesundheit und Zufriedenheit beeinflussen
            if pollutionImpact.air > 50 then
                city.health = math.max(0, city.health - 0.1)
                city.happiness = math.max(0, city.happiness - 0.05)
            end
            
            if pollutionImpact.noise > 40 then
                city.happiness = math.max(0, city.happiness - 0.03)
            end
            
            if pollutionImpact.water > 30 then
                city.health = math.max(0, city.health - 0.15)
            end
        end
    end
end

-- Verschmutzungs-Impact für Position berechnen
function EnvironmentManager:CalculatePollutionImpact(position)
    local impact = {air = 0, water = 0, noise = 0, co2 = 0}
    local maxDistance = 10000 -- 10km Einflussradius
    
    for sourceId, source in pairs(self.pollutionSources) do
        if source.active then
            local distance = (position - source.position).Magnitude
            if distance <= maxDistance then
                local factor = 1 - (distance / maxDistance) -- Lineare Abnahme
                
                impact.air = impact.air + (source.airPollution * factor)
                impact.water = impact.water + (source.waterPollution * factor)
                impact.noise = impact.noise + (source.noisePollution * factor)
                impact.co2 = impact.co2 + (source.co2Emission * factor)
            end
        end
    end
    
    return impact
end

-- Statistiken aktualisieren
function EnvironmentManager:UpdateStatistics(deltaTime)
    -- CO2-Emissionen akkumulieren
    self.statistics.totalCO2Emissions = self.statistics.totalCO2Emissions + (self.globalPollution.co2 * deltaTime)
    
    -- Bäume zählen
    local treeCount = 0
    for improvementId, improvement in pairs(self.environmentalImprovements) do
        if improvement.type == "TREE_PLANTING" and improvement.active then
            treeCount = treeCount + (improvement.options.treeCount or 10)
        end
    end
    self.statistics.totalTreesPlanted = treeCount
    
    -- Erneuerbare Energie berechnen
    local totalEnergy = 0
    local renewableEnergy = 0
    
    for improvementId, improvement in pairs(self.environmentalImprovements) do
        if improvement.active and (improvement.type == "SOLAR_POWER" or improvement.type == "WIND_POWER") then
            local energyProduction = ENVIRONMENTAL_IMPROVEMENTS[improvement.type].energyProduction or 0
            renewableEnergy = renewableEnergy + energyProduction
        end
    end
    
    -- Gesamt-Energie (vereinfacht)
    totalEnergy = renewableEnergy + (self.globalPollution.co2 * 10) -- CO2 als Proxy für fossile Energie
    
    if totalEnergy > 0 then
        self.statistics.renewableEnergyPercentage = (renewableEnergy / totalEnergy) * 100
    else
        self.statistics.renewableEnergyPercentage = 0
    end
    
    -- Biodiversitäts-Index
    local biodiversityBonus = 0
    for improvementId, improvement in pairs(self.environmentalImprovements) do
        if improvement.active then
            biodiversityBonus = biodiversityBonus + improvement.biodiversityBonus
        end
    end
    
    self.statistics.biodiversityIndex = math.max(0, math.min(200, 100 + biodiversityBonus - (self.globalPollution.air * 0.5)))
    
    -- Gesamt-Umwelt-Score
    local airQuality = math.max(0, 100 - self.globalPollution.air)
    local waterQuality = math.max(0, 100 - self.globalPollution.water)
    local noiseLevel = math.max(0, 100 - self.globalPollution.noise)
    local climateScore = math.max(0, 100 - (self.globalPollution.co2 * 0.5))
    
    self.statistics.overallEnvironmentalScore = (airQuality + waterQuality + noiseLevel + climateScore + self.statistics.biodiversityIndex) / 5
end

-- Umwelt-Daten abrufen
function EnvironmentManager:GetEnvironmentData(player)
    local playerId = player.UserId
    
    -- Spieler-spezifische Verbesserungen
    local playerImprovements = {}
    for improvementId, improvement in pairs(self.environmentalImprovements) do
        if improvement.ownerId == playerId then
            playerImprovements[improvementId] = {
                id = improvement.id,
                type = improvement.type,
                position = improvement.position,
                active = improvement.active,
                maintenanceCost = improvement.maintenanceCost
            }
        end
    end
    
    return {
        globalPollution = self.globalPollution,
        statistics = self.statistics,
        playerImprovements = playerImprovements,
        availableImprovements = ENVIRONMENTAL_IMPROVEMENTS,
        availableZones = ENVIRONMENTAL_ZONES
    }
end

-- Cleanup
function EnvironmentManager:Destroy()
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end
end

return EnvironmentManager
