# Infrastructure Assets für Transport Empire
# 3D-Modell-Spezifikationen für Infrastruktur und Gebäude

## EISENBAHN-INFRASTRUKTUR

### <PERSON><PERSON>enen (Rails)
[RAIL_STRAIGHT]
Name = "Gerade Schiene"
ModelId = "rbxassetid://RAIL_STRAIGHT"
Scale = Vector3(1, 0.2, 8)
Cost = 500
MaintenanceCost = 5
Material = Enum.Material.Metal
Colors = {
    Primary = Color3(0.4, 0.4, 0.4),    # Stahl
    Secondary = Color3(0.3, 0.2, 0.1)   # Ho<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
}
Connections = {"North", "South"}
MaxSpeed = 120

[RAIL_CURVE_45]
Name = "45° Kurve"
ModelId = "rbxassetid://RAIL_CURVE_45"
Scale = Vector3(8, 0.2, 8)
Cost = 750
MaintenanceCost = 8
Material = Enum.Material.Metal
Connections = {"North", "East"}
MaxSpeed = 80
CurveRadius = 4

[RAIL_CURVE_90]
Name = "90° Kurve"
ModelId = "rbxassetid://RAIL_CURVE_90"
Scale = Vector3(8, 0.2, 8)
Cost = 1000
MaintenanceCost = 10
Material = Enum.Material.Metal
Connections = {"North", "East"}
MaxSpeed = 60
CurveRadius = 4

[RAIL_SWITCH]
Name = "Weiche"
ModelId = "rbxassetid://RAIL_SWITCH"
Scale = Vector3(8, 0.2, 8)
Cost = 2500
MaintenanceCost = 25
Material = Enum.Material.Metal
Connections = {"North", "South", "East"}
MaxSpeed = 40
RequiresSignaling = true

[RAIL_CROSSING]
Name = "Bahnübergang"
ModelId = "rbxassetid://RAIL_CROSSING"
Scale = Vector3(8, 0.2, 8)
Cost = 1500
MaintenanceCost = 20
Material = Enum.Material.Metal
Connections = {"North", "South"}
MaxSpeed = 30
RequiresSignals = true
SafetyFeatures = {"Barriers", "Lights", "Sound"}

### Elektrifizierung
[RAIL_ELECTRIC_POLE]
Name = "Oberleitungsmast"
ModelId = "rbxassetid://ELECTRIC_POLE"
Scale = Vector3(0.5, 8, 0.5)
Cost = 800
MaintenanceCost = 15
Material = Enum.Material.Metal
Color = Color3(0.6, 0.6, 0.6)
PowerSupply = 1000
Range = 16

[RAIL_ELECTRIC_WIRE]
Name = "Oberleitung"
ModelId = "rbxassetid://ELECTRIC_WIRE"
Scale = Vector3(0.1, 0.1, 8)
Cost = 200
MaintenanceCost = 5
Material = Enum.Material.Neon
Color = Color3(0.9, 0.9, 0.1)
Transparency = 0.3

## BAHNHÖFE UND DEPOTS

### Bahnhöfe
[STATION_SMALL]
Name = "Kleiner Bahnhof"
ModelId = "rbxassetid://STATION_SMALL"
Scale = Vector3(12, 6, 20)
Cost = 25000
MaintenanceCost = 200
Capacity = 100
PlatformCount = 2
Services = {"Passenger", "Freight"}
Era = 1850-1950
Colors = {
    Primary = Color3(0.7, 0.3, 0.2),    # Backstein
    Secondary = Color3(0.2, 0.4, 0.2),  # Grüne Dächer
    Accent = Color3(0.9, 0.9, 0.9)      # Weiße Details
}

[STATION_MEDIUM]
Name = "Mittlerer Bahnhof"
ModelId = "rbxassetid://STATION_MEDIUM"
Scale = Vector3(20, 8, 35)
Cost = 75000
MaintenanceCost = 500
Capacity = 300
PlatformCount = 4
Services = {"Passenger", "Freight", "Express"}
Era = 1880-2000

[STATION_LARGE]
Name = "Großer Hauptbahnhof"
ModelId = "rbxassetid://STATION_LARGE"
Scale = Vector3(40, 12, 60)
Cost = 200000
MaintenanceCost = 1500
Capacity = 800
PlatformCount = 8
Services = {"Passenger", "Freight", "Express", "International"}
Era = 1900-2000
Features = {"Restaurant", "Hotel", "Shopping"}

### Depots
[DEPOT_TRAIN_SMALL]
Name = "Kleines Lokdepot"
ModelId = "rbxassetid://DEPOT_TRAIN_SMALL"
Scale = Vector3(15, 8, 25)
Cost = 35000
MaintenanceCost = 300
Capacity = 5
Services = {"Maintenance", "Refuel", "Repair"}
VehicleTypes = {"Trains"}

[DEPOT_TRAIN_LARGE]
Name = "Großes Lokdepot"
ModelId = "rbxassetid://DEPOT_TRAIN_LARGE"
Scale = Vector3(30, 10, 50)
Cost = 100000
MaintenanceCost = 800
Capacity = 15
Services = {"Maintenance", "Refuel", "Repair", "Upgrade"}
VehicleTypes = {"Trains"}
Features = {"Turntable", "Workshop", "Fuel_Station"}

## STRASSEN-INFRASTRUKTUR

### Straßen
[ROAD_DIRT]
Name = "Feldweg"
ModelId = "rbxassetid://ROAD_DIRT"
Scale = Vector3(6, 0.1, 8)
Cost = 100
MaintenanceCost = 2
Material = Enum.Material.Ground
Color = Color3(0.6, 0.4, 0.2)
MaxSpeed = 25
Era = 1850-1920
WeatherAffected = true

[ROAD_COBBLESTONE]
Name = "Kopfsteinpflaster"
ModelId = "rbxassetid://ROAD_COBBLESTONE"
Scale = Vector3(6, 0.2, 8)
Cost = 300
MaintenanceCost = 5
Material = Enum.Material.Cobblestone
Color = Color3(0.5, 0.5, 0.5)
MaxSpeed = 35
Era = 1850-1950

[ROAD_ASPHALT]
Name = "Asphaltstraße"
ModelId = "rbxassetid://ROAD_ASPHALT"
Scale = Vector3(6, 0.1, 8)
Cost = 800
MaintenanceCost = 8
Material = Enum.Material.Asphalt
Color = Color3(0.2, 0.2, 0.2)
MaxSpeed = 60
Era = 1920-2000

[ROAD_HIGHWAY]
Name = "Autobahn"
ModelId = "rbxassetid://ROAD_HIGHWAY"
Scale = Vector3(12, 0.1, 8)
Cost = 2000
MaintenanceCost = 20
Material = Enum.Material.Asphalt
Color = Color3(0.15, 0.15, 0.15)
MaxSpeed = 120
Era = 1950-2000
Lanes = 4
Features = {"Median_Strip", "Guard_Rails"}

### Brücken
[BRIDGE_WOODEN]
Name = "Holzbrücke"
ModelId = "rbxassetid://BRIDGE_WOODEN"
Scale = Vector3(8, 4, 16)
Cost = 5000
MaintenanceCost = 100
Material = Enum.Material.Wood
Color = Color3(0.6, 0.4, 0.2)
MaxWeight = 50
Era = 1850-1920
WeatherAffected = true

[BRIDGE_STONE]
Name = "Steinbrücke"
ModelId = "rbxassetid://BRIDGE_STONE"
Scale = Vector3(8, 6, 20)
Cost = 15000
MaintenanceCost = 50
Material = Enum.Material.Brick
Color = Color3(0.6, 0.6, 0.5)
MaxWeight = 200
Era = 1850-2000

[BRIDGE_STEEL]
Name = "Stahlbrücke"
ModelId = "rbxassetid://BRIDGE_STEEL"
Scale = Vector3(8, 8, 32)
Cost = 50000
MaintenanceCost = 200
Material = Enum.Material.Metal
Color = Color3(0.4, 0.4, 0.4)
MaxWeight = 500
Era = 1880-2000

## WASSER-INFRASTRUKTUR

### Häfen
[PORT_SMALL]
Name = "Kleiner Hafen"
ModelId = "rbxassetid://PORT_SMALL"
Scale = Vector3(20, 4, 30)
Cost = 50000
MaintenanceCost = 400
Capacity = 3
Services = {"Loading", "Unloading", "Fuel"}
DockCount = 2
MaxShipSize = "Medium"

[PORT_LARGE]
Name = "Großer Hafen"
ModelId = "rbxassetid://PORT_LARGE"
Scale = Vector3(50, 6, 80)
Cost = 200000
MaintenanceCost = 1500
Capacity = 10
Services = {"Loading", "Unloading", "Fuel", "Repair", "Customs"}
DockCount = 6
MaxShipSize = "Large"
Features = {"Cranes", "Warehouses", "Container_Yard"}

### Kanäle
[CANAL_STRAIGHT]
Name = "Gerader Kanal"
ModelId = "rbxassetid://CANAL_STRAIGHT"
Scale = Vector3(8, 2, 16)
Cost = 3000
MaintenanceCost = 30
Material = Enum.Material.Water
Color = Color3(0.2, 0.4, 0.6)
Depth = 2
MaxShipSize = "Medium"

[CANAL_LOCK]
Name = "Schleuse"
ModelId = "rbxassetid://CANAL_LOCK"
Scale = Vector3(8, 4, 20)
Cost = 25000
MaintenanceCost = 200
Material = Enum.Material.Concrete
Features = {"Gates", "Control_House", "Pumps"}
ElevationChange = 4

## SIGNALE UND SICHERHEIT

### Eisenbahn-Signale
[SIGNAL_SEMAPHORE]
Name = "Formsignal"
ModelId = "rbxassetid://SIGNAL_SEMAPHORE"
Scale = Vector3(0.5, 6, 0.5)
Cost = 1500
MaintenanceCost = 20
Era = 1850-1950
SignalTypes = {"Stop", "Caution", "Clear"}
Range = 100

[SIGNAL_LIGHT]
Name = "Lichtsignal"
ModelId = "rbxassetid://SIGNAL_LIGHT"
Scale = Vector3(0.8, 4, 0.8)
Cost = 3000
MaintenanceCost = 30
Era = 1920-2000
SignalTypes = {"Red", "Yellow", "Green"}
Range = 150
RequiresElectricity = true

### Straßen-Signale
[TRAFFIC_LIGHT]
Name = "Ampel"
ModelId = "rbxassetid://TRAFFIC_LIGHT"
Scale = Vector3(0.5, 4, 0.5)
Cost = 2000
MaintenanceCost = 25
Era = 1920-2000
SignalTypes = {"Red", "Yellow", "Green"}
RequiresElectricity = true

[ROAD_SIGN]
Name = "Verkehrsschild"
ModelId = "rbxassetid://ROAD_SIGN"
Scale = Vector3(1, 2, 0.1)
Cost = 200
MaintenanceCost = 5
Era = 1900-2000
SignTypes = {"Speed_Limit", "Stop", "Yield", "Direction"}

## PERFORMANCE-OPTIMIERUNG

[LOD_SETTINGS]
# Level of Detail für Infrastruktur
Distance_High = 100     # Volle Details bis 100 Studs
Distance_Medium = 300   # Mittlere Details bis 300 Studs
Distance_Low = 600      # Niedrige Details bis 600 Studs
Distance_Cull = 1000    # Ausblenden ab 1000 Studs

[CULLING_SETTINGS]
# Optimierung für große Netzwerke
MaxVisibleRails = 500
MaxVisibleRoads = 300
MaxVisibleBridges = 50
MaxVisibleBuildings = 200
UpdateFrequency = 2     # Sekunden zwischen Updates

## UPGRADE-SYSTEM

[INFRASTRUCTURE_UPGRADES]
Rails = {
    Level1 = {SpeedBonus = 0.1, Cost = 200},
    Level2 = {SpeedBonus = 0.2, Cost = 500},
    Level3 = {SpeedBonus = 0.3, Cost = 1200}
}

Roads = {
    Level1 = {SpeedBonus = 0.15, Cost = 150},
    Level2 = {SpeedBonus = 0.3, Cost = 400},
    Level3 = {SpeedBonus = 0.5, Cost = 1000}
}

Stations = {
    Level1 = {CapacityBonus = 0.2, Cost = 5000},
    Level2 = {CapacityBonus = 0.4, Cost = 15000},
    Level3 = {CapacityBonus = 0.6, Cost = 40000}
}
