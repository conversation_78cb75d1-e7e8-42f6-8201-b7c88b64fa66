-- ServerScriptService/GameManager.lua
-- ROBLOX SCRIPT TYPE: Script
-- Zentraler Game Manager für Transport Fever 2 - Koordiniert alle Spielsysteme

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")

-- DataStores
local PlayerDataStore = DataStoreService:GetDataStore("PlayerData")
local GameStateStore = DataStoreService:GetDataStore("GameState")

-- Module Dependencies
local EconomyManager = require(script.Parent.EconomyManager)
local VehicleManager = require(script.Parent.VehicleManager)
local CityManager = require(script.Parent.CityManager)
local TransportManager = require(script.Parent.TransportManager)
local TerrainManager = require(script.Parent.TerrainManager)
local AIManager = require(script.Parent.AIManager)

local GameManager = {}
GameManager.__index = GameManager

-- Singleton Instance
local instance = nil

function GameManager.new()
    if instance then return instance end
    
    local self = setmetatable({}, GameManager)
    
    -- Game State
    self.gameState = {
        isRunning = false,
        isPaused = false,
        gameSpeed = 1,
        currentDate = {year = 1850, month = 1, day = 1},
        weather = "Clear",
        season = "Spring"
    }
    
    -- Player Data
    self.playerData = {}
    
    -- Managers
    self.economyManager = EconomyManager.new()
    self.vehicleManager = VehicleManager.new()
    self.transportManager = TransportManager.new()
    self.terrainManager = TerrainManager.new()
    self.cityManager = CityManager.new()

    -- Initialize cities from terrain
    self.cityManager:InitializeCities(self.terrainManager)

    -- AI Manager (placeholder for now)
    -- self.aiManager = AIManager.new()
    
    -- Events Setup
    self:SetupEvents()
    
    -- Game Loop
    self:StartGameLoop()
    
    instance = self
    return self
end

-- Events Setup
function GameManager:SetupEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Player Management
    Events.GetPlayerDataFunction.OnServerInvoke = function(player)
        return self:GetPlayerData(player)
    end
    
    Events.SavePlayerDataEvent.OnServerEvent:Connect(function(player, data)
        self:SavePlayerData(player, data)
    end)
    
    -- Game State
    Events.GetGameStateFunction.OnServerInvoke = function(player)
        return self.gameState
    end
    
    Events.SetGameSpeedEvent.OnServerEvent:Connect(function(player, speed)
        self:SetGameSpeed(player, speed)
    end)
    
    Events.PauseGameEvent.OnServerEvent:Connect(function(player, paused)
        self:PauseGame(player, paused)
    end)
    
    -- Economy Events
    Events.GetFinanceDataFunction.OnServerInvoke = function(player)
        return self.economyManager:GetPlayerFinances(player)
    end
    
    Events.RequestLoanEvent.OnServerEvent:Connect(function(player, amount)
        self.economyManager:RequestLoan(player, amount)
    end)
    
    Events.RepayLoanEvent.OnServerEvent:Connect(function(player, loanId, amount)
        self.economyManager:RepayLoan(player, loanId, amount)
    end)
    
    -- Vehicle Events
    Events.BuyVehicleEvent.OnServerEvent:Connect(function(player, vehicleType, vehicleId)
        self.vehicleManager:BuyVehicle(player, vehicleType, vehicleId)
    end)
    
    Events.SellVehicleEvent.OnServerEvent:Connect(function(player, vehicleInstanceId)
        self.vehicleManager:SellVehicle(player, vehicleInstanceId)
    end)
    
    Events.GetVehicleDataFunction.OnServerInvoke = function(player)
        return self.vehicleManager:GetPlayerVehicles(player)
    end
    
    -- Transport Events
    Events.CreateLineEvent.OnServerEvent:Connect(function(player, lineData)
        self.transportManager:CreateLine(player, lineData)
    end)

    Events.DeleteLineEvent.OnServerEvent:Connect(function(player, lineId)
        self.transportManager:DeleteLine(player, lineId)
    end)

    -- City Events
    Events.GetCityStatsEvent.OnServerEvent:Connect(function(player, cityId)
        local stats = self.cityManager:GetCityStats(cityId)
        Events.CityStatsResponseEvent:FireClient(player, cityId, stats)
    end)

    Events.RenameCityEvent.OnServerEvent:Connect(function(player, cityId, newName)
        self:RenameCity(player, cityId, newName)
    end)

    Events.GetAllCitiesEvent.OnServerEvent:Connect(function(player)
        local cities = {}
        for cityId, city in pairs(self.cityManager.cities) do
            cities[cityId] = {
                id = cityId,
                name = city.name,
                population = city.population,
                size = city.size,
                x = city.x,
                z = city.z,
                demands = city.demands
            }
        end
        Events.AllCitiesResponseEvent:FireClient(player, cities)
    end)

    -- Industry Events
    Events.GetIndustryStatsEvent.OnServerEvent:Connect(function(player, industryId)
        local stats = self.cityManager:GetIndustryStats(industryId)
        Events.IndustryStatsResponseEvent:FireClient(player, industryId, stats)
    end)

    Events.UpgradeIndustryEvent.OnServerEvent:Connect(function(player, industryId)
        local success, message = self.cityManager:UpgradeIndustry(player, industryId)
        Events.IndustryUpgradeResponseEvent:FireClient(player, industryId, success, message)

        if success then
            Events.NotificationEvent:FireClient(player, message, "success")
        else
            Events.NotificationEvent:FireClient(player, message, "error")
        end
    end)
    
    Events.GetLinesDataFunction.OnServerInvoke = function(player)
        return self.transportManager:GetPlayerLines(player)
    end)
    
    -- Building Events
    Events.PlaceBuildingEvent.OnServerEvent:Connect(function(player, buildingType, position, rotation)
        self:PlaceBuilding(player, buildingType, position, rotation)
    end)
    
    Events.RemoveBuildingEvent.OnServerEvent:Connect(function(player, buildingId)
        self:RemoveBuilding(player, buildingId)
    end)
    
    Events.GetBuildableItemsFunction.OnServerInvoke = function(player)
        return self:GetBuildableItems(player)
    end)
    
    -- City Events
    Events.GetCityStatsFunction.OnServerInvoke = function(player, cityId)
        return self.cityManager:GetCityStats(cityId)
    end)
    
    Events.RenameCityEvent.OnServerEvent:Connect(function(player, cityId, newName)
        self.cityManager:RenameCity(player, cityId, newName)
    end)
    
    -- Terrain Events
    Events.ModifyTerrainEvent.OnServerEvent:Connect(function(player, operation, position, size, strength)
        self.terrainManager:ModifyTerrain(player, operation, position, size, strength)
    end)
    
    Events.GetTerrainInfoFunction.OnServerInvoke = function(player, position)
        return self.terrainManager:GetTerrainInfo(position)
    end)
    
    -- Statistics Events
    Events.GetStatisticsFunction.OnServerInvoke = function(player)
        return self:GetPlayerStatistics(player)
    end)
    
    Events.GetTransportStatsFunction.OnServerInvoke = function(player)
        return self.transportManager:GetTransportStats(player)
    end)
    
    -- Settings Events
    Events.SaveSettingsEvent.OnServerEvent:Connect(function(player, settings)
        self:SavePlayerSettings(player, settings)
    end)
    
    Events.LoadSettingsFunction.OnServerInvoke = function(player)
        return self:LoadPlayerSettings(player)
    end)
end

-- Player Data Management
function GameManager:GetPlayerData(player)
    if not self.playerData[player.UserId] then
        self:LoadPlayerData(player)
    end
    return self.playerData[player.UserId]
end

function GameManager:LoadPlayerData(player)
    local success, data = pcall(function()
        return PlayerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        self.playerData[player.UserId] = data
        print("✅ Player data loaded for:", player.Name)
    else
        -- Create new player data
        self.playerData[player.UserId] = {
            money = 2000000, -- Starting money: 2M
            reputation = 50,
            level = 1,
            experience = 0,
            companies = {},
            vehicles = {},
            lines = {},
            buildings = {},
            research = {
                points = 0,
                completed = {},
                current = nil
            },
            statistics = {
                totalProfit = 0,
                totalPassengers = 0,
                totalCargo = 0,
                gamesPlayed = 0,
                playtime = 0
            },
            settings = {
                graphics = {quality = "High", shadows = true},
                audio = {masterVolume = 0.8, musicVolume = 0.6},
                controls = {mouseSensitivity = 0.5},
                gameplay = {autosave = true, difficulty = "Normal"}
            },
            loans = {},
            achievements = {},
            lastSave = os.time()
        }
        print("🆕 New player data created for:", player.Name)
    end
end

function GameManager:SavePlayerData(player, additionalData)
    if not self.playerData[player.UserId] then return end
    
    -- Merge additional data if provided
    if additionalData then
        for key, value in pairs(additionalData) do
            self.playerData[player.UserId][key] = value
        end
    end
    
    self.playerData[player.UserId].lastSave = os.time()
    
    local success, error = pcall(function()
        PlayerDataStore:SetAsync(player.UserId, self.playerData[player.UserId])
    end)
    
    if success then
        print("💾 Player data saved for:", player.Name)
    else
        warn("❌ Failed to save player data for:", player.Name, error)
    end
end

-- Game State Management
function GameManager:SetGameSpeed(player, speed)
    if speed < 0 or speed > 4 then return end
    
    self.gameState.gameSpeed = speed
    self.gameState.isPaused = (speed == 0)
    
    -- Broadcast to all players
    ReplicatedStorage.Events.GameSpeedChangedEvent:FireAllClients(speed)
    print("⏱️ Game speed changed to:", speed, "by", player.Name)
end

function GameManager:PauseGame(player, paused)
    self.gameState.isPaused = paused
    if paused then
        self.gameState.gameSpeed = 0
    else
        self.gameState.gameSpeed = 1
    end
    
    ReplicatedStorage.Events.GamePausedEvent:FireAllClients(paused)
    print(paused and "⏸️ Game paused" or "▶️ Game resumed", "by", player.Name)
end

-- Building System
function GameManager:PlaceBuilding(player, buildingType, position, rotation)
    local playerData = self:GetPlayerData(player)
    local buildingInfo = self:GetBuildingInfo(buildingType)
    
    if not buildingInfo then
        warn("❌ Invalid building type:", buildingType)
        return
    end
    
    -- Check if player has enough money
    if playerData.money < buildingInfo.cost then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Nicht genügend Geld!", "error")
        return
    end
    
    -- Check terrain suitability
    if not self.terrainManager:CanPlaceBuilding(position, buildingInfo.size) then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Ungültiger Bauplatz!", "error")
        return
    end
    
    -- Create building
    local buildingId = HttpService:GenerateGUID(false)
    local building = {
        id = buildingId,
        type = buildingType,
        position = position,
        rotation = rotation,
        owner = player.UserId,
        built = os.time(),
        level = 1,
        condition = 100,
        production = buildingInfo.production or 0,
        maintenance = buildingInfo.maintenance or 0
    }
    
    -- Deduct money
    playerData.money = playerData.money - buildingInfo.cost
    
    -- Add to player buildings
    playerData.buildings[buildingId] = building
    
    -- Create 3D model
    self:CreateBuildingModel(building)
    
    -- Save data
    self:SavePlayerData(player)
    
    -- Notify client
    ReplicatedStorage.Events.BuildingPlacedEvent:FireClient(player, building)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, buildingInfo.name .. " gebaut!", "success")
    
    print("🏗️ Building placed:", buildingType, "by", player.Name, "at", position)
end

function GameManager:GetBuildingInfo(buildingType)
    local buildings = {
        ["train_station"] = {name = "Bahnhof", cost = 25000, size = {x = 10, z = 6}, maintenance = 500},
        ["cargo_station"] = {name = "Güterbahnhof", cost = 30000, size = {x = 12, z = 8}, maintenance = 600},
        ["bus_stop"] = {name = "Bushaltestelle", cost = 5000, size = {x = 4, z = 2}, maintenance = 100},
        ["airport"] = {name = "Flughafen", cost = 100000, size = {x = 30, z = 20}, maintenance = 2000},
        ["depot"] = {name = "Depot", cost = 50000, size = {x = 15, z = 10}, maintenance = 800},
        ["maintenance_hall"] = {name = "Wartungshalle", cost = 75000, size = {x = 20, z = 12}, maintenance = 1000},
        ["power_plant"] = {name = "Kraftwerk", cost = 200000, size = {x = 25, z = 15}, maintenance = 3000, production = 1000},
        ["office_building"] = {name = "Bürogebäude", cost = 150000, size = {x = 8, z = 8}, maintenance = 1200}
    }
    return buildings[buildingType]
end

function GameManager:GetBuildableItems(player)
    local playerData = self:GetPlayerData(player)
    
    return {
        tracks = {
            {id = "simple_track", name = "Einfache Gleise", cost = 1000, unlocked = true},
            {id = "electric_track", name = "Elektrifizierte Gleise", cost = 2500, unlocked = playerData.level >= 5},
            {id = "highspeed_track", name = "Hochgeschwindigkeits-Gleise", cost = 5000, unlocked = playerData.level >= 10}
        },
        roads = {
            {id = "country_road", name = "Landstraße", cost = 500, unlocked = true},
            {id = "highway", name = "Autobahn", cost = 2000, unlocked = playerData.level >= 3},
            {id = "city_street", name = "Stadtstraße", cost = 1000, unlocked = true}
        },
        stations = {
            {id = "train_station", name = "Bahnhof", cost = 25000, unlocked = true},
            {id = "cargo_station", name = "Güterbahnhof", cost = 30000, unlocked = playerData.level >= 2},
            {id = "bus_stop", name = "Bushaltestelle", cost = 5000, unlocked = true},
            {id = "airport", name = "Flughafen", cost = 100000, unlocked = playerData.level >= 15}
        },
        buildings = {
            {id = "depot", name = "Depot", cost = 50000, unlocked = true},
            {id = "maintenance_hall", name = "Wartungshalle", cost = 75000, unlocked = playerData.level >= 5},
            {id = "power_plant", name = "Kraftwerk", cost = 200000, unlocked = playerData.level >= 20},
            {id = "office_building", name = "Bürogebäude", cost = 150000, unlocked = playerData.level >= 10}
        }
    }
end

-- Statistics
function GameManager:GetPlayerStatistics(player)
    local playerData = self:GetPlayerData(player)
    
    return {
        overview = {
            totalVehicles = #playerData.vehicles,
            totalLines = #playerData.lines,
            totalBuildings = #playerData.buildings,
            monthlyProfit = self.economyManager:GetMonthlyProfit(player),
            reputation = playerData.reputation,
            level = playerData.level
        },
        transport = self.transportManager:GetTransportStats(player),
        economy = self.economyManager:GetEconomyStats(player),
        cities = self.cityManager:GetCityStats(),
        environment = {
            co2Emissions = 1250,
            airQuality = 78,
            noiseLevel = 45,
            waterQuality = 85
        }
    }
end

-- Settings
function GameManager:SavePlayerSettings(player, settings)
    local playerData = self:GetPlayerData(player)
    playerData.settings = settings
    self:SavePlayerData(player)
end

function GameManager:LoadPlayerSettings(player)
    local playerData = self:GetPlayerData(player)
    return playerData.settings
end

-- Game Loop
function GameManager:StartGameLoop()
    local lastUpdate = tick()
    
    RunService.Heartbeat:Connect(function()
        local now = tick()
        local deltaTime = now - lastUpdate
        
        if not self.gameState.isPaused and deltaTime >= (1 / self.gameState.gameSpeed) then
            self:UpdateGame(deltaTime)
            lastUpdate = now
        end
    end)
    
    print("🎮 Game loop started")
end

-- Rename city
function GameManager:RenameCity(player, cityId, newName)
    local city = self.cityManager.cities[cityId]
    if not city then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Stadt nicht gefunden!", "error")
        return
    end

    -- Validate name
    if not newName or newName == "" or string.len(newName) > 50 then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Ungültiger Stadtname!", "error")
        return
    end

    local oldName = city.name
    city.name = newName

    -- Update 3D model label
    local cityModel = Workspace.Cities:FindFirstChild("City_" .. oldName)
    if cityModel then
        cityModel.Name = "City_" .. newName
        local cityCenter = cityModel:FindFirstChild("CityCenter")
        if cityCenter then
            local nameGui = cityCenter:FindFirstChild("BillboardGui")
            if nameGui then
                local nameLabel = nameGui:FindFirstChild("TextLabel")
                if nameLabel then
                    nameLabel.Text = newName
                end
            end
        end
    end

    -- Notify client
    ReplicatedStorage.Events.CityRenamedEvent:FireClient(player, cityId, newName)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Stadt umbenannt: " .. newName, "success")

    print("🏙️ City renamed:", player.Name, oldName, "->", newName)
end

function GameManager:UpdateGame(deltaTime)
    -- Update game date
    self:UpdateGameDate()
    
    -- Update all managers
    self.economyManager:Update(deltaTime)
    self.vehicleManager:Update(deltaTime)
    self.cityManager:UpdateCities(deltaTime)
    self.transportManager:Update(deltaTime)
    -- self.aiManager:Update(deltaTime) -- Placeholder
    
    -- Auto-save every 5 minutes
    if tick() % 300 < 1 then
        self:AutoSave()
    end
end

function GameManager:UpdateGameDate()
    -- Simplified date progression (1 real second = 1 game day at speed 1)
    self.gameState.currentDate.day = self.gameState.currentDate.day + 1
    
    if self.gameState.currentDate.day > 30 then
        self.gameState.currentDate.day = 1
        self.gameState.currentDate.month = self.gameState.currentDate.month + 1
        
        if self.gameState.currentDate.month > 12 then
            self.gameState.currentDate.month = 1
            self.gameState.currentDate.year = self.gameState.currentDate.year + 1
        end
    end
    
    -- Update season
    local month = self.gameState.currentDate.month
    if month >= 3 and month <= 5 then
        self.gameState.season = "Spring"
    elseif month >= 6 and month <= 8 then
        self.gameState.season = "Summer"
    elseif month >= 9 and month <= 11 then
        self.gameState.season = "Autumn"
    else
        self.gameState.season = "Winter"
    end
end

function GameManager:AutoSave()
    for _, player in pairs(Players:GetPlayers()) do
        self:SavePlayerData(player)
    end
    print("💾 Auto-save completed")
end

-- Player Events
Players.PlayerAdded:Connect(function(player)
    print("👋 Player joined:", player.Name)
    GameManager.new():LoadPlayerData(player)
end)

Players.PlayerRemoving:Connect(function(player)
    print("👋 Player leaving:", player.Name)
    GameManager.new():SavePlayerData(player)
end)

-- Initialize
return GameManager.new()
