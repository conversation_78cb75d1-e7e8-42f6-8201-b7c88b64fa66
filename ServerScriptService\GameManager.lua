-- ServerScriptService/GameManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Zentraler Game Manager für Transport Fever 2 - Koordiniert alle Spielsysteme

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")

-- DataStores
local PlayerDataStore = DataStoreService:GetDataStore("PlayerData")
local GameStateStore = DataStoreService:GetDataStore("GameState")

-- Module Dependencies (Safe Loading)
local function safeRequire(modulePath, moduleName)
    local success, result = pcall(function()
        return require(modulePath)
    end)

    if success then
        print("✅ Manager geladen:", moduleName)
        return result
    else
        warn("❌ Manager-Fehler:", moduleName, "-", tostring(result))
        return nil
    end
end

-- Manager laden (mit Fehlerbehandlung)
local managersFolder = script.Parent:FindFirstChild("Managers")
local EconomyManager = managersFolder and safeRequire(managersFolder.EconomyManager, "EconomyManager")
local VehicleManager = managersFolder and safeRequire(managersFolder.VehicleManager, "VehicleManager")
local CityManager = managersFolder and safeRequire(managersFolder.CityManager, "CityManager")
local TransportManager = managersFolder and safeRequire(managersFolder.TransportManager, "TransportManager")
local TerrainManager = managersFolder and safeRequire(managersFolder.TerrainManager, "TerrainManager")
local AIManager = managersFolder and safeRequire(managersFolder.AIManager, "AIManager")

local GameManager = {}
GameManager.__index = GameManager

-- Singleton Instance
local instance = nil

function GameManager.new()
    if instance then return instance end
    
    local self = setmetatable({}, GameManager)
    
    -- Game State
    self.gameState = {
        isRunning = false,
        isPaused = false,
        gameSpeed = 1,
        currentDate = {year = 1850, month = 1, day = 1},
        weather = "Clear",
        season = "Spring"
    }
    
    -- Player Data
    self.playerData = {}
    
    -- Managers (Safe Initialization)
    self.economyManager = EconomyManager and EconomyManager.new() or nil
    self.vehicleManager = VehicleManager and VehicleManager.new() or nil
    self.transportManager = TransportManager and TransportManager.new() or nil
    self.terrainManager = TerrainManager and TerrainManager.new() or nil
    self.cityManager = CityManager and CityManager.new() or nil

    -- Additional Manager Systems (Safe Loading)
    local FinanceManager = managersFolder and safeRequire(managersFolder:FindFirstChild("FinanceManager"), "FinanceManager")
    local AICompetitorManager = managersFolder and safeRequire(managersFolder:FindFirstChild("AICompetitorManager"), "AICompetitorManager")
    local CampaignManager = managersFolder and safeRequire(managersFolder:FindFirstChild("CampaignManager"), "CampaignManager")
    local AchievementManager = managersFolder and safeRequire(managersFolder:FindFirstChild("AchievementManager"), "AchievementManager")
    local MultiplayerManager = managersFolder and safeRequire(managersFolder:FindFirstChild("MultiplayerManager"), "MultiplayerManager")
    local AdvancedAIManager = managersFolder and safeRequire(managersFolder:FindFirstChild("AdvancedAIManager"), "AdvancedAIManager")
    local CooperationManager = managersFolder and safeRequire(managersFolder:FindFirstChild("CooperationManager"), "CooperationManager")

    -- Audio and Visual systems
    local AudioManager = require(script.Parent.Managers.AudioManager)
    local VisualEffectsManager = require(script.Parent.Managers.VisualEffectsManager)
    local WeatherSystem = require(script.Parent.Managers.WeatherManager)

    -- Performance and Testing systems
    local PerformanceManager = require(script.Parent.Managers.PerformanceManager)
    local GameTester = require(script.Parent.Managers.GameTester)

    self.financeManager = FinanceManager.new()
    self.aiCompetitorManager = AICompetitorManager.new()
    self.campaignManager = CampaignManager.new()
    self.achievementManager = AchievementManager.new()

    -- Initialize multiplayer systems
    self.multiplayerManager = MultiplayerManager.new()
    self.advancedAIManager = AdvancedAIManager.new()
    self.cooperationManager = CooperationManager.new()

    -- Initialize audio and visual systems
    self.audioManager = AudioManager.new()
    self.visualEffectsManager = VisualEffectsManager.new()
    self.weatherSystem = WeatherSystem.new()

    -- Initialize performance and testing systems
    self.performanceManager = PerformanceManager.new()
    self.gameTester = GameTester.new()

    -- Initialize cities from terrain
    self.cityManager:InitializeCities(self.terrainManager)

    -- Initialize AI competitors (if enabled)
    if self.gameState.aiCompetitors then
        self.aiCompetitorManager:InitializeCompetitors(self.gameState.numAICompetitors or 3, self.gameState)
    end

    -- Initialize audio and visual systems
    self.audioManager:Initialize()
    self.visualEffectsManager:Initialize()
    self.weatherSystem:Initialize()

    -- Initialize performance and testing systems
    self.performanceManager:Initialize()
    self.gameTester:Initialize()

    -- Events Setup
    self:SetupEvents()
    
    -- Game Loop
    self:StartGameLoop()
    
    instance = self
    return self
end

-- Events Setup
function GameManager:SetupEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Player Management
    Events.GetPlayerDataFunction.OnServerInvoke = function(player)
        return self:GetPlayerData(player)
    end

    Events.SavePlayerDataEvent.OnServerEvent:Connect(function(player, data)
        self:SavePlayerData(player, data)
    end)

    -- Financial Events
    Events.GetFinancialSummaryEvent.OnServerEvent:Connect(function(player)
        local summary = self.financeManager:GetFinancialSummary(tostring(player.UserId))
        Events.FinancialSummaryResponseEvent:FireClient(player, summary)
    end)

    Events.ApplyForLoanEvent.OnServerEvent:Connect(function(player, loanId)
        local success, message = self.financeManager:ApplyForLoan(tostring(player.UserId), loanId)
        Events.LoanApplicationResponseEvent:FireClient(player, success, message)

        if success then
            Events.NotificationEvent:FireClient(player, message, "success")
        else
            Events.NotificationEvent:FireClient(player, message, "error")
        end
    end)

    -- AI Competitor Events
    Events.GetAICompetitorsEvent.OnServerEvent:Connect(function(player)
        local competitors = self.aiCompetitorManager:GetAllCompaniesData()
        Events.AICompetitorsResponseEvent:FireClient(player, competitors)
    end)

    Events.GetMarketOverviewEvent.OnServerEvent:Connect(function(player)
        local playerData = self:GetPlayerData(player)
        local gameData = {
            playerRevenue = playerData.monthlyRevenue or 0,
            playerActive = true,
            marketConditions = self.financeManager.marketConditions
        }
        local overview = self.aiCompetitorManager:GetMarketOverview(gameData)
        Events.MarketOverviewResponseEvent:FireClient(player, overview)
    end)

    -- Campaign Events
    Events.GetAvailableCampaignsEvent.OnServerEvent:Connect(function(player)
        local campaigns = self.campaignManager:GetAvailableCampaigns()
        Events.AvailableCampaignsResponseEvent:FireClient(player, campaigns)
    end)

    Events.StartCampaignEvent.OnServerEvent:Connect(function(player, campaignId)
        local success, message = self.campaignManager:StartCampaign(tostring(player.UserId), campaignId)
        Events.CampaignStartResponseEvent:FireClient(player, success, message)

        if success then
            Events.NotificationEvent:FireClient(player, message, "success")
        else
            Events.NotificationEvent:FireClient(player, message, "error")
        end
    end)

    Events.GetCampaignProgressEvent.OnServerEvent:Connect(function(player)
        local progress = self.campaignManager:GetCampaignProgress(tostring(player.UserId))
        Events.CampaignProgressResponseEvent:FireClient(player, progress)
    end)

    Events.ResearchTechnologyEvent.OnServerEvent:Connect(function(player, technologyId)
        local success, message = self.campaignManager:ResearchTechnology(tostring(player.UserId), technologyId)
        Events.TechnologyResearchResponseEvent:FireClient(player, success, message)

        if success then
            Events.NotificationEvent:FireClient(player, message, "success")
        else
            Events.NotificationEvent:FireClient(player, message, "error")
        end
    end)

    -- Achievement Events
    Events.GetPlayerAchievementsEvent.OnServerEvent:Connect(function(player)
        local achievements = self.achievementManager:GetPlayerAchievements(tostring(player.UserId))
        Events.PlayerAchievementsResponseEvent:FireClient(player, achievements)
    end)

    Events.GetAchievementStatsEvent.OnServerEvent:Connect(function(player)
        local stats = self.achievementManager:GetAchievementStats(tostring(player.UserId))
        Events.AchievementStatsResponseEvent:FireClient(player, stats)
    end)

    -- Multiplayer Events
    Events.GetConnectedPlayersEvent.OnServerEvent:Connect(function(player)
        local players = self.multiplayerManager:GetConnectedPlayers()
        Events.ConnectedPlayersResponseEvent:FireClient(player, players)
    end)

    Events.SendChatMessageEvent.OnServerEvent:Connect(function(player, message, channel)
        local success, result = self.multiplayerManager:SendChatMessage(player, message, channel)
        if success then
            -- Chat message is broadcasted by the manager
        else
            Events.NotificationEvent:FireClient(player, result, "error")
        end
    end)

    -- Alliance Events
    Events.CreateAllianceEvent.OnServerEvent:Connect(function(player, allianceName, settings)
        local success, result = self.cooperationManager:CreateAlliance(tostring(player.UserId), allianceName, settings)
        Events.AllianceCreationResponseEvent:FireClient(player, success, result)

        if success then
            Events.NotificationEvent:FireClient(player, "Allianz erstellt: " .. allianceName, "success")
        else
            Events.NotificationEvent:FireClient(player, result, "error")
        end
    end)

    Events.InviteToAllianceEvent.OnServerEvent:Connect(function(player, targetPlayerId, allianceId)
        local success, result = self.cooperationManager:InviteToAlliance(tostring(player.UserId), targetPlayerId, allianceId)
        Events.AllianceInviteResponseEvent:FireClient(player, success, result)
    end)

    Events.AcceptAllianceInviteEvent.OnServerEvent:Connect(function(player, inviteId)
        local success, result = self.cooperationManager:AcceptAllianceInvite(tostring(player.UserId), inviteId)
        Events.AllianceJoinResponseEvent:FireClient(player, success, result)

        if success then
            Events.NotificationEvent:FireClient(player, "Allianz beigetreten: " .. result, "success")
        end
    end)

    Events.LeaveAllianceEvent.OnServerEvent:Connect(function(player, allianceId)
        local success, result = self.cooperationManager:LeaveAlliance(tostring(player.UserId), allianceId)
        Events.AllianceLeaveResponseEvent:FireClient(player, success, result)
    end)

    -- Trade Events
    Events.CreateTradeOfferEvent.OnServerEvent:Connect(function(player, targetPlayerId, offer, request)
        local success, result = self.cooperationManager:CreateTradeOffer(tostring(player.UserId), targetPlayerId, offer, request)
        Events.TradeOfferResponseEvent:FireClient(player, success, result)
    end)

    Events.AcceptTradeOfferEvent.OnServerEvent:Connect(function(player, tradeId)
        local success, result = self.cooperationManager:AcceptTradeOffer(tostring(player.UserId), tradeId)
        Events.TradeAcceptResponseEvent:FireClient(player, success, result)
    end)

    Events.GetPlayerTradeOffersEvent.OnServerEvent:Connect(function(player)
        local offers = self.cooperationManager:GetPlayerTradeOffers(tostring(player.UserId))
        Events.PlayerTradeOffersResponseEvent:FireClient(player, offers)
    end)

    -- Cooperation Project Events
    Events.CreateCooperationProjectEvent.OnServerEvent:Connect(function(player, projectType, projectData)
        local success, result = self.cooperationManager:CreateCooperationProject(tostring(player.UserId), projectType, projectData)
        Events.CooperationProjectResponseEvent:FireClient(player, success, result)
    end)
    
    -- Game State
    Events.GetGameStateFunction.OnServerInvoke = function(player)
        return self.gameState
    end
    
    Events.SetGameSpeedEvent.OnServerEvent:Connect(function(player, speed)
        self:SetGameSpeed(player, speed)
    end)
    
    Events.PauseGameEvent.OnServerEvent:Connect(function(player, paused)
        self:PauseGame(player, paused)
    end)
    
    -- Economy Events
    Events.GetFinanceDataFunction.OnServerInvoke = function(player)
        return self.economyManager:GetPlayerFinances(player)
    end
    
    Events.RequestLoanEvent.OnServerEvent:Connect(function(player, amount)
        self.economyManager:RequestLoan(player, amount)
    end)
    
    Events.RepayLoanEvent.OnServerEvent:Connect(function(player, loanId, amount)
        self.economyManager:RepayLoan(player, loanId, amount)
    end)
    
    -- Vehicle Events
    Events.BuyVehicleEvent.OnServerEvent:Connect(function(player, vehicleType, vehicleId)
        self.vehicleManager:BuyVehicle(player, vehicleType, vehicleId)
    end)
    
    Events.SellVehicleEvent.OnServerEvent:Connect(function(player, vehicleInstanceId)
        self.vehicleManager:SellVehicle(player, vehicleInstanceId)
    end)
    
    Events.GetVehicleDataFunction.OnServerInvoke = function(player)
        return self.vehicleManager:GetPlayerVehicles(player)
    end
    
    -- Transport Events
    Events.CreateLineEvent.OnServerEvent:Connect(function(player, lineData)
        self.transportManager:CreateLine(player, lineData)
    end)

    Events.DeleteLineEvent.OnServerEvent:Connect(function(player, lineId)
        self.transportManager:DeleteLine(player, lineId)
    end)

    -- City Events
    Events.GetCityStatsEvent.OnServerEvent:Connect(function(player, cityId)
        local stats = self.cityManager:GetCityStats(cityId)
        Events.CityStatsResponseEvent:FireClient(player, cityId, stats)
    end)

    Events.RenameCityEvent.OnServerEvent:Connect(function(player, cityId, newName)
        self:RenameCity(player, cityId, newName)
    end)

    Events.GetAllCitiesEvent.OnServerEvent:Connect(function(player)
        local cities = {}
        for cityId, city in pairs(self.cityManager.cities) do
            cities[cityId] = {
                id = cityId,
                name = city.name,
                population = city.population,
                size = city.size,
                x = city.x,
                z = city.z,
                demands = city.demands
            }
        end
        Events.AllCitiesResponseEvent:FireClient(player, cities)
    end)

    -- Industry Events
    Events.GetIndustryStatsEvent.OnServerEvent:Connect(function(player, industryId)
        local stats = self.cityManager:GetIndustryStats(industryId)
        Events.IndustryStatsResponseEvent:FireClient(player, industryId, stats)
    end)

    Events.UpgradeIndustryEvent.OnServerEvent:Connect(function(player, industryId)
        local success, message = self.cityManager:UpgradeIndustry(player, industryId)
        Events.IndustryUpgradeResponseEvent:FireClient(player, industryId, success, message)

        if success then
            Events.NotificationEvent:FireClient(player, message, "success")
        else
            Events.NotificationEvent:FireClient(player, message, "error")
        end
    end)
    
    Events.GetLinesDataFunction.OnServerInvoke = function(player)
        return self.transportManager:GetPlayerLines(player)
    end)
    
    -- Building Events
    Events.PlaceBuildingEvent.OnServerEvent:Connect(function(player, buildingType, position, rotation)
        self:PlaceBuilding(player, buildingType, position, rotation)
    end)
    
    Events.RemoveBuildingEvent.OnServerEvent:Connect(function(player, buildingId)
        self:RemoveBuilding(player, buildingId)
    end)
    
    Events.GetBuildableItemsFunction.OnServerInvoke = function(player)
        return self:GetBuildableItems(player)
    end)
    
    -- City Events
    Events.GetCityStatsFunction.OnServerInvoke = function(player, cityId)
        return self.cityManager:GetCityStats(cityId)
    end)
    
    Events.RenameCityEvent.OnServerEvent:Connect(function(player, cityId, newName)
        self.cityManager:RenameCity(player, cityId, newName)
    end)
    
    -- Terrain Events
    Events.ModifyTerrainEvent.OnServerEvent:Connect(function(player, operation, position, size, strength)
        self.terrainManager:ModifyTerrain(player, operation, position, size, strength)
    end)
    
    Events.GetTerrainInfoFunction.OnServerInvoke = function(player, position)
        return self.terrainManager:GetTerrainInfo(position)
    end)
    
    -- Statistics Events
    Events.GetStatisticsFunction.OnServerInvoke = function(player)
        return self:GetPlayerStatistics(player)
    end)
    
    Events.GetTransportStatsFunction.OnServerInvoke = function(player)
        return self.transportManager:GetTransportStats(player)
    end)
    
    -- Settings Events
    Events.SaveSettingsEvent.OnServerEvent:Connect(function(player, settings)
        self:SavePlayerSettings(player, settings)
    end)
    
    Events.LoadSettingsFunction.OnServerInvoke = function(player)
        return self:LoadPlayerSettings(player)
    end)
end

-- Player Data Management
function GameManager:GetPlayerData(player)
    if not self.playerData[player.UserId] then
        self:LoadPlayerData(player)
    end
    return self.playerData[player.UserId]
end

function GameManager:LoadPlayerData(player)
    local success, data = pcall(function()
        return PlayerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        self.playerData[player.UserId] = data
        print("✅ Player data loaded for:", player.Name)
    else
        -- Create new player data
        self.playerData[player.UserId] = {
            money = 2000000, -- Starting money: 2M
            reputation = 50,
            level = 1,
            experience = 0,
            companies = {},
            vehicles = {},
            lines = {},
            buildings = {},
            research = {
                points = 0,
                completed = {},
                current = nil
            },
            statistics = {
                totalProfit = 0,
                totalPassengers = 0,
                totalCargo = 0,
                gamesPlayed = 0,
                playtime = 0
            },
            settings = {
                graphics = {quality = "High", shadows = true},
                audio = {masterVolume = 0.8, musicVolume = 0.6},
                controls = {mouseSensitivity = 0.5},
                gameplay = {autosave = true, difficulty = "Normal"}
            },
            loans = {},
            achievements = {},
            lastSave = os.time()
        }
        print("🆕 New player data created for:", player.Name)
    end
end

function GameManager:SavePlayerData(player, additionalData)
    if not self.playerData[player.UserId] then return end
    
    -- Merge additional data if provided
    if additionalData then
        for key, value in pairs(additionalData) do
            self.playerData[player.UserId][key] = value
        end
    end
    
    self.playerData[player.UserId].lastSave = os.time()
    
    local success, error = pcall(function()
        PlayerDataStore:SetAsync(player.UserId, self.playerData[player.UserId])
    end)
    
    if success then
        print("💾 Player data saved for:", player.Name)
    else
        warn("❌ Failed to save player data for:", player.Name, error)
    end
end

-- Game State Management
function GameManager:SetGameSpeed(player, speed)
    if speed < 0 or speed > 4 then return end
    
    self.gameState.gameSpeed = speed
    self.gameState.isPaused = (speed == 0)
    
    -- Broadcast to all players
    ReplicatedStorage.Events.GameSpeedChangedEvent:FireAllClients(speed)
    print("⏱️ Game speed changed to:", speed, "by", player.Name)
end

function GameManager:PauseGame(player, paused)
    self.gameState.isPaused = paused
    if paused then
        self.gameState.gameSpeed = 0
    else
        self.gameState.gameSpeed = 1
    end
    
    ReplicatedStorage.Events.GamePausedEvent:FireAllClients(paused)
    print(paused and "⏸️ Game paused" or "▶️ Game resumed", "by", player.Name)
end

-- Building System
function GameManager:PlaceBuilding(player, buildingType, position, rotation)
    local playerData = self:GetPlayerData(player)
    local buildingInfo = self:GetBuildingInfo(buildingType)
    
    if not buildingInfo then
        warn("❌ Invalid building type:", buildingType)
        return
    end
    
    -- Check if player has enough money
    if playerData.money < buildingInfo.cost then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Nicht genügend Geld!", "error")
        return
    end
    
    -- Check terrain suitability
    if not self.terrainManager:CanPlaceBuilding(position, buildingInfo.size) then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Ungültiger Bauplatz!", "error")
        return
    end
    
    -- Create building
    local buildingId = HttpService:GenerateGUID(false)
    local building = {
        id = buildingId,
        type = buildingType,
        position = position,
        rotation = rotation,
        owner = player.UserId,
        built = os.time(),
        level = 1,
        condition = 100,
        production = buildingInfo.production or 0,
        maintenance = buildingInfo.maintenance or 0
    }
    
    -- Deduct money
    playerData.money = playerData.money - buildingInfo.cost
    
    -- Add to player buildings
    playerData.buildings[buildingId] = building
    
    -- Create 3D model
    self:CreateBuildingModel(building)
    
    -- Save data
    self:SavePlayerData(player)
    
    -- Notify client
    ReplicatedStorage.Events.BuildingPlacedEvent:FireClient(player, building)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, buildingInfo.name .. " gebaut!", "success")
    
    print("🏗️ Building placed:", buildingType, "by", player.Name, "at", position)
end

function GameManager:GetBuildingInfo(buildingType)
    local buildings = {
        ["train_station"] = {name = "Bahnhof", cost = 25000, size = {x = 10, z = 6}, maintenance = 500},
        ["cargo_station"] = {name = "Güterbahnhof", cost = 30000, size = {x = 12, z = 8}, maintenance = 600},
        ["bus_stop"] = {name = "Bushaltestelle", cost = 5000, size = {x = 4, z = 2}, maintenance = 100},
        ["airport"] = {name = "Flughafen", cost = 100000, size = {x = 30, z = 20}, maintenance = 2000},
        ["depot"] = {name = "Depot", cost = 50000, size = {x = 15, z = 10}, maintenance = 800},
        ["maintenance_hall"] = {name = "Wartungshalle", cost = 75000, size = {x = 20, z = 12}, maintenance = 1000},
        ["power_plant"] = {name = "Kraftwerk", cost = 200000, size = {x = 25, z = 15}, maintenance = 3000, production = 1000},
        ["office_building"] = {name = "Bürogebäude", cost = 150000, size = {x = 8, z = 8}, maintenance = 1200}
    }
    return buildings[buildingType]
end

function GameManager:GetBuildableItems(player)
    local playerData = self:GetPlayerData(player)
    
    return {
        tracks = {
            {id = "simple_track", name = "Einfache Gleise", cost = 1000, unlocked = true},
            {id = "electric_track", name = "Elektrifizierte Gleise", cost = 2500, unlocked = playerData.level >= 5},
            {id = "highspeed_track", name = "Hochgeschwindigkeits-Gleise", cost = 5000, unlocked = playerData.level >= 10}
        },
        roads = {
            {id = "country_road", name = "Landstraße", cost = 500, unlocked = true},
            {id = "highway", name = "Autobahn", cost = 2000, unlocked = playerData.level >= 3},
            {id = "city_street", name = "Stadtstraße", cost = 1000, unlocked = true}
        },
        stations = {
            {id = "train_station", name = "Bahnhof", cost = 25000, unlocked = true},
            {id = "cargo_station", name = "Güterbahnhof", cost = 30000, unlocked = playerData.level >= 2},
            {id = "bus_stop", name = "Bushaltestelle", cost = 5000, unlocked = true},
            {id = "airport", name = "Flughafen", cost = 100000, unlocked = playerData.level >= 15}
        },
        buildings = {
            {id = "depot", name = "Depot", cost = 50000, unlocked = true},
            {id = "maintenance_hall", name = "Wartungshalle", cost = 75000, unlocked = playerData.level >= 5},
            {id = "power_plant", name = "Kraftwerk", cost = 200000, unlocked = playerData.level >= 20},
            {id = "office_building", name = "Bürogebäude", cost = 150000, unlocked = playerData.level >= 10}
        }
    }
end

-- Statistics
function GameManager:GetPlayerStatistics(player)
    local playerData = self:GetPlayerData(player)
    
    return {
        overview = {
            totalVehicles = #playerData.vehicles,
            totalLines = #playerData.lines,
            totalBuildings = #playerData.buildings,
            monthlyProfit = self.economyManager:GetMonthlyProfit(player),
            reputation = playerData.reputation,
            level = playerData.level
        },
        transport = self.transportManager:GetTransportStats(player),
        economy = self.economyManager:GetEconomyStats(player),
        cities = self.cityManager:GetCityStats(),
        environment = {
            co2Emissions = 1250,
            airQuality = 78,
            noiseLevel = 45,
            waterQuality = 85
        }
    }
end

-- Settings
function GameManager:SavePlayerSettings(player, settings)
    local playerData = self:GetPlayerData(player)
    playerData.settings = settings
    self:SavePlayerData(player)
end

function GameManager:LoadPlayerSettings(player)
    local playerData = self:GetPlayerData(player)
    return playerData.settings
end

-- Game Loop
function GameManager:StartGameLoop()
    local lastUpdate = tick()
    
    RunService.Heartbeat:Connect(function()
        local now = tick()
        local deltaTime = now - lastUpdate
        
        if not self.gameState.isPaused and deltaTime >= (1 / self.gameState.gameSpeed) then
            self:UpdateGame(deltaTime)
            lastUpdate = now
        end
    end)
    
    print("🎮 Game loop started")
end

-- Rename city
function GameManager:RenameCity(player, cityId, newName)
    local city = self.cityManager.cities[cityId]
    if not city then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Stadt nicht gefunden!", "error")
        return
    end

    -- Validate name
    if not newName or newName == "" or string.len(newName) > 50 then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Ungültiger Stadtname!", "error")
        return
    end

    local oldName = city.name
    city.name = newName

    -- Update 3D model label
    local cityModel = Workspace.Cities:FindFirstChild("City_" .. oldName)
    if cityModel then
        cityModel.Name = "City_" .. newName
        local cityCenter = cityModel:FindFirstChild("CityCenter")
        if cityCenter then
            local nameGui = cityCenter:FindFirstChild("BillboardGui")
            if nameGui then
                local nameLabel = nameGui:FindFirstChild("TextLabel")
                if nameLabel then
                    nameLabel.Text = newName
                end
            end
        end
    end

    -- Notify client
    ReplicatedStorage.Events.CityRenamedEvent:FireClient(player, cityId, newName)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Stadt umbenannt: " .. newName, "success")

    print("🏙️ City renamed:", player.Name, oldName, "->", newName)
end

function GameManager:UpdateGame(deltaTime)
    -- Update game date
    self:UpdateGameDate()

    -- Update all managers
    self.economyManager:Update(deltaTime)
    self.vehicleManager:Update(deltaTime)
    self.cityManager:UpdateCities(deltaTime)
    self.transportManager:Update(deltaTime)

    -- Update financial system
    self.financeManager:UpdateMarketConditions(deltaTime)

    -- Update AI competitors
    if self.gameState.aiCompetitors then
        local gameData = {
            playerRevenue = self:GetTotalPlayerRevenue(),
            playerActive = true,
            marketConditions = self.financeManager.marketConditions,
            vehicles = self:GetAllPlayerVehicles(),
            infrastructure = self:GetAllPlayerInfrastructure(),
            activePlayers = #Players:GetPlayers(),
            economicGrowth = self.economyManager.economicGrowth or 1.0
        }
        self.aiCompetitorManager:UpdateAICompanies(deltaTime, gameData)
        self.advancedAIManager:Update(deltaTime, gameData)
    end

    -- Update multiplayer systems
    self.multiplayerManager:Update(deltaTime)
    self.cooperationManager:CleanupExpiredData()

    -- Update audio and visual systems
    local weatherInfo = self.weatherSystem:GetWeatherInfo()
    local gameStateForAV = {
        timeOfDay = weatherInfo.time,
        weather = weatherInfo.type,
        weatherIntensity = weatherInfo.intensity,
        gameSpeed = self.gameState.gameSpeed,
        economicActivity = self:GetEconomicActivity(),
        crisis = self.gameState.crisis or false,
        recentAchievement = self.gameState.recentAchievement or false
    }

    self.audioManager:Update(deltaTime, gameStateForAV, nil)
    self.visualEffectsManager:Update(deltaTime, gameStateForAV)

    -- Update performance system
    self.performanceManager:Update(deltaTime)

    -- Monthly financial updates
    if self.gameState.currentDate.day == 1 then -- First day of month
        self:ProcessMonthlyFinances()
        self:ProcessMonthlyCampaignUpdates()
    end

    -- Auto-save every 5 minutes
    if tick() % 300 < 1 then
        self:AutoSave()
    end
end

function GameManager:UpdateGameDate()
    -- Simplified date progression (1 real second = 1 game day at speed 1)
    self.gameState.currentDate.day = self.gameState.currentDate.day + 1
    
    if self.gameState.currentDate.day > 30 then
        self.gameState.currentDate.day = 1
        self.gameState.currentDate.month = self.gameState.currentDate.month + 1
        
        if self.gameState.currentDate.month > 12 then
            self.gameState.currentDate.month = 1
            self.gameState.currentDate.year = self.gameState.currentDate.year + 1
        end
    end
    
    -- Update season
    local month = self.gameState.currentDate.month
    if month >= 3 and month <= 5 then
        self.gameState.season = "Spring"
    elseif month >= 6 and month <= 8 then
        self.gameState.season = "Summer"
    elseif month >= 9 and month <= 11 then
        self.gameState.season = "Autumn"
    else
        self.gameState.season = "Winter"
    end
end

function GameManager:AutoSave()
    for _, player in pairs(Players:GetPlayers()) do
        self:SavePlayerData(player)
    end
    print("💾 Auto-save completed")
end

-- Process monthly finances for all players
function GameManager:ProcessMonthlyFinances()
    for playerId, playerData in pairs(self.playerData) do
        -- Initialize player finances if not exists
        if not self.financeManager.playerFinances[playerId] then
            self.financeManager:InitializePlayer(playerId)
        end

        -- Calculate monthly expenses
        local gameData = {
            vehicles = playerData.vehicles or {},
            infrastructure = playerData.infrastructure or {}
        }
        self.financeManager:CalculateMonthlyExpenses(playerId, gameData)

        -- Reset monthly counters
        self.financeManager:ResetPeriodCounters(playerId, "monthly")

        -- Update company valuation
        self.financeManager:CalculateCompanyValue(playerId, gameData)
    end

    print("💰 Monthly finances processed for all players")
end

-- Get total player revenue (for AI competition calculations)
function GameManager:GetTotalPlayerRevenue()
    local totalRevenue = 0
    for playerId, _ in pairs(self.playerData) do
        local finances = self.financeManager.playerFinances[playerId]
        if finances then
            totalRevenue = totalRevenue + finances.monthlyRevenue
        end
    end
    return totalRevenue
end

-- Get all player vehicles (for AI calculations)
function GameManager:GetAllPlayerVehicles()
    local allVehicles = {}
    for playerId, playerData in pairs(self.playerData) do
        if playerData.vehicles then
            for _, vehicle in pairs(playerData.vehicles) do
                table.insert(allVehicles, vehicle)
            end
        end
    end
    return allVehicles
end

-- Get all player infrastructure (for AI calculations)
function GameManager:GetAllPlayerInfrastructure()
    local allInfrastructure = {}
    for playerId, playerData in pairs(self.playerData) do
        if playerData.infrastructure then
            for _, infra in pairs(playerData.infrastructure) do
                table.insert(allInfrastructure, infra)
            end
        end
    end
    return allInfrastructure
end

-- Get comprehensive game data for AI systems
function GameManager:GetGameData()
    return {
        playerRevenue = self:GetTotalPlayerRevenue(),
        playerActive = true,
        marketConditions = self.financeManager.marketConditions,
        vehicles = self:GetAllPlayerVehicles(),
        infrastructure = self:GetAllPlayerInfrastructure(),
        activePlayers = #Players:GetPlayers(),
        economicGrowth = self.economyManager.economicGrowth or 1.0,
        cities = self.cityManager.cities,
        industries = self.economyManager.industries,
        gameDate = self.gameState.currentDate,
        gameSpeed = self.gameState.gameSpeed,
        isPaused = self.gameState.isPaused,

        -- Multiplayer specific data
        playerCooperationEnabled = true,
        underservedRoutes = self:FindUnderservedRoutes(),
        availableMarkets = self:FindAvailableMarkets()
    }
end

-- Find underserved routes for AI expansion
function GameManager:FindUnderservedRoutes()
    local routes = {}
    -- This would analyze current transport coverage and find gaps
    -- Simplified implementation
    for cityId, city in pairs(self.cityManager.cities) do
        if city.population > 1000 and not city.wellConnected then
            table.insert(routes, {
                fromCity = cityId,
                demand = city.population / 1000,
                competition = 0.3, -- Low competition
                estimatedCost = 200000
            })
        end
    end
    return routes
end

-- Find available markets for expansion
function GameManager:FindAvailableMarkets()
    local markets = {}
    -- This would identify new market opportunities
    -- Simplified implementation
    for industryId, industry in pairs(self.economyManager.industries) do
        if industry.production > industry.transportCapacity then
            table.insert(markets, {
                industryId = industryId,
                type = industry.type,
                growthRate = 0.05,
                competitionLevel = 0.4
            })
        end
    end
    return markets
end

-- Get economic activity level for audio/visual systems
function GameManager:GetEconomicActivity()
    local totalRevenue = self:GetTotalPlayerRevenue()
    local vehicleCount = 0

    for playerId, playerData in pairs(self.playerData) do
        if playerData.vehicles then
            vehicleCount = vehicleCount + #playerData.vehicles
        end
    end

    -- Normalize activity level (0.0 to 1.0)
    local revenueActivity = math.min(1.0, totalRevenue / 1000000) -- Max at 1M revenue
    local vehicleActivity = math.min(1.0, vehicleCount / 50) -- Max at 50 vehicles

    return (revenueActivity + vehicleActivity) / 2
end

-- Initialize player finances when they join
function GameManager:InitializePlayerFinances(player)
    local playerId = tostring(player.UserId)
    if not self.financeManager.playerFinances[playerId] then
        self.financeManager:InitializePlayer(playerId)
        print("💰 Initialized finances for player:", player.Name)
    end
end

-- Add revenue to player (called by transport/economy systems)
function GameManager:AddPlayerRevenue(player, amount, category, description)
    local playerId = tostring(player.UserId)
    self:InitializePlayerFinances(player)
    return self.financeManager:AddRevenue(playerId, amount, category, description)
end

-- Add expense to player (called by various systems)
function GameManager:AddPlayerExpense(player, amount, category, description)
    local playerId = tostring(player.UserId)
    self:InitializePlayerFinances(player)
    return self.financeManager:AddExpense(playerId, amount, category, description)
end

-- Process monthly campaign updates
function GameManager:ProcessMonthlyCampaignUpdates()
    for playerId, playerData in pairs(self.playerData) do
        -- Update campaign progress
        local finances = self.financeManager.playerFinances[playerId]
        if finances then
            self.campaignManager:UpdateProgress(playerId, "monthly_profit", {profit = finances.monthlyRevenue - finances.monthlyExpenses})
            self.campaignManager:UpdateProgress(playerId, "cash_update", {cash = finances.cash})
        end

        -- Update achievement progress
        if finances then
            self.achievementManager:UpdateProgress(playerId, "cash", finances.cash, false)
            self.achievementManager:UpdateProgress(playerId, "cumulative_revenue", finances.monthlyRevenue, true)
            self.achievementManager:UpdateProgress(playerId, "company_value", finances.companyValue, false)
        end
    end
end

-- Track player action for campaigns and achievements
function GameManager:TrackPlayerAction(player, actionType, data)
    local playerId = tostring(player.UserId)

    -- Initialize systems if needed
    if not self.campaignManager.playerCampaigns[playerId] then
        self.campaignManager:InitializePlayer(playerId)
    end
    if not self.achievementManager.playerAchievements[playerId] then
        self.achievementManager:InitializePlayer(playerId)
    end

    -- Update campaign progress
    self.campaignManager:UpdateProgress(playerId, actionType, data)

    -- Update achievement progress
    if actionType == "route_created" then
        self.achievementManager:UpdateProgress(playerId, "routes_created", 1, true)
    elseif actionType == "vehicle_bought" then
        self.achievementManager:UpdateProgress(playerId, "max_vehicle_speed", data.speed or 0, false)
    elseif actionType == "city_connected" then
        self.achievementManager:UpdateProgress(playerId, "cities_connected", 1, true)
    elseif actionType == "cargo_transported" then
        self.achievementManager:UpdateProgress(playerId, "cargo_transported", data.amount or 0, true)
    elseif actionType == "passengers_transported" then
        self.achievementManager:UpdateProgress(playerId, "passengers_transported", data.amount or 0, true)
    elseif actionType == "infrastructure_built" then
        self.achievementManager:UpdateProgress(playerId, "infrastructure_length", data.length or 0, true)
        if data.infrastructureType == "bridge" then
            self.achievementManager:UpdateProgress(playerId, "bridges_built", 1, true)
        elseif data.infrastructureType == "tunnel" then
            self.achievementManager:UpdateProgress(playerId, "tunnels_built", 1, true)
        end
    elseif actionType == "station_built" then
        self.achievementManager:UpdateProgress(playerId, "stations_built", 1, true)
    end

    print("📊 Tracked action for player", player.Name, ":", actionType)
end

-- Initialize player campaigns and achievements when they join
function GameManager:InitializePlayerProgression(player)
    local playerId = tostring(player.UserId)

    if not self.campaignManager.playerCampaigns[playerId] then
        self.campaignManager:InitializePlayer(playerId)
        print("🎯 Initialized campaign progress for player:", player.Name)
    end

    if not self.achievementManager.playerAchievements[playerId] then
        self.achievementManager:InitializePlayer(playerId)
        print("🏆 Initialized achievements for player:", player.Name)
    end
end

-- Player Events
Players.PlayerAdded:Connect(function(player)
    print("👋 Player joined:", player.Name)
    local gameManager = GameManager.new()
    gameManager:LoadPlayerData(player)
    gameManager:InitializePlayerFinances(player)
    gameManager:InitializePlayerProgression(player)

    -- Initialize multiplayer systems
    gameManager.multiplayerManager:InitializePlayer(player)
    gameManager.cooperationManager:InitializePlayer(tostring(player.UserId))

    print("🌐 Multiplayer systems initialized for:", player.Name)
end)

Players.PlayerRemoving:Connect(function(player)
    print("👋 Player leaving:", player.Name)
    local gameManager = GameManager.new()
    gameManager:SavePlayerData(player)

    -- Handle multiplayer disconnect
    gameManager.multiplayerManager:PlayerDisconnected(player)

    print("🌐 Multiplayer disconnect handled for:", player.Name)
end)

-- Initialize
return GameManager.new()
