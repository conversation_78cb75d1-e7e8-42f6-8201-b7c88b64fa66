-- ServerScriptService/PerformanceManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Performance-Optimierung und LOD-System

local RunService = game:GetService("RunService")
local Players = game:GetService("Players")
local HttpService = game:GetService("HttpService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local PerformanceManager = {}
PerformanceManager.__index = PerformanceManager

function PerformanceManager.new()
    local self = setmetatable({}, PerformanceManager)
    
    -- Performance monitoring
    self.performanceMetrics = {
        fps = 60,
        memory = 0,
        networkLatency = 0,
        serverLoad = 0,
        activeObjects = 0,
        renderDistance = 1000,
        lastUpdate = tick()
    }
    
    -- LOD (Level of Detail) system
    self.lodSystem = {
        enabled = true,
        updateInterval = 0.5, -- Update every 0.5 seconds
        lastUpdate = 0,
        lodLevels = {
            high = {distance = 200, quality = 1.0},
            medium = {distance = 500, quality = 0.6},
            low = {distance = 1000, quality = 0.3},
            culled = {distance = 2000, quality = 0.0}
        }
    }
    
    -- Object management
    self.managedObjects = {}
    self.objectPools = {}
    self.culledObjects = {}
    
    -- Performance settings
    self.settings = {
        maxActiveVehicles = 100,
        maxActiveBuildings = 500,
        maxParticleEffects = 50,
        maxSoundSources = 20,
        enableDynamicLOD = true,
        enableObjectPooling = true,
        enableCulling = true,
        targetFPS = 60,
        memoryLimit = 2048 -- MB
    }
    
    -- Optimization strategies
    self.optimizations = {
        vehicleOptimization = true,
        buildingOptimization = true,
        effectOptimization = true,
        audioOptimization = true,
        networkOptimization = true
    }
    
    -- Performance profiles
    self.performanceProfiles = {
        ultra = {
            renderDistance = 2000,
            maxActiveVehicles = 200,
            maxActiveBuildings = 1000,
            maxParticleEffects = 100,
            lodEnabled = false,
            shadowQuality = "High",
            textureQuality = "High"
        },
        
        high = {
            renderDistance = 1500,
            maxActiveVehicles = 150,
            maxActiveBuildings = 750,
            maxParticleEffects = 75,
            lodEnabled = true,
            shadowQuality = "High",
            textureQuality = "High"
        },
        
        medium = {
            renderDistance = 1000,
            maxActiveVehicles = 100,
            maxActiveBuildings = 500,
            maxParticleEffects = 50,
            lodEnabled = true,
            shadowQuality = "Medium",
            textureQuality = "Medium"
        },
        
        low = {
            renderDistance = 500,
            maxActiveVehicles = 50,
            maxActiveBuildings = 250,
            maxParticleEffects = 25,
            lodEnabled = true,
            shadowQuality = "Low",
            textureQuality = "Low"
        },
        
        potato = {
            renderDistance = 300,
            maxActiveVehicles = 25,
            maxActiveBuildings = 100,
            maxParticleEffects = 10,
            lodEnabled = true,
            shadowQuality = "Off",
            textureQuality = "Low"
        }
    }
    
    self.currentProfile = "medium"
    
    return self
end

-- Initialize performance manager
function PerformanceManager:Initialize()
    -- Apply initial performance profile
    self:ApplyPerformanceProfile(self.currentProfile)
    
    -- Start performance monitoring
    self:StartPerformanceMonitoring()
    
    -- Initialize LOD system
    self:InitializeLODSystem()
    
    -- Initialize object pooling
    self:InitializeObjectPooling()
    
    print("⚡ Performance Manager initialized with profile:", self.currentProfile)
end

-- Start performance monitoring
function PerformanceManager:StartPerformanceMonitoring()
    spawn(function()
        while true do
            self:UpdatePerformanceMetrics()
            self:OptimizePerformance()
            wait(1) -- Update every second
        end
    end)
end

-- Update performance metrics
function PerformanceManager:UpdatePerformanceMetrics()
    local currentTime = tick()
    local deltaTime = currentTime - self.performanceMetrics.lastUpdate
    
    -- Calculate FPS
    if deltaTime > 0 then
        self.performanceMetrics.fps = math.floor(1 / deltaTime)
    end
    
    -- Get memory usage
    local stats = game:GetService("Stats")
    if stats then
        local memoryStats = stats:FindFirstChild("MemoryUsage")
        if memoryStats then
            local totalMemory = 0
            for _, child in pairs(memoryStats:GetChildren()) do
                if child:IsA("IntValue") then
                    totalMemory = totalMemory + child.Value
                end
            end
            self.performanceMetrics.memory = totalMemory / 1024 / 1024 -- Convert to MB
        end
    end
    
    -- Count active objects
    self.performanceMetrics.activeObjects = #self.managedObjects
    
    -- Calculate server load (simplified)
    local playerCount = #Players:GetPlayers()
    self.performanceMetrics.serverLoad = math.min(1.0, playerCount / 20) -- Max load at 20 players
    
    self.performanceMetrics.lastUpdate = currentTime
end

-- Optimize performance based on metrics
function PerformanceManager:OptimizePerformance()
    local metrics = self.performanceMetrics
    
    -- Auto-adjust performance profile based on FPS
    if metrics.fps < 30 and self.currentProfile ~= "low" and self.currentProfile ~= "potato" then
        if self.currentProfile == "ultra" then
            self:ApplyPerformanceProfile("high")
        elseif self.currentProfile == "high" then
            self:ApplyPerformanceProfile("medium")
        elseif self.currentProfile == "medium" then
            self:ApplyPerformanceProfile("low")
        end
        print("⚡ Performance profile downgraded due to low FPS:", metrics.fps)
    elseif metrics.fps > 55 and self.currentProfile ~= "ultra" then
        if self.currentProfile == "potato" then
            self:ApplyPerformanceProfile("low")
        elseif self.currentProfile == "low" then
            self:ApplyPerformanceProfile("medium")
        elseif self.currentProfile == "medium" then
            self:ApplyPerformanceProfile("high")
        elseif self.currentProfile == "high" then
            self:ApplyPerformanceProfile("ultra")
        end
        print("⚡ Performance profile upgraded due to good FPS:", metrics.fps)
    end
    
    -- Memory optimization
    if metrics.memory > self.settings.memoryLimit then
        self:OptimizeMemoryUsage()
    end
    
    -- Object count optimization
    if metrics.activeObjects > self.settings.maxActiveVehicles + self.settings.maxActiveBuildings then
        self:OptimizeObjectCount()
    end
end

-- Apply performance profile
function PerformanceManager:ApplyPerformanceProfile(profileName)
    local profile = self.performanceProfiles[profileName]
    if not profile then
        warn("Performance profile not found:", profileName)
        return
    end
    
    -- Update settings
    self.settings.maxActiveVehicles = profile.maxActiveVehicles
    self.settings.maxActiveBuildings = profile.maxActiveBuildings
    self.settings.maxParticleEffects = profile.maxParticleEffects
    self.performanceMetrics.renderDistance = profile.renderDistance
    
    -- Apply LOD settings
    if profile.lodEnabled then
        self:EnableLOD()
    else
        self:DisableLOD()
    end
    
    -- Apply graphics settings
    self:ApplyGraphicsSettings(profile)
    
    self.currentProfile = profileName
    print("⚡ Applied performance profile:", profileName)
end

-- Apply graphics settings
function PerformanceManager:ApplyGraphicsSettings(profile)
    local Lighting = game:GetService("Lighting")
    
    -- Shadow quality
    if profile.shadowQuality == "High" then
        Lighting.ShadowSoftness = 1
        Lighting.Technology = Enum.Technology.Future
    elseif profile.shadowQuality == "Medium" then
        Lighting.ShadowSoftness = 0.5
        Lighting.Technology = Enum.Technology.ShadowMap
    elseif profile.shadowQuality == "Low" then
        Lighting.ShadowSoftness = 0.2
        Lighting.Technology = Enum.Technology.Legacy
    else -- Off
        Lighting.ShadowSoftness = 0
        Lighting.Technology = Enum.Technology.Legacy
    end
    
    -- Apply texture quality (would need to be implemented per-object)
    -- This is a simplified representation
    print("🎨 Graphics settings applied:", profile.shadowQuality, "shadows,", profile.textureQuality, "textures")
end

-- Initialize LOD system
function PerformanceManager:InitializeLODSystem()
    if not self.lodSystem.enabled then return end
    
    spawn(function()
        while self.lodSystem.enabled do
            self:UpdateLOD()
            wait(self.lodSystem.updateInterval)
        end
    end)
    
    print("🔍 LOD system initialized")
end

-- Update LOD for all managed objects
function PerformanceManager:UpdateLOD()
    if not self.lodSystem.enabled then return end
    
    local players = Players:GetPlayers()
    if #players == 0 then return end
    
    -- Use first player as reference (in multiplayer, could use closest player)
    local referencePlayer = players[1]
    if not referencePlayer.Character or not referencePlayer.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local playerPosition = referencePlayer.Character.HumanoidRootPart.Position
    
    for objectId, objectData in pairs(self.managedObjects) do
        if objectData.object and objectData.object.Parent then
            local distance = (objectData.object.Position - playerPosition).Magnitude
            local newLOD = self:CalculateLODLevel(distance)
            
            if newLOD ~= objectData.currentLOD then
                self:ApplyLOD(objectData, newLOD)
                objectData.currentLOD = newLOD
            end
        else
            -- Object was destroyed, remove from management
            self.managedObjects[objectId] = nil
        end
    end
end

-- Calculate LOD level based on distance
function PerformanceManager:CalculateLODLevel(distance)
    local levels = self.lodSystem.lodLevels
    
    if distance <= levels.high.distance then
        return "high"
    elseif distance <= levels.medium.distance then
        return "medium"
    elseif distance <= levels.low.distance then
        return "low"
    else
        return "culled"
    end
end

-- Apply LOD to object
function PerformanceManager:ApplyLOD(objectData, lodLevel)
    local object = objectData.object
    local levels = self.lodSystem.lodLevels
    local quality = levels[lodLevel].quality
    
    if lodLevel == "culled" then
        -- Hide object completely
        object.Transparency = 1
        if object:FindFirstChild("ParticleEmitter") then
            object.ParticleEmitter.Enabled = false
        end
        
        -- Store in culled objects for potential restoration
        self.culledObjects[object] = true
    else
        -- Show object with appropriate quality
        object.Transparency = math.max(0, 1 - quality)
        
        -- Adjust particle effects
        if object:FindFirstChild("ParticleEmitter") then
            local emitter = object.ParticleEmitter
            emitter.Enabled = quality > 0.3
            emitter.Rate = emitter.Rate * quality
        end
        
        -- Remove from culled objects
        self.culledObjects[object] = nil
    end
    
    -- Adjust mesh detail (simplified)
    if object:FindFirstChild("Mesh") then
        local mesh = object.Mesh
        if mesh:IsA("SpecialMesh") then
            mesh.Scale = mesh.Scale * Vector3.new(quality, quality, quality)
        end
    end
end

-- Register object for management
function PerformanceManager:RegisterObject(object, objectType)
    if not object then return end
    
    local objectId = HttpService:GenerateGUID(false)
    self.managedObjects[objectId] = {
        object = object,
        type = objectType,
        currentLOD = "high",
        registrationTime = tick()
    }
    
    return objectId
end

-- Unregister object
function PerformanceManager:UnregisterObject(objectId)
    if self.managedObjects[objectId] then
        local objectData = self.managedObjects[objectId]
        if objectData.object then
            self.culledObjects[objectData.object] = nil
        end
        self.managedObjects[objectId] = nil
    end
end

-- Initialize object pooling
function PerformanceManager:InitializeObjectPooling()
    if not self.settings.enableObjectPooling then return end
    
    -- Create pools for common objects
    self.objectPools = {
        vehicles = {},
        buildings = {},
        effects = {},
        sounds = {}
    }
    
    print("🔄 Object pooling initialized")
end

-- Get object from pool
function PerformanceManager:GetPooledObject(poolName, objectType)
    if not self.objectPools[poolName] then
        self.objectPools[poolName] = {}
    end
    
    local pool = self.objectPools[poolName]
    
    -- Find available object in pool
    for i, obj in pairs(pool) do
        if obj and obj.Parent == nil then
            -- Reuse existing object
            table.remove(pool, i)
            return obj
        end
    end
    
    -- No available object, create new one
    return nil -- Caller should create new object
end

-- Return object to pool
function PerformanceManager:ReturnToPool(poolName, object)
    if not object or not self.objectPools[poolName] then return end
    
    -- Clean up object for reuse
    object.Parent = nil
    
    -- Reset common properties
    if object:IsA("Part") then
        object.Position = Vector3.new(0, 0, 0)
        object.Rotation = Vector3.new(0, 0, 0)
        object.Velocity = Vector3.new(0, 0, 0)
        object.AngularVelocity = Vector3.new(0, 0, 0)
    end
    
    -- Add to pool
    table.insert(self.objectPools[poolName], object)
    
    -- Limit pool size
    local maxPoolSize = 50
    if #self.objectPools[poolName] > maxPoolSize then
        local oldestObject = table.remove(self.objectPools[poolName], 1)
        if oldestObject then
            oldestObject:Destroy()
        end
    end
end

-- Optimize memory usage
function PerformanceManager:OptimizeMemoryUsage()
    -- Clear unused objects from pools
    for poolName, pool in pairs(self.objectPools) do
        local toRemove = {}
        for i, obj in pairs(pool) do
            if not obj or not obj.Parent then
                table.insert(toRemove, i)
                if obj then
                    obj:Destroy()
                end
            end
        end
        
        for i = #toRemove, 1, -1 do
            table.remove(pool, toRemove[i])
        end
    end
    
    -- Force garbage collection
    game:GetService("RunService").Heartbeat:Wait()
    
    print("🧹 Memory optimization completed")
end

-- Optimize object count
function PerformanceManager:OptimizeObjectCount()
    -- Remove oldest managed objects if over limit
    local objectsByAge = {}
    for objectId, objectData in pairs(self.managedObjects) do
        table.insert(objectsByAge, {id = objectId, data = objectData})
    end
    
    -- Sort by registration time (oldest first)
    table.sort(objectsByAge, function(a, b)
        return a.data.registrationTime < b.data.registrationTime
    end)
    
    -- Remove oldest objects
    local maxObjects = self.settings.maxActiveVehicles + self.settings.maxActiveBuildings
    local toRemove = math.max(0, #objectsByAge - maxObjects)
    
    for i = 1, toRemove do
        local objectData = objectsByAge[i]
        if objectData.data.object then
            self:ReturnToPool(objectData.data.type .. "s", objectData.data.object)
        end
        self.managedObjects[objectData.id] = nil
    end
    
    if toRemove > 0 then
        print("🗑️ Removed", toRemove, "objects to optimize count")
    end
end

-- Enable LOD system
function PerformanceManager:EnableLOD()
    self.lodSystem.enabled = true
    self:InitializeLODSystem()
end

-- Disable LOD system
function PerformanceManager:DisableLOD()
    self.lodSystem.enabled = false
    
    -- Restore all culled objects
    for object in pairs(self.culledObjects) do
        if object and object.Parent then
            object.Transparency = 0
            if object:FindFirstChild("ParticleEmitter") then
                object.ParticleEmitter.Enabled = true
            end
        end
    end
    self.culledObjects = {}
end

-- Get performance statistics
function PerformanceManager:GetPerformanceStats()
    return {
        fps = self.performanceMetrics.fps,
        memory = self.performanceMetrics.memory,
        activeObjects = self.performanceMetrics.activeObjects,
        serverLoad = self.performanceMetrics.serverLoad,
        currentProfile = self.currentProfile,
        lodEnabled = self.lodSystem.enabled,
        culledObjects = 0,
        pooledObjects = 0
    }
end

-- Set performance profile manually
function PerformanceManager:SetPerformanceProfile(profileName)
    if self.performanceProfiles[profileName] then
        self:ApplyPerformanceProfile(profileName)
        return true
    else
        warn("Invalid performance profile:", profileName)
        return false
    end
end

-- Update performance manager
function PerformanceManager:Update(deltaTime)
    -- Update LOD system
    if self.lodSystem.enabled and tick() - self.lodSystem.lastUpdate > self.lodSystem.updateInterval then
        self:UpdateLOD()
        self.lodSystem.lastUpdate = tick()
    end
end

return PerformanceManager
