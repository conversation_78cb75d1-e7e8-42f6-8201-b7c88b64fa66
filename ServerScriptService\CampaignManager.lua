-- ServerScriptService/CampaignManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Kampagnen-System mit Missionen, Technologie-Baum und Achievements

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local CampaignManager = {}
CampaignManager.__index = CampaignManager

function CampaignManager.new()
    local self = setmetatable({}, CampaignManager)
    
    -- Player campaign data
    self.playerCampaigns = {}
    
    -- Available campaigns
    self.campaigns = {
        tutorial = {
            id = "tutorial",
            name = "Transport-Pionier",
            description = "Lerne die Grundlagen des Transport-Geschäfts",
            difficulty = "Einfach",
            startYear = 1850,
            duration = 50, -- years
            startingMoney = 500000,
            objectives = {
                {
                    id = "first_route",
                    name = "Erste Route",
                    description = "Erstelle deine erste Transport-Route",
                    type = "create_route",
                    target = 1,
                    reward = {money = 50000, experience = 100}
                },
                {
                    id = "profitable_month",
                    name = "<PERSON>rst<PERSON>",
                    description = "Erziele einen monatlichen Gewinn von 10.000 €",
                    type = "monthly_profit",
                    target = 10000,
                    reward = {money = 25000, experience = 150}
                },
                {
                    id = "buy_vehicles",
                    name = "Fuhrpark erweitern",
                    description = "Kaufe 5 Fahrzeuge",
                    type = "buy_vehicles",
                    target = 5,
                    reward = {money = 100000, experience = 200}
                }
            }
        },
        industrial_revolution = {
            id = "industrial_revolution",
            name = "Industrielle Revolution",
            description = "Baue ein Transport-Imperium in der Industrialisierung auf",
            difficulty = "Mittel",
            startYear = 1850,
            duration = 100,
            startingMoney = 1000000,
            objectives = {
                {
                    id = "connect_cities",
                    name = "Städte verbinden",
                    description = "Verbinde 10 Städte mit deinem Transport-Netzwerk",
                    type = "connect_cities",
                    target = 10,
                    reward = {money = 200000, experience = 500}
                },
                {
                    id = "transport_coal",
                    name = "Kohle-Transport",
                    description = "Transportiere 10.000 Tonnen Kohle",
                    type = "transport_cargo",
                    cargoType = "coal",
                    target = 10000,
                    reward = {money = 150000, experience = 300}
                },
                {
                    id = "railway_network",
                    name = "Eisenbahn-Netzwerk",
                    description = "Baue 100 km Eisenbahnstrecke",
                    type = "build_infrastructure",
                    infrastructureType = "railway",
                    target = 100000, -- meters
                    reward = {money = 500000, experience = 800}
                }
            }
        },
        modern_transport = {
            id = "modern_transport",
            name = "Moderne Mobilität",
            description = "Führe dein Unternehmen ins 21. Jahrhundert",
            difficulty = "Schwer",
            startYear = 1950,
            duration = 70,
            startingMoney = 5000000,
            objectives = {
                {
                    id = "electric_trains",
                    name = "Elektrifizierung",
                    description = "Elektrifiziere 50 km Eisenbahnstrecke",
                    type = "electrify_railway",
                    target = 50000,
                    reward = {money = 1000000, experience = 1000}
                },
                {
                    id = "passenger_service",
                    name = "Passagier-Service",
                    description = "Transportiere 1 Million Passagiere",
                    type = "transport_passengers",
                    target = 1000000,
                    reward = {money = 2000000, experience = 1500}
                }
            }
        }
    }
    
    -- Technology tree
    self.technologies = {
        -- Transport Technologies
        steam_engine = {
            id = "steam_engine",
            name = "Dampfmaschine",
            description = "Ermöglicht Dampflokomotiven",
            category = "transport",
            cost = 50000,
            year = 1850,
            prerequisites = {},
            unlocks = {"steam_locomotive", "steam_ship"}
        },
        electric_motor = {
            id = "electric_motor",
            name = "Elektromotor",
            description = "Ermöglicht elektrische Fahrzeuge",
            category = "transport",
            cost = 200000,
            year = 1880,
            prerequisites = {"steam_engine"},
            unlocks = {"electric_train", "tram"}
        },
        diesel_engine = {
            id = "diesel_engine",
            name = "Dieselmotor",
            description = "Ermöglicht Dieselfahrzeuge",
            category = "transport",
            cost = 300000,
            year = 1900,
            prerequisites = {"steam_engine"},
            unlocks = {"diesel_locomotive", "truck", "bus"}
        },
        
        -- Infrastructure Technologies
        steel_construction = {
            id = "steel_construction",
            name = "Stahlbau",
            description = "Ermöglicht stärkere Brücken und Tunnel",
            category = "infrastructure",
            cost = 100000,
            year = 1860,
            prerequisites = {},
            unlocks = {"steel_bridge", "long_tunnel"}
        },
        concrete_construction = {
            id = "concrete_construction",
            name = "Betonbau",
            description = "Ermöglicht moderne Infrastruktur",
            category = "infrastructure",
            cost = 250000,
            year = 1920,
            prerequisites = {"steel_construction"},
            unlocks = {"concrete_bridge", "highway", "airport"}
        },
        
        -- Economic Technologies
        banking = {
            id = "banking",
            name = "Bankwesen",
            description = "Verbessert Kredit-Konditionen",
            category = "economy",
            cost = 75000,
            year = 1870,
            prerequisites = {},
            unlocks = {"better_loans", "investment_options"}
        },
        logistics = {
            id = "logistics",
            name = "Logistik",
            description = "Verbessert Transport-Effizienz",
            category = "economy",
            cost = 150000,
            year = 1950,
            prerequisites = {"banking"},
            unlocks = {"container_transport", "just_in_time"}
        }
    }
    
    -- Achievement system
    self.achievements = {
        first_million = {
            id = "first_million",
            name = "Millionär",
            description = "Erreiche 1 Million € Bargeld",
            icon = "💰",
            type = "cash",
            target = 1000000,
            reward = {experience = 500}
        },
        transport_tycoon = {
            id = "transport_tycoon",
            name = "Transport-Tycoon",
            description = "Erziele 10 Millionen € Jahresumsatz",
            icon = "🏆",
            type = "yearly_revenue",
            target = ********,
            reward = {experience = 2000}
        },
        speed_demon = {
            id = "speed_demon",
            name = "Geschwindigkeits-Dämon",
            description = "Kaufe ein Fahrzeug mit über 200 km/h",
            icon = "🚄",
            type = "vehicle_speed",
            target = 200,
            reward = {experience = 300}
        },
        network_builder = {
            id = "network_builder",
            name = "Netzwerk-Baumeister",
            description = "Baue 500 km Infrastruktur",
            icon = "🛤️",
            type = "infrastructure_length",
            target = 500000,
            reward = {experience = 1000}
        },
        passenger_king = {
            id = "passenger_king",
            name = "Passagier-König",
            description = "Transportiere 10 Millionen Passagiere",
            icon = "👥",
            type = "passengers_transported",
            target = ********,
            reward = {experience = 1500}
        }
    }
    
    return self
end

-- Initialize player campaign
function CampaignManager:InitializePlayer(playerId)
    self.playerCampaigns[playerId] = {
        currentCampaign = nil,
        completedCampaigns = {},
        
        -- Progress tracking
        objectives = {},
        
        -- Technology progress
        researchedTechnologies = {},
        availableTechnologies = self:GetAvailableTechnologies({}),
        researchPoints = 0,
        
        -- Achievements
        unlockedAchievements = {},
        achievementProgress = {},
        
        -- Experience system
        experience = 0,
        level = 1,
        
        -- Statistics for objectives
        stats = {
            routesCreated = 0,
            vehiclesBought = 0,
            citiesConnected = 0,
            cargoTransported = {},
            passengersTransported = 0,
            infrastructureBuilt = {},
            monthlyProfits = {},
            maxCash = 0,
            yearlyRevenues = {}
        }
    }
end

-- Start campaign
function CampaignManager:StartCampaign(playerId, campaignId)
    local campaign = self.campaigns[campaignId]
    if not campaign then
        return false, "Kampagne nicht gefunden"
    end
    
    local playerData = self.playerCampaigns[playerId]
    if not playerData then
        self:InitializePlayer(playerId)
        playerData = self.playerCampaigns[playerId]
    end
    
    -- Set current campaign
    playerData.currentCampaign = campaignId
    
    -- Initialize objectives
    playerData.objectives = {}
    for _, objective in pairs(campaign.objectives) do
        playerData.objectives[objective.id] = {
            id = objective.id,
            completed = false,
            progress = 0,
            target = objective.target
        }
    end
    
    -- Reset stats for new campaign
    playerData.stats = {
        routesCreated = 0,
        vehiclesBought = 0,
        citiesConnected = 0,
        cargoTransported = {},
        passengersTransported = 0,
        infrastructureBuilt = {},
        monthlyProfits = {},
        maxCash = 0,
        yearlyRevenues = {}
    }
    
    return true, "Kampagne gestartet: " .. campaign.name
end

-- Update player progress
function CampaignManager:UpdateProgress(playerId, eventType, data)
    local playerData = self.playerCampaigns[playerId]
    if not playerData or not playerData.currentCampaign then return end
    
    -- Update statistics
    self:UpdateStatistics(playerId, eventType, data)
    
    -- Check objectives
    self:CheckObjectives(playerId)
    
    -- Check achievements
    self:CheckAchievements(playerId)
end

-- Update statistics
function CampaignManager:UpdateStatistics(playerId, eventType, data)
    local playerData = self.playerCampaigns[playerId]
    local stats = playerData.stats
    
    if eventType == "route_created" then
        stats.routesCreated = stats.routesCreated + 1
    elseif eventType == "vehicle_bought" then
        stats.vehiclesBought = stats.vehiclesBought + 1
    elseif eventType == "city_connected" then
        stats.citiesConnected = stats.citiesConnected + 1
    elseif eventType == "cargo_transported" then
        local cargoType = data.cargoType or "unknown"
        local amount = data.amount or 0
        if not stats.cargoTransported[cargoType] then
            stats.cargoTransported[cargoType] = 0
        end
        stats.cargoTransported[cargoType] = stats.cargoTransported[cargoType] + amount
    elseif eventType == "passengers_transported" then
        stats.passengersTransported = stats.passengersTransported + (data.amount or 0)
    elseif eventType == "infrastructure_built" then
        local infraType = data.infrastructureType or "unknown"
        local length = data.length or 0
        if not stats.infrastructureBuilt[infraType] then
            stats.infrastructureBuilt[infraType] = 0
        end
        stats.infrastructureBuilt[infraType] = stats.infrastructureBuilt[infraType] + length
    elseif eventType == "monthly_profit" then
        table.insert(stats.monthlyProfits, data.profit or 0)
    elseif eventType == "cash_update" then
        stats.maxCash = math.max(stats.maxCash, data.cash or 0)
    elseif eventType == "yearly_revenue" then
        table.insert(stats.yearlyRevenues, data.revenue or 0)
    end
end

-- Check objectives
function CampaignManager:CheckObjectives(playerId)
    local playerData = self.playerCampaigns[playerId]
    local campaign = self.campaigns[playerData.currentCampaign]
    if not campaign then return end
    
    local stats = playerData.stats
    local completedObjectives = {}
    
    for _, objective in pairs(campaign.objectives) do
        local progress = playerData.objectives[objective.id]
        if progress and not progress.completed then
            local currentProgress = 0
            
            -- Calculate progress based on objective type
            if objective.type == "create_route" then
                currentProgress = stats.routesCreated
            elseif objective.type == "monthly_profit" then
                currentProgress = #stats.monthlyProfits > 0 and stats.monthlyProfits[#stats.monthlyProfits] or 0
            elseif objective.type == "buy_vehicles" then
                currentProgress = stats.vehiclesBought
            elseif objective.type == "connect_cities" then
                currentProgress = stats.citiesConnected
            elseif objective.type == "transport_cargo" then
                currentProgress = stats.cargoTransported[objective.cargoType] or 0
            elseif objective.type == "build_infrastructure" then
                currentProgress = stats.infrastructureBuilt[objective.infrastructureType] or 0
            elseif objective.type == "transport_passengers" then
                currentProgress = stats.passengersTransported
            end
            
            progress.progress = currentProgress
            
            -- Check if objective is completed
            if currentProgress >= objective.target then
                progress.completed = true
                table.insert(completedObjectives, objective)
                
                -- Award rewards
                self:AwardObjectiveReward(playerId, objective.reward)
            end
        end
    end
    
    return completedObjectives
end

-- Award objective reward
function CampaignManager:AwardObjectiveReward(playerId, reward)
    local playerData = self.playerCampaigns[playerId]
    
    if reward.money then
        -- Award money (would need to integrate with finance system)
        print("💰 Player", playerId, "earned", reward.money, "from objective")
    end
    
    if reward.experience then
        playerData.experience = playerData.experience + reward.experience
        
        -- Check for level up
        local newLevel = math.floor(playerData.experience / 1000) + 1
        if newLevel > playerData.level then
            playerData.level = newLevel
            print("🎉 Player", playerId, "leveled up to level", newLevel)
        end
    end
end

-- Check achievements
function CampaignManager:CheckAchievements(playerId)
    local playerData = self.playerCampaigns[playerId]
    local stats = playerData.stats
    local newAchievements = {}
    
    for achievementId, achievement in pairs(self.achievements) do
        if not playerData.unlockedAchievements[achievementId] then
            local currentProgress = 0
            
            if achievement.type == "cash" then
                currentProgress = stats.maxCash
            elseif achievement.type == "yearly_revenue" then
                currentProgress = #stats.yearlyRevenues > 0 and stats.yearlyRevenues[#stats.yearlyRevenues] or 0
            elseif achievement.type == "vehicle_speed" then
                -- Would need vehicle data integration
                currentProgress = 0
            elseif achievement.type == "infrastructure_length" then
                local totalLength = 0
                for _, length in pairs(stats.infrastructureBuilt) do
                    totalLength = totalLength + length
                end
                currentProgress = totalLength
            elseif achievement.type == "passengers_transported" then
                currentProgress = stats.passengersTransported
            end
            
            playerData.achievementProgress[achievementId] = currentProgress
            
            if currentProgress >= achievement.target then
                playerData.unlockedAchievements[achievementId] = true
                table.insert(newAchievements, achievement)
                
                -- Award achievement reward
                if achievement.reward and achievement.reward.experience then
                    playerData.experience = playerData.experience + achievement.reward.experience
                end
            end
        end
    end
    
    return newAchievements
end

-- Research technology
function CampaignManager:ResearchTechnology(playerId, technologyId)
    local playerData = self.playerCampaigns[playerId]
    if not playerData then return false, "Spieler nicht gefunden" end
    
    local technology = self.technologies[technologyId]
    if not technology then return false, "Technologie nicht gefunden" end
    
    -- Check if already researched
    if playerData.researchedTechnologies[technologyId] then
        return false, "Technologie bereits erforscht"
    end
    
    -- Check prerequisites
    for _, prerequisite in pairs(technology.prerequisites) do
        if not playerData.researchedTechnologies[prerequisite] then
            return false, "Voraussetzungen nicht erfüllt"
        end
    end
    
    -- Check research points (simplified - could use money instead)
    if playerData.researchPoints < technology.cost then
        return false, "Nicht genügend Forschungspunkte"
    end
    
    -- Research technology
    playerData.researchedTechnologies[technologyId] = true
    playerData.researchPoints = playerData.researchPoints - technology.cost
    
    -- Update available technologies
    playerData.availableTechnologies = self:GetAvailableTechnologies(playerData.researchedTechnologies)
    
    return true, "Technologie erforscht: " .. technology.name
end

-- Get available technologies
function CampaignManager:GetAvailableTechnologies(researchedTechnologies)
    local available = {}
    
    for techId, technology in pairs(self.technologies) do
        if not researchedTechnologies[techId] then
            local canResearch = true
            
            -- Check prerequisites
            for _, prerequisite in pairs(technology.prerequisites) do
                if not researchedTechnologies[prerequisite] then
                    canResearch = false
                    break
                end
            end
            
            if canResearch then
                available[techId] = technology
            end
        end
    end
    
    return available
end

-- Get campaign progress
function CampaignManager:GetCampaignProgress(playerId)
    local playerData = self.playerCampaigns[playerId]
    if not playerData then return nil end
    
    local campaign = playerData.currentCampaign and self.campaigns[playerData.currentCampaign] or nil
    
    return {
        currentCampaign = campaign,
        objectives = playerData.objectives,
        experience = playerData.experience,
        level = playerData.level,
        researchedTechnologies = playerData.researchedTechnologies,
        availableTechnologies = playerData.availableTechnologies,
        unlockedAchievements = playerData.unlockedAchievements,
        achievementProgress = playerData.achievementProgress,
        stats = playerData.stats
    }
end

-- Get available campaigns
function CampaignManager:GetAvailableCampaigns()
    return self.campaigns
end

return CampaignManager
