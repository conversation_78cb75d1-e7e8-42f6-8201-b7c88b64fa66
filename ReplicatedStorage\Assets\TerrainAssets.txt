# Terrain Assets Configuration
# Definition aller 3D-Modelle und Texturen für das Terrain-System

## Terrain-Tiles (Boden-Texturen)

### Basis-Terrain
- Grass: Grünes Gras für Ebenen
  * Textur: rbxasset://textures/terrain/grass.jpg
  * Material: Grass
  * Color: RGB(34, 139, 34)

- Dirt: Erde für Hügel und Berge
  * Textur: rbxasset://textures/terrain/ground.jpg
  * Material: Ground
  * Color: RGB(139, 69, 19)

- Rock: Fels für hohe Berge
  * Textur: rbxasset://textures/terrain/rock.jpg
  * Material: Rock
  * Color: RGB(105, 105, 105)

- Sand: Sand für Wüsten (American Map)
  * Textur: rbxasset://textures/terrain/sand.jpg
  * Material: Sand
  * Color: RGB(238, 203, 173)

- Snow: Schnee für hohe Berge
  * Textur: rbxasset://textures/terrain/snow.jpg
  * Material: Snow
  * Color: RGB(255, 250, 250)

### Wasser
- Water: Wasser für Flüsse und Seen
  * Material: Water
  * Color: RGB(65, 105, 225)
  * Transparency: 0.3
  * Reflectance: 0.1

- DeepWater: Tiefes Wasser für Ozeane
  * Material: Water
  * Color: RGB(25, 25, 112)
  * Transparency: 0.5
  * Reflectance: 0.2

## Vegetation (Deko-Objekte)

### Bäume
- OakTree: Eiche für europäische Karten
  * Model: Einfacher Baum mit braunem Stamm und grüner Krone
  * Size: 8-12 Studs hoch
  * Collision: Nur Stamm

- PineTree: Kiefer für bergige Gebiete
  * Model: Spitzer Nadelbaum
  * Size: 10-15 Studs hoch
  * Color: Dunkelgrün

- PalmTree: Palme für asiatische Inseln
  * Model: Palme mit gebogenen Wedeln
  * Size: 6-10 Studs hoch
  * Color: Grün mit braunem Stamm

- Cactus: Kaktus für amerikanische Wüsten
  * Model: Großer Säulenkaktus
  * Size: 5-8 Studs hoch
  * Color: Dunkelgrün

### Büsche und Gras
- Bush: Kleiner Busch
  * Model: Runder grüner Busch
  * Size: 2-3 Studs
  * Spawn-Rate: Hoch auf Grasland

- TallGrass: Hohes Gras
  * Model: Grasbüschel
  * Size: 1-2 Studs
  * Color: Verschiedene Grüntöne

## Felsen und Steine

### Große Felsen
- Boulder: Großer Felsbrocken
  * Model: Unregelmäßiger Stein
  * Size: 4-8 Studs
  * Material: Rock
  * Color: Grau

- Canyon_Rock: Canyon-Felsen für amerikanische Karten
  * Model: Geschichteter Sandstein
  * Size: 10-20 Studs
  * Color: Rotbraun

### Kleine Steine
- SmallRock: Kleine Steine
  * Model: Kleine Felsbrocken
  * Size: 0.5-1.5 Studs
  * Spawn-Rate: Mittel

## Spezial-Terrain

### Europäisch
- Forest_Floor: Waldboden mit Laub
  * Textur: Braun mit Blättern
  * Spawn unter Bäumen

- River_Bank: Flussufer
  * Textur: Kies und Sand
  * Spawn neben Wasser

### Amerikanisch
- Desert_Rock: Wüstenfelsen
  * Model: Rote Sandsteinformationen
  * Size: Variabel
  * Color: Rotbraun

- Mesa: Tafelberg
  * Model: Flacher Berg mit steilen Seiten
  * Size: 20-40 Studs hoch
  * Material: Rock

### Asiatisch
- Bamboo: Bambus für asiatische Karten
  * Model: Hohe Bambusrohre
  * Size: 8-12 Studs
  * Color: Grün

- Coral: Korallen für Unterwasser
  * Model: Bunte Korallen
  * Size: 1-3 Studs
  * Spawn nur unter Wasser

## Terrain-Generation Regeln

### Höhen-basierte Spawns
- 0-2 Studs: Gras, Wasser
- 2-8 Studs: Gras, Bäume, Büsche
- 8-15 Studs: Dirt, Felsen, weniger Vegetation
- 15+ Studs: Rock, Snow (bei hohen Bergen)

### Biom-spezifische Spawns
- European: 60% Bäume, 30% Büsche, 10% Felsen
- American: 20% Bäume, 40% Kakteen, 40% Felsen
- Asian: 40% Palmen, 30% Bambus, 30% Korallen (Wasser)

### Dichte-Einstellungen
- Vegetation_Density: 0.3 (30% der Tiles haben Vegetation)
- Rock_Density: 0.1 (10% der Tiles haben Felsen)
- Water_Vegetation_Density: 0.2 (20% der Wasser-Tiles haben Unterwasser-Pflanzen)

## Performance-Optimierung

### LOD (Level of Detail)
- Nahe Objekte (< 100 Studs): Volle Details
- Mittlere Distanz (100-300 Studs): Reduzierte Details
- Ferne Objekte (> 300 Studs): Nur Silhouetten

### Culling
- Frustum Culling: Objekte außerhalb der Kamera werden nicht gerendert
- Occlusion Culling: Verdeckte Objekte werden nicht gerendert
- Distance Culling: Sehr weit entfernte Objekte werden ausgeblendet
