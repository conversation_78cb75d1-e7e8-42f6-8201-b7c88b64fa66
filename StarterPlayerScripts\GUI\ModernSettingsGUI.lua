-- StarterPlayerScripts/GUI/ModernSettingsGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Einstellungen-GUI mit Kategorien und Live-Vorschau

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")
local Lighting = game:GetService("Lighting")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local SaveSettingsEvent = Events:WaitForChild("SaveSettingsEvent")
local LoadSettingsFunction = Events:WaitForChild("LoadSettingsFunction")

local ModernSettingsGUI = {}
ModernSettingsGUI.__index = ModernSettingsGUI

-- Konstruktor
function ModernSettingsGUI.new()
    local self = setmetatable({}, ModernSettingsGUI)
    
    self.isOpen = false
    self.isDocked = false
    self.currentCategory = "GRAPHICS" -- GRAPHICS, AUDIO, CONTROLS, GAMEPLAY, ADVANCED
    self.settings = {
        graphics = {
            quality = "High",
            shadows = true,
            particles = true,
            antialiasing = true,
            vsync = false
        },
        audio = {
            masterVolume = 0.8,
            musicVolume = 0.6,
            effectsVolume = 0.7,
            uiSounds = true
        },
        controls = {
            mouseSensitivity = 0.5,
            invertY = false,
            keyBindings = {}
        },
        gameplay = {
            autosave = true,
            pauseOnFocusLoss = true,
            showTutorials = true,
            difficulty = "Normal"
        }
    }
    
    return self
end

-- GUI erstellen
function ModernSettingsGUI:CreateGUI()
    if self.screenGui then return end
    
    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernSettingsGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Hauptfenster
    local mainWindow = Instance.new("Frame")
    mainWindow.Size = UDim2.new(0, 1200, 0, 800)
    mainWindow.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainWindow.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    mainWindow.BorderSizePixel = 0
    mainWindow.Visible = false
    mainWindow.Parent = self.screenGui
    
    local windowCorner = Instance.new("UICorner")
    windowCorner.CornerRadius = UDim.new(0, 15)
    windowCorner.Parent = mainWindow
    
    -- Fenster-Gradient
    local windowGradient = Instance.new("UIGradient")
    windowGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(15, 20, 25)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(5, 10, 15))
    }
    windowGradient.Rotation = 45
    windowGradient.Parent = mainWindow
    
    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 60)
    titleBar.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainWindow
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 15)
    titleCorner.Parent = titleBar
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -200, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "⚙️ EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Dock-Button
    local dockButton = Instance.new("TextButton")
    dockButton.Size = UDim2.new(0, 45, 0, 45)
    dockButton.Position = UDim2.new(1, -140, 0, 7.5)
    dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    dockButton.Text = "📌"
    dockButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    dockButton.TextScaled = true
    dockButton.Font = Enum.Font.SourceSans
    dockButton.BorderSizePixel = 0
    dockButton.Parent = titleBar
    
    local dockCorner = Instance.new("UICorner")
    dockCorner.CornerRadius = UDim.new(0, 10)
    dockCorner.Parent = dockButton
    
    -- Speichern-Button
    local saveButton = Instance.new("TextButton")
    saveButton.Size = UDim2.new(0, 80, 0, 45)
    saveButton.Position = UDim2.new(1, -135, 0, 7.5)
    saveButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
    saveButton.Text = "💾 SPEICHERN"
    saveButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    saveButton.TextScaled = true
    saveButton.Font = Enum.Font.SourceSansBold
    saveButton.BorderSizePixel = 0
    saveButton.Parent = titleBar
    
    local saveCorner = Instance.new("UICorner")
    saveCorner.CornerRadius = UDim.new(0, 10)
    saveCorner.Parent = saveButton
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 45, 0, 45)
    closeButton.Position = UDim2.new(1, -50, 0, 7.5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 10)
    closeCorner.Parent = closeButton
    
    -- Kategorie-Sidebar
    local sidebar = Instance.new("Frame")
    sidebar.Size = UDim2.new(0, 250, 1, -70)
    sidebar.Position = UDim2.new(0, 10, 0, 70)
    sidebar.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    sidebar.BorderSizePixel = 0
    sidebar.Parent = mainWindow
    
    local sidebarCorner = Instance.new("UICorner")
    sidebarCorner.CornerRadius = UDim.new(0, 12)
    sidebarCorner.Parent = sidebar
    
    -- Kategorie-Buttons Container
    local categoryContainer = Instance.new("Frame")
    categoryContainer.Size = UDim2.new(1, -20, 1, -20)
    categoryContainer.Position = UDim2.new(0, 10, 0, 10)
    categoryContainer.BackgroundTransparency = 1
    categoryContainer.Parent = sidebar
    
    local categoryLayout = Instance.new("UIListLayout")
    categoryLayout.SortOrder = Enum.SortOrder.LayoutOrder
    categoryLayout.Padding = UDim.new(0, 10)
    categoryLayout.Parent = categoryContainer
    
    -- Kategorien definieren
    local categories = {
        {text = "🎨 GRAFIK", id = "GRAPHICS", color = Color3.fromRGB(100, 150, 255), order = 1},
        {text = "🔊 AUDIO", id = "AUDIO", color = Color3.fromRGB(255, 150, 100), order = 2},
        {text = "🎮 STEUERUNG", id = "CONTROLS", color = Color3.fromRGB(100, 255, 100), order = 3},
        {text = "⚡ GAMEPLAY", id = "GAMEPLAY", color = Color3.fromRGB(255, 200, 100), order = 4},
        {text = "🔧 ERWEITERT", id = "ADVANCED", color = Color3.fromRGB(150, 255, 150), order = 5}
    }
    
    self.categoryButtons = {}
    
    for _, categoryData in ipairs(categories) do
        local categoryButton = Instance.new("TextButton")
        categoryButton.Size = UDim2.new(1, 0, 0, 50)
        categoryButton.BackgroundColor3 = categoryData.color
        categoryButton.Text = categoryData.text
        categoryButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        categoryButton.TextScaled = true
        categoryButton.Font = Enum.Font.SourceSansBold
        categoryButton.BorderSizePixel = 0
        categoryButton.LayoutOrder = categoryData.order
        categoryButton.Parent = categoryContainer
        
        local categoryButtonCorner = Instance.new("UICorner")
        categoryButtonCorner.CornerRadius = UDim.new(0, 10)
        categoryButtonCorner.Parent = categoryButton
        
        -- Hover-Effekt
        categoryButton.MouseEnter:Connect(function()
            if self.currentCategory ~= categoryData.id then
                TweenService:Create(categoryButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.new(
                        math.min(categoryData.color.R + 0.1, 1),
                        math.min(categoryData.color.G + 0.1, 1),
                        math.min(categoryData.color.B + 0.1, 1)
                    )
                }):Play()
            end
        end)
        
        categoryButton.MouseLeave:Connect(function()
            if self.currentCategory ~= categoryData.id then
                TweenService:Create(categoryButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = categoryData.color
                }):Play()
            end
        end)
        
        -- Click-Handler
        categoryButton.MouseButton1Click:Connect(function()
            self:SwitchCategory(categoryData.id)
        end)
        
        self.categoryButtons[categoryData.id] = {button = categoryButton, color = categoryData.color}
    end
    
    -- Content-Bereich
    self.contentFrame = Instance.new("ScrollingFrame")
    self.contentFrame.Size = UDim2.new(1, -280, 1, -70)
    self.contentFrame.Position = UDim2.new(0, 270, 0, 70)
    self.contentFrame.BackgroundColor3 = Color3.fromRGB(5, 10, 15)
    self.contentFrame.BorderSizePixel = 0
    self.contentFrame.ScrollBarThickness = 8
    self.contentFrame.Parent = mainWindow
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 12)
    contentCorner.Parent = self.contentFrame
    
    local contentLayout = Instance.new("UIListLayout")
    contentLayout.SortOrder = Enum.SortOrder.LayoutOrder
    contentLayout.Padding = UDim.new(0, 15)
    contentLayout.Parent = self.contentFrame
    
    local contentPadding = Instance.new("UIPadding")
    contentPadding.PaddingAll = UDim.new(0, 20)
    contentPadding.Parent = self.contentFrame
    
    -- Referenzen speichern
    self.mainWindow = mainWindow
    self.titleBar = titleBar
    self.dockButton = dockButton
    self.saveButton = saveButton
    self.closeButton = closeButton
    
    -- Event-Handler
    dockButton.MouseButton1Click:Connect(function()
        self:ToggleDock()
    end)
    
    saveButton.MouseButton1Click:Connect(function()
        self:SaveSettings()
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Drag-Funktionalität
    self:SetupDragging()
    
    return self.screenGui
end

-- Kategorie wechseln
function ModernSettingsGUI:SwitchCategory(categoryId)
    if self.currentCategory == categoryId then return end
    
    -- Alten Button deaktivieren
    if self.categoryButtons[self.currentCategory] then
        local oldButton = self.categoryButtons[self.currentCategory].button
        local oldColor = self.categoryButtons[self.currentCategory].color
        TweenService:Create(oldButton, TweenInfo.new(0.2), {
            BackgroundColor3 = oldColor,
            BackgroundTransparency = 0.3
        }):Play()
    end
    
    -- Neuen Button aktivieren
    if self.categoryButtons[categoryId] then
        local newButton = self.categoryButtons[categoryId].button
        TweenService:Create(newButton, TweenInfo.new(0.2), {
            BackgroundTransparency = 0,
            Size = UDim2.new(1, 5, 0, 52)
        }):Play()
    end
    
    self.currentCategory = categoryId
    
    -- Content aktualisieren
    self:UpdateCategoryContent()
end

-- Kategorie-Inhalt aktualisieren
function ModernSettingsGUI:UpdateCategoryContent()
    -- Alten Inhalt löschen
    for _, child in pairs(self.contentFrame:GetChildren()) do
        if not child:IsA("UIListLayout") and not child:IsA("UIPadding") then
            child:Destroy()
        end
    end
    
    -- Neuen Inhalt basierend auf Kategorie erstellen
    if self.currentCategory == "GRAPHICS" then
        self:CreateGraphicsContent()
    elseif self.currentCategory == "AUDIO" then
        self:CreateAudioContent()
    elseif self.currentCategory == "CONTROLS" then
        self:CreateControlsContent()
    elseif self.currentCategory == "GAMEPLAY" then
        self:CreateGameplayContent()
    elseif self.currentCategory == "ADVANCED" then
        self:CreateAdvancedContent()
    end
    
    -- Canvas-Größe aktualisieren
    self.contentFrame.CanvasSize = UDim2.new(0, 0, 0, self.contentFrame.UIListLayout.AbsoluteContentSize.Y + 40)
end

-- Grafik-Einstellungen erstellen
function ModernSettingsGUI:CreateGraphicsContent()
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🎨 GRAFIK-EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.LayoutOrder = 1
    title.Parent = self.contentFrame
    
    -- Qualitäts-Einstellung
    self:CreateDropdownSetting("Grafik-Qualität", {"Low", "Medium", "High", "Ultra"}, self.settings.graphics.quality, 2, function(value)
        self.settings.graphics.quality = value
        self:ApplyGraphicsSettings()
    end)
    
    -- Schatten-Einstellung
    self:CreateToggleSetting("Schatten", self.settings.graphics.shadows, 3, function(value)
        self.settings.graphics.shadows = value
        self:ApplyGraphicsSettings()
    end)
    
    -- Partikel-Einstellung
    self:CreateToggleSetting("Partikel-Effekte", self.settings.graphics.particles, 4, function(value)
        self.settings.graphics.particles = value
        self:ApplyGraphicsSettings()
    end)
    
    -- Anti-Aliasing
    self:CreateToggleSetting("Anti-Aliasing", self.settings.graphics.antialiasing, 5, function(value)
        self.settings.graphics.antialiasing = value
        self:ApplyGraphicsSettings()
    end)
    
    -- V-Sync
    self:CreateToggleSetting("V-Sync", self.settings.graphics.vsync, 6, function(value)
        self.settings.graphics.vsync = value
        self:ApplyGraphicsSettings()
    end)
end

-- Audio-Einstellungen erstellen
function ModernSettingsGUI:CreateAudioContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🔊 AUDIO-EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.LayoutOrder = 1
    title.Parent = self.contentFrame

    -- Master-Lautstärke
    self:CreateSliderSetting("Master-Lautstärke", self.settings.audio.masterVolume, 2, function(value)
        self.settings.audio.masterVolume = value
        SoundService.Volume = value
    end)

    -- Musik-Lautstärke
    self:CreateSliderSetting("Musik-Lautstärke", self.settings.audio.musicVolume, 3, function(value)
        self.settings.audio.musicVolume = value
    end)

    -- Effekt-Lautstärke
    self:CreateSliderSetting("Effekt-Lautstärke", self.settings.audio.effectsVolume, 4, function(value)
        self.settings.audio.effectsVolume = value
    end)

    -- UI-Sounds
    self:CreateToggleSetting("UI-Sounds", self.settings.audio.uiSounds, 5, function(value)
        self.settings.audio.uiSounds = value
    end)
end

-- Steuerungs-Einstellungen erstellen
function ModernSettingsGUI:CreateControlsContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🎮 STEUERUNGS-EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.LayoutOrder = 1
    title.Parent = self.contentFrame

    -- Maus-Empfindlichkeit
    self:CreateSliderSetting("Maus-Empfindlichkeit", self.settings.controls.mouseSensitivity, 2, function(value)
        self.settings.controls.mouseSensitivity = value
    end)

    -- Y-Achse invertieren
    self:CreateToggleSetting("Y-Achse invertieren", self.settings.controls.invertY, 3, function(value)
        self.settings.controls.invertY = value
    end)

    -- Tastenbelegung-Info
    local keyBindingInfo = Instance.new("TextLabel")
    keyBindingInfo.Size = UDim2.new(1, 0, 0, 100)
    keyBindingInfo.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    keyBindingInfo.Text = "🎯 TASTENBELEGUNG\n\nF1-F10: GUI-Fenster\nESC: Hauptmenü\nWASD: Kamera-Bewegung\nMaus: Kamera-Rotation"
    keyBindingInfo.TextColor3 = Color3.fromRGB(180, 180, 180)
    keyBindingInfo.TextScaled = true
    keyBindingInfo.Font = Enum.Font.SourceSans
    keyBindingInfo.BorderSizePixel = 0
    keyBindingInfo.LayoutOrder = 4
    keyBindingInfo.Parent = self.contentFrame

    local keyBindingCorner = Instance.new("UICorner")
    keyBindingCorner.CornerRadius = UDim.new(0, 10)
    keyBindingCorner.Parent = keyBindingInfo
end

-- Gameplay-Einstellungen erstellen
function ModernSettingsGUI:CreateGameplayContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "⚡ GAMEPLAY-EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.LayoutOrder = 1
    title.Parent = self.contentFrame

    -- Auto-Speichern
    self:CreateToggleSetting("Auto-Speichern", self.settings.gameplay.autosave, 2, function(value)
        self.settings.gameplay.autosave = value
    end)

    -- Pause bei Fokusverlust
    self:CreateToggleSetting("Pause bei Fokusverlust", self.settings.gameplay.pauseOnFocusLoss, 3, function(value)
        self.settings.gameplay.pauseOnFocusLoss = value
    end)

    -- Tutorials anzeigen
    self:CreateToggleSetting("Tutorials anzeigen", self.settings.gameplay.showTutorials, 4, function(value)
        self.settings.gameplay.showTutorials = value
    end)

    -- Schwierigkeitsgrad
    self:CreateDropdownSetting("Schwierigkeitsgrad", {"Easy", "Normal", "Hard", "Expert"}, self.settings.gameplay.difficulty, 5, function(value)
        self.settings.gameplay.difficulty = value
    end)
end

-- Erweiterte Einstellungen erstellen
function ModernSettingsGUI:CreateAdvancedContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🔧 ERWEITERTE EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.LayoutOrder = 1
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, 0, 0, 200)
    placeholder.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    placeholder.Text = "🚧 ERWEITERTE OPTIONEN\n\n• Debug-Modus\n• Performance-Monitoring\n• Entwickler-Tools\n• Experimentelle Features\n• Cache-Verwaltung\n\nErweiterte Einstellungen werden hier implementiert."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.BorderSizePixel = 0
    placeholder.LayoutOrder = 2
    placeholder.Parent = self.contentFrame

    local placeholderCorner = Instance.new("UICorner")
    placeholderCorner.CornerRadius = UDim.new(0, 10)
    placeholderCorner.Parent = placeholder
end

-- Toggle-Einstellung erstellen
function ModernSettingsGUI:CreateToggleSetting(name, currentValue, order, callback)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, 0, 0, 60)
    container.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    container.BorderSizePixel = 0
    container.LayoutOrder = order
    container.Parent = self.contentFrame

    local containerCorner = Instance.new("UICorner")
    containerCorner.CornerRadius = UDim.new(0, 10)
    containerCorner.Parent = container

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, -100, 1, 0)
    label.Position = UDim2.new(0, 20, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = container

    local toggle = Instance.new("TextButton")
    toggle.Size = UDim2.new(0, 60, 0, 30)
    toggle.Position = UDim2.new(1, -80, 0.5, -15)
    toggle.BackgroundColor3 = currentValue and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(100, 100, 100)
    toggle.Text = currentValue and "ON" or "OFF"
    toggle.TextColor3 = Color3.fromRGB(255, 255, 255)
    toggle.TextScaled = true
    toggle.Font = Enum.Font.SourceSansBold
    toggle.BorderSizePixel = 0
    toggle.Parent = container

    local toggleCorner = Instance.new("UICorner")
    toggleCorner.CornerRadius = UDim.new(0, 8)
    toggleCorner.Parent = toggle

    toggle.MouseButton1Click:Connect(function()
        currentValue = not currentValue
        toggle.Text = currentValue and "ON" or "OFF"
        toggle.BackgroundColor3 = currentValue and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(100, 100, 100)
        callback(currentValue)
    end)
end

-- Slider-Einstellung erstellen
function ModernSettingsGUI:CreateSliderSetting(name, currentValue, order, callback)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, 0, 0, 80)
    container.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    container.BorderSizePixel = 0
    container.LayoutOrder = order
    container.Parent = self.contentFrame

    local containerCorner = Instance.new("UICorner")
    containerCorner.CornerRadius = UDim.new(0, 10)
    containerCorner.Parent = container

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, -100, 0, 30)
    label.Position = UDim2.new(0, 20, 0, 10)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = container

    local valueLabel = Instance.new("TextLabel")
    valueLabel.Size = UDim2.new(0, 80, 0, 30)
    valueLabel.Position = UDim2.new(1, -100, 0, 10)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = string.format("%.0f%%", currentValue * 100)
    valueLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSansBold
    valueLabel.TextXAlignment = Enum.TextXAlignment.Right
    valueLabel.Parent = container

    local sliderTrack = Instance.new("Frame")
    sliderTrack.Size = UDim2.new(1, -40, 0, 10)
    sliderTrack.Position = UDim2.new(0, 20, 0, 50)
    sliderTrack.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    sliderTrack.BorderSizePixel = 0
    sliderTrack.Parent = container

    local trackCorner = Instance.new("UICorner")
    trackCorner.CornerRadius = UDim.new(0, 5)
    trackCorner.Parent = sliderTrack

    local sliderFill = Instance.new("Frame")
    sliderFill.Size = UDim2.new(currentValue, 0, 1, 0)
    sliderFill.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    sliderFill.BorderSizePixel = 0
    sliderFill.Parent = sliderTrack

    local fillCorner = Instance.new("UICorner")
    fillCorner.CornerRadius = UDim.new(0, 5)
    fillCorner.Parent = sliderFill

    -- Slider-Interaktion (vereinfacht)
    local dragging = false
    sliderTrack.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = true
            local relativeX = (input.Position.X - sliderTrack.AbsolutePosition.X) / sliderTrack.AbsoluteSize.X
            currentValue = math.clamp(relativeX, 0, 1)
            sliderFill.Size = UDim2.new(currentValue, 0, 1, 0)
            valueLabel.Text = string.format("%.0f%%", currentValue * 100)
            callback(currentValue)
        end
    end)
end

-- Dropdown-Einstellung erstellen
function ModernSettingsGUI:CreateDropdownSetting(name, options, currentValue, order, callback)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, 0, 0, 60)
    container.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    container.BorderSizePixel = 0
    container.LayoutOrder = order
    container.Parent = self.contentFrame

    local containerCorner = Instance.new("UICorner")
    containerCorner.CornerRadius = UDim.new(0, 10)
    containerCorner.Parent = container

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, -150, 1, 0)
    label.Position = UDim2.new(0, 20, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = container

    local dropdown = Instance.new("TextButton")
    dropdown.Size = UDim2.new(0, 120, 0, 35)
    dropdown.Position = UDim2.new(1, -140, 0.5, -17.5)
    dropdown.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    dropdown.Text = currentValue .. " ▼"
    dropdown.TextColor3 = Color3.fromRGB(255, 255, 255)
    dropdown.TextScaled = true
    dropdown.Font = Enum.Font.SourceSansBold
    dropdown.BorderSizePixel = 0
    dropdown.Parent = container

    local dropdownCorner = Instance.new("UICorner")
    dropdownCorner.CornerRadius = UDim.new(0, 8)
    dropdownCorner.Parent = dropdown

    -- Vereinfachte Dropdown-Funktionalität (Cycle durch Optionen)
    local currentIndex = 1
    for i, option in ipairs(options) do
        if option == currentValue then
            currentIndex = i
            break
        end
    end

    dropdown.MouseButton1Click:Connect(function()
        currentIndex = currentIndex % #options + 1
        currentValue = options[currentIndex]
        dropdown.Text = currentValue .. " ▼"
        callback(currentValue)
    end)
end

-- Grafik-Einstellungen anwenden
function ModernSettingsGUI:ApplyGraphicsSettings()
    -- Hier würden die Grafik-Einstellungen angewendet
    print("🎨 Grafik-Einstellungen angewendet:", self.settings.graphics)
end

-- Einstellungen speichern
function ModernSettingsGUI:SaveSettings()
    SaveSettingsEvent:FireServer(self.settings)

    -- Feedback
    self.saveButton.Text = "✅ GESPEICHERT"
    self.saveButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)

    wait(1)

    self.saveButton.Text = "💾 SPEICHERN"
    self.saveButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
end

-- Einstellungen laden
function ModernSettingsGUI:LoadSettings()
    local success, data = pcall(function()
        return LoadSettingsFunction:InvokeServer()
    end)

    if success and data then
        self.settings = data
        print("⚙️ Einstellungen geladen")
    else
        warn("❌ Fehler beim Laden der Einstellungen:", data)
    end
end

-- Docking umschalten
function ModernSettingsGUI:ToggleDock()
    self.isDocked = not self.isDocked

    if self.isDocked then
        -- An rechte Seite andocken
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 600, 1, -20),
            Position = UDim2.new(1, -610, 0, 10)
        }):Play()
        self.dockButton.Text = "🔓"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    else
        -- Floating
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 1200, 0, 800),
            Position = UDim2.new(0.5, -600, 0.5, -400)
        }):Play()
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    end
end

-- Dragging Setup
function ModernSettingsGUI:SetupDragging()
    local dragging = false
    local dragStart = nil
    local startPos = nil

    self.titleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 and not self.isDocked then
            dragging = true
            dragStart = input.Position
            startPos = self.mainWindow.Position
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            self.mainWindow.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)

    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
end

-- GUI öffnen
function ModernSettingsGUI:OpenGUI()
    if not self.screenGui then
        self:CreateGUI()
    end

    self.mainWindow.Visible = true
    self.isOpen = true

    -- Smooth fade-in
    self.mainWindow.BackgroundTransparency = 1
    TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    }):Play()

    -- Standard-Kategorie setzen und Einstellungen laden
    self:LoadSettings()
    self:SwitchCategory("GRAPHICS")
end

-- GUI schließen
function ModernSettingsGUI:CloseGUI()
    if self.mainWindow then
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        }):Play()

        wait(0.3)
        self.mainWindow.Visible = false
        self.isOpen = false
    end
end

-- Singleton-Instanz
local ModernSettingsGUIInstance = ModernSettingsGUI.new()

return ModernSettingsGUIInstance
