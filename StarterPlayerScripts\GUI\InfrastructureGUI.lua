-- StarterPlayerScripts/GUI/InfrastructureGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Infrastruktur-Bau GUI für Straßen, Sc<PERSON><PERSON>n, Wasserwege

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local Mouse = game.Players.LocalPlayer:GetMouse()

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local BuildInfrastructureEvent = Events:WaitForChild("BuildInfrastructureEvent")
local PlaceSignalEvent = Events:WaitForChild("PlaceSignalEvent")
local GetInfrastructureDataFunction = Events:WaitForChild("GetInfrastructureDataFunction")

local InfrastructureGUI = {}
InfrastructureGUI.IsOpen = false
InfrastructureGUI.CurrentMode = "BUILD" -- BUILD, SIGNALS, MANAGE
InfrastructureGUI.SelectedInfraType = "ROAD"
InfrastructureGUI.SelectedSignalType = "TRAFFIC_LIGHT"
InfrastructureGUI.IsBuildingMode = false
InfrastructureGUI.StartPosition = nil
InfrastructureGUI.InfraData = {}

-- Infrastruktur-Icons
local InfraIcons = {
    ROAD = "🛣️",
    RAILWAY = "🛤️",
    WATERWAY = "🌊",
    BRIDGE = "🌉",
    TUNNEL = "🕳️"
}

-- Signal-Icons
local SignalIcons = {
    TRAFFIC_LIGHT = "🚦",
    RAILWAY_SIGNAL = "🚥",
    SPEED_LIMIT = "⚠️"
}

-- GUI erstellen
function InfrastructureGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "InfrastructureGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 500, 0, 700)
    mainFrame.Position = UDim2.new(0, 20, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🏗️ INFRASTRUKTUR-BAU"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "BUILD", text = "🏗️ Bauen", icon = "🏗️"},
        {name = "SIGNALS", text = "🚦 Signale", icon = "🚦"},
        {name = "MANAGE", text = "📊 Verwalten", icon = "📊"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentMode and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(50, 55, 60)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 570)
    contentFrame.Position = UDim2.new(0, 10, 0, 120)
    contentFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function InfrastructureGUI:SwitchTab(tabName)
    self.CurrentMode = tabName
    self.IsBuildingMode = false
    self.StartPosition = nil
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function InfrastructureGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentMode then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(50, 55, 60)
        end
    end
end

-- Content aktualisieren
function InfrastructureGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentMode == "BUILD" then
        self:CreateBuildContent()
    elseif self.CurrentMode == "SIGNALS" then
        self:CreateSignalsContent()
    elseif self.CurrentMode == "MANAGE" then
        self:CreateManageContent()
    end
end

-- Bau-Content erstellen
function InfrastructureGUI:CreateBuildContent()
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    local yPos = 10
    
    -- Infrastruktur-Typen
    self:CreateSection(scrollFrame, "🏗️ INFRASTRUKTUR-TYPEN", yPos)
    yPos = yPos + 40
    
    local infraTypes = {
        {type = "ROAD", name = "🛣️ Straße", cost = 100, desc = "Für LKWs und Busse"},
        {type = "RAILWAY", name = "🛤️ Schiene", cost = 500, desc = "Für Züge"},
        {type = "WATERWAY", name = "🌊 Wasserweg", cost = 300, desc = "Für Schiffe"},
        {type = "BRIDGE", name = "🌉 Brücke", cost = 2000, desc = "Überquerung von Hindernissen"},
        {type = "TUNNEL", name = "🕳️ Tunnel", cost = 5000, desc = "Unterirdische Passage"}
    }
    
    for _, infraType in ipairs(infraTypes) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, -20, 0, 60)
        button.Position = UDim2.new(0, 10, 0, yPos)
        button.BackgroundColor3 = infraType.type == self.SelectedInfraType and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 70, 80)
        button.Text = infraType.name .. "\n💰 " .. infraType.cost .. " pro Segment\n" .. infraType.desc
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.BorderSizePixel = 0
        button.Parent = scrollFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 8)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self.SelectedInfraType = infraType.type
            self:UpdateContent()
        end)
        
        yPos = yPos + 70
    end
    
    -- Bau-Anweisungen
    self:CreateSection(scrollFrame, "📋 BAU-ANWEISUNGEN", yPos)
    yPos = yPos + 40
    
    local instructions = Instance.new("TextLabel")
    instructions.Size = UDim2.new(1, -20, 0, 100)
    instructions.Position = UDim2.new(0, 10, 0, yPos)
    instructions.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    instructions.Text = "1. Wähle einen Infrastruktur-Typ\n2. Klicke 'Bau-Modus starten'\n3. Klicke auf Startpunkt\n4. Klicke auf Endpunkt\n5. Bestätige den Bau"
    instructions.TextColor3 = Color3.fromRGB(200, 200, 200)
    instructions.TextScaled = true
    instructions.Font = Enum.Font.SourceSans
    instructions.BorderSizePixel = 0
    instructions.Parent = scrollFrame
    
    local instructionsCorner = Instance.new("UICorner")
    instructionsCorner.CornerRadius = UDim.new(0, 5)
    instructionsCorner.Parent = instructions
    
    yPos = yPos + 110
    
    -- Bau-Modus Button
    local buildModeButton = Instance.new("TextButton")
    buildModeButton.Size = UDim2.new(1, -20, 0, 50)
    buildModeButton.Position = UDim2.new(0, 10, 0, yPos)
    buildModeButton.BackgroundColor3 = self.IsBuildingMode and Color3.fromRGB(255, 100, 100) or Color3.fromRGB(100, 200, 100)
    buildModeButton.Text = self.IsBuildingMode and "🛑 BAU-MODUS BEENDEN" or "▶️ BAU-MODUS STARTEN"
    buildModeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    buildModeButton.TextScaled = true
    buildModeButton.Font = Enum.Font.SourceSansBold
    buildModeButton.BorderSizePixel = 0
    buildModeButton.Parent = scrollFrame
    
    local buildModeCorner = Instance.new("UICorner")
    buildModeCorner.CornerRadius = UDim.new(0, 8)
    buildModeCorner.Parent = buildModeButton
    
    buildModeButton.MouseButton1Click:Connect(function()
        self:ToggleBuildMode()
        self:UpdateContent()
    end)
    
    yPos = yPos + 60
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
end

-- Sektion erstellen
function InfrastructureGUI:CreateSection(parent, title, yPos)
    local section = Instance.new("TextLabel")
    section.Size = UDim2.new(1, -20, 0, 30)
    section.Position = UDim2.new(0, 10, 0, yPos)
    section.BackgroundColor3 = Color3.fromRGB(80, 120, 160)
    section.Text = title
    section.TextColor3 = Color3.fromRGB(255, 255, 255)
    section.TextScaled = true
    section.Font = Enum.Font.SourceSansBold
    section.BorderSizePixel = 0
    section.Parent = parent
    
    local sectionCorner = Instance.new("UICorner")
    sectionCorner.CornerRadius = UDim.new(0, 5)
    sectionCorner.Parent = section
end

-- Signal-Content erstellen
function InfrastructureGUI:CreateSignalsContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 0.3, 0)
    label.BackgroundTransparency = 1
    label.Text = "🚦 SIGNALE\n\nWähle eine Infrastruktur aus,\num Signale zu platzieren."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Verwaltungs-Content erstellen
function InfrastructureGUI:CreateManageContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 0.3, 0)
    label.BackgroundTransparency = 1
    label.Text = "📊 INFRASTRUKTUR-VERWALTUNG\n\nÜbersicht über alle\ngebauten Infrastrukturen."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Bau-Modus umschalten
function InfrastructureGUI:ToggleBuildMode()
    self.IsBuildingMode = not self.IsBuildingMode
    self.StartPosition = nil
    
    if self.IsBuildingMode then
        print("🏗️ Bau-Modus aktiviert für", self.SelectedInfraType)
    else
        print("🛑 Bau-Modus deaktiviert")
    end
end

-- Infrastruktur bauen
function InfrastructureGUI:BuildInfrastructure(position)
    if not self.IsBuildingMode then return end
    
    if not self.StartPosition then
        -- Startpunkt setzen
        self.StartPosition = position
        print("📍 Startpunkt gesetzt:", position)
    else
        -- Endpunkt setzen und bauen
        local endPosition = position
        print("🏁 Endpunkt gesetzt:", endPosition)
        
        -- Infrastruktur bauen
        BuildInfrastructureEvent:FireServer(self.SelectedInfraType, self.StartPosition, endPosition)
        
        -- Reset
        self.StartPosition = nil
        self.IsBuildingMode = false
        self:UpdateContent()
    end
end

-- Daten laden
function InfrastructureGUI:LoadInfrastructureData()
    local success, data = pcall(function()
        return GetInfrastructureDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.InfraData = data
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Infrastruktur-Daten")
    end
end

-- GUI öffnen
function InfrastructureGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadInfrastructureData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function InfrastructureGUI:CloseGUI()
    if self.MainFrame then
        self.IsBuildingMode = false
        self.StartPosition = nil
        
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Maus-Klick Handler
Mouse.Button1Down:Connect(function()
    if InfrastructureGUI.IsBuildingMode and InfrastructureGUI.IsOpen then
        local hit = Mouse.Hit
        if hit then
            InfrastructureGUI:BuildInfrastructure(hit.Position)
        end
    end
end)

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.B then
        if InfrastructureGUI.IsOpen then
            InfrastructureGUI:CloseGUI()
        else
            InfrastructureGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function InfrastructureGUI:Initialize()
    print("🏗️ InfrastructureGUI initialisiert - Drücke 'B' zum Öffnen")
end

-- Auto-Start
InfrastructureGUI:Initialize()

return InfrastructureGUI
