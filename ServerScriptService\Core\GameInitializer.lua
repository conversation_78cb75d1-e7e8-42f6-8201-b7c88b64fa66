-- ServerScriptService/Core/GameInitializer.lua
-- Hauptinitialisierung des Spiels auf Server-Seite
-- ROBLOX SCRIPT TYPE: <PERSON><PERSON>t (Server-Side)

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Module laden
local AssetManager = require(script.Parent.Parent.Managers.AssetManager)
local GameStateManager = require(script.Parent.Parent.Managers.GameStateManager)
local EconomyManager = require(script.Parent.Parent.Managers.EconomyManager)
local MapGenerator = require(script.Parent.Parent.Managers.MapGenerator)
local TransportManager = require(script.Parent.Parent.Managers.TransportManager)
local SaveManager = require(script.Parent.Parent.Managers.SaveManager)

-- RemoteEvents erstellen
require(ReplicatedStorage.Events.CreateRemoteEvents)

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GenerateMapEvent = Events:WaitForChild("GenerateMapEvent")
local SaveGameEvent = Events:WaitForChild("SaveGameEvent")
local LoadGameEvent = Events:WaitForChild("LoadGameEvent")

local GameInitializer = {}

-- Spiel initialisieren
function GameInitializer.Initialize()
    print("🚂 Transport Empire - Server wird initialisiert...")
    
    -- Manager initialisieren
    AssetManager:Initialize()
    GameStateManager:Initialize()
    EconomyManager:Initialize()
    MapGenerator:Initialize()
    TransportManager:Initialize()
    SaveManager:Initialize()
    
    -- Events verbinden
    GameInitializer.ConnectEvents()
    
    -- Game Loop starten
    GameInitializer.StartGameLoop()
    
    print("✅ Transport Empire - Server erfolgreich initialisiert!")
end

-- Events verbinden
function GameInitializer.ConnectEvents()
    -- Karte generieren
    GenerateMapEvent.OnServerEvent:Connect(function(player, mapConfig)
        print("🗺️ Generiere Karte für Spieler:", player.Name)
        local success = MapGenerator:GenerateMap(mapConfig)
        if success then
            -- Spieler über erfolgreiche Generierung informieren
            Events.MapGeneratedEvent:FireClient(player, true)
        else
            Events.MapGeneratedEvent:FireClient(player, false)
        end
    end)
    
    -- Spiel speichern
    SaveGameEvent.OnServerEvent:Connect(function(player, saveSlot)
        print("💾 Speichere Spiel für Spieler:", player.Name)
        local success = SaveManager:SaveGame(player, saveSlot)
        Events.GameSavedEvent:FireClient(player, success)
    end)
    
    -- Spiel laden
    LoadGameEvent.OnServerEvent:Connect(function(player, saveSlot)
        print("📂 Lade Spiel für Spieler:", player.Name)
        local success = SaveManager:LoadGame(player, saveSlot)
        Events.GameLoadedEvent:FireClient(player, success)
    end)
end

-- Game Loop starten
function GameInitializer.StartGameLoop()
    local lastUpdate = tick()
    
    RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local deltaTime = currentTime - lastUpdate
        
        -- Nur alle 0.1 Sekunden updaten (10 FPS für Simulation)
        if deltaTime >= 0.1 then
            GameStateManager:Update(deltaTime)
            EconomyManager:Update(deltaTime)
            TransportManager:Update(deltaTime)
            
            lastUpdate = currentTime
        end
    end)
end

-- Spieler beigetreten
Players.PlayerAdded:Connect(function(player)
    print("👤 Spieler beigetreten:", player.Name)
    
    -- Spieler-spezifische Initialisierung
    wait(1) -- Warten bis GUI geladen ist
    
    -- Willkommensnachricht senden
    Events.PlayerJoinedEvent:FireClient(player, {
        message = "Willkommen bei Transport Empire!",
        version = "1.0.0"
    })
end)

-- Spieler verlassen
Players.PlayerRemoving:Connect(function(player)
    print("👋 Spieler verlässt das Spiel:", player.Name)
    
    -- Auto-Save beim Verlassen
    SaveManager:AutoSave(player)
end)

-- Initialisierung starten
GameInitializer.Initialize()

return GameInitializer
