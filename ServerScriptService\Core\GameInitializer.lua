-- ServerScriptService/Core/GameInitializer.lua
-- Hauptinitialisierung des Spiels auf Server-Seite
-- ROBLOX SCRIPT TYPE: Script (Server-Side)

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Module laden
local AssetManager = require(script.Parent.Parent.Managers.AssetManager)
local GameStateManager = require(script.Parent.Parent.Managers.GameStateManager)
local EconomyManager = require(script.Parent.Parent.Managers.EconomyManager)
local MapGenerator = require(script.Parent.Parent.Managers.MapGenerator)
local TransportManager = require(script.Parent.Parent.Managers.TransportManager)
local SaveManager = require(script.Parent.Parent.Managers.SaveManager)
local TechTreeManager = require(script.Parent.Parent.Managers.TechTreeManager)
local BuildingManager = require(script.Parent.Parent.Managers.BuildingManager)
local VehicleManager = require(script.Parent.Parent.Managers.VehicleManager)

-- RemoteEvents erstellen
require(ReplicatedStorage.Events.CreateRemoteEvents)

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GenerateMapEvent = Events:WaitForChild("GenerateMapEvent")
local SaveGameEvent = Events:WaitForChild("SaveGameEvent")
local LoadGameEvent = Events:WaitForChild("LoadGameEvent")

local GameInitializer = {}

-- Spiel initialisieren
function GameInitializer.Initialize()
    print("🚂 Transport Empire - Server wird initialisiert...")
    
    -- Manager initialisieren
    local assetManager = AssetManager.new()
    local gameStateManager = GameStateManager.new()
    local economyManager = EconomyManager.new()
    local mapGenerator = MapGenerator.new()
    local transportManager = TransportManager.new()
    local saveManager = SaveManager.new()
    local techTreeManager = TechTreeManager.new()
    local buildingManager = BuildingManager.new(assetManager, techTreeManager, economyManager)
    local vehicleManager = VehicleManager.new()
    
    -- Events verbinden
    GameInitializer.ConnectEvents(buildingManager, techTreeManager, economyManager, vehicleManager)

    -- Game Loop starten
    GameInitializer.StartGameLoop(buildingManager, techTreeManager, economyManager, vehicleManager)
    
    print("✅ Transport Empire - Server erfolgreich initialisiert!")
end

-- Events verbinden
function GameInitializer.ConnectEvents(buildingManager, techTreeManager, economyManager, vehicleManager)
    -- Building-System Events
    local BuildBuildingEvent = Events:WaitForChild("BuildBuildingEvent")
    local UpgradeBuildingEvent = Events:WaitForChild("UpgradeBuildingEvent")
    local GetBuildingDataFunction = Events:WaitForChild("GetBuildingDataFunction")

    -- Tech-Tree Events
    local StartResearchEvent = Events:WaitForChild("StartResearchEvent")
    local GetTechDataFunction = Events:WaitForChild("GetTechDataFunction")

    -- Building Events
    BuildBuildingEvent.OnServerEvent:Connect(function(player, buildingId, position)
        buildingManager:InitializePlayerBuildings(player.UserId)
        local success, message = buildingManager:BuildBuilding(player.UserId, buildingId, position)
        print("🏗️ Gebäude-Bau:", player.Name, buildingId, success and "✅" or "❌", message)
    end)

    UpgradeBuildingEvent.OnServerEvent:Connect(function(player, buildingIndex)
        local success, message = buildingManager:UpgradeBuilding(player.UserId, buildingIndex)
        print("🔧 Gebäude-Upgrade:", player.Name, success and "✅" or "❌", message)
    end)

    GetBuildingDataFunction.OnServerInvoke = function(player, category)
        buildingManager:InitializePlayerBuildings(player.UserId)
        local availableBuildings = buildingManager:GetAvailableBuildings(player.UserId, category)
        local currentYear = economyManager:GetCurrentYear()
        return availableBuildings, currentYear
    end

    -- Tech-Tree Events
    StartResearchEvent.OnServerEvent:Connect(function(player, techId)
        techTreeManager:InitializePlayerTech(player.UserId)
        local success, message = techTreeManager:StartResearch(player.UserId, techId)
        print("🔬 Forschung:", player.Name, techId, success and "✅" or "❌", message)
    end)

    GetTechDataFunction.OnServerInvoke = function(player)
        techTreeManager:InitializePlayerTech(player.UserId)
        local currentYear = economyManager:GetCurrentYear()
        local availableTech = techTreeManager:GetAvailableTech(player.UserId, currentYear)
        return availableTech, currentYear
    end

    -- Vehicle Events
    local CreateVehicleEvent = Events:WaitForChild("CreateVehicleEvent")
    local SetVehicleRouteEvent = Events:WaitForChild("SetVehicleRouteEvent")

    CreateVehicleEvent.OnServerEvent:Connect(function(player, vehicleType, position)
        local success, vehicleId = vehicleManager:CreateVehicle(player.UserId, vehicleType, position)
        print("🚂 Fahrzeug erstellt:", player.Name, vehicleType, success and "✅" or "❌")
    end)

    SetVehicleRouteEvent.OnServerEvent:Connect(function(player, vehicleId, waypoints)
        local success = vehicleManager:SetVehicleRoute(vehicleId, waypoints)
        print("🗺️ Route gesetzt:", player.Name, vehicleId, success and "✅" or "❌")
    end)

    -- Karte generieren
    GenerateMapEvent.OnServerEvent:Connect(function(player, mapConfig)
        print("🗺️ Generiere Karte für Spieler:", player.Name)

        -- Karte generieren
        local success = MapGenerator:GenerateMap(mapConfig)

        -- Wirtschaft mit Kartendaten initialisieren
        economyManager:InitializeGlobalEconomy(mapConfig)

        -- Demo-Städte erstellen
        economyManager:CreateCity("Berlin", Vector3.new(100, 0, 100), 5000)
        economyManager:CreateCity("Hamburg", Vector3.new(-200, 0, 300), 3000)
        economyManager:CreateCity("München", Vector3.new(400, 0, -100), 4000)

        -- Demo-Fahrzeuge erstellen
        vehicleManager:CreateVehicle(player.UserId, "STEAM_TRAIN", Vector3.new(0, 5, 0))
        vehicleManager:CreateVehicle(player.UserId, "TRUCK_EARLY", Vector3.new(50, 5, 50))

        if success then
            -- Spieler über erfolgreiche Generierung informieren
            Events.MapGeneratedEvent:FireClient(player, true)
        else
            Events.MapGeneratedEvent:FireClient(player, false)
        end
    end)
    
    -- Spiel speichern
    SaveGameEvent.OnServerEvent:Connect(function(player, saveSlot)
        print("💾 Speichere Spiel für Spieler:", player.Name)
        local success = SaveManager:SaveGame(player, saveSlot)
        Events.GameSavedEvent:FireClient(player, success)
    end)
    
    -- Spiel laden
    LoadGameEvent.OnServerEvent:Connect(function(player, saveSlot)
        print("📂 Lade Spiel für Spieler:", player.Name)
        local success = SaveManager:LoadGame(player, saveSlot)
        Events.GameLoadedEvent:FireClient(player, success)
    end)
end

-- Game Loop starten
function GameInitializer.StartGameLoop(buildingManager, techTreeManager, economyManager, vehicleManager)
    local lastUpdate = tick()

    RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local deltaTime = currentTime - lastUpdate

        -- Nur alle 0.1 Sekunden updaten (10 FPS für Simulation)
        if deltaTime >= 0.1 then
            -- Manager Updates
            for playerId, _ in pairs(buildingManager.PlayerBuildings) do
                buildingManager:UpdateConstruction(playerId, deltaTime)
                techTreeManager:UpdateResearch(playerId, deltaTime)
            end

            -- Fahrzeug-Updates
            vehicleManager:UpdateVehicles(deltaTime)

            -- Wirtschafts-Updates
            economyManager:Update(deltaTime)

            -- Bestehende Manager
            GameStateManager:Update(deltaTime)
            TransportManager:Update(deltaTime)

            lastUpdate = currentTime
        end
    end)

    print("🔄 Game Loop gestartet - Alle Systeme aktiv")
end

-- Spieler beigetreten
Players.PlayerAdded:Connect(function(player)
    print("👤 Spieler beigetreten:", player.Name)
    
    -- Spieler-spezifische Initialisierung
    wait(1) -- Warten bis GUI geladen ist
    
    -- Willkommensnachricht senden
    Events.PlayerJoinedEvent:FireClient(player, {
        message = "Willkommen bei Transport Empire!",
        version = "1.0.0"
    })
end)

-- Spieler verlassen
Players.PlayerRemoving:Connect(function(player)
    print("👋 Spieler verlässt das Spiel:", player.Name)
    
    -- Auto-Save beim Verlassen
    SaveManager:AutoSave(player)
end)

-- Initialisierung starten
GameInitializer.Initialize()

return GameInitializer
