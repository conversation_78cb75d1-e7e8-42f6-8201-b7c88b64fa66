-- ServerScriptService/Managers/GameTester.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Automatisierte Test-Suite für alle Spielsysteme

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local GameTester = {}
GameTester.__index = GameTester

function GameTester.new()
    local self = setmetatable({}, GameTester)
    
    -- Test-Ergebnisse
    self.testResults = {
        passed = 0,
        failed = 0,
        total = 0,
        details = {}
    }
    
    -- Test-Suites
    self.testSuites = {
        "EconomyTests",
        "VehicleTests", 
        "CityTests",
        "TransportTests",
        "MultiplayerTests",
        "PerformanceTests"
    }
    
    return self
end

-- Alle Tests ausführen
function GameTester:RunAllTests(gameManager)
    print("🧪 Starte vollständige Test-Suite...")
    
    self:ResetResults()
    
    -- Economy-Tests
    self:RunEconomyTests(gameManager)
    
    -- Vehicle-Tests
    self:RunVehicleTests(gameManager)
    
    -- City-Tests
    self:RunCityTests(gameManager)
    
    -- Transport-Tests
    self:RunTransportTests(gameManager)
    
    -- Multiplayer-Tests
    self:RunMultiplayerTests(gameManager)
    
    -- Performance-Tests
    self:RunPerformanceTests(gameManager)
    
    -- Ergebnisse ausgeben
    self:PrintResults()
    
    return self.testResults
end

-- Test-Ergebnisse zurücksetzen
function GameTester:ResetResults()
    self.testResults = {
        passed = 0,
        failed = 0,
        total = 0,
        details = {}
    }
end

-- Economy-Tests
function GameTester:RunEconomyTests(gameManager)
    print("💰 Teste Economy-System...")
    
    -- Test: Spieler-Finanzen initialisieren
    self:RunTest("Economy: Player Finance Initialization", function()
        local testPlayerId = 12345
        gameManager.financeManager:InitializePlayerFinances(testPlayerId)
        local finances = gameManager.financeManager:GetPlayerFinanceData(testPlayerId)
        
        return finances ~= nil and finances.cash == 2000000
    end)
    
    -- Test: Einnahmen hinzufügen
    self:RunTest("Economy: Add Revenue", function()
        local testPlayerId = 12345
        local initialCash = gameManager.financeManager:GetPlayerFinanceData(testPlayerId).cash
        
        gameManager.financeManager:AddRevenue(testPlayerId, 100000, "Test Revenue")
        local newCash = gameManager.financeManager:GetPlayerFinanceData(testPlayerId).cash
        
        return newCash == initialCash + 100000
    end)
    
    -- Test: Kredit-System
    self:RunTest("Economy: Loan System", function()
        local testPlayerId = 12345
        local success, loanId = gameManager.financeManager:ProcessLoan(testPlayerId, 500000, 12)
        
        return success and loanId ~= nil
    end)
end

-- Vehicle-Tests
function GameTester:RunVehicleTests(gameManager)
    print("🚗 Teste Vehicle-System...")
    
    -- Test: Fahrzeug erstellen
    self:RunTest("Vehicle: Create Vehicle", function()
        local testPlayerId = 12345
        local vehicleData = {
            type = "bus",
            model = "city_bus",
            capacity = 50
        }
        
        local vehicleId = gameManager.vehicleManager:CreateVehicle(testPlayerId, vehicleData)
        return vehicleId ~= nil
    end)
    
    -- Test: Fahrzeug-Route zuweisen
    self:RunTest("Vehicle: Assign Route", function()
        local vehicles = gameManager.vehicleManager:GetPlayerVehicles(12345)
        if #vehicles == 0 then return false end
        
        local vehicleId = vehicles[1].id
        local routeData = {
            startCity = "city_1",
            endCity = "city_2",
            stops = {}
        }
        
        local success = gameManager.vehicleManager:AssignRoute(vehicleId, routeData)
        return success
    end)
end

-- City-Tests
function GameTester:RunCityTests(gameManager)
    print("🏙️ Teste City-System...")
    
    -- Test: Stadt erstellen
    self:RunTest("City: Create City", function()
        local cityData = {
            name = "Test City",
            x = 100,
            z = 100,
            population = 10000
        }
        
        local cityId = gameManager.cityManager:CreateCity(cityData)
        return cityId ~= nil
    end)
    
    -- Test: Stadt-Wachstum
    self:RunTest("City: Population Growth", function()
        local cities = gameManager.cityManager:GetAllCities()
        if not cities or #cities == 0 then return false end
        
        local city = cities[1]
        local initialPop = city.population
        
        gameManager.cityManager:UpdateCityGrowth(city.id, 0.05) -- 5% Wachstum
        
        local updatedCity = gameManager.cityManager:GetCity(city.id)
        return updatedCity.population > initialPop
    end)
end

-- Transport-Tests
function GameTester:RunTransportTests(gameManager)
    print("🚛 Teste Transport-System...")
    
    -- Test: Route erstellen
    self:RunTest("Transport: Create Route", function()
        local routeData = {
            name = "Test Route",
            startCity = "city_1",
            endCity = "city_2",
            transportType = "road"
        }
        
        local routeId = gameManager.transportManager:CreateRoute(12345, routeData)
        return routeId ~= nil
    end)
    
    -- Test: Waren-Transport
    self:RunTest("Transport: Cargo Transport", function()
        local cargoData = {
            type = "coal",
            amount = 100,
            from = "city_1",
            to = "city_2"
        }
        
        local success = gameManager.transportManager:TransportCargo(12345, cargoData)
        return success
    end)
end

-- Multiplayer-Tests
function GameTester:RunMultiplayerTests(gameManager)
    print("👥 Teste Multiplayer-System...")
    
    -- Test: Chat-Nachricht
    self:RunTest("Multiplayer: Chat Message", function()
        local mockPlayer = {UserId = 12345, Name = "TestPlayer"}
        local success = gameManager.multiplayerManager:ProcessChatMessage(mockPlayer, "Test message", "global")
        
        return success
    end)
    
    -- Test: Allianz erstellen
    self:RunTest("Multiplayer: Create Alliance", function()
        local success, allianceId = gameManager.multiplayerManager:CreateAlliance(12345, "Test Alliance")
        
        return success and allianceId ~= nil
    end)
end

-- Performance-Tests
function GameTester:RunPerformanceTests(gameManager)
    print("⚡ Teste Performance-System...")
    
    -- Test: Metriken sammeln
    self:RunTest("Performance: Collect Metrics", function()
        gameManager.performanceManager:CollectMetrics()
        local metrics = gameManager.performanceManager.metrics
        
        return metrics.fps > 0 and metrics.memory >= 0
    end)
    
    -- Test: LOD-System
    self:RunTest("Performance: LOD System", function()
        local testPart = Instance.new("Part")
        testPart.Parent = workspace
        
        gameManager.performanceManager:AddLODObject("test_object", testPart)
        
        local playerPositions = {Vector3.new(0, 0, 0)}
        gameManager.performanceManager:UpdateLOD(playerPositions)
        
        testPart:Destroy()
        return true
    end)
end

-- Einzelnen Test ausführen
function GameTester:RunTest(testName, testFunction)
    self.testResults.total = self.testResults.total + 1
    
    local success, result = pcall(testFunction)
    
    if success and result then
        self.testResults.passed = self.testResults.passed + 1
        table.insert(self.testResults.details, {
            name = testName,
            status = "PASSED",
            message = "Test erfolgreich"
        })
        print("✅", testName, "- PASSED")
    else
        self.testResults.failed = self.testResults.failed + 1
        table.insert(self.testResults.details, {
            name = testName,
            status = "FAILED",
            message = result or "Test fehlgeschlagen"
        })
        print("❌", testName, "- FAILED:", result or "Unbekannter Fehler")
    end
end

-- Test-Ergebnisse ausgeben
function GameTester:PrintResults()
    print("\n" .. string.rep("=", 50))
    print("🧪 TEST-ERGEBNISSE")
    print(string.rep("=", 50))
    print("Gesamt:", self.testResults.total)
    print("Erfolgreich:", self.testResults.passed)
    print("Fehlgeschlagen:", self.testResults.failed)
    print("Erfolgsrate:", math.floor((self.testResults.passed / self.testResults.total) * 100) .. "%")
    print(string.rep("=", 50))
    
    -- Fehlgeschlagene Tests im Detail
    if self.testResults.failed > 0 then
        print("\n❌ FEHLGESCHLAGENE TESTS:")
        for _, detail in pairs(self.testResults.details) do
            if detail.status == "FAILED" then
                print("  -", detail.name, ":", detail.message)
            end
        end
    end
    
    print("\n🎯 Test-Suite abgeschlossen!\n")
end

-- Stress-Test
function GameTester:RunStressTest(gameManager, duration)
    print("🔥 Starte Stress-Test für", duration, "Sekunden...")
    
    local startTime = tick()
    local operations = 0
    
    while tick() - startTime < duration do
        -- Verschiedene Operationen simulieren
        
        -- Fahrzeuge erstellen und löschen
        local vehicleId = gameManager.vehicleManager:CreateVehicle(99999, {
            type = "bus",
            model = "test_bus",
            capacity = 30
        })
        
        if vehicleId then
            gameManager.vehicleManager:RemoveVehicle(vehicleId)
        end
        
        -- Städte aktualisieren
        local cities = gameManager.cityManager:GetAllCities()
        if cities and #cities > 0 then
            gameManager.cityManager:UpdateCityGrowth(cities[1].id, 0.01)
        end
        
        -- Performance-Metriken sammeln
        gameManager.performanceManager:CollectMetrics()
        
        operations = operations + 1
        wait(0.1) -- Kurze Pause
    end
    
    local endTime = tick()
    local actualDuration = endTime - startTime
    local opsPerSecond = operations / actualDuration
    
    print("🔥 Stress-Test abgeschlossen:")
    print("  Dauer:", math.floor(actualDuration), "Sekunden")
    print("  Operationen:", operations)
    print("  Ops/Sekunde:", math.floor(opsPerSecond))
    
    return {
        duration = actualDuration,
        operations = operations,
        opsPerSecond = opsPerSecond
    }
end

-- Memory-Leak-Test
function GameTester:RunMemoryLeakTest(gameManager, iterations)
    print("🧠 Starte Memory-Leak-Test mit", iterations, "Iterationen...")
    
    local initialMemory = gameManager.performanceManager.metrics.memory
    
    for i = 1, iterations do
        -- Objekte erstellen und wieder freigeben
        local objects = {}
        
        for j = 1, 100 do
            local part = Instance.new("Part")
            part.Parent = workspace
            table.insert(objects, part)
        end
        
        -- Objekte wieder löschen
        for _, obj in pairs(objects) do
            obj:Destroy()
        end
        
        -- Garbage Collection
        collectgarbage("collect")
        
        if i % 10 == 0 then
            print("  Iteration", i, "/", iterations)
        end
    end
    
    -- Finale Speicher-Messung
    gameManager.performanceManager:CollectMetrics()
    local finalMemory = gameManager.performanceManager.metrics.memory
    local memoryDiff = finalMemory - initialMemory
    
    print("🧠 Memory-Leak-Test abgeschlossen:")
    print("  Initial Memory:", initialMemory, "MB")
    print("  Final Memory:", finalMemory, "MB")
    print("  Differenz:", memoryDiff, "MB")
    
    local hasLeak = memoryDiff > 10 -- Mehr als 10MB Differenz = potentieller Leak
    
    return {
        initialMemory = initialMemory,
        finalMemory = finalMemory,
        memoryDiff = memoryDiff,
        hasLeak = hasLeak
    }
end

return GameTester
