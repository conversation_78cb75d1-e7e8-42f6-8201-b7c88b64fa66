# Kleines Zeitgenössisches Haus
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "SMALL_HOUSE_CONTEMPORARY"
Name = "Kleines Zeitgenössisches Haus"
Category = "Residential"
Size = "Small"
Era = "Contemporary"
Era_Years = {1990, 2000}

[MODEL_DATA]
ModelId = "rbxassetid://SMALL_HOUSE_CONTEMPORARY"
Scale = Vector3(12, 5, 14)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.95, 0.95, 0.95)     # Weiße Außenwände
Secondary = Color3(0.3, 0.3, 0.3)      # Dunkles Flachdach
Accent = Color3(0.2, 0.2, 0.8)         # Blaue Akzente
Trim = Color3(0.1, 0.1, 0.1)           # Schwarze Fensterrahmen

[GAMEPLAY_STATS]
Population = 4
BuildCost = 12000
MaintenanceCost = 120
BuildTime = 20
PowerConsumption = 15
WaterConsumption = 8
LandSize = Vector2(3, 3)  # 3x3 Felder

[REQUIREMENTS]
MinPopulation = 5000
MinYear = 1990
RequiredTech = {"Contemporary_Construction", "Advanced_Utilities"}
RequiredResources = {"Advanced_Materials", "Glass", "Steel"}
UnlockCost = 8000

[FEATURES]
ArchitecturalStyle = "Contemporary_Minimalist"
HasGarden = true
HasChimney = false
HasBasement = false
Floors = 2
WindowStyle = "Floor_To_Ceiling_Windows"
RoofStyle = "Flat_Contemporary"
HasDoubleGarage = true
HasModernKitchen = true
HasCentralAC = true
HasSecuritySystem = true

[UTILITIES]
RequiresElectricity = true
RequiresWater = true
RequiresSewer = true
RequiresTelephone = true
RequiresCableTV = true
RequiresInternet = true

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "MEDIUM_HOUSE_CONTEMPORARY"
UpgradeCost = 8000
UpgradeTime = 25
UpgradeRequirements = {"High_Tech_Infrastructure"}

[ECONOMIC_DATA]
TaxRevenue = 85
PropertyValue = 12000
MaintenanceJobs = 2
ConstructionJobs = 8
AttractsYoungProfessionals = true

[DESCRIPTION]
ShortDesc = "Modernes minimalistisches Haus der 1990er"
LongDesc = "Ein zeitgenössisches Haus mit minimalistischem Design, großen Fenstern und modernster Technik. Ausgestattet mit Doppelgarage, Klimaanlage und Sicherheitssystem - perfekt für die moderne Familie."
