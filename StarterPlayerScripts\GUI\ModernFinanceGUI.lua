-- StarterPlayerScripts/GUI/ModernFinanceGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Finanz-GUI mit Dashboard, Krediten und detaillierter Finanzübersicht

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetFinanceDataFunction = Events:WaitForChild("GetFinanceDataFunction")
local GetLoanDataFunction = Events:WaitForChild("GetLoanDataFunction")
local RequestLoanEvent = Events:WaitForChild("RequestLoanEvent")
local RepayLoanEvent = Events:WaitForChild("RepayLoanEvent")

local ModernFinanceGUI = {}
ModernFinanceGUI.__index = ModernFinanceGUI

-- Konstruktor
function ModernFinanceGUI.new()
    local self = setmetatable({}, ModernFinanceGUI)

    self.isOpen = false
    self.isDocked = false
    self.currentTab = "DASHBOARD" -- DASHBOARD, INCOME, EXPENSES, LOANS, STATISTICS
    self.financeData = {}
    self.loanData = {}
    self.updateConnection = nil

    return self
end

-- GUI erstellen
function ModernFinanceGUI:CreateGUI()
    if self.screenGui then return end

    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernFinanceGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui

    -- Hauptfenster
    local mainWindow = Instance.new("Frame")
    mainWindow.Size = UDim2.new(0, 1200, 0, 800)
    mainWindow.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainWindow.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainWindow.BorderSizePixel = 0
    mainWindow.Visible = false
    mainWindow.Parent = self.screenGui

    local windowCorner = Instance.new("UICorner")
    windowCorner.CornerRadius = UDim.new(0, 15)
    windowCorner.Parent = mainWindow

    -- Fenster-Gradient
    local windowGradient = Instance.new("UIGradient")
    windowGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(20, 25, 30)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(10, 15, 20))
    }
    windowGradient.Rotation = 45
    windowGradient.Parent = mainWindow

    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 60)
    titleBar.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainWindow

    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 15)
    titleCorner.Parent = titleBar

    -- Titel mit Icon
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -200, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "💰 FINANZ-ZENTRALE"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar

    -- Aktueller Kontostand (in Titel-Bar)
    local balanceLabel = Instance.new("TextLabel")
    balanceLabel.Size = UDim2.new(0, 200, 0, 30)
    balanceLabel.Position = UDim2.new(1, -350, 0, 15)
    balanceLabel.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    balanceLabel.Text = "💵 Laden..."
    balanceLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    balanceLabel.TextScaled = true
    balanceLabel.Font = Enum.Font.SourceSansBold
    balanceLabel.BorderSizePixel = 0
    balanceLabel.Parent = titleBar

    local balanceCorner = Instance.new("UICorner")
    balanceCorner.CornerRadius = UDim.new(0, 8)
    balanceCorner.Parent = balanceLabel

    -- Dock-Button
    local dockButton = Instance.new("TextButton")
    dockButton.Size = UDim2.new(0, 45, 0, 45)
    dockButton.Position = UDim2.new(1, -100, 0, 7.5)
    dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    dockButton.Text = "📌"
    dockButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    dockButton.TextScaled = true
    dockButton.Font = Enum.Font.SourceSans
    dockButton.BorderSizePixel = 0
    dockButton.Parent = titleBar

    local dockCorner = Instance.new("UICorner")
    dockCorner.CornerRadius = UDim.new(0, 10)
    dockCorner.Parent = dockButton

    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 45, 0, 45)
    closeButton.Position = UDim2.new(1, -50, 0, 7.5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar

    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 10)
    closeCorner.Parent = closeButton

    -- Tab-Navigation
    local tabContainer = Instance.new("Frame")
    tabContainer.Size = UDim2.new(1, -20, 0, 60)
    tabContainer.Position = UDim2.new(0, 10, 0, 70)
    tabContainer.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    tabContainer.BorderSizePixel = 0
    tabContainer.Parent = mainWindow

    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 12)
    tabCorner.Parent = tabContainer

    local tabLayout = Instance.new("UIListLayout")
    tabLayout.FillDirection = Enum.FillDirection.Horizontal
    tabLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    tabLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    tabLayout.Padding = UDim.new(0, 10)
    tabLayout.Parent = tabContainer

    local tabPadding = Instance.new("UIPadding")
    tabPadding.PaddingAll = UDim.new(0, 15)
    tabPadding.Parent = tabContainer

    -- Tab-Buttons erstellen
    local tabs = {
        {text = "📊 DASHBOARD", id = "DASHBOARD", color = Color3.fromRGB(100, 150, 255)},
        {text = "💹 EINNAHMEN", id = "INCOME", color = Color3.fromRGB(100, 255, 100)},
        {text = "💸 AUSGABEN", id = "EXPENSES", color = Color3.fromRGB(255, 150, 100)},
        {text = "🏦 KREDITE", id = "LOANS", color = Color3.fromRGB(255, 200, 100)},
        {text = "📈 STATISTIKEN", id = "STATISTICS", color = Color3.fromRGB(150, 255, 150)}
    }

    self.tabButtons = {}

    for _, tabData in ipairs(tabs) do
        local tabButton = Instance.new("TextButton")
        tabButton.Size = UDim2.new(0, 150, 0, 40)
        tabButton.BackgroundColor3 = tabData.color
        tabButton.Text = tabData.text
        tabButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        tabButton.TextScaled = true
        tabButton.Font = Enum.Font.SourceSansBold
        tabButton.BorderSizePixel = 0
        tabButton.Parent = tabContainer

        local tabButtonCorner = Instance.new("UICorner")
        tabButtonCorner.CornerRadius = UDim.new(0, 8)
        tabButtonCorner.Parent = tabButton

        -- Hover-Effekt
        tabButton.MouseEnter:Connect(function()
            if self.currentTab ~= tabData.id then
                TweenService:Create(tabButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.new(
                        math.min(tabData.color.R + 0.1, 1),
                        math.min(tabData.color.G + 0.1, 1),
                        math.min(tabData.color.B + 0.1, 1)
                    )
                }):Play()
            end
        end)

        tabButton.MouseLeave:Connect(function()
            if self.currentTab ~= tabData.id then
                TweenService:Create(tabButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = tabData.color
                }):Play()
            end
        end)

        -- Click-Handler
        tabButton.MouseButton1Click:Connect(function()
            self:SwitchTab(tabData.id)
        end)

        self.tabButtons[tabData.id] = {button = tabButton, color = tabData.color}
    end

    -- Content-Bereich
    self.contentFrame = Instance.new("Frame")
    self.contentFrame.Size = UDim2.new(1, -20, 1, -150)
    self.contentFrame.Position = UDim2.new(0, 10, 0, 140)
    self.contentFrame.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    self.contentFrame.BorderSizePixel = 0
    self.contentFrame.Parent = mainWindow

    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 12)
    contentCorner.Parent = self.contentFrame

    -- Referenzen speichern
    self.mainWindow = mainWindow
    self.titleBar = titleBar
    self.balanceLabel = balanceLabel
    self.dockButton = dockButton
    self.closeButton = closeButton
    self.tabContainer = tabContainer

    -- Event-Handler
    dockButton.MouseButton1Click:Connect(function()
        self:ToggleDock()
    end)

    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)

    -- Drag-Funktionalität
    self:SetupDragging()

    return self.screenGui
end

-- Tab wechseln
function ModernFinanceGUI:SwitchTab(tabId)
    if self.currentTab == tabId then return end

    -- Alten Tab deaktivieren
    if self.tabButtons[self.currentTab] then
        local oldButton = self.tabButtons[self.currentTab].button
        local oldColor = self.tabButtons[self.currentTab].color
        TweenService:Create(oldButton, TweenInfo.new(0.2), {
            BackgroundColor3 = oldColor,
            BackgroundTransparency = 0.3
        }):Play()
    end

    -- Neuen Tab aktivieren
    if self.tabButtons[tabId] then
        local newButton = self.tabButtons[tabId].button
        TweenService:Create(newButton, TweenInfo.new(0.2), {
            BackgroundTransparency = 0,
            Size = UDim2.new(0, 155, 0, 42)
        }):Play()
    end

    self.currentTab = tabId

    -- Content aktualisieren
    self:UpdateTabContent()
end

-- Tab-Inhalt aktualisieren
function ModernFinanceGUI:UpdateTabContent()
    -- Alten Inhalt löschen
    for _, child in pairs(self.contentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end

    -- Neuen Inhalt basierend auf Tab erstellen
    if self.currentTab == "DASHBOARD" then
        self:CreateDashboardContent()
    elseif self.currentTab == "INCOME" then
        self:CreateIncomeContent()
    elseif self.currentTab == "EXPENSES" then
        self:CreateExpensesContent()
    elseif self.currentTab == "LOANS" then
        self:CreateLoansContent()
    elseif self.currentTab == "STATISTICS" then
        self:CreateStatisticsContent()
    end
end

-- Dashboard-Inhalt erstellen
function ModernFinanceGUI:CreateDashboardContent()
    -- Überschrift
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "📊 FINANZ-DASHBOARD"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    -- Karten-Container
    local cardsContainer = Instance.new("Frame")
    cardsContainer.Size = UDim2.new(1, -40, 0, 200)
    cardsContainer.Position = UDim2.new(0, 20, 0, 80)
    cardsContainer.BackgroundTransparency = 1
    cardsContainer.Parent = self.contentFrame

    local cardsLayout = Instance.new("UIListLayout")
    cardsLayout.FillDirection = Enum.FillDirection.Horizontal
    cardsLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    cardsLayout.VerticalAlignment = Enum.VerticalAlignment.Top
    cardsLayout.Padding = UDim.new(0, 20)
    cardsLayout.Parent = cardsContainer

    -- Finanz-Karten erstellen
    local cards = {
        {title = "💰 KONTOSTAND", value = "2.5M $", color = Color3.fromRGB(100, 150, 255), change = "+15%"},
        {title = "📈 MONATSEINKOMMEN", value = "450K $", color = Color3.fromRGB(100, 255, 100), change = "+8%"},
        {title = "📉 MONATSAUSGABEN", value = "320K $", color = Color3.fromRGB(255, 150, 100), change = "-3%"},
        {title = "🏦 OFFENE KREDITE", value = "1.2M $", color = Color3.fromRGB(255, 200, 100), change = "-12%"}
    }

    for _, cardData in ipairs(cards) do
        local card = Instance.new("Frame")
        card.Size = UDim2.new(0, 270, 1, 0)
        card.BackgroundColor3 = cardData.color
        card.BorderSizePixel = 0
        card.Parent = cardsContainer

        local cardCorner = Instance.new("UICorner")
        cardCorner.CornerRadius = UDim.new(0, 12)
        cardCorner.Parent = card

        local cardGradient = Instance.new("UIGradient")
        cardGradient.Color = ColorSequence.new{
            ColorSequenceKeypoint.new(0, cardData.color),
            ColorSequenceKeypoint.new(1, Color3.new(
                math.max(cardData.color.R - 0.1, 0),
                math.max(cardData.color.G - 0.1, 0),
                math.max(cardData.color.B - 0.1, 0)
            ))
        }
        cardGradient.Rotation = 45
        cardGradient.Parent = card

        -- Karten-Titel
        local cardTitle = Instance.new("TextLabel")
        cardTitle.Size = UDim2.new(1, -20, 0, 40)
        cardTitle.Position = UDim2.new(0, 10, 0, 15)
        cardTitle.BackgroundTransparency = 1
        cardTitle.Text = cardData.title
        cardTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
        cardTitle.TextScaled = true
        cardTitle.Font = Enum.Font.SourceSansBold
        cardTitle.TextXAlignment = Enum.TextXAlignment.Left
        cardTitle.Parent = card

        -- Karten-Wert
        local cardValue = Instance.new("TextLabel")
        cardValue.Size = UDim2.new(1, -20, 0, 60)
        cardValue.Position = UDim2.new(0, 10, 0, 55)
        cardValue.BackgroundTransparency = 1
        cardValue.Text = cardData.value
        cardValue.TextColor3 = Color3.fromRGB(255, 255, 255)
        cardValue.TextScaled = true
        cardValue.Font = Enum.Font.SourceSansBold
        cardValue.TextXAlignment = Enum.TextXAlignment.Left
        cardValue.Parent = card

        -- Änderung
        local cardChange = Instance.new("TextLabel")
        cardChange.Size = UDim2.new(0, 80, 0, 30)
        cardChange.Position = UDim2.new(1, -90, 0, 130)
        cardChange.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
        cardChange.BackgroundTransparency = 0.2
        cardChange.Text = cardData.change
        cardChange.TextColor3 = Color3.fromRGB(255, 255, 255)
        cardChange.TextScaled = true
        cardChange.Font = Enum.Font.SourceSansBold
        cardChange.BorderSizePixel = 0
        cardChange.Parent = card

        local changeCorner = Instance.new("UICorner")
        changeCorner.CornerRadius = UDim.new(0, 8)
        changeCorner.Parent = cardChange
    end

    -- Schnellaktionen
    local actionsContainer = Instance.new("Frame")
    actionsContainer.Size = UDim2.new(1, -40, 0, 100)
    actionsContainer.Position = UDim2.new(0, 20, 0, 300)
    actionsContainer.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    actionsContainer.BorderSizePixel = 0
    actionsContainer.Parent = self.contentFrame

    local actionsCorner = Instance.new("UICorner")
    actionsCorner.CornerRadius = UDim.new(0, 12)
    actionsCorner.Parent = actionsContainer

    local actionsTitle = Instance.new("TextLabel")
    actionsTitle.Size = UDim2.new(1, -20, 0, 30)
    actionsTitle.Position = UDim2.new(0, 10, 0, 10)
    actionsTitle.BackgroundTransparency = 1
    actionsTitle.Text = "⚡ SCHNELLAKTIONEN"
    actionsTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    actionsTitle.TextScaled = true
    actionsTitle.Font = Enum.Font.SourceSansBold
    actionsTitle.TextXAlignment = Enum.TextXAlignment.Left
    actionsTitle.Parent = actionsContainer

    local actionsButtonContainer = Instance.new("Frame")
    actionsButtonContainer.Size = UDim2.new(1, -20, 0, 50)
    actionsButtonContainer.Position = UDim2.new(0, 10, 0, 40)
    actionsButtonContainer.BackgroundTransparency = 1
    actionsButtonContainer.Parent = actionsContainer

    local actionsLayout = Instance.new("UIListLayout")
    actionsLayout.FillDirection = Enum.FillDirection.Horizontal
    actionsLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    actionsLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    actionsLayout.Padding = UDim.new(0, 15)
    actionsLayout.Parent = actionsButtonContainer

    -- Schnellaktions-Buttons
    local actionButtons = {
        {text = "🏦 KREDIT BEANTRAGEN", color = Color3.fromRGB(100, 150, 255), action = "REQUEST_LOAN"},
        {text = "💰 KREDIT ZURÜCKZAHLEN", color = Color3.fromRGB(100, 255, 100), action = "REPAY_LOAN"},
        {text = "📊 DETAILBERICHT", color = Color3.fromRGB(255, 150, 100), action = "DETAILED_REPORT"},
        {text = "📈 PROGNOSE", color = Color3.fromRGB(150, 255, 150), action = "FORECAST"}
    }

    for _, buttonData in ipairs(actionButtons) do
        local actionButton = Instance.new("TextButton")
        actionButton.Size = UDim2.new(0, 200, 1, 0)
        actionButton.BackgroundColor3 = buttonData.color
        actionButton.Text = buttonData.text
        actionButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        actionButton.TextScaled = true
        actionButton.Font = Enum.Font.SourceSansBold
        actionButton.BorderSizePixel = 0
        actionButton.Parent = actionsButtonContainer

        local actionCorner = Instance.new("UICorner")
        actionCorner.CornerRadius = UDim.new(0, 8)
        actionCorner.Parent = actionButton

        -- Hover-Effekt
        actionButton.MouseEnter:Connect(function()
            TweenService:Create(actionButton, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.new(
                    math.min(buttonData.color.R + 0.1, 1),
                    math.min(buttonData.color.G + 0.1, 1),
                    math.min(buttonData.color.B + 0.1, 1)
                )
            }):Play()
        end)

        actionButton.MouseLeave:Connect(function()
            TweenService:Create(actionButton, TweenInfo.new(0.2), {
                BackgroundColor3 = buttonData.color
            }):Play()
        end)

        -- Click-Handler
        actionButton.MouseButton1Click:Connect(function()
            self:HandleQuickAction(buttonData.action)
        end)
    end
end

-- Einnahmen-Inhalt erstellen
function ModernFinanceGUI:CreateIncomeContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "💹 EINNAHMEN-ÜBERSICHT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Einnahmen-Details\n\n• Passagier-Transport\n• Fracht-Transport\n• Immobilien-Erträge\n• Sonstige Einnahmen\n\nDetaillierte Aufschlüsselung wird hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Ausgaben-Inhalt erstellen
function ModernFinanceGUI:CreateExpensesContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "💸 AUSGABEN-ÜBERSICHT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Ausgaben-Details\n\n• Fahrzeug-Wartung\n• Infrastruktur-Kosten\n• Personal-Gehälter\n• Kreditzinsen\n• Sonstige Ausgaben\n\nDetaillierte Aufschlüsselung wird hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Kredite-Inhalt erstellen
function ModernFinanceGUI:CreateLoansContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🏦 KREDIT-VERWALTUNG"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Kredit-Management\n\n• Aktuelle Kredite\n• Verfügbare Kreditlinien\n• Zinssätze\n• Rückzahlungspläne\n• Kreditwürdigkeit\n\nVollständige Kredit-Verwaltung wird hier implementiert."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Statistiken-Inhalt erstellen
function ModernFinanceGUI:CreateStatisticsContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "📈 FINANZ-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Finanz-Statistiken\n\n• Gewinn-/Verlust-Diagramme\n• Cashflow-Analyse\n• ROI-Berechnungen\n• Trend-Analysen\n• Vergleichsdaten\n\nDetaillierte Statistiken und Diagramme werden hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Schnellaktion behandeln
function ModernFinanceGUI:HandleQuickAction(action)
    if action == "REQUEST_LOAN" then
        print("🏦 Kredit beantragen")
        -- Hier würde ein Kredit-Dialog geöffnet
    elseif action == "REPAY_LOAN" then
        print("💰 Kredit zurückzahlen")
        -- Hier würde ein Rückzahlungs-Dialog geöffnet
    elseif action == "DETAILED_REPORT" then
        print("📊 Detailbericht erstellen")
        -- Hier würde ein detaillierter Finanzbericht generiert
    elseif action == "FORECAST" then
        print("📈 Finanzprognose erstellen")
        -- Hier würde eine Finanzprognose angezeigt
    end
end

-- Docking umschalten
function ModernFinanceGUI:ToggleDock()
    self.isDocked = not self.isDocked

    if self.isDocked then
        -- An linke Seite andocken
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 500, 1, -100),
            Position = UDim2.new(0, 10, 0, 50)
        }):Play()
        self.dockButton.Text = "🔓"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    else
        -- Floating
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 1200, 0, 800),
            Position = UDim2.new(0.5, -600, 0.5, -400)
        }):Play()
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    end
end

-- Dragging Setup
function ModernFinanceGUI:SetupDragging()
    local dragging = false
    local dragStart = nil
    local startPos = nil

    self.titleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 and not self.isDocked then
            dragging = true
            dragStart = input.Position
            startPos = self.mainWindow.Position
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            self.mainWindow.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)

    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
end

-- Finanzdaten aktualisieren
function ModernFinanceGUI:UpdateFinanceData()
    local success, data = pcall(function()
        return GetFinanceDataFunction:InvokeServer()
    end)

    if success and data then
        self.financeData = data

        -- Kontostand in Titel-Bar aktualisieren
        if self.balanceLabel then
            self.balanceLabel.Text = "💵 " .. self:FormatMoney(data.balance or 0)

            -- Farbe basierend auf Kontostand
            if (data.balance or 0) < 0 then
                self.balanceLabel.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
            elseif (data.balance or 0) < 500000 then
                self.balanceLabel.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
            else
                self.balanceLabel.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            end
        end

        -- Tab-Inhalt aktualisieren falls Dashboard aktiv
        if self.currentTab == "DASHBOARD" then
            self:UpdateTabContent()
        end
    else
        warn("❌ Fehler beim Laden der Finanzdaten:", data)
    end
end

-- Geld formatieren
function ModernFinanceGUI:FormatMoney(amount)
    if amount >= 1000000 then
        return string.format("%.1fM $", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.1fK $", amount / 1000)
    else
        return string.format("%d $", amount)
    end
end

-- GUI öffnen
function ModernFinanceGUI:OpenGUI()
    if not self.screenGui then
        self:CreateGUI()
    end

    self.mainWindow.Visible = true
    self.isOpen = true

    -- Smooth fade-in
    self.mainWindow.BackgroundTransparency = 1
    TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    }):Play()

    -- Standard-Tab setzen und Daten laden
    self:SwitchTab("DASHBOARD")
    self:UpdateFinanceData()

    -- Auto-Update starten
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end

    self.updateConnection = RunService.Heartbeat:Connect(function()
        -- Alle 5 Sekunden aktualisieren
        if tick() % 5 < 0.1 then
            self:UpdateFinanceData()
        end
    end)
end

-- GUI schließen
function ModernFinanceGUI:CloseGUI()
    if self.mainWindow then
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        }):Play()

        wait(0.3)
        self.mainWindow.Visible = false
        self.isOpen = false

        -- Auto-Update stoppen
        if self.updateConnection then
            self.updateConnection:Disconnect()
            self.updateConnection = nil
        end
    end
end

-- Singleton-Instanz
local ModernFinanceGUIInstance = ModernFinanceGUI.new()

return ModernFinanceGUIInstance