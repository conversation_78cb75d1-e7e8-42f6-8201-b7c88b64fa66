-- ServerScriptService/Managers/EconomyManager.lua
-- Vollständiges Wirtschaftssystem mit Städten, Industrien und Warenfluss
-- ROBLOX SCRIPT TYPE: ModuleScript

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local EconomyManager = {}
EconomyManager.PlayerData = {} -- Spieler-spezifische Wirtschaftsdaten
EconomyManager.GlobalEconomy = {} -- Globale Wirtschaftsdaten

-- Spieler-Wirtschaftsdaten initialisieren
function EconomyManager:InitializePlayerEconomy(player)
    local playerId = tostring(player.UserId)
    
    self.PlayerData[playerId] = {
        money = GameConfig.Game.StartMoney,
        monthlyIncome = 0,
        monthlyExpenses = 0,
        totalIncome = 0,
        totalExpenses = 0,
        vehicles = {},
        routes = {},
        infrastructure = {
            railways = {},
            roads = {},
            stations = {}
        },
        statistics = {
            passengersTransported = 0,
            cargoTransported = 0,
            totalDistance = 0
        }
    }
    
    print("💰 Wirtschaftsdaten für Spieler initialisiert:", player.Name)
end

-- Globale Wirtschaft initialisieren
function EconomyManager:InitializeGlobalEconomy(mapData)
    if not mapData then
        warn("Keine Kartendaten für Wirtschaftsinitialisierung!")
        return
    end
    
    self.GlobalEconomy = {
        cities = {},
        industries = {},
        currentYear = mapData.startYear or 1850,
        currentMonth = 1,
        gameTime = 0 -- In Sekunden
    }
    
    -- Städte initialisieren
    for _, cityData in pairs(mapData.cities) do
        local city = {
            id = cityData.id,
            name = cityData.name,
            x = cityData.x,
            z = cityData.z,
            size = cityData.size,
            population = cityData.population,
            demands = {},
            satisfaction = cityData.satisfaction,
            growth = cityData.growth,
            connections = {}, -- Transportverbindungen
            supply = {} -- Aktuelle Warenversorgung
        }
        
        -- Nachfrage initialisieren
        for _, good in pairs(cityData.demands) do
            city.demands[good] = {
                amount = math.floor(city.population * 0.1), -- 10% der Bevölkerung
                satisfied = 0,
                price = self:GetGoodBasePrice(good)
            }
            city.supply[good] = 0
        end
        
        self.GlobalEconomy.cities[cityData.id] = city
    end
    
    -- Industrien initialisieren
    for _, industryData in pairs(mapData.industries) do
        local industry = {
            id = industryData.id,
            type = industryData.type,
            name = industryData.name,
            x = industryData.x,
            z = industryData.z,
            produces = industryData.produces,
            consumes = industryData.consumes,
            productionRate = industryData.productionRate,
            workers = industryData.workers,
            stockpile = {},
            connections = {},
            efficiency = 1.0 -- Produktionseffizienz
        }
        
        -- Lager initialisieren
        for _, good in pairs(industry.produces) do
            industry.stockpile[good] = 0
        end
        for _, good in pairs(industry.consumes) do
            industry.stockpile[good] = 0
        end
        
        self.GlobalEconomy.industries[industryData.id] = industry
    end
    
    print("🏭 Globale Wirtschaft initialisiert:")
    print("   - Städte:", #mapData.cities)
    print("   - Industrien:", #mapData.industries)
end

-- Basis-Preis für Waren
function EconomyManager:GetGoodBasePrice(good)
    local prices = {
        Passengers = 2,
        Mail = 1,
        Coal = 5,
        Iron = 8,
        Steel = 15,
        Wood = 3,
        Planks = 7,
        Food = 4,
        Goods = 12
    }
    return prices[good] or 5
end

-- Wirtschaft aktualisieren (wird jeden Frame aufgerufen)
function EconomyManager:Update(deltaTime)
    if not self.GlobalEconomy.cities then return end
    
    self.GlobalEconomy.gameTime = self.GlobalEconomy.gameTime + deltaTime
    
    -- Jeden Spieltag (1 Sekunde) aktualisieren
    if self.GlobalEconomy.gameTime >= 1 then
        self:UpdateDaily()
        self.GlobalEconomy.gameTime = 0
    end
end

-- Tägliche Wirtschafts-Updates
function EconomyManager:UpdateDaily()
    -- Industrie-Produktion
    self:UpdateIndustryProduction()
    
    -- Stadt-Nachfrage
    self:UpdateCityDemand()
    
    -- Monatliche Updates
    if self.GlobalEconomy.gameTime % 30 == 0 then -- Alle 30 Tage = 1 Monat
        self:UpdateMonthly()
    end
end

-- Industrie-Produktion aktualisieren
function EconomyManager:UpdateIndustryProduction()
    for _, industry in pairs(self.GlobalEconomy.industries) do
        -- Prüfen ob alle benötigten Rohstoffe vorhanden sind
        local canProduce = true
        for _, good in pairs(industry.consumes) do
            if (industry.stockpile[good] or 0) < 1 then
                canProduce = false
                break
            end
        end
        
        if canProduce or #industry.consumes == 0 then
            -- Rohstoffe verbrauchen
            for _, good in pairs(industry.consumes) do
                industry.stockpile[good] = math.max(0, (industry.stockpile[good] or 0) - 1)
            end
            
            -- Waren produzieren
            for _, good in pairs(industry.produces) do
                local production = industry.productionRate * industry.efficiency / 30 -- Pro Tag
                industry.stockpile[good] = (industry.stockpile[good] or 0) + production
            end
        end
    end
end

-- Stadt-Nachfrage aktualisieren
function EconomyManager:UpdateCityDemand()
    for _, city in pairs(self.GlobalEconomy.cities) do
        local totalSatisfaction = 0
        local demandCount = 0
        
        for good, demand in pairs(city.demands) do
            -- Nachfrage basierend auf Bevölkerung
            demand.amount = math.floor(city.population * 0.1)
            
            -- Zufriedenheit berechnen
            local satisfaction = math.min(1, city.supply[good] / demand.amount)
            demand.satisfied = satisfaction
            totalSatisfaction = totalSatisfaction + satisfaction
            demandCount = demandCount + 1
            
            -- Versorgung reduzieren (Verbrauch)
            city.supply[good] = math.max(0, city.supply[good] - demand.amount * 0.1)
        end
        
        -- Gesamt-Zufriedenheit der Stadt
        city.satisfaction = demandCount > 0 and (totalSatisfaction / demandCount) or 0.5
    end
end

-- Monatliche Updates
function EconomyManager:UpdateMonthly()
    self.GlobalEconomy.currentMonth = self.GlobalEconomy.currentMonth + 1
    if self.GlobalEconomy.currentMonth > 12 then
        self.GlobalEconomy.currentMonth = 1
        self.GlobalEconomy.currentYear = self.GlobalEconomy.currentYear + 1
    end
    
    -- Stadt-Wachstum
    for _, city in pairs(self.GlobalEconomy.cities) do
        if city.satisfaction > 0.7 then
            local growth = city.growth * city.satisfaction
            city.population = math.floor(city.population * (1 + growth))
        end
    end
    
    -- Spieler-Ausgaben (Wartung)
    for playerId, playerData in pairs(self.PlayerData) do
        local monthlyExpenses = 0
        
        -- Fahrzeug-Wartung
        for _, vehicle in pairs(playerData.vehicles) do
            monthlyExpenses = monthlyExpenses + (vehicle.maintenance or 0)
        end
        
        -- Infrastruktur-Wartung
        for _, railway in pairs(playerData.infrastructure.railways) do
            monthlyExpenses = monthlyExpenses + (railway.maintenance or 0)
        end
        
        for _, road in pairs(playerData.infrastructure.roads) do
            monthlyExpenses = monthlyExpenses + (road.maintenance or 0)
        end
        
        for _, station in pairs(playerData.infrastructure.stations) do
            monthlyExpenses = monthlyExpenses + (station.maintenance or 0)
        end
        
        -- Geld abziehen
        playerData.money = playerData.money - monthlyExpenses
        playerData.monthlyExpenses = monthlyExpenses
        playerData.totalExpenses = playerData.totalExpenses + monthlyExpenses
        
        print("💸 Monatliche Ausgaben für Spieler " .. playerId .. ":", monthlyExpenses)
    end
    
    print("📅 Neuer Monat:", self.GlobalEconomy.currentMonth .. "/" .. self.GlobalEconomy.currentYear)
end

-- Waren liefern
function EconomyManager:DeliverGoods(fromId, toId, good, amount, playerId)
    local playerData = self.PlayerData[tostring(playerId)]
    if not playerData then return false end
    
    local delivered = false
    local income = 0
    
    -- An Stadt liefern
    local city = self.GlobalEconomy.cities[toId]
    if city and city.demands[good] then
        local needed = city.demands[good].amount - city.supply[good]
        local actualAmount = math.min(amount, needed)
        
        if actualAmount > 0 then
            city.supply[good] = city.supply[good] + actualAmount
            income = actualAmount * city.demands[good].price
            delivered = true
            
            -- Statistiken aktualisieren
            if good == "Passengers" then
                playerData.statistics.passengersTransported = playerData.statistics.passengersTransported + actualAmount
            else
                playerData.statistics.cargoTransported = playerData.statistics.cargoTransported + actualAmount
            end
        end
    end
    
    -- Von Industrie abholen
    local industry = self.GlobalEconomy.industries[fromId]
    if industry and industry.stockpile[good] then
        local available = industry.stockpile[good]
        local actualAmount = math.min(amount, available)
        
        if actualAmount > 0 then
            industry.stockpile[good] = industry.stockpile[good] - actualAmount
            delivered = true
        end
    end
    
    if delivered and income > 0 then
        playerData.money = playerData.money + income
        playerData.monthlyIncome = playerData.monthlyIncome + income
        playerData.totalIncome = playerData.totalIncome + income
    end
    
    return delivered
end

-- Fahrzeug kaufen
function EconomyManager:BuyVehicle(playerId, vehicleType, routeId)
    local playerData = self.PlayerData[tostring(playerId)]
    if not playerData then return false end
    
    local vehicleConfig = GameConfig.Transport.Vehicles[vehicleType]
    if not vehicleConfig then return false end
    
    -- Prüfen ob verfügbar (Jahr)
    if self.GlobalEconomy.currentYear < vehicleConfig.availableFrom then
        return false, "Fahrzeug noch nicht verfügbar"
    end
    
    -- Prüfen ob genug Geld
    if playerData.money < vehicleConfig.cost then
        return false, "Nicht genug Geld"
    end
    
    -- Fahrzeug kaufen
    local vehicleId = "vehicle_" .. #playerData.vehicles + 1
    local vehicle = {
        id = vehicleId,
        type = vehicleType,
        name = vehicleConfig.name,
        routeId = routeId,
        speed = vehicleConfig.speed,
        capacity = vehicleConfig.capacity,
        maintenance = vehicleConfig.maintenance,
        fuelType = vehicleConfig.fuelType,
        position = {x = 0, z = 0},
        cargo = {},
        status = "idle"
    }
    
    table.insert(playerData.vehicles, vehicle)
    playerData.money = playerData.money - vehicleConfig.cost
    
    return true, vehicleId
end

-- Wirtschaftsdaten für Client abrufen
function EconomyManager:GetEconomyData(playerId)
    local playerData = self.PlayerData[tostring(playerId)]
    if not playerData then return nil end
    
    return {
        player = playerData,
        global = {
            cities = self.GlobalEconomy.cities,
            industries = self.GlobalEconomy.industries,
            currentYear = self.GlobalEconomy.currentYear,
            currentMonth = self.GlobalEconomy.currentMonth
        }
    }
end

-- Update-Funktion
function EconomyManager:Update(deltaTime)
    -- Zeit voranschreiten lassen
    self:UpdateTime(deltaTime)

    -- Städte aktualisieren
    for cityId, city in pairs(self.GlobalEconomy.cities) do
        self:UpdateCity(city, deltaTime)
    end

    -- Industrien aktualisieren
    for industryId, industry in pairs(self.GlobalEconomy.industries) do
        self:UpdateIndustry(industry, deltaTime)
    end

    -- Preise basierend auf Angebot/Nachfrage anpassen
    self:UpdatePrices()
end

-- Zeit-System
function EconomyManager:UpdateTime(deltaTime)
    if not self.GlobalEconomy.currentDay then return end

    self.GlobalEconomy.currentDay = self.GlobalEconomy.currentDay + deltaTime * self.GlobalEconomy.timeScale

    if self.GlobalEconomy.currentDay >= 30 then
        self.GlobalEconomy.currentDay = 0
        self.GlobalEconomy.currentMonth = self.GlobalEconomy.currentMonth + 1

        if self.GlobalEconomy.currentMonth > 12 then
            self.GlobalEconomy.currentMonth = 1
            self.GlobalEconomy.currentYear = self.GlobalEconomy.currentYear + 1
            print("📅 Neues Jahr:", self.GlobalEconomy.currentYear)
        end
    end
end

-- Stadt aktualisieren
function EconomyManager:UpdateCity(city, deltaTime)
    if not city or not city.population then return end

    -- Bevölkerungswachstum
    local growthFactor = 1 + (city.growthRate * deltaTime / 30) -- Pro Monat
    city.population = math.floor(city.population * growthFactor)

    -- Nachfrage basierend auf Bevölkerung
    city.demand.passengers = city.population * 0.3
    city.demand.food = city.population * 0.1
    city.demand.goods = city.population * 0.05

    -- Stadt-Label aktualisieren
    if city.billboard and city.billboard.TextLabel then
        city.billboard.TextLabel.Text = city.name .. "\nPop: " .. city.population
    end
end

-- Aktuelles Jahr abrufen
function EconomyManager:GetCurrentYear()
    return self.GlobalEconomy.currentYear or 1850
end

-- Initialisierung
function EconomyManager:Initialize()
    print("💰 EconomyManager initialisiert")
end

return EconomyManager
