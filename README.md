# Transport Empire - Roblox Game

Ein komplettes Transport-Tycoon-Spiel für Roblox, inspiriert von Transport Fever 2. Baue dein Transportimperium auf, verwalte Städte und Industrien, und erschaffe ein florierendes Wirtschaftssystem!

## Projektstruktur

```
Transport Empire/
├── ServerScriptService/          # Server-seitige Skripte
│   ├── Core/                     # Kern-Systeme
│   ├── Managers/                 # Manager-Skripte
│   └── Services/                 # Service-Skripte
├── ReplicatedStorage/            # Geteilte Ressourcen
│   ├── Modules/                  # Geteilte Module
│   ├── Events/                   # RemoteEvents/Functions
│   └── Assets/                   # Asset-Konfigurationen
├── StarterGui/                   # Client-GUI
│   ├── MainMenu/                 # Hauptmenü
│   ├── GameUI/                   # Ingame-Interface
│   └── Shared/                   # Geteilte GUI-Komponenten
├── Workspace/                    # 3D-Welt
│   ├── Terrain/                  # Terrain-Objekte
│   ├── Buildings/                # Gebäude
│   └── Vehicles/                 # Fahrzeuge
└── StarterPlayer/                # Spieler-Konfiguration
    └── StarterPlayerScripts/     # Client-seitige Skripte
```

## 🎮 Spielfeatures

### Kernfunktionen

- **Prozedural generierte Welten** mit verschiedenen Biomen (Europäisch, Amerikanisch, Asiatisch)
- **Komplettes Wirtschaftssystem** mit Städten, Industrien und Warenkreisläufen
- **Umfassendes Transportsystem** mit Zügen, LKWs, Schiffen und Bussen
- **Zeitbasierte Simulation** von 1850 bis 2000 mit technologischem Fortschritt
- **Vollständiges GUI-System** für intuitive Spielsteuerung
- **Speicher-/Ladesystem** mit mehreren Speicherslots und Auto-Save

### Transportmittel

- **Züge**: Dampf-, Diesel- und Elektrolokomotiven mit verschiedenen Waggons
- **LKWs**: Vom frühen Lieferwagen bis zum modernen Sattelschlepper
- **Schiffe**: Dampfschiffe, Motorschiffe und Containerschiffe
- **Busse**: Für Personentransport in Städten

### Wirtschaftssystem

- **Dynamische Städte** die wachsen und sich entwickeln
- **Vielfältige Industrien** mit realistischen Produktionsketten
- **Angebot und Nachfrage** bestimmen Preise und Gewinne
- **Saisonale Schwankungen** und wirtschaftliche Ereignisse

## Installation

1. Erstelle ein neues Roblox Studio Projekt
2. Kopiere alle Dateien in die entsprechenden Ordner
3. Starte das Spiel

## Technische Details

- Lua/Luau Scripting
- Client-Server Architektur
- ModuleScript System
- RemoteEvents für Kommunikation
