-- ServerScriptService/VisualEffectsManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Visual Effects System für Partikel, Beleuchtung und atmosphärische Effekte

local TweenService = game:GetService("TweenService")
local Lighting = game:GetService("Lighting")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local HttpService = game:GetService("HttpService")

local VisualEffectsManager = {}
VisualEffectsManager.__index = VisualEffectsManager

function VisualEffectsManager.new()
    local self = setmetatable({}, VisualEffectsManager)
    
    -- Effect categories
    self.effectCategories = {
        particles = {},
        lighting = {},
        atmospheric = {},
        ui = {},
        transport = {}
    }
    
    -- Active effects tracking
    self.activeEffects = {}
    self.effectInstances = {}
    
    -- Particle effect templates
    self.particleEffects = {
        -- Transport effects
        train_steam = {
            type = "Smoke",
            color = ColorSequence.new(Color3.fromRGB(240, 240, 240)),
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.5),
                NumberSequenceKeypoint.new(0.5, 1.2),
                NumberSequenceKeypoint.new(1, 0.8)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.3),
                NumberSequenceKeypoint.new(0.7, 0.6),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(2, 4),
            rate = 50,
            speed = NumberRange.new(5, 10)
        },
        
        truck_exhaust = {
            type = "Smoke",
            color = ColorSequence.new(Color3.fromRGB(100, 100, 100)),
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.3),
                NumberSequenceKeypoint.new(0.5, 0.8),
                NumberSequenceKeypoint.new(1, 0.5)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.4),
                NumberSequenceKeypoint.new(0.8, 0.8),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(1, 2),
            rate = 30,
            speed = NumberRange.new(3, 6)
        },
        
        ship_wake = {
            type = "Splash",
            color = ColorSequence.new(Color3.fromRGB(255, 255, 255)),
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.8),
                NumberSequenceKeypoint.new(0.3, 1.5),
                NumberSequenceKeypoint.new(1, 0.2)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.2),
                NumberSequenceKeypoint.new(0.5, 0.5),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(1.5, 3),
            rate = 25,
            speed = NumberRange.new(2, 5)
        },
        
        -- Construction effects
        construction_dust = {
            type = "Smoke",
            color = ColorSequence.new(Color3.fromRGB(200, 180, 150)),
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.4),
                NumberSequenceKeypoint.new(0.5, 1.0),
                NumberSequenceKeypoint.new(1, 0.6)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.5),
                NumberSequenceKeypoint.new(0.6, 0.7),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(2, 5),
            rate = 40,
            speed = NumberRange.new(1, 4)
        },
        
        construction_sparks = {
            type = "Fire",
            color = ColorSequence.new{
                ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 200, 100)),
                ColorSequenceKeypoint.new(0.5, Color3.fromRGB(255, 150, 50)),
                ColorSequenceKeypoint.new(1, Color3.fromRGB(200, 100, 50))
            },
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.2),
                NumberSequenceKeypoint.new(0.3, 0.4),
                NumberSequenceKeypoint.new(1, 0.1)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.1),
                NumberSequenceKeypoint.new(0.8, 0.6),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(0.5, 1.5),
            rate = 80,
            speed = NumberRange.new(8, 15)
        },
        
        -- Weather effects
        rain_drops = {
            type = "Rain",
            color = ColorSequence.new(Color3.fromRGB(200, 220, 255)),
            size = NumberSequence.new(0.1),
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.3),
                NumberSequenceKeypoint.new(1, 0.8)
            },
            lifetime = NumberRange.new(1, 2),
            rate = 200,
            speed = NumberRange.new(20, 30)
        },
        
        snow_flakes = {
            type = "Snow",
            color = ColorSequence.new(Color3.fromRGB(255, 255, 255)),
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.2),
                NumberSequenceKeypoint.new(0.5, 0.3),
                NumberSequenceKeypoint.new(1, 0.1)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.2),
                NumberSequenceKeypoint.new(1, 0.9)
            },
            lifetime = NumberRange.new(5, 10),
            rate = 100,
            speed = NumberRange.new(2, 5)
        },
        
        -- Economic effects
        money_sparkle = {
            type = "Sparkle",
            color = ColorSequence.new{
                ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 215, 0)),
                ColorSequenceKeypoint.new(0.5, Color3.fromRGB(255, 255, 100)),
                ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 215, 0))
            },
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.1),
                NumberSequenceKeypoint.new(0.5, 0.3),
                NumberSequenceKeypoint.new(1, 0.1)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.2),
                NumberSequenceKeypoint.new(0.8, 0.5),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(1, 2),
            rate = 20,
            speed = NumberRange.new(3, 8)
        },
        
        -- Achievement effects
        achievement_burst = {
            type = "Explosion",
            color = ColorSequence.new{
                ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 100)),
                ColorSequenceKeypoint.new(0.3, Color3.fromRGB(255, 200, 50)),
                ColorSequenceKeypoint.new(0.7, Color3.fromRGB(255, 150, 100)),
                ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 100, 50))
            },
            size = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.5),
                NumberSequenceKeypoint.new(0.2, 2.0),
                NumberSequenceKeypoint.new(1, 0.1)
            },
            transparency = NumberSequence.new{
                NumberSequenceKeypoint.new(0, 0.1),
                NumberSequenceKeypoint.new(0.5, 0.3),
                NumberSequenceKeypoint.new(1, 1)
            },
            lifetime = NumberRange.new(0.8, 1.5),
            rate = 150,
            speed = NumberRange.new(10, 25)
        }
    }
    
    -- Lighting presets
    self.lightingPresets = {
        dawn = {
            ambient = Color3.fromRGB(100, 120, 150),
            brightness = 1.5,
            colorShift_Bottom = Color3.fromRGB(255, 200, 150),
            colorShift_Top = Color3.fromRGB(200, 220, 255),
            outdoorAmbient = Color3.fromRGB(120, 140, 180),
            shadowSoftness = 0.3,
            clockTime = 6
        },
        
        day = {
            ambient = Color3.fromRGB(140, 140, 140),
            brightness = 2.5,
            colorShift_Bottom = Color3.fromRGB(255, 255, 255),
            colorShift_Top = Color3.fromRGB(200, 220, 255),
            outdoorAmbient = Color3.fromRGB(180, 180, 180),
            shadowSoftness = 0.2,
            clockTime = 12
        },
        
        dusk = {
            ambient = Color3.fromRGB(120, 100, 80),
            brightness = 1.2,
            colorShift_Bottom = Color3.fromRGB(255, 150, 100),
            colorShift_Top = Color3.fromRGB(150, 100, 200),
            outdoorAmbient = Color3.fromRGB(100, 80, 120),
            shadowSoftness = 0.4,
            clockTime = 18
        },
        
        night = {
            ambient = Color3.fromRGB(50, 60, 80),
            brightness = 0.8,
            colorShift_Bottom = Color3.fromRGB(100, 120, 150),
            colorShift_Top = Color3.fromRGB(50, 80, 120),
            outdoorAmbient = Color3.fromRGB(60, 70, 100),
            shadowSoftness = 0.6,
            clockTime = 0
        },
        
        storm = {
            ambient = Color3.fromRGB(60, 60, 70),
            brightness = 0.5,
            colorShift_Bottom = Color3.fromRGB(80, 80, 90),
            colorShift_Top = Color3.fromRGB(40, 40, 50),
            outdoorAmbient = Color3.fromRGB(50, 50, 60),
            shadowSoftness = 0.8,
            clockTime = 12
        }
    }
    
    -- Current lighting state
    self.currentLighting = "day"
    self.lightingTransition = nil
    
    -- Weather system
    self.currentWeather = "clear"
    self.weatherEffects = {}
    
    return self
end

-- Initialize visual effects system
function VisualEffectsManager:Initialize()
    -- Set initial lighting
    self:ApplyLightingPreset("day", false)
    
    -- Initialize atmospheric effects
    self:InitializeAtmosphericEffects()
    
    print("✨ Visual Effects system initialized")
end

-- Create particle effect
function VisualEffectsManager:CreateParticleEffect(effectName, position, parent, duration)
    local effectData = self.particleEffects[effectName]
    if not effectData then
        warn("Particle effect not found:", effectName)
        return nil
    end
    
    -- Create attachment point
    local attachment = Instance.new("Attachment")
    if parent then
        attachment.Parent = parent
        if position then
            attachment.Position = position
        end
    else
        -- Create invisible part for world position
        local part = Instance.new("Part")
        part.Name = "EffectAnchor"
        part.Size = Vector3.new(0.1, 0.1, 0.1)
        part.Position = position or Vector3.new(0, 0, 0)
        part.Anchored = true
        part.CanCollide = false
        part.Transparency = 1
        part.Parent = workspace
        
        attachment.Parent = part
        
        -- Clean up part after effect
        if duration then
            game:GetService("Debris"):AddItem(part, duration + 2)
        end
    end
    
    -- Create particle emitter
    local emitter = Instance.new("ParticleEmitter")
    emitter.Parent = attachment
    
    -- Apply effect properties
    if effectData.color then emitter.Color = effectData.color end
    if effectData.size then emitter.Size = effectData.size end
    if effectData.transparency then emitter.Transparency = effectData.transparency end
    if effectData.lifetime then emitter.Lifetime = effectData.lifetime end
    if effectData.rate then emitter.Rate = effectData.rate end
    if effectData.speed then emitter.Speed = effectData.speed end
    
    -- Set texture based on effect type
    if effectData.type == "Smoke" then
        emitter.Texture = "rbxasset://textures/particles/smoke_main.dds"
    elseif effectData.type == "Fire" then
        emitter.Texture = "rbxasset://textures/particles/fire_main.dds"
    elseif effectData.type == "Sparkle" then
        emitter.Texture = "rbxasset://textures/particles/sparkles_main.dds"
    elseif effectData.type == "Rain" then
        emitter.Texture = "rbxasset://textures/particles/water_splash.dds"
        emitter.Shape = Enum.ParticleEmitterShape.Box
        emitter.ShapeInOut = Enum.ParticleEmitterShapeInOut.Outward
    elseif effectData.type == "Snow" then
        emitter.Texture = "rbxasset://textures/particles/snow_main.dds"
        emitter.Shape = Enum.ParticleEmitterShape.Box
        emitter.ShapeInOut = Enum.ParticleEmitterShapeInOut.Outward
    end
    
    -- Auto-disable after duration
    if duration then
        spawn(function()
            wait(duration)
            emitter.Enabled = false
            
            -- Clean up after particles finish
            wait(effectData.lifetime and effectData.lifetime.Max or 5)
            if emitter.Parent then
                emitter:Destroy()
            end
        end)
    end
    
    -- Track active effect
    local effectId = HttpService:GenerateGUID(false)
    self.activeEffects[effectId] = {
        emitter = emitter,
        attachment = attachment,
        effectName = effectName,
        startTime = tick(),
        duration = duration
    }
    
    return emitter, effectId
end

-- Apply lighting preset
function VisualEffectsManager:ApplyLightingPreset(presetName, animate)
    local preset = self.lightingPresets[presetName]
    if not preset then
        warn("Lighting preset not found:", presetName)
        return
    end
    
    if animate then
        -- Smooth transition
        local tweenInfo = TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
        
        local lightingTween = TweenService:Create(Lighting, tweenInfo, {
            Ambient = preset.ambient,
            Brightness = preset.brightness,
            ColorShift_Bottom = preset.colorShift_Bottom,
            ColorShift_Top = preset.colorShift_Top,
            OutdoorAmbient = preset.outdoorAmbient,
            ShadowSoftness = preset.shadowSoftness,
            ClockTime = preset.clockTime
        })
        
        lightingTween:Play()
        self.lightingTransition = lightingTween
    else
        -- Immediate change
        Lighting.Ambient = preset.ambient
        Lighting.Brightness = preset.brightness
        Lighting.ColorShift_Bottom = preset.colorShift_Bottom
        Lighting.ColorShift_Top = preset.colorShift_Top
        Lighting.OutdoorAmbient = preset.outdoorAmbient
        Lighting.ShadowSoftness = preset.shadowSoftness
        Lighting.ClockTime = preset.clockTime
    end
    
    self.currentLighting = presetName
    print("🌅 Applied lighting preset:", presetName)
end

-- Create transport effect
function VisualEffectsManager:CreateTransportEffect(vehicleType, effectType, vehicle, duration)
    local effectName = vehicleType .. "_" .. effectType
    local position = Vector3.new(0, 0, 0) -- Relative to vehicle
    
    return self:CreateParticleEffect(effectName, position, vehicle, duration)
end

-- Create construction effect
function VisualEffectsManager:CreateConstructionEffect(constructionType, position, duration)
    local effectName = "construction_" .. constructionType
    return self:CreateParticleEffect(effectName, position, nil, duration or 5)
end

-- Create weather effect
function VisualEffectsManager:CreateWeatherEffect(weatherType, intensity)
    local effectName = weatherType .. "_drops"
    if weatherType == "snow" then
        effectName = "snow_flakes"
    end
    
    -- Create weather effect over large area
    local weatherPart = Instance.new("Part")
    weatherPart.Name = "WeatherEffect"
    weatherPart.Size = Vector3.new(1000, 1, 1000)
    weatherPart.Position = Vector3.new(0, 200, 0)
    weatherPart.Anchored = true
    weatherPart.CanCollide = false
    weatherPart.Transparency = 1
    weatherPart.Parent = workspace
    
    local emitter, effectId = self:CreateParticleEffect(effectName, Vector3.new(0, 0, 0), weatherPart)
    
    if emitter then
        -- Adjust intensity
        emitter.Rate = emitter.Rate * intensity
        
        -- Set emission shape for weather
        emitter.Shape = Enum.ParticleEmitterShape.Box
        emitter.ShapeInOut = Enum.ParticleEmitterShapeInOut.Outward
        emitter.ShapePartial = 1
        
        -- Set emission area
        local attachment = emitter.Parent
        attachment.Position = Vector3.new(0, 0, 0)
    end
    
    self.weatherEffects[weatherType] = {
        emitter = emitter,
        part = weatherPart,
        effectId = effectId,
        intensity = intensity
    }
    
    return emitter, effectId
end

-- Create economic effect
function VisualEffectsManager:CreateEconomicEffect(effectType, position, amount)
    local effectName = effectType
    local duration = 2
    
    -- Adjust effect based on amount
    local emitter, effectId = self:CreateParticleEffect(effectName, position, nil, duration)
    
    if emitter and amount then
        -- Scale effect intensity based on amount
        local scale = math.min(3, 1 + (amount / 100000))
        emitter.Rate = emitter.Rate * scale
    end
    
    return emitter, effectId
end

-- Create achievement effect
function VisualEffectsManager:CreateAchievementEffect(position)
    return self:CreateParticleEffect("achievement_burst", position, nil, 3)
end

-- Initialize atmospheric effects
function VisualEffectsManager:InitializeAtmosphericEffects()
    -- Add atmospheric fog
    local atmosphere = Instance.new("Atmosphere")
    atmosphere.Density = 0.3
    atmosphere.Offset = 0.25
    atmosphere.Color = Color3.fromRGB(199, 199, 199)
    atmosphere.Decay = Color3.fromRGB(92, 60, 13)
    atmosphere.Glare = 0.4
    atmosphere.Haze = 1.8
    atmosphere.Parent = Lighting
    
    -- Add bloom effect
    local bloom = Instance.new("BloomEffect")
    bloom.Intensity = 0.4
    bloom.Size = 24
    bloom.Threshold = 1.2
    bloom.Parent = Lighting
    
    -- Add color correction
    local colorCorrection = Instance.new("ColorCorrectionEffect")
    colorCorrection.Brightness = 0.05
    colorCorrection.Contrast = 0.1
    colorCorrection.Saturation = 0.1
    colorCorrection.TintColor = Color3.fromRGB(255, 255, 255)
    colorCorrection.Parent = Lighting
    
    print("🌫️ Atmospheric effects initialized")
end

-- Update time of day
function VisualEffectsManager:UpdateTimeOfDay(timeOfDay)
    local preset = "day"
    
    if timeOfDay >= 5 and timeOfDay < 7 then
        preset = "dawn"
    elseif timeOfDay >= 7 and timeOfDay < 17 then
        preset = "day"
    elseif timeOfDay >= 17 and timeOfDay < 19 then
        preset = "dusk"
    else
        preset = "night"
    end
    
    if preset ~= self.currentLighting then
        self:ApplyLightingPreset(preset, true)
    end
end

-- Set weather
function VisualEffectsManager:SetWeather(weatherType, intensity)
    -- Clear existing weather effects
    self:ClearWeatherEffects()
    
    if weatherType ~= "clear" then
        self:CreateWeatherEffect(weatherType, intensity or 1.0)
        
        -- Apply weather lighting
        if weatherType == "storm" or weatherType == "rain" then
            self:ApplyLightingPreset("storm", true)
        end
    end
    
    self.currentWeather = weatherType
    print("🌦️ Weather set to:", weatherType, "intensity:", intensity or 1.0)
end

-- Clear weather effects
function VisualEffectsManager:ClearWeatherEffects()
    for weatherType, effect in pairs(self.weatherEffects) do
        if effect.emitter then
            effect.emitter.Enabled = false
        end
        if effect.part then
            effect.part:Destroy()
        end
        if effect.effectId then
            self.activeEffects[effect.effectId] = nil
        end
    end
    self.weatherEffects = {}
end

-- Stop effect
function VisualEffectsManager:StopEffect(effectId)
    local effect = self.activeEffects[effectId]
    if effect and effect.emitter then
        effect.emitter.Enabled = false
        
        -- Clean up after particles finish
        spawn(function()
            wait(5) -- Wait for particles to finish
            if effect.emitter.Parent then
                effect.emitter:Destroy()
            end
        end)
        
        self.activeEffects[effectId] = nil
    end
end

-- Clean up finished effects
function VisualEffectsManager:CleanupEffects()
    local currentTime = tick()
    
    for effectId, effect in pairs(self.activeEffects) do
        if effect.duration and currentTime - effect.startTime > effect.duration + 10 then
            -- Effect should have finished, clean up
            if effect.emitter and effect.emitter.Parent then
                effect.emitter:Destroy()
            end
            self.activeEffects[effectId] = nil
        elseif not effect.emitter or not effect.emitter.Parent then
            -- Effect was destroyed externally
            self.activeEffects[effectId] = nil
        end
    end
end

-- Update visual effects system
function VisualEffectsManager:Update(deltaTime, gameState)
    -- Update time of day lighting
    if gameState.timeOfDay then
        self:UpdateTimeOfDay(gameState.timeOfDay)
    end

    -- Update weather based on game state
    if gameState.weather and gameState.weather ~= self.currentWeather then
        self:SetWeather(gameState.weather, gameState.weatherIntensity)
    end

    -- Update temporary effects
    self:UpdateTemporaryEffects()

    -- Clean up finished effects
    self:CleanupEffects()
end

-- Get visual effects statistics
function VisualEffectsManager:GetEffectsStats()
    local activeCount = 0
    for _ in pairs(self.activeEffects) do
        activeCount = activeCount + 1
    end

    local weatherCount = 0
    for _ in pairs(self.weatherEffects) do
        weatherCount = weatherCount + 1
    end

    return {
        activeEffects = activeCount,
        currentLighting = self.currentLighting,
        currentWeather = self.currentWeather,
        weatherEffects = weatherCount
    }
end

-- Create vehicle movement effects
function VisualEffectsManager:CreateVehicleEffects(vehicle, vehicleType)
    if not vehicle or not vehicle.Parent then return end

    local effectName = nil
    if vehicleType == "train" then
        effectName = "steam"
    elseif vehicleType == "truck" or vehicleType == "car" then
        effectName = "exhaust"
    elseif vehicleType == "ship" then
        effectName = "wake"
    end

    if effectName then
        local emitter, effectId = self:CreateParticleEffect(effectName, vehicle.Position, vehicle, 0)
        if emitter and effectId then
            -- Store effect with vehicle for cleanup
            if not self.vehicleEffects then
                self.vehicleEffects = {}
            end
            if not self.vehicleEffects[vehicle] then
                self.vehicleEffects[vehicle] = {}
            end
            table.insert(self.vehicleEffects[vehicle], effectId)

            -- Auto-cleanup when vehicle is removed
            vehicle.AncestryChanged:Connect(function()
                if not vehicle.Parent then
                    self:CleanupVehicleEffects(vehicle)
                end
            end)
        end
    end
end

-- Cleanup vehicle effects
function VisualEffectsManager:CleanupVehicleEffects(vehicle)
    if self.vehicleEffects and self.vehicleEffects[vehicle] then
        for _, effectId in pairs(self.vehicleEffects[vehicle]) do
            self:StopEffect(effectId)
        end
        self.vehicleEffects[vehicle] = nil
    end
end

-- Create construction effects
function VisualEffectsManager:CreateConstructionEffects(position, constructionType)
    local effectName = constructionType == "building" and "dust" or "sparks"
    local duration = 5 -- 5 seconds

    local emitter, effectId = self:CreateParticleEffect(effectName, position, workspace, duration)
    if emitter and effectId then
        -- Add to temporary effects for cleanup
        if not self.temporaryEffects then
            self.temporaryEffects = {}
        end
        table.insert(self.temporaryEffects, {
            id = effectId,
            endTime = tick() + duration
        })
    end
end

-- Create floating text effect
function VisualEffectsManager:CreateFloatingText(position, text, color)
    local part = Instance.new("Part")
    part.Name = "FloatingText"
    part.Size = Vector3.new(1, 1, 1)
    part.Position = position + Vector3.new(0, 5, 0)
    part.Anchored = true
    part.CanCollide = false
    part.Transparency = 1
    part.Parent = workspace

    local billboardGui = Instance.new("BillboardGui")
    billboardGui.Size = UDim2.new(0, 200, 0, 50)
    billboardGui.StudsOffset = Vector3.new(0, 0, 0)
    billboardGui.Parent = part

    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = text
    textLabel.TextColor3 = color
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.TextStrokeTransparency = 0
    textLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
    textLabel.Parent = billboardGui

    -- Animate floating text
    local TweenService = game:GetService("TweenService")
    local tween = TweenService:Create(part,
        TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {Position = position + Vector3.new(0, 15, 0)}
    )

    local fadeTween = TweenService:Create(textLabel,
        TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {TextTransparency = 1}
    )

    tween:Play()
    fadeTween:Play()

    -- Cleanup
    game:GetService("Debris"):AddItem(part, 2)
end

-- Format money for display
function VisualEffectsManager:FormatMoney(amount)
    if amount >= 1000000 then
        return string.format("%.1fM", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.1fK", amount / 1000)
    else
        return tostring(amount)
    end
end

-- Update temporary effects
function VisualEffectsManager:UpdateTemporaryEffects()
    if not self.temporaryEffects then return end

    local currentTime = tick()
    local toRemove = {}

    for i, effect in pairs(self.temporaryEffects) do
        if currentTime >= effect.endTime then
            self:StopEffect(effect.id)
            table.insert(toRemove, i)
        end
    end

    -- Remove expired effects from list
    for i = #toRemove, 1, -1 do
        table.remove(self.temporaryEffects, toRemove[i])
    end
end

return VisualEffectsManager
