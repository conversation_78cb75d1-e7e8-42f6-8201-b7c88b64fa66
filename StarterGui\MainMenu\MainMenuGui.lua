-- StarterGui/MainMenu/MainMenuGui.lua
-- Hauptmenü-GUI für Transport Empire

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

-- <PERSON>ten auf Module
wait(1) -- <PERSON>rz warten bis alles geladen ist
local GameConfig = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("GameConfig"))

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events (werden später erstellt, falls nicht vorhanden)
local Events = ReplicatedStorage:FindFirstChild("Events")
if not Events then
    Events = Instance.new("Folder")
    Events.Name = "Events"
    Events.Parent = ReplicatedStorage
end

local GenerateMapEvent = Events:FindFirstChild("GenerateMapEvent")
if not GenerateMapEvent then
    GenerateMapEvent = Instance.new("RemoteEvent")
    GenerateMapEvent.Name = "GenerateMapEvent"
    GenerateMapEvent.Parent = Events
end

local LoadGameEvent = Events:FindFirstChild("LoadGameEvent")
if not LoadGameEvent then
    LoadGameEvent = Instance.new("RemoteEvent")
    LoadGameEvent.Name = "LoadGameEvent"
    LoadGameEvent.Parent = Events
end

local MainMenuGui = {}
MainMenuGui.CurrentScreen = "main"
MainMenuGui.MapConfig = {
    seed = "",
    mapSize = "Medium",
    mapType = "European",
    hilliness = 0.6,
    waterAmount = 0.3,
    startYear = 1850
}

-- GUI erstellen
function MainMenuGui:CreateGui()
    -- Haupt-ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainMenuGui"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hintergrund
    local background = Instance.new("Frame")
    background.Name = "Background"
    background.Size = UDim2.new(1, 0, 1, 0)
    background.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    background.BorderSizePixel = 0
    background.Parent = screenGui
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(0, 400, 0, 80)
    title.Position = UDim2.new(0.5, -200, 0.1, 0)
    title.BackgroundTransparency = 1
    title.Text = "TRANSPORT EMPIRE"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Title
    title.Parent = background
    
    -- Hauptmenü-Container
    local mainMenu = Instance.new("Frame")
    mainMenu.Name = "MainMenu"
    mainMenu.Size = UDim2.new(0, 300, 0, 400)
    mainMenu.Position = UDim2.new(0.5, -150, 0.5, -200)
    mainMenu.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    mainMenu.BorderSizePixel = 0
    mainMenu.Parent = background
    
    -- Ecken abrunden
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = mainMenu
    
    -- Buttons erstellen
    self:CreateMainMenuButtons(mainMenu)
    
    -- Neues Spiel-Menü
    local newGameMenu = self:CreateNewGameMenu(background)
    newGameMenu.Visible = false
    
    -- Spiel laden-Menü
    local loadGameMenu = self:CreateLoadGameMenu(background)
    loadGameMenu.Visible = false
    
    self.ScreenGui = screenGui
    self.MainMenu = mainMenu
    self.NewGameMenu = newGameMenu
    self.LoadGameMenu = loadGameMenu
    
    return screenGui
end

-- Hauptmenü-Buttons erstellen
function MainMenuGui:CreateMainMenuButtons(parent)
    local buttons = {
        {text = "Neues Spiel", action = "newGame"},
        {text = "Spiel laden", action = "loadGame"},
        {text = "Einstellungen", action = "settings"},
        {text = "Beenden", action = "quit"}
    }
    
    for i, buttonData in pairs(buttons) do
        local button = Instance.new("TextButton")
        button.Name = buttonData.action .. "Button"
        button.Size = UDim2.new(0.8, 0, 0, GameConfig.GUI.Sizes.ButtonHeight)
        button.Position = UDim2.new(0.1, 0, 0.2 + (i - 1) * 0.15, 0)
        button.BackgroundColor3 = GameConfig.GUI.Colors.Secondary
        button.Text = buttonData.text
        button.TextColor3 = GameConfig.GUI.Colors.Light
        button.TextScaled = true
        button.Font = GameConfig.GUI.Fonts.Body
        button.BorderSizePixel = 0
        button.Parent = parent
        
        -- Ecken abrunden
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 5)
        corner.Parent = button
        
        -- Hover-Effekt
        button.MouseEnter:Connect(function()
            local tween = TweenService:Create(button, TweenInfo.new(0.2), {
                BackgroundColor3 = GameConfig.GUI.Colors.Success
            })
            tween:Play()
        end)
        
        button.MouseLeave:Connect(function()
            local tween = TweenService:Create(button, TweenInfo.new(0.2), {
                BackgroundColor3 = GameConfig.GUI.Colors.Secondary
            })
            tween:Play()
        end)
        
        -- Click-Handler
        button.MouseButton1Click:Connect(function()
            self:HandleButtonClick(buttonData.action)
        end)
    end
end

-- Neues Spiel-Menü erstellen
function MainMenuGui:CreateNewGameMenu(parent)
    local newGameMenu = Instance.new("Frame")
    newGameMenu.Name = "NewGameMenu"
    newGameMenu.Size = UDim2.new(0, 500, 0, 600)
    newGameMenu.Position = UDim2.new(0.5, -250, 0.5, -300)
    newGameMenu.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    newGameMenu.BorderSizePixel = 0
    newGameMenu.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = newGameMenu
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.Position = UDim2.new(0, 0, 0, 10)
    title.BackgroundTransparency = 1
    title.Text = "Neues Spiel erstellen"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Subtitle
    title.Parent = newGameMenu
    
    -- Seed-Eingabe
    self:CreateInputField(newGameMenu, "Seed (optional)", UDim2.new(0, 20, 0, 80), function(text)
        self.MapConfig.seed = text
    end)
    
    -- Kartengröße
    self:CreateDropdown(newGameMenu, "Kartengröße", UDim2.new(0, 20, 0, 140), 
        {"Small", "Medium", "Large"}, "Medium", function(value)
        self.MapConfig.mapSize = value
    end)
    
    -- Kartentyp
    self:CreateDropdown(newGameMenu, "Kartentyp", UDim2.new(0, 20, 0, 200),
        {"European", "American", "Asian"}, "European", function(value)
        self.MapConfig.mapType = value
    end)
    
    -- Hügeligkeit-Slider
    self:CreateSlider(newGameMenu, "Hügeligkeit", UDim2.new(0, 20, 0, 260), 0.6, function(value)
        self.MapConfig.hilliness = value
    end)
    
    -- Wasser-Slider
    self:CreateSlider(newGameMenu, "Wassermenge", UDim2.new(0, 20, 0, 320), 0.3, function(value)
        self.MapConfig.waterAmount = value
    end)
    
    -- Startjahr
    self:CreateInputField(newGameMenu, "Startjahr", UDim2.new(0, 20, 0, 380), function(text)
        local year = tonumber(text)
        if year and year >= 1800 and year <= 2000 then
            self.MapConfig.startYear = year
        end
    end, "1850")
    
    -- Buttons
    local startButton = Instance.new("TextButton")
    startButton.Size = UDim2.new(0, 120, 0, 40)
    startButton.Position = UDim2.new(0, 20, 0, 520)
    startButton.BackgroundColor3 = GameConfig.GUI.Colors.Success
    startButton.Text = "Spiel starten"
    startButton.TextColor3 = GameConfig.GUI.Colors.Light
    startButton.TextScaled = true
    startButton.Font = GameConfig.GUI.Fonts.Body
    startButton.BorderSizePixel = 0
    startButton.Parent = newGameMenu
    
    local startCorner = Instance.new("UICorner")
    startCorner.CornerRadius = UDim.new(0, 5)
    startCorner.Parent = startButton
    
    startButton.MouseButton1Click:Connect(function()
        self:StartNewGame()
    end)
    
    local backButton = Instance.new("TextButton")
    backButton.Size = UDim2.new(0, 120, 0, 40)
    backButton.Position = UDim2.new(0, 160, 0, 520)
    backButton.BackgroundColor3 = GameConfig.GUI.Colors.Danger
    backButton.Text = "Zurück"
    backButton.TextColor3 = GameConfig.GUI.Colors.Light
    backButton.TextScaled = true
    backButton.Font = GameConfig.GUI.Fonts.Body
    backButton.BorderSizePixel = 0
    backButton.Parent = newGameMenu
    
    local backCorner = Instance.new("UICorner")
    backCorner.CornerRadius = UDim.new(0, 5)
    backCorner.Parent = backButton
    
    backButton.MouseButton1Click:Connect(function()
        self:ShowScreen("main")
    end)
    
    return newGameMenu
end

-- Spiel laden-Menü erstellen
function MainMenuGui:CreateLoadGameMenu(parent)
    local loadGameMenu = Instance.new("Frame")
    loadGameMenu.Name = "LoadGameMenu"
    loadGameMenu.Size = UDim2.new(0, 400, 0, 500)
    loadGameMenu.Position = UDim2.new(0.5, -200, 0.5, -250)
    loadGameMenu.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    loadGameMenu.BorderSizePixel = 0
    loadGameMenu.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = loadGameMenu
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "Spiel laden"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Subtitle
    title.Parent = loadGameMenu
    
    -- Speicherslot-Liste (vereinfacht)
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -40, 0, 350)
    scrollFrame.Position = UDim2.new(0, 20, 0, 70)
    scrollFrame.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 10
    scrollFrame.Parent = loadGameMenu
    
    -- Zurück-Button
    local backButton = Instance.new("TextButton")
    backButton.Size = UDim2.new(0, 120, 0, 40)
    backButton.Position = UDim2.new(0.5, -60, 0, 440)
    backButton.BackgroundColor3 = GameConfig.GUI.Colors.Danger
    backButton.Text = "Zurück"
    backButton.TextColor3 = GameConfig.GUI.Colors.Light
    backButton.TextScaled = true
    backButton.Font = GameConfig.GUI.Fonts.Body
    backButton.BorderSizePixel = 0
    backButton.Parent = loadGameMenu
    
    local backCorner = Instance.new("UICorner")
    backCorner.CornerRadius = UDim.new(0, 5)
    backCorner.Parent = backButton
    
    backButton.MouseButton1Click:Connect(function()
        self:ShowScreen("main")
    end)
    
    return loadGameMenu
end

-- Input-Feld erstellen
function MainMenuGui:CreateInputField(parent, labelText, position, callback, defaultValue)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(0, 150, 0, 30)
    label.Position = position
    label.BackgroundTransparency = 1
    label.Text = labelText
    label.TextColor3 = GameConfig.GUI.Colors.Light
    label.TextScaled = true
    label.Font = GameConfig.GUI.Fonts.Body
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = parent
    
    local textBox = Instance.new("TextBox")
    textBox.Size = UDim2.new(0, 200, 0, 30)
    textBox.Position = UDim2.new(0, 200, 0, position.Y.Offset)
    textBox.BackgroundColor3 = GameConfig.GUI.Colors.Light
    textBox.Text = defaultValue or ""
    textBox.TextColor3 = GameConfig.GUI.Colors.Dark
    textBox.TextScaled = true
    textBox.Font = GameConfig.GUI.Fonts.Body
    textBox.BorderSizePixel = 0
    textBox.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 3)
    corner.Parent = textBox
    
    textBox.FocusLost:Connect(function()
        if callback then callback(textBox.Text) end
    end)
end

-- Dropdown erstellen (vereinfacht)
function MainMenuGui:CreateDropdown(parent, labelText, position, options, defaultValue, callback)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(0, 150, 0, 30)
    label.Position = position
    label.BackgroundTransparency = 1
    label.Text = labelText
    label.TextColor3 = GameConfig.GUI.Colors.Light
    label.TextScaled = true
    label.Font = GameConfig.GUI.Fonts.Body
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = parent
    
    local dropdown = Instance.new("TextButton")
    dropdown.Size = UDim2.new(0, 200, 0, 30)
    dropdown.Position = UDim2.new(0, 200, 0, position.Y.Offset)
    dropdown.BackgroundColor3 = GameConfig.GUI.Colors.Light
    dropdown.Text = defaultValue
    dropdown.TextColor3 = GameConfig.GUI.Colors.Dark
    dropdown.TextScaled = true
    dropdown.Font = GameConfig.GUI.Fonts.Body
    dropdown.BorderSizePixel = 0
    dropdown.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 3)
    corner.Parent = dropdown
    
    -- Vereinfachte Dropdown-Logik (zyklisch durch Optionen)
    local currentIndex = 1
    for i, option in pairs(options) do
        if option == defaultValue then
            currentIndex = i
            break
        end
    end
    
    dropdown.MouseButton1Click:Connect(function()
        currentIndex = currentIndex + 1
        if currentIndex > #options then currentIndex = 1 end
        dropdown.Text = options[currentIndex]
        if callback then callback(options[currentIndex]) end
    end)
end

-- Slider erstellen
function MainMenuGui:CreateSlider(parent, labelText, position, defaultValue, callback)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(0, 150, 0, 30)
    label.Position = position
    label.BackgroundTransparency = 1
    label.Text = labelText
    label.TextColor3 = GameConfig.GUI.Colors.Light
    label.TextScaled = true
    label.Font = GameConfig.GUI.Fonts.Body
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = parent
    
    local sliderFrame = Instance.new("Frame")
    sliderFrame.Size = UDim2.new(0, 200, 0, 20)
    sliderFrame.Position = UDim2.new(0, 200, 0, position.Y.Offset + 5)
    sliderFrame.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    sliderFrame.BorderSizePixel = 0
    sliderFrame.Parent = parent
    
    local sliderButton = Instance.new("TextButton")
    sliderButton.Size = UDim2.new(0, 20, 0, 20)
    sliderButton.Position = UDim2.new(defaultValue, -10, 0, 0)
    sliderButton.BackgroundColor3 = GameConfig.GUI.Colors.Success
    sliderButton.Text = ""
    sliderButton.BorderSizePixel = 0
    sliderButton.Parent = sliderFrame
    
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Size = UDim2.new(0, 50, 0, 30)
    valueLabel.Position = UDim2.new(0, 410, 0, position.Y.Offset)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = string.format("%.1f", defaultValue)
    valueLabel.TextColor3 = GameConfig.GUI.Colors.Light
    valueLabel.TextScaled = true
    valueLabel.Font = GameConfig.GUI.Fonts.Body
    valueLabel.Parent = parent
    
    -- Vereinfachte Slider-Logik
    sliderButton.MouseButton1Click:Connect(function()
        -- Hier würde normalerweise Drag-Logik stehen
        -- Für Vereinfachung: Klick = nächster Wert
        local newValue = (defaultValue + 0.1) % 1
        sliderButton.Position = UDim2.new(newValue, -10, 0, 0)
        valueLabel.Text = string.format("%.1f", newValue)
        if callback then callback(newValue) end
        defaultValue = newValue
    end)
end

-- Button-Klick behandeln
function MainMenuGui:HandleButtonClick(action)
    if action == "newGame" then
        self:ShowScreen("newGame")
    elseif action == "loadGame" then
        self:ShowScreen("loadGame")
    elseif action == "settings" then
        print("Einstellungen noch nicht implementiert")
    elseif action == "quit" then
        -- In Roblox kann man das Spiel nicht direkt beenden
        print("Spiel beenden...")
    end
end

-- Screen wechseln
function MainMenuGui:ShowScreen(screenName)
    self.MainMenu.Visible = screenName == "main"
    self.NewGameMenu.Visible = screenName == "newGame"
    self.LoadGameMenu.Visible = screenName == "loadGame"
    self.CurrentScreen = screenName
end

-- Neues Spiel starten
function MainMenuGui:StartNewGame()
    print("🎮 Starte neues Spiel mit Konfiguration:")
    for key, value in pairs(self.MapConfig) do
        print("  " .. key .. ":", value)
    end
    
    -- Event an Server senden
    GenerateMapEvent:FireServer(self.MapConfig)
    
    -- GUI ausblenden
    self.ScreenGui.Enabled = false
end

-- Initialisierung
function MainMenuGui:Initialize()
    self:CreateGui()
    print("🎨 Hauptmenü-GUI erstellt")
end

return MainMenuGui
