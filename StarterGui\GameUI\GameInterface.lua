-- StarterGui/GameUI/GameInterface.lua
-- Hauptspiel-Interface für Transport Empire

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")

-- Warten auf Module
wait(1)
local GameConfig = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("GameConfig"))

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:FindFirstChild("Events") or Instance.new("Folder")
Events.Name = "Events"
Events.Parent = ReplicatedStorage

local GameInterface = {}
GameInterface.IsVisible = false
GameInterface.CurrentTool = nil
GameInterface.EconomyData = nil

-- GUI erstellen
function GameInterface:CreateGui()
    -- Haupt-ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "GameInterface"
    screenGui.ResetOnSpawn = false
    screenGui.Enabled = false
    screenGui.Parent = playerGui
    
    -- Top-Bar (Geld, Zeit, etc.)
    local topBar = self:CreateTopBar(screenGui)
    
    -- Tool-Panel (links)
    local toolPanel = self:CreateToolPanel(screenGui)
    
    -- Info-Panel (rechts)
    local infoPanel = self:CreateInfoPanel(screenGui)
    
    -- Bottom-Bar (Nachrichten, Pause, etc.)
    local bottomBar = self:CreateBottomBar(screenGui)
    
    -- Minimap (rechts oben)
    local minimap = self:CreateMinimap(screenGui)
    
    self.ScreenGui = screenGui
    self.TopBar = topBar
    self.ToolPanel = toolPanel
    self.InfoPanel = infoPanel
    self.BottomBar = bottomBar
    self.Minimap = minimap
    
    return screenGui
end

-- Top-Bar erstellen (Geld, Zeit, Statistiken)
function GameInterface:CreateTopBar(parent)
    local topBar = Instance.new("Frame")
    topBar.Name = "TopBar"
    topBar.Size = UDim2.new(1, 0, 0, 60)
    topBar.Position = UDim2.new(0, 0, 0, 0)
    topBar.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    topBar.BorderSizePixel = 0
    topBar.Parent = parent
    
    -- Geld-Anzeige
    local moneyFrame = Instance.new("Frame")
    moneyFrame.Size = UDim2.new(0, 200, 0, 50)
    moneyFrame.Position = UDim2.new(0, 10, 0, 5)
    moneyFrame.BackgroundColor3 = GameConfig.GUI.Colors.Success
    moneyFrame.BorderSizePixel = 0
    moneyFrame.Parent = topBar
    
    local moneyCorner = Instance.new("UICorner")
    moneyCorner.CornerRadius = UDim.new(0, 5)
    moneyCorner.Parent = moneyFrame
    
    local moneyLabel = Instance.new("TextLabel")
    moneyLabel.Size = UDim2.new(1, -10, 1, 0)
    moneyLabel.Position = UDim2.new(0, 5, 0, 0)
    moneyLabel.BackgroundTransparency = 1
    moneyLabel.Text = "💰 $100,000"
    moneyLabel.TextColor3 = GameConfig.GUI.Colors.Light
    moneyLabel.TextScaled = true
    moneyLabel.Font = GameConfig.GUI.Fonts.UI
    moneyLabel.TextXAlignment = Enum.TextXAlignment.Left
    moneyLabel.Parent = moneyFrame
    
    -- Zeit-Anzeige
    local timeFrame = Instance.new("Frame")
    timeFrame.Size = UDim2.new(0, 150, 0, 50)
    timeFrame.Position = UDim2.new(0, 220, 0, 5)
    timeFrame.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    timeFrame.BorderSizePixel = 0
    timeFrame.Parent = topBar
    
    local timeCorner = Instance.new("UICorner")
    timeCorner.CornerRadius = UDim.new(0, 5)
    timeCorner.Parent = timeFrame
    
    local timeLabel = Instance.new("TextLabel")
    timeLabel.Size = UDim2.new(1, -10, 1, 0)
    timeLabel.Position = UDim2.new(0, 5, 0, 0)
    timeLabel.BackgroundTransparency = 1
    timeLabel.Text = "📅 Jan 1850"
    timeLabel.TextColor3 = GameConfig.GUI.Colors.Light
    timeLabel.TextScaled = true
    timeLabel.Font = GameConfig.GUI.Fonts.UI
    timeLabel.TextXAlignment = Enum.TextXAlignment.Left
    timeLabel.Parent = timeFrame
    
    -- Statistiken
    local statsFrame = Instance.new("Frame")
    statsFrame.Size = UDim2.new(0, 300, 0, 50)
    statsFrame.Position = UDim2.new(0, 380, 0, 5)
    statsFrame.BackgroundColor3 = GameConfig.GUI.Colors.Secondary
    statsFrame.BorderSizePixel = 0
    statsFrame.Parent = topBar
    
    local statsCorner = Instance.new("UICorner")
    statsCorner.CornerRadius = UDim.new(0, 5)
    statsCorner.Parent = statsFrame
    
    local statsLabel = Instance.new("TextLabel")
    statsLabel.Size = UDim2.new(1, -10, 1, 0)
    statsLabel.Position = UDim2.new(0, 5, 0, 0)
    statsLabel.BackgroundTransparency = 1
    statsLabel.Text = "🚂 0 Züge | 🚛 0 LKWs | 🚢 0 Schiffe"
    statsLabel.TextColor3 = GameConfig.GUI.Colors.Light
    statsLabel.TextScaled = true
    statsLabel.Font = GameConfig.GUI.Fonts.UI
    statsLabel.TextXAlignment = Enum.TextXAlignment.Left
    statsLabel.Parent = statsFrame
    
    -- Pause/Speed-Buttons
    local speedFrame = Instance.new("Frame")
    speedFrame.Size = UDim2.new(0, 200, 0, 50)
    speedFrame.Position = UDim2.new(1, -210, 0, 5)
    speedFrame.BackgroundTransparency = 1
    speedFrame.Parent = topBar
    
    local speedButtons = {"⏸️", "▶️", "⏩", "⏭️"}
    for i, icon in pairs(speedButtons) do
        local speedButton = Instance.new("TextButton")
        speedButton.Size = UDim2.new(0, 40, 0, 40)
        speedButton.Position = UDim2.new(0, (i-1) * 45, 0, 5)
        speedButton.BackgroundColor3 = GameConfig.GUI.Colors.Warning
        speedButton.Text = icon
        speedButton.TextColor3 = GameConfig.GUI.Colors.Dark
        speedButton.TextScaled = true
        speedButton.Font = GameConfig.GUI.Fonts.Body
        speedButton.BorderSizePixel = 0
        speedButton.Parent = speedFrame
        
        local speedCorner = Instance.new("UICorner")
        speedCorner.CornerRadius = UDim.new(0, 5)
        speedCorner.Parent = speedButton
        
        speedButton.MouseButton1Click:Connect(function()
            self:HandleSpeedChange(i - 1) -- 0=Pause, 1=Normal, 2=Fast, 3=Fastest
        end)
    end
    
    -- Referenzen speichern
    topBar.MoneyLabel = moneyLabel
    topBar.TimeLabel = timeLabel
    topBar.StatsLabel = statsLabel
    
    return topBar
end

-- Tool-Panel erstellen (Bau-Tools)
function GameInterface:CreateToolPanel(parent)
    local toolPanel = Instance.new("Frame")
    toolPanel.Name = "ToolPanel"
    toolPanel.Size = UDim2.new(0, GameConfig.GUI.Sizes.SidebarWidth, 1, -120)
    toolPanel.Position = UDim2.new(0, 0, 0, 60)
    toolPanel.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    toolPanel.BorderSizePixel = 0
    toolPanel.Parent = parent
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "🔧 WERKZEUGE"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Subtitle
    title.Parent = toolPanel
    
    -- Tool-Buttons
    local tools = {
        {name = "Eisenbahn", icon = "🚂", tool = "railway"},
        {name = "Straße", icon = "🛣️", tool = "road"},
        {name = "Bahnhof", icon = "🏢", tool = "station"},
        {name = "Depot", icon = "🏭", tool = "depot"},
        {name = "Fahrzeuge", icon = "🚗", tool = "vehicles"},
        {name = "Routen", icon = "🗺️", tool = "routes"},
        {name = "Abriss", icon = "💥", tool = "demolish"}
    }
    
    for i, toolData in pairs(tools) do
        local toolButton = Instance.new("TextButton")
        toolButton.Size = UDim2.new(1, -20, 0, 50)
        toolButton.Position = UDim2.new(0, 10, 0, 40 + i * 60)
        toolButton.BackgroundColor3 = GameConfig.GUI.Colors.Primary
        toolButton.Text = toolData.icon .. " " .. toolData.name
        toolButton.TextColor3 = GameConfig.GUI.Colors.Light
        toolButton.TextScaled = true
        toolButton.Font = GameConfig.GUI.Fonts.Body
        toolButton.BorderSizePixel = 0
        toolButton.Parent = toolPanel
        
        local toolCorner = Instance.new("UICorner")
        toolCorner.CornerRadius = UDim.new(0, 5)
        toolCorner.Parent = toolButton
        
        toolButton.MouseButton1Click:Connect(function()
            self:SelectTool(toolData.tool)
        end)
    end
    
    return toolPanel
end

-- Info-Panel erstellen (Details zu ausgewählten Objekten)
function GameInterface:CreateInfoPanel(parent)
    local infoPanel = Instance.new("Frame")
    infoPanel.Name = "InfoPanel"
    infoPanel.Size = UDim2.new(0, GameConfig.GUI.Sizes.SidebarWidth, 1, -120)
    infoPanel.Position = UDim2.new(1, -GameConfig.GUI.Sizes.SidebarWidth, 0, 60)
    infoPanel.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    infoPanel.BorderSizePixel = 0
    infoPanel.Parent = parent
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "ℹ️ INFORMATIONEN"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Subtitle
    title.Parent = infoPanel
    
    -- Scroll-Frame für Inhalte
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 1, -60)
    scrollFrame.Position = UDim2.new(0, 10, 0, 50)
    scrollFrame.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = infoPanel
    
    local scrollCorner = Instance.new("UICorner")
    scrollCorner.CornerRadius = UDim.new(0, 5)
    scrollCorner.Parent = scrollFrame
    
    -- Standard-Info
    local infoText = Instance.new("TextLabel")
    infoText.Size = UDim2.new(1, -20, 0, 200)
    infoText.Position = UDim2.new(0, 10, 0, 10)
    infoText.BackgroundTransparency = 1
    infoText.Text = "Wähle ein Objekt aus, um Details zu sehen."
    infoText.TextColor3 = GameConfig.GUI.Colors.Light
    infoText.TextWrapped = true
    infoText.TextYAlignment = Enum.TextYAlignment.Top
    infoText.Font = GameConfig.GUI.Fonts.Body
    infoText.Parent = scrollFrame
    
    infoPanel.InfoText = infoText
    
    return infoPanel
end

-- Bottom-Bar erstellen (Nachrichten, Menü-Buttons)
function GameInterface:CreateBottomBar(parent)
    local bottomBar = Instance.new("Frame")
    bottomBar.Name = "BottomBar"
    bottomBar.Size = UDim2.new(1, 0, 0, 60)
    bottomBar.Position = UDim2.new(0, 0, 1, -60)
    bottomBar.BackgroundColor3 = GameConfig.GUI.Colors.Dark
    bottomBar.BorderSizePixel = 0
    bottomBar.Parent = parent
    
    -- Nachrichten-Bereich
    local messageFrame = Instance.new("Frame")
    messageFrame.Size = UDim2.new(1, -200, 1, -10)
    messageFrame.Position = UDim2.new(0, 10, 0, 5)
    messageFrame.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    messageFrame.BorderSizePixel = 0
    messageFrame.Parent = bottomBar
    
    local messageCorner = Instance.new("UICorner")
    messageCorner.CornerRadius = UDim.new(0, 5)
    messageCorner.Parent = messageFrame
    
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, -10, 1, 0)
    messageLabel.Position = UDim2.new(0, 5, 0, 0)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = "🎮 Willkommen bei Transport Empire!"
    messageLabel.TextColor3 = GameConfig.GUI.Colors.Light
    messageLabel.TextScaled = true
    messageLabel.Font = GameConfig.GUI.Fonts.Body
    messageLabel.TextXAlignment = Enum.TextXAlignment.Left
    messageLabel.Parent = messageFrame
    
    -- Menü-Buttons
    local menuButtons = {"💾", "📊", "⚙️", "❌"}
    local menuActions = {"save", "statistics", "settings", "menu"}
    
    for i, icon in pairs(menuButtons) do
        local menuButton = Instance.new("TextButton")
        menuButton.Size = UDim2.new(0, 40, 0, 40)
        menuButton.Position = UDim2.new(1, -50 * i, 0, 10)
        menuButton.BackgroundColor3 = GameConfig.GUI.Colors.Secondary
        menuButton.Text = icon
        menuButton.TextColor3 = GameConfig.GUI.Colors.Light
        menuButton.TextScaled = true
        menuButton.Font = GameConfig.GUI.Fonts.Body
        menuButton.BorderSizePixel = 0
        menuButton.Parent = bottomBar
        
        local menuCorner = Instance.new("UICorner")
        menuCorner.CornerRadius = UDim.new(0, 5)
        menuCorner.Parent = menuButton
        
        menuButton.MouseButton1Click:Connect(function()
            self:HandleMenuAction(menuActions[i])
        end)
    end
    
    bottomBar.MessageLabel = messageLabel
    
    return bottomBar
end

-- Minimap erstellen
function GameInterface:CreateMinimap(parent)
    local minimap = Instance.new("Frame")
    minimap.Name = "Minimap"
    minimap.Size = UDim2.new(0, 200, 0, 200)
    minimap.Position = UDim2.new(1, -210, 0, 70)
    minimap.BackgroundColor3 = GameConfig.GUI.Colors.Primary
    minimap.BorderSizePixel = 0
    minimap.Parent = parent
    
    local minimapCorner = Instance.new("UICorner")
    minimapCorner.CornerRadius = UDim.new(0, 10)
    minimapCorner.Parent = minimap
    
    -- Minimap-Inhalt (vereinfacht)
    local mapContent = Instance.new("Frame")
    mapContent.Size = UDim2.new(1, -10, 1, -30)
    mapContent.Position = UDim2.new(0, 5, 0, 25)
    mapContent.BackgroundColor3 = GameConfig.GUI.Colors.Success
    mapContent.BorderSizePixel = 0
    mapContent.Parent = minimap
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 5)
    contentCorner.Parent = mapContent
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🗺️ KARTE"
    title.TextColor3 = GameConfig.GUI.Colors.Light
    title.TextScaled = true
    title.Font = GameConfig.GUI.Fonts.Body
    title.Parent = minimap
    
    return minimap
end

-- Tool auswählen
function GameInterface:SelectTool(toolName)
    self.CurrentTool = toolName
    print("🔧 Tool ausgewählt:", toolName)
    
    -- Tool-spezifische Aktionen
    if toolName == "vehicles" then
        self:ShowVehicleMenu()
    elseif toolName == "routes" then
        self:ShowRouteMenu()
    end
end

-- Geschwindigkeit ändern
function GameInterface:HandleSpeedChange(speed)
    print("⏱️ Geschwindigkeit geändert:", speed)
    -- Hier würde die Spielgeschwindigkeit geändert werden
end

-- Menü-Aktion behandeln
function GameInterface:HandleMenuAction(action)
    if action == "save" then
        print("💾 Spiel speichern...")
    elseif action == "statistics" then
        print("📊 Statistiken anzeigen...")
    elseif action == "settings" then
        print("⚙️ Einstellungen öffnen...")
    elseif action == "menu" then
        print("❌ Zurück zum Hauptmenü...")
        self:Hide()
    end
end

-- Fahrzeug-Menü anzeigen (vereinfacht)
function GameInterface:ShowVehicleMenu()
    print("🚗 Fahrzeug-Menü öffnen...")
end

-- Routen-Menü anzeigen (vereinfacht)
function GameInterface:ShowRouteMenu()
    print("🗺️ Routen-Menü öffnen...")
end

-- Interface anzeigen
function GameInterface:Show()
    if self.ScreenGui then
        self.ScreenGui.Enabled = true
        self.IsVisible = true
        print("🎨 Spiel-Interface angezeigt")
    end
end

-- Interface ausblenden
function GameInterface:Hide()
    if self.ScreenGui then
        self.ScreenGui.Enabled = false
        self.IsVisible = false
        print("🎨 Spiel-Interface ausgeblendet")
    end
end

-- Daten aktualisieren
function GameInterface:UpdateData(economyData)
    self.EconomyData = economyData
    
    if self.TopBar and economyData then
        -- Geld aktualisieren
        if economyData.player and self.TopBar.MoneyLabel then
            self.TopBar.MoneyLabel.Text = "💰 $" .. string.format("%,d", economyData.player.money)
        end
        
        -- Zeit aktualisieren
        if economyData.global and self.TopBar.TimeLabel then
            local months = {"Jan", "Feb", "Mär", "Apr", "Mai", "Jun", 
                          "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"}
            local monthName = months[economyData.global.currentMonth] or "Jan"
            self.TopBar.TimeLabel.Text = "📅 " .. monthName .. " " .. economyData.global.currentYear
        end
        
        -- Statistiken aktualisieren
        if economyData.player and self.TopBar.StatsLabel then
            local vehicles = economyData.player.vehicles or {}
            local trains = 0
            local trucks = 0
            local ships = 0
            
            for _, vehicle in pairs(vehicles) do
                if vehicle.type == "Train" then trains = trains + 1
                elseif vehicle.type == "Truck" then trucks = trucks + 1
                elseif vehicle.type == "Ship" then ships = ships + 1
                end
            end
            
            self.TopBar.StatsLabel.Text = string.format("🚂 %d Züge | 🚛 %d LKWs | 🚢 %d Schiffe", trains, trucks, ships)
        end
    end
end

-- Nachricht anzeigen
function GameInterface:ShowMessage(message)
    if self.BottomBar and self.BottomBar.MessageLabel then
        self.BottomBar.MessageLabel.Text = message
    end
end

-- Initialisierung
function GameInterface:Initialize()
    self:CreateGui()
    print("🎨 Spiel-Interface erstellt")
end

return GameInterface
