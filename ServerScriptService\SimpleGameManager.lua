-- ServerScriptService/SimpleGameManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vereinfachter GameManager für Tests - Garantiert funktionsfähig

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local SimpleGameManager = {}
SimpleGameManager.__index = SimpleGameManager

-- Singleton Instance
local instance = nil

function SimpleGameManager.new()
    if instance then return instance end
    
    local self = setmetatable({}, SimpleGameManager)
    
    -- Basic Game State
    self.gameState = {
        isRunning = false,
        isPaused = false,
        gameSpeed = 1,
        currentDate = {year = 1850, month = 1, day = 1},
        weather = "Clear",
        season = "Spring",
        playerCount = 0
    }
    
    -- Player Data
    self.playerData = {}
    
    -- Manager Status
    self.managers = {
        economy = nil,
        vehicle = nil,
        transport = nil,
        city = nil,
        terrain = nil,
        ai = nil
    }
    
    -- Events
    self.events = {}
    
    instance = self
    print("✅ SimpleGameManager initialisiert")
    return self
end

function SimpleGameManager.GetInstance()
    if not instance then
        return SimpleGameManager.new()
    end
    return instance
end

-- Basic Game Functions
function SimpleGameManager:StartGame()
    self.gameState.isRunning = true
    print("🎮 Spiel gestartet!")
    return true
end

function SimpleGameManager:PauseGame()
    self.gameState.isPaused = not self.gameState.isPaused
    print("⏸️ Spiel pausiert:", self.gameState.isPaused)
    return self.gameState.isPaused
end

function SimpleGameManager:StopGame()
    self.gameState.isRunning = false
    self.gameState.isPaused = false
    print("🛑 Spiel gestoppt!")
    return true
end

-- Player Management
function SimpleGameManager:AddPlayer(player)
    if not self.playerData[player.UserId] then
        self.playerData[player.UserId] = {
            name = player.Name,
            money = 100000,
            reputation = 50,
            joinTime = tick()
        }
        self.gameState.playerCount = self.gameState.playerCount + 1
        print("👤 Spieler hinzugefügt:", player.Name)
    end
    return self.playerData[player.UserId]
end

function SimpleGameManager:RemovePlayer(player)
    if self.playerData[player.UserId] then
        self.playerData[player.UserId] = nil
        self.gameState.playerCount = math.max(0, self.gameState.playerCount - 1)
        print("👋 Spieler entfernt:", player.Name)
    end
end

function SimpleGameManager:GetPlayerData(player)
    return self.playerData[player.UserId]
end

-- Manager Loading (Optional)
function SimpleGameManager:LoadManager(managerName, managerModule)
    if managerModule then
        local success, result = pcall(function()
            return managerModule.new()
        end)
        
        if success then
            self.managers[managerName] = result
            print("✅ Manager geladen:", managerName)
            return true
        else
            warn("❌ Manager-Fehler:", managerName, "-", tostring(result))
            return false
        end
    end
    return false
end

-- Game State
function SimpleGameManager:GetGameState()
    return {
        isRunning = self.gameState.isRunning,
        isPaused = self.gameState.isPaused,
        gameSpeed = self.gameState.gameSpeed,
        currentDate = self.gameState.currentDate,
        weather = self.gameState.weather,
        season = self.gameState.season,
        playerCount = self.gameState.playerCount,
        managersLoaded = self:GetLoadedManagerCount()
    }
end

function SimpleGameManager:GetLoadedManagerCount()
    local count = 0
    for _, manager in pairs(self.managers) do
        if manager then count = count + 1 end
    end
    return count
end

-- Update Loop (Basic)
function SimpleGameManager:Update()
    if not self.gameState.isRunning or self.gameState.isPaused then
        return
    end
    
    -- Basic time progression
    -- (Hier könnten weitere Updates hinzugefügt werden)
end

-- Cleanup
function SimpleGameManager:Destroy()
    if instance == self then
        instance = nil
    end
    
    -- Cleanup managers
    for name, manager in pairs(self.managers) do
        if manager and manager.Destroy then
            manager:Destroy()
        end
    end
    
    print("🧹 SimpleGameManager zerstört")
end

-- Status Report
function SimpleGameManager:GetStatus()
    return {
        initialized = instance ~= nil,
        running = self.gameState.isRunning,
        paused = self.gameState.isPaused,
        players = self.gameState.playerCount,
        managers = self:GetLoadedManagerCount()
    }
end

function SimpleGameManager:PrintStatus()
    local status = self:GetStatus()
    print("📊 GAMEMANAGER STATUS:")
    print("  🎮 Initialisiert:", status.initialized)
    print("  ▶️ Läuft:", status.running)
    print("  ⏸️ Pausiert:", status.paused)
    print("  👥 Spieler:", status.players)
    print("  🔧 Manager:", status.managers)
end

return SimpleGameManager
