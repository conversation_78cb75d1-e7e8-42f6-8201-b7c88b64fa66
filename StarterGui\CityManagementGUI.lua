-- StarterGui/CityManagementGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Stadt-Management GUI für detaillierte Stadtinformationen

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for Events
local Events = ReplicatedStorage:WaitForChild("Events")

-- City Management System
local CityManagementGUI = {}
CityManagementGUI.__index = CityManagementGUI

function CityManagementGUI.new()
    local self = setmetatable({}, CityManagementGUI)
    
    -- Data
    self.cities = {}
    self.selectedCity = nil
    self.isVisible = false
    
    -- Create GUI
    self:CreateGUI()
    self:SetupEvents()
    
    return self
end

-- Create main GUI
function CityManagementGUI:CreateGUI()
    -- Main ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "CityManagementGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Main Frame
    self.mainFrame = Instance.new("Frame")
    self.mainFrame.Name = "MainFrame"
    self.mainFrame.Size = UDim2.new(0, 800, 0, 600)
    self.mainFrame.Position = UDim2.new(0.5, -400, 0.5, -300)
    self.mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    self.mainFrame.BorderSizePixel = 0
    self.mainFrame.Visible = false
    self.mainFrame.Parent = self.screenGui
    
    -- Add corner radius
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 10)
    corner.Parent = self.mainFrame
    
    -- Title Bar
    self.titleBar = Instance.new("Frame")
    self.titleBar.Name = "TitleBar"
    self.titleBar.Size = UDim2.new(1, 0, 0, 40)
    self.titleBar.Position = UDim2.new(0, 0, 0, 0)
    self.titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    self.titleBar.BorderSizePixel = 0
    self.titleBar.Parent = self.mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 10)
    titleCorner.Parent = self.titleBar
    
    -- Title Text
    self.titleLabel = Instance.new("TextLabel")
    self.titleLabel.Name = "TitleLabel"
    self.titleLabel.Size = UDim2.new(1, -100, 1, 0)
    self.titleLabel.Position = UDim2.new(0, 10, 0, 0)
    self.titleLabel.BackgroundTransparency = 1
    self.titleLabel.Text = "🏙️ Stadt-Management"
    self.titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    self.titleLabel.TextScaled = true
    self.titleLabel.Font = Enum.Font.SourceSansBold
    self.titleLabel.TextXAlignment = Enum.TextXAlignment.Left
    self.titleLabel.Parent = self.titleBar
    
    -- Close Button
    self.closeButton = Instance.new("TextButton")
    self.closeButton.Name = "CloseButton"
    self.closeButton.Size = UDim2.new(0, 30, 0, 30)
    self.closeButton.Position = UDim2.new(1, -35, 0, 5)
    self.closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    self.closeButton.BorderSizePixel = 0
    self.closeButton.Text = "✕"
    self.closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    self.closeButton.TextScaled = true
    self.closeButton.Font = Enum.Font.SourceSansBold
    self.closeButton.Parent = self.titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = self.closeButton
    
    -- Content Frame
    self.contentFrame = Instance.new("Frame")
    self.contentFrame.Name = "ContentFrame"
    self.contentFrame.Size = UDim2.new(1, -20, 1, -60)
    self.contentFrame.Position = UDim2.new(0, 10, 0, 50)
    self.contentFrame.BackgroundTransparency = 1
    self.contentFrame.Parent = self.mainFrame
    
    -- Left Panel (City List)
    self.leftPanel = Instance.new("Frame")
    self.leftPanel.Name = "LeftPanel"
    self.leftPanel.Size = UDim2.new(0, 250, 1, 0)
    self.leftPanel.Position = UDim2.new(0, 0, 0, 0)
    self.leftPanel.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    self.leftPanel.BorderSizePixel = 0
    self.leftPanel.Parent = self.contentFrame
    
    local leftCorner = Instance.new("UICorner")
    leftCorner.CornerRadius = UDim.new(0, 5)
    leftCorner.Parent = self.leftPanel
    
    -- City List Header
    self.cityListHeader = Instance.new("TextLabel")
    self.cityListHeader.Name = "CityListHeader"
    self.cityListHeader.Size = UDim2.new(1, -10, 0, 30)
    self.cityListHeader.Position = UDim2.new(0, 5, 0, 5)
    self.cityListHeader.BackgroundTransparency = 1
    self.cityListHeader.Text = "Städte"
    self.cityListHeader.TextColor3 = Color3.fromRGB(255, 255, 255)
    self.cityListHeader.TextScaled = true
    self.cityListHeader.Font = Enum.Font.SourceSansBold
    self.cityListHeader.Parent = self.leftPanel
    
    -- City List ScrollingFrame
    self.cityListFrame = Instance.new("ScrollingFrame")
    self.cityListFrame.Name = "CityListFrame"
    self.cityListFrame.Size = UDim2.new(1, -10, 1, -40)
    self.cityListFrame.Position = UDim2.new(0, 5, 0, 35)
    self.cityListFrame.BackgroundTransparency = 1
    self.cityListFrame.BorderSizePixel = 0
    self.cityListFrame.ScrollBarThickness = 5
    self.cityListFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    self.cityListFrame.Parent = self.leftPanel
    
    -- City List Layout
    self.cityListLayout = Instance.new("UIListLayout")
    self.cityListLayout.SortOrder = Enum.SortOrder.LayoutOrder
    self.cityListLayout.Padding = UDim.new(0, 2)
    self.cityListLayout.Parent = self.cityListFrame
    
    -- Right Panel (City Details)
    self.rightPanel = Instance.new("Frame")
    self.rightPanel.Name = "RightPanel"
    self.rightPanel.Size = UDim2.new(1, -260, 1, 0)
    self.rightPanel.Position = UDim2.new(0, 260, 0, 0)
    self.rightPanel.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    self.rightPanel.BorderSizePixel = 0
    self.rightPanel.Parent = self.contentFrame
    
    local rightCorner = Instance.new("UICorner")
    rightCorner.CornerRadius = UDim.new(0, 5)
    rightCorner.Parent = self.rightPanel
    
    -- City Details Content
    self:CreateCityDetailsPanel()
    
    -- Setup button events
    self.closeButton.MouseButton1Click:Connect(function()
        self:Hide()
    end)
end

-- Create city details panel
function CityManagementGUI:CreateCityDetailsPanel()
    -- City Name Header
    self.cityNameFrame = Instance.new("Frame")
    self.cityNameFrame.Name = "CityNameFrame"
    self.cityNameFrame.Size = UDim2.new(1, -20, 0, 50)
    self.cityNameFrame.Position = UDim2.new(0, 10, 0, 10)
    self.cityNameFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    self.cityNameFrame.BorderSizePixel = 0
    self.cityNameFrame.Parent = self.rightPanel
    
    local nameCorner = Instance.new("UICorner")
    nameCorner.CornerRadius = UDim.new(0, 5)
    nameCorner.Parent = self.cityNameFrame
    
    -- City Name Label
    self.cityNameLabel = Instance.new("TextLabel")
    self.cityNameLabel.Name = "CityNameLabel"
    self.cityNameLabel.Size = UDim2.new(1, -100, 1, 0)
    self.cityNameLabel.Position = UDim2.new(0, 10, 0, 0)
    self.cityNameLabel.BackgroundTransparency = 1
    self.cityNameLabel.Text = "Stadt auswählen..."
    self.cityNameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    self.cityNameLabel.TextScaled = true
    self.cityNameLabel.Font = Enum.Font.SourceSansBold
    self.cityNameLabel.TextXAlignment = Enum.TextXAlignment.Left
    self.cityNameLabel.Parent = self.cityNameFrame
    
    -- Rename Button
    self.renameButton = Instance.new("TextButton")
    self.renameButton.Name = "RenameButton"
    self.renameButton.Size = UDim2.new(0, 80, 0, 30)
    self.renameButton.Position = UDim2.new(1, -90, 0, 10)
    self.renameButton.BackgroundColor3 = Color3.fromRGB(70, 130, 180)
    self.renameButton.BorderSizePixel = 0
    self.renameButton.Text = "Umbenennen"
    self.renameButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    self.renameButton.TextScaled = true
    self.renameButton.Font = Enum.Font.SourceSans
    self.renameButton.Parent = self.cityNameFrame
    
    local renameCorner = Instance.new("UICorner")
    renameCorner.CornerRadius = UDim.new(0, 5)
    renameCorner.Parent = self.renameButton
    
    -- Statistics Frame
    self.statsFrame = Instance.new("ScrollingFrame")
    self.statsFrame.Name = "StatsFrame"
    self.statsFrame.Size = UDim2.new(1, -20, 1, -80)
    self.statsFrame.Position = UDim2.new(0, 10, 0, 70)
    self.statsFrame.BackgroundTransparency = 1
    self.statsFrame.BorderSizePixel = 0
    self.statsFrame.ScrollBarThickness = 5
    self.statsFrame.CanvasSize = UDim2.new(0, 0, 0, 800)
    self.statsFrame.Parent = self.rightPanel
    
    -- Stats Layout
    self.statsLayout = Instance.new("UIListLayout")
    self.statsLayout.SortOrder = Enum.SortOrder.LayoutOrder
    self.statsLayout.Padding = UDim.new(0, 10)
    self.statsLayout.Parent = self.statsFrame
    
    -- Setup rename button
    self.renameButton.MouseButton1Click:Connect(function()
        self:ShowRenameDialog()
    end)
end

-- Create city list item
function CityManagementGUI:CreateCityListItem(cityData)
    local item = Instance.new("TextButton")
    item.Name = "CityItem_" .. cityData.id
    item.Size = UDim2.new(1, -10, 0, 40)
    item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    item.BorderSizePixel = 0
    item.Text = ""
    item.Parent = self.cityListFrame
    
    local itemCorner = Instance.new("UICorner")
    itemCorner.CornerRadius = UDim.new(0, 5)
    itemCorner.Parent = item
    
    -- City Name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(1, -10, 0, 20)
    nameLabel.Position = UDim2.new(0, 5, 0, 2)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = cityData.name
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = item
    
    -- Population
    local popLabel = Instance.new("TextLabel")
    popLabel.Name = "PopLabel"
    popLabel.Size = UDim2.new(1, -10, 0, 15)
    popLabel.Position = UDim2.new(0, 5, 0, 22)
    popLabel.BackgroundTransparency = 1
    popLabel.Text = "👥 " .. self:FormatNumber(cityData.population)
    popLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    popLabel.TextScaled = true
    popLabel.Font = Enum.Font.SourceSans
    popLabel.TextXAlignment = Enum.TextXAlignment.Left
    popLabel.Parent = item
    
    -- Click event
    item.MouseButton1Click:Connect(function()
        self:SelectCity(cityData.id)
    end)
    
    return item
end

-- Create stat item
function CityManagementGUI:CreateStatItem(title, value, icon)
    local item = Instance.new("Frame")
    item.Name = "StatItem"
    item.Size = UDim2.new(1, 0, 0, 60)
    item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    item.BorderSizePixel = 0
    item.Parent = self.statsFrame
    
    local itemCorner = Instance.new("UICorner")
    itemCorner.CornerRadius = UDim.new(0, 5)
    itemCorner.Parent = item
    
    -- Icon
    local iconLabel = Instance.new("TextLabel")
    iconLabel.Name = "IconLabel"
    iconLabel.Size = UDim2.new(0, 40, 1, 0)
    iconLabel.Position = UDim2.new(0, 10, 0, 0)
    iconLabel.BackgroundTransparency = 1
    iconLabel.Text = icon or "📊"
    iconLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    iconLabel.TextScaled = true
    iconLabel.Font = Enum.Font.SourceSans
    iconLabel.Parent = item
    
    -- Title
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, -120, 0, 25)
    titleLabel.Position = UDim2.new(0, 60, 0, 5)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = title
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.TextXAlignment = Enum.TextXAlignment.Left
    titleLabel.Parent = item
    
    -- Value
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Name = "ValueLabel"
    valueLabel.Size = UDim2.new(1, -120, 0, 25)
    valueLabel.Position = UDim2.new(0, 60, 0, 30)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = tostring(value)
    valueLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSans
    valueLabel.TextXAlignment = Enum.TextXAlignment.Left
    valueLabel.Parent = item
    
    return item
end

-- Setup events
function CityManagementGUI:SetupEvents()
    -- Response events
    Events.AllCitiesResponseEvent.OnClientEvent:Connect(function(cities)
        self:UpdateCityList(cities)
    end)
    
    Events.CityStatsResponseEvent.OnClientEvent:Connect(function(cityId, stats)
        if cityId == self.selectedCity then
            self:UpdateCityStats(stats)
        end
    end)
    
    Events.CityRenamedEvent.OnClientEvent:Connect(function(cityId, newName)
        self:OnCityRenamed(cityId, newName)
    end)
    
    -- Hotkey to toggle
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.C then
            self:Toggle()
        end
    end)
end

-- Show GUI
function CityManagementGUI:Show()
    if self.isVisible then return end
    
    self.isVisible = true
    self.mainFrame.Visible = true
    
    -- Tween in
    local tween = TweenService:Create(self.mainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
        Size = UDim2.new(0, 800, 0, 600)
    })
    tween:Play()
    
    -- Request city data
    Events.GetAllCitiesEvent:FireServer()
end

-- Hide GUI
function CityManagementGUI:Hide()
    if not self.isVisible then return end
    
    self.isVisible = false
    
    -- Tween out
    local tween = TweenService:Create(self.mainFrame, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
        Size = UDim2.new(0, 0, 0, 0)
    })
    tween:Play()
    
    tween.Completed:Connect(function()
        self.mainFrame.Visible = false
    end)
end

-- Toggle GUI
function CityManagementGUI:Toggle()
    if self.isVisible then
        self:Hide()
    else
        self:Show()
    end
end

-- Update city list
function CityManagementGUI:UpdateCityList(cities)
    -- Clear existing items
    for _, child in pairs(self.cityListFrame:GetChildren()) do
        if child:IsA("TextButton") then
            child:Destroy()
        end
    end
    
    -- Add cities
    self.cities = cities
    for cityId, cityData in pairs(cities) do
        self:CreateCityListItem(cityData)
    end
    
    -- Update canvas size
    self.cityListFrame.CanvasSize = UDim2.new(0, 0, 0, self.cityListLayout.AbsoluteContentSize.Y + 10)
end

-- Select city
function CityManagementGUI:SelectCity(cityId)
    self.selectedCity = cityId
    local cityData = self.cities[cityId]
    
    if cityData then
        self.cityNameLabel.Text = cityData.name
        
        -- Request detailed stats
        Events.GetCityStatsEvent:FireServer(cityId)
        
        -- Highlight selected item
        for _, child in pairs(self.cityListFrame:GetChildren()) do
            if child:IsA("TextButton") then
                if child.Name == "CityItem_" .. cityId then
                    child.BackgroundColor3 = Color3.fromRGB(70, 130, 180)
                else
                    child.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
                end
            end
        end
    end
end

-- Update city stats
function CityManagementGUI:UpdateCityStats(stats)
    -- Clear existing stats
    for _, child in pairs(self.statsFrame:GetChildren()) do
        if child:IsA("Frame") and child.Name == "StatItem" then
            child:Destroy()
        end
    end
    
    if not stats then return end
    
    -- Add stats
    self:CreateStatItem("Einwohner", self:FormatNumber(stats.population), "👥")
    self:CreateStatItem("Stadtgröße", "Stufe " .. stats.size, "🏙️")
    self:CreateStatItem("Industrien", stats.totalIndustries, "🏭")
    self:CreateStatItem("Produktion", self:FormatNumber(math.floor(stats.totalProduction)), "📦")
    self:CreateStatItem("Verbrauch", self:FormatNumber(math.floor(stats.totalConsumption)), "⚡")
    self:CreateStatItem("Zufriedenheit", math.floor(stats.averageSatisfaction) .. "%", "😊")
    
    -- Demands
    if stats.demands then
        self:CreateStatItem("Passagier-Nachfrage", self:FormatNumber(stats.demands.passengers or 0), "🚌")
        self:CreateStatItem("Post-Nachfrage", self:FormatNumber(stats.demands.mail or 0), "📮")
        self:CreateStatItem("Waren-Nachfrage", self:FormatNumber(stats.demands.goods or 0), "📦")
    end
    
    -- Growth factors
    if stats.growth then
        local transportStatus = stats.growth.hasTransport and "✅ Verbunden" or "❌ Nicht verbunden"
        local diversityStatus = stats.growth.hasDiversity and "✅ Vielfältig" or "❌ Begrenzt"
        local resourceStatus = stats.growth.hasResources and "✅ Verfügbar" or "❌ Keine"
        
        self:CreateStatItem("Transport-Anbindung", transportStatus, "🚂")
        self:CreateStatItem("Industrie-Vielfalt", diversityStatus, "🏭")
        self:CreateStatItem("Ressourcen-Zugang", resourceStatus, "⛏️")
    end
end

-- Show rename dialog
function CityManagementGUI:ShowRenameDialog()
    if not self.selectedCity then return end
    
    local cityData = self.cities[self.selectedCity]
    if not cityData then return end
    
    -- Simple input dialog (placeholder - would need proper dialog)
    local newName = "New " .. cityData.name -- Placeholder
    Events.RenameCityEvent:FireServer(self.selectedCity, newName)
end

-- Handle city renamed
function CityManagementGUI:OnCityRenamed(cityId, newName)
    if self.cities[cityId] then
        self.cities[cityId].name = newName
        
        -- Update list item
        local listItem = self.cityListFrame:FindFirstChild("CityItem_" .. cityId)
        if listItem then
            local nameLabel = listItem:FindFirstChild("NameLabel")
            if nameLabel then
                nameLabel.Text = newName
            end
        end
        
        -- Update selected city name
        if cityId == self.selectedCity then
            self.cityNameLabel.Text = newName
        end
    end
end

-- Format number with commas
function CityManagementGUI:FormatNumber(num)
    if not num then return "0" end
    
    local formatted = tostring(math.floor(num))
    local k = 0
    while k < string.len(formatted) do
        k = k + 4
        if k < string.len(formatted) then
            formatted = string.sub(formatted, 1, string.len(formatted) - k + 1) .. "," .. string.sub(formatted, string.len(formatted) - k + 2)
        end
    end
    return formatted
end

-- Initialize
local cityManagementGUI = CityManagementGUI.new()

print("🏙️ City Management GUI initialized - Press 'C' to open")
