-- ServerScriptService/Managers/MapGenerator.lua
-- Prozedurale Kartengenerierung mit Seed-basierter Determinismus

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local MapGenerator = {}
MapGenerator.CurrentMap = nil

-- Pseudo-Random Generator für deterministische Generierung
local function CreateSeededRandom(seed)
    local rng = Random.new()
    if seed and seed ~= "" then
        -- String zu Zahl konvertieren
        local numSeed = 0
        for i = 1, #seed do
            numSeed = numSeed + string.byte(seed, i) * i
        end
        rng = Random.new(numSeed)
    end
    return rng
end

-- Perlin Noise für Terrain-Generierung
local function GenerateHeightMap(width, height, rng, hilliness)
    local heightMap = {}
    local scale = 0.1
    
    for x = 1, width do
        heightMap[x] = {}
        for z = 1, height do
            local noise = math.noise(x * scale, z * scale, 0) * hilliness
            heightMap[x][z] = math.max(0, noise * 20) -- Höhe zwischen 0-20
        end
    end
    
    return heightMap
end

-- Wasser-Gebiete generieren
local function GenerateWaterMap(width, height, rng, waterAmount, mapType)
    local waterMap = {}
    
    for x = 1, width do
        waterMap[x] = {}
        for z = 1, height do
            waterMap[x][z] = false
        end
    end
    
    if mapType == "Asian" then
        -- Inseln für asiatische Karte
        local islandCount = math.floor(width * height * 0.001)
        for i = 1, islandCount do
            local centerX = rng:NextInteger(10, width - 10)
            local centerZ = rng:NextInteger(10, height - 10)
            local radius = rng:NextInteger(5, 15)
            
            for x = math.max(1, centerX - radius), math.min(width, centerX + radius) do
                for z = math.max(1, centerZ - radius), math.min(height, centerZ + radius) do
                    local distance = math.sqrt((x - centerX)^2 + (z - centerZ)^2)
                    if distance <= radius then
                        waterMap[x][z] = true
                    end
                end
            end
        end
    else
        -- Flüsse und Seen für andere Kartentypen
        local waterTiles = math.floor(width * height * waterAmount)
        for i = 1, waterTiles do
            local x = rng:NextInteger(1, width)
            local z = rng:NextInteger(1, height)
            waterMap[x][z] = true
            
            -- Wasser ausbreiten
            local spread = rng:NextInteger(1, 3)
            for dx = -spread, spread do
                for dz = -spread, spread do
                    local nx, nz = x + dx, z + dz
                    if nx >= 1 and nx <= width and nz >= 1 and nz <= height then
                        if rng:NextNumber() < 0.6 then
                            waterMap[nx][nz] = true
                        end
                    end
                end
            end
        end
    end
    
    return waterMap
end

-- Städte platzieren
local function GenerateCities(width, height, rng, mapType, heightMap, waterMap)
    local cities = {}
    local mapConfig = GameConfig.Map.Types[mapType]
    local cityCount = rng:NextInteger(mapConfig.cities.min, mapConfig.cities.max)
    
    local cityNames = {
        "Neustadt", "Altburg", "Hafenstadt", "Bergdorf", "Waldheim",
        "Steinbrück", "Goldtal", "Eisenhut", "Grünfeld", "Rotenstein",
        "Blauberg", "Silberbach", "Kupfertal", "Schwarzwald", "Weißdorf"
    }
    
    for i = 1, cityCount do
        local attempts = 0
        local placed = false
        
        while not placed and attempts < 50 do
            local x = rng:NextInteger(5, width - 5)
            local z = rng:NextInteger(5, height - 5)
            
            -- Prüfen ob Position geeignet ist
            local suitable = true
            
            -- Nicht auf Wasser
            if waterMap[x][z] then
                suitable = false
            end
            
            -- Nicht zu nah an anderen Städten
            for _, city in pairs(cities) do
                local distance = math.sqrt((x - city.x)^2 + (z - city.z)^2)
                if distance < 10 then
                    suitable = false
                    break
                end
            end
            
            if suitable then
                local citySize = rng:NextNumber() < 0.7 and "Small" or (rng:NextNumber() < 0.8 and "Medium" or "Large")
                local cityConfig = GameConfig.Economy.CityDemands[citySize]
                
                local city = {
                    id = "city_" .. i,
                    name = cityNames[rng:NextInteger(1, #cityNames)] .. " " .. i,
                    x = x,
                    z = z,
                    size = citySize,
                    population = rng:NextInteger(cityConfig.population.min, cityConfig.population.max),
                    demands = cityConfig.demands,
                    growth = cityConfig.growth,
                    satisfaction = 0.5 -- Startzufriedenheit
                }
                
                table.insert(cities, city)
                placed = true
            end
            
            attempts = attempts + 1
        end
    end
    
    return cities
end

-- Industrien platzieren
local function GenerateIndustries(width, height, rng, heightMap, waterMap, cities)
    local industries = {}
    local industryTypes = {"CoalMine", "IronMine", "SteelMill", "Sawmill", "Farm"}
    local industryCount = math.floor(#cities * 1.5) -- 1.5 Industrien pro Stadt
    
    for i = 1, industryCount do
        local attempts = 0
        local placed = false
        
        while not placed and attempts < 50 do
            local x = rng:NextInteger(3, width - 3)
            local z = rng:NextInteger(3, height - 3)
            
            -- Prüfen ob Position geeignet ist
            local suitable = true
            
            -- Nicht auf Wasser
            if waterMap[x][z] then
                suitable = false
            end
            
            -- Nicht zu nah an Städten
            for _, city in pairs(cities) do
                local distance = math.sqrt((x - city.x)^2 + (z - city.z)^2)
                if distance < 3 then
                    suitable = false
                    break
                end
            end
            
            -- Nicht zu nah an anderen Industrien
            for _, industry in pairs(industries) do
                local distance = math.sqrt((x - industry.x)^2 + (z - industry.z)^2)
                if distance < 5 then
                    suitable = false
                    break
                end
            end
            
            if suitable then
                local industryType = industryTypes[rng:NextInteger(1, #industryTypes)]
                local industryConfig = GameConfig.Economy.Industries[industryType]
                
                local industry = {
                    id = "industry_" .. i,
                    type = industryType,
                    name = industryConfig.name .. " " .. i,
                    x = x,
                    z = z,
                    produces = industryConfig.produces,
                    consumes = industryConfig.consumes,
                    productionRate = industryConfig.productionRate,
                    workers = industryConfig.workers,
                    stockpile = {} -- Lager für Waren
                }
                
                -- Stockpile initialisieren
                for _, good in pairs(industryConfig.produces) do
                    industry.stockpile[good] = 0
                end
                
                table.insert(industries, industry)
                placed = true
            end
            
            attempts = attempts + 1
        end
    end
    
    return industries
end

-- Hauptfunktion zur Kartengenerierung
function MapGenerator:GenerateMap(mapConfig)
    print("🗺️ Generiere Karte mit Konfiguration:", mapConfig)
    
    local rng = CreateSeededRandom(mapConfig.seed)
    local mapType = GameConfig.Map.Types[mapConfig.mapType]
    local mapSize = GameConfig.Map.Sizes[mapConfig.mapSize]
    
    local width, height = mapSize.x, mapSize.z
    
    -- Terrain generieren
    print("⛰️ Generiere Terrain...")
    local heightMap = GenerateHeightMap(width, height, rng, mapConfig.hilliness or mapType.hilliness.default)
    
    -- Wasser generieren
    print("🌊 Generiere Wasser...")
    local waterMap = GenerateWaterMap(width, height, rng, mapConfig.waterAmount or mapType.water.default, mapConfig.mapType)
    
    -- Städte platzieren
    print("🏙️ Platziere Städte...")
    local cities = GenerateCities(width, height, rng, mapConfig.mapType, heightMap, waterMap)
    
    -- Industrien platzieren
    print("🏭 Platziere Industrien...")
    local industries = GenerateIndustries(width, height, rng, heightMap, waterMap, cities)
    
    -- Karte speichern
    self.CurrentMap = {
        config = mapConfig,
        size = {width = width, height = height},
        heightMap = heightMap,
        waterMap = waterMap,
        cities = cities,
        industries = industries,
        startYear = mapConfig.startYear or 1850,
        generatedAt = os.time()
    }
    
    print("✅ Karte erfolgreich generiert!")
    print("📊 Statistiken:")
    print("   - Größe:", width .. "x" .. height)
    print("   - Städte:", #cities)
    print("   - Industrien:", #industries)
    
    return true
end

-- Karte in Workspace erstellen (vereinfacht als Konfiguration)
function MapGenerator:CreateWorldObjects()
    if not self.CurrentMap then
        warn("Keine Karte zum Erstellen vorhanden!")
        return false
    end
    
    -- Terrain-Folder erstellen
    local terrainFolder = Workspace:FindFirstChild("Terrain") or Instance.new("Folder")
    terrainFolder.Name = "Terrain"
    terrainFolder.Parent = Workspace
    
    -- Städte-Folder erstellen
    local citiesFolder = Workspace:FindFirstChild("Cities") or Instance.new("Folder")
    citiesFolder.Name = "Cities"
    citiesFolder.Parent = Workspace
    
    -- Industrien-Folder erstellen
    local industriesFolder = Workspace:FindFirstChild("Industries") or Instance.new("Folder")
    industriesFolder.Name = "Industries"
    industriesFolder.Parent = Workspace
    
    print("🌍 Welt-Objekte erstellt (als Folder-Struktur)")
    return true
end

-- Kartendaten abrufen
function MapGenerator:GetMapData()
    return self.CurrentMap
end

-- Initialisierung
function MapGenerator:Initialize()
    print("🗺️ MapGenerator initialisiert")
end

return MapGenerator
