-- ServerScriptService/Managers/PerformanceManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Performance-Überwachung und Optimierung

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Stats = game:GetService("Stats")

local PerformanceManager = {}
PerformanceManager.__index = PerformanceManager

function PerformanceManager.new()
    local self = setmetatable({}, PerformanceManager)
    
    -- Performance-Metriken
    self.metrics = {
        fps = 60,
        memory = 0,
        networkReceive = 0,
        networkSend = 0,
        scriptActivity = 0,
        renderTime = 0
    }
    
    -- Performance-Historie
    self.history = {
        fps = {},
        memory = {},
        network = {}
    }
    
    -- LOD-System (Level of Detail)
    self.lodSystem = {
        enabled = true,
        distances = {
            high = 100,    -- Hohe Details bis 100 Studs
            medium = 300,  -- Mittlere Details bis 300 Studs
            low = 1000     -- <PERSON><PERSON><PERSON><PERSON> Details bis 1000 Studs
        },
        objects = {}
    }
    
    -- Object Pooling
    self.objectPools = {
        vehicles = {},
        buildings = {},
        effects = {}
    }
    
    -- Performance-Einstellungen
    self.settings = {
        targetFPS = 60,
        maxMemoryMB = 1000,
        enableLOD = true,
        enableObjectPooling = true,
        enableAutoOptimization = true
    }
    
    -- Überwachungs-Timer
    self.lastUpdate = tick()
    self.updateInterval = 1 -- Sekunden
    
    return self
end

-- Performance-Metriken sammeln
function PerformanceManager:CollectMetrics()
    -- FPS berechnen
    self.metrics.fps = math.floor(1 / RunService.Heartbeat:Wait())
    
    -- Speicher-Verbrauch
    local memoryStats = Stats:FindFirstChild("MemoryStoreService")
    if memoryStats then
        self.metrics.memory = memoryStats:GetTotalMemoryUsageMb()
    end
    
    -- Netzwerk-Statistiken
    local networkStats = Stats:FindFirstChild("Network")
    if networkStats then
        local receive = networkStats:FindFirstChild("ServerStatsItem"):FindFirstChild("Data Receive")
        local send = networkStats:FindFirstChild("ServerStatsItem"):FindFirstChild("Data Send")
        
        if receive then self.metrics.networkReceive = receive.Value end
        if send then self.metrics.networkSend = send.Value end
    end
    
    -- Script-Aktivität
    local scriptStats = Stats:FindFirstChild("Script")
    if scriptStats then
        self.metrics.scriptActivity = scriptStats:FindFirstChild("Activity").Value
    end
end

-- Performance-Historie aktualisieren
function PerformanceManager:UpdateHistory()
    -- FPS-Historie
    table.insert(self.history.fps, self.metrics.fps)
    if #self.history.fps > 60 then -- Nur letzte 60 Werte behalten
        table.remove(self.history.fps, 1)
    end
    
    -- Speicher-Historie
    table.insert(self.history.memory, self.metrics.memory)
    if #self.history.memory > 60 then
        table.remove(self.history.memory, 1)
    end
    
    -- Netzwerk-Historie
    table.insert(self.history.network, {
        receive = self.metrics.networkReceive,
        send = self.metrics.networkSend
    })
    if #self.history.network > 60 then
        table.remove(self.history.network, 1)
    end
end

-- LOD-System aktualisieren
function PerformanceManager:UpdateLOD(playerPositions)
    if not self.settings.enableLOD then return end
    
    for objectId, object in pairs(self.lodSystem.objects) do
        if object.part and object.part.Parent then
            local minDistance = math.huge
            
            -- Nächste Spieler-Distanz finden
            for _, playerPos in pairs(playerPositions) do
                local distance = (object.part.Position - playerPos).Magnitude
                minDistance = math.min(minDistance, distance)
            end
            
            -- LOD-Level bestimmen
            local lodLevel = self:GetLODLevel(minDistance)
            
            -- LOD anwenden wenn geändert
            if object.currentLOD ~= lodLevel then
                self:ApplyLOD(object, lodLevel)
                object.currentLOD = lodLevel
            end
        else
            -- Objekt nicht mehr vorhanden, aus LOD entfernen
            self.lodSystem.objects[objectId] = nil
        end
    end
end

-- LOD-Level bestimmen
function PerformanceManager:GetLODLevel(distance)
    if distance <= self.lodSystem.distances.high then
        return "high"
    elseif distance <= self.lodSystem.distances.medium then
        return "medium"
    elseif distance <= self.lodSystem.distances.low then
        return "low"
    else
        return "hidden"
    end
end

-- LOD auf Objekt anwenden
function PerformanceManager:ApplyLOD(object, lodLevel)
    if not object.part then return end
    
    if lodLevel == "high" then
        -- Hohe Details: Alle Teile sichtbar
        for _, child in pairs(object.part:GetChildren()) do
            if child:IsA("BasePart") then
                child.Transparency = object.originalTransparency or 0
            end
        end
        
    elseif lodLevel == "medium" then
        -- Mittlere Details: Einige Details ausblenden
        for _, child in pairs(object.part:GetChildren()) do
            if child:IsA("BasePart") and child.Name:find("Detail") then
                child.Transparency = 1
            end
        end
        
    elseif lodLevel == "low" then
        -- Niedrige Details: Nur Hauptform
        for _, child in pairs(object.part:GetChildren()) do
            if child:IsA("BasePart") and child ~= object.part then
                child.Transparency = 1
            end
        end
        
    elseif lodLevel == "hidden" then
        -- Komplett ausblenden
        object.part.Transparency = 1
        for _, child in pairs(object.part:GetChildren()) do
            if child:IsA("BasePart") then
                child.Transparency = 1
            end
        end
    end
end

-- Objekt zu LOD-System hinzufügen
function PerformanceManager:AddLODObject(objectId, part, originalTransparency)
    self.lodSystem.objects[objectId] = {
        part = part,
        originalTransparency = originalTransparency or 0,
        currentLOD = "high"
    }
end

-- Object Pool erstellen
function PerformanceManager:CreateObjectPool(poolName, createFunction, initialSize)
    self.objectPools[poolName] = {
        available = {},
        inUse = {},
        createFunction = createFunction
    }
    
    -- Initial-Objekte erstellen
    for i = 1, initialSize or 10 do
        local obj = createFunction()
        obj.Parent = nil -- Nicht in Workspace
        table.insert(self.objectPools[poolName].available, obj)
    end
end

-- Objekt aus Pool holen
function PerformanceManager:GetPooledObject(poolName)
    local pool = self.objectPools[poolName]
    if not pool then return nil end
    
    local obj
    if #pool.available > 0 then
        -- Verfügbares Objekt wiederverwenden
        obj = table.remove(pool.available)
    else
        -- Neues Objekt erstellen
        obj = pool.createFunction()
    end
    
    table.insert(pool.inUse, obj)
    return obj
end

-- Objekt zu Pool zurückgeben
function PerformanceManager:ReturnPooledObject(poolName, obj)
    local pool = self.objectPools[poolName]
    if not pool then return end
    
    -- Aus inUse entfernen
    for i, usedObj in pairs(pool.inUse) do
        if usedObj == obj then
            table.remove(pool.inUse, i)
            break
        end
    end
    
    -- Objekt zurücksetzen
    obj.Parent = nil
    if obj:IsA("BasePart") then
        obj.Position = Vector3.new(0, 0, 0)
        obj.Rotation = Vector3.new(0, 0, 0)
    end
    
    -- Zu verfügbaren hinzufügen
    table.insert(pool.available, obj)
end

-- Automatische Optimierung
function PerformanceManager:AutoOptimize()
    if not self.settings.enableAutoOptimization then return end
    
    -- FPS zu niedrig?
    if self.metrics.fps < self.settings.targetFPS * 0.8 then
        print("⚡ Performance-Optimierung: FPS zu niedrig")
        
        -- LOD-Distanzen reduzieren
        self.lodSystem.distances.high = math.max(50, self.lodSystem.distances.high * 0.9)
        self.lodSystem.distances.medium = math.max(150, self.lodSystem.distances.medium * 0.9)
        
        -- Partikel-Effekte reduzieren
        self:ReduceParticleEffects()
    end
    
    -- Speicher zu hoch?
    if self.metrics.memory > self.settings.maxMemoryMB then
        print("⚡ Performance-Optimierung: Speicher zu hoch")
        
        -- Garbage Collection forcieren
        collectgarbage("collect")
        
        -- Object Pools bereinigen
        self:CleanupObjectPools()
    end
end

-- Partikel-Effekte reduzieren
function PerformanceManager:ReduceParticleEffects()
    for _, obj in pairs(workspace:GetDescendants()) do
        if obj:IsA("ParticleEmitter") then
            obj.Rate = obj.Rate * 0.7
        end
    end
end

-- Object Pools bereinigen
function PerformanceManager:CleanupObjectPools()
    for poolName, pool in pairs(self.objectPools) do
        -- Nur die Hälfte der verfügbaren Objekte behalten
        local keepCount = math.ceil(#pool.available / 2)
        
        for i = keepCount + 1, #pool.available do
            local obj = pool.available[i]
            if obj and obj.Parent then
                obj:Destroy()
            end
        end
        
        -- Array kürzen
        for i = #pool.available, keepCount + 1, -1 do
            pool.available[i] = nil
        end
    end
end

-- Performance-Bericht erstellen
function PerformanceManager:GenerateReport()
    local avgFPS = 0
    local avgMemory = 0
    
    -- Durchschnittswerte berechnen
    if #self.history.fps > 0 then
        local sum = 0
        for _, fps in pairs(self.history.fps) do
            sum = sum + fps
        end
        avgFPS = sum / #self.history.fps
    end
    
    if #self.history.memory > 0 then
        local sum = 0
        for _, memory in pairs(self.history.memory) do
            sum = sum + memory
        end
        avgMemory = sum / #self.history.memory
    end
    
    return {
        current = self.metrics,
        averages = {
            fps = avgFPS,
            memory = avgMemory
        },
        lodObjects = #self.lodSystem.objects,
        pooledObjects = self:CountPooledObjects(),
        optimizationsApplied = self.optimizationsApplied or 0
    }
end

-- Gepoolte Objekte zählen
function PerformanceManager:CountPooledObjects()
    local total = 0
    for _, pool in pairs(self.objectPools) do
        total = total + #pool.available + #pool.inUse
    end
    return total
end

-- Update-Funktion
function PerformanceManager:Update(deltaTime, playerPositions)
    local currentTime = tick()
    
    -- Nur alle updateInterval Sekunden aktualisieren
    if currentTime - self.lastUpdate >= self.updateInterval then
        self:CollectMetrics()
        self:UpdateHistory()
        self:AutoOptimize()
        
        self.lastUpdate = currentTime
    end
    
    -- LOD jedes Frame aktualisieren
    if playerPositions then
        self:UpdateLOD(playerPositions)
    end
end

return PerformanceManager
