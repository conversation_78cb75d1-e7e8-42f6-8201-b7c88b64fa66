-- ServerScriptService/Managers/InfrastructureManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Infrastruktur-Bau-System für Straßen, Schienen, Wasserwege

local InfrastructureManager = {}
InfrastructureManager.__index = InfrastructureManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local PathfindingService = game:GetService("PathfindingService")

-- Infrastruktur-Typen
local INFRASTRUCTURE_TYPES = {
    ROAD = {
        name = "Straße",
        cost = 100, -- Pro Segment
        maxSpeed = 80, -- km/h
        width = 8,
        height = 0.5,
        material = Enum.Material.Asphalt,
        color = Color3.fromRGB(60, 60, 60),
        vehicleTypes = {"TRUCK", "BUS", "CAR"}
    },
    RAILWAY = {
        name = "Schiene",
        cost = 500,
        maxSpeed = 120,
        width = 4,
        height = 0.3,
        material = Enum.Material.Metal,
        color = Color3.fromRGB(120, 100, 80),
        vehicleTypes = {"TRAIN"}
    },
    WATERWAY = {
        name = "Wasserweg",
        cost = 300,
        maxSpeed = 40,
        width = 20,
        height = -2, -- Unter Wasser
        material = Enum.Material.Water,
        color = Color3.fromRGB(50, 100, 150),
        vehicleTypes = {"SHIP"}
    },
    BRIDGE = {
        name = "Brücke",
        cost = 2000,
        maxSpeed = 60,
        width = 10,
        height = 15,
        material = Enum.Material.Concrete,
        color = Color3.fromRGB(150, 150, 150),
        vehicleTypes = {"TRUCK", "BUS", "CAR", "TRAIN"}
    },
    TUNNEL = {
        name = "Tunnel",
        cost = 5000,
        maxSpeed = 80,
        width = 12,
        height = -8, -- Unter der Erde
        material = Enum.Material.Rock,
        color = Color3.fromRGB(80, 80, 80),
        vehicleTypes = {"TRUCK", "BUS", "CAR", "TRAIN"}
    }
}

-- Signal-Typen
local SIGNAL_TYPES = {
    TRAFFIC_LIGHT = {
        name = "Ampel",
        cost = 1000,
        size = Vector3.new(2, 8, 2),
        color = Color3.fromRGB(200, 200, 50),
        applicableInfra = {"ROAD"}
    },
    RAILWAY_SIGNAL = {
        name = "Bahnsignal",
        cost = 2000,
        size = Vector3.new(1, 6, 1),
        color = Color3.fromRGB(255, 0, 0),
        applicableInfra = {"RAILWAY"}
    },
    SPEED_LIMIT = {
        name = "Geschwindigkeitsbegrenzung",
        cost = 200,
        size = Vector3.new(1, 3, 0.2),
        color = Color3.fromRGB(255, 255, 255),
        applicableInfra = {"ROAD", "RAILWAY"}
    }
}

-- Konstruktor
function InfrastructureManager.new()
    local self = setmetatable({}, InfrastructureManager)
    
    self.infrastructure = {}
    self.signals = {}
    self.bridges = {}
    self.tunnels = {}
    self.nextInfraId = 1
    self.nextSignalId = 1
    
    self:InitializeEvents()
    self:CreateInfrastructureFolder()
    
    return self
end

-- Events initialisieren
function InfrastructureManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("BuildInfrastructureEvent") then
        local buildInfraEvent = Instance.new("RemoteEvent")
        buildInfraEvent.Name = "BuildInfrastructureEvent"
        buildInfraEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("PlaceSignalEvent") then
        local placeSignalEvent = Instance.new("RemoteEvent")
        placeSignalEvent.Name = "PlaceSignalEvent"
        placeSignalEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("GetInfrastructureDataFunction") then
        local getInfraDataFunction = Instance.new("RemoteFunction")
        getInfraDataFunction.Name = "GetInfrastructureDataFunction"
        getInfraDataFunction.Parent = Events
    end
    
    -- Event-Handler
    Events.BuildInfrastructureEvent.OnServerEvent:Connect(function(player, infraType, startPos, endPos)
        self:BuildInfrastructure(player, infraType, startPos, endPos)
    end)
    
    Events.PlaceSignalEvent.OnServerEvent:Connect(function(player, signalType, position, infraId)
        self:PlaceSignal(player, signalType, position, infraId)
    end)
    
    Events.GetInfrastructureDataFunction.OnServerInvoke = function(player)
        return self:GetInfrastructureData(player)
    end
end

-- Infrastruktur-Ordner erstellen
function InfrastructureManager:CreateInfrastructureFolder()
    if not Workspace:FindFirstChild("Infrastructure") then
        local infraFolder = Instance.new("Folder")
        infraFolder.Name = "Infrastructure"
        infraFolder.Parent = Workspace
        
        -- Unterordner
        local roadsFolder = Instance.new("Folder")
        roadsFolder.Name = "Roads"
        roadsFolder.Parent = infraFolder
        
        local railwaysFolder = Instance.new("Folder")
        railwaysFolder.Name = "Railways"
        railwaysFolder.Parent = infraFolder
        
        local waterwaysFolder = Instance.new("Folder")
        waterwaysFolder.Name = "Waterways"
        waterwaysFolder.Parent = infraFolder
        
        local bridgesFolder = Instance.new("Folder")
        bridgesFolder.Name = "Bridges"
        bridgesFolder.Parent = infraFolder
        
        local tunnelsFolder = Instance.new("Folder")
        tunnelsFolder.Name = "Tunnels"
        tunnelsFolder.Parent = infraFolder
        
        local signalsFolder = Instance.new("Folder")
        signalsFolder.Name = "Signals"
        signalsFolder.Parent = infraFolder
    end
end

-- Infrastruktur bauen
function InfrastructureManager:BuildInfrastructure(player, infraType, startPos, endPos)
    local infraConfig = INFRASTRUCTURE_TYPES[infraType]
    if not infraConfig then
        warn("Unbekannter Infrastruktur-Typ:", infraType)
        return false
    end
    
    -- Distanz und Kosten berechnen
    local distance = (endPos - startPos).Magnitude
    local segments = math.ceil(distance / 10) -- 10 Studs pro Segment
    local totalCost = segments * infraConfig.cost
    
    -- Kosten prüfen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager and not economyManager:CanPlayerAfford(player.UserId, totalCost) then
        return false
    end
    
    -- Infrastruktur-ID generieren
    local infraId = "infra_" .. self.nextInfraId
    self.nextInfraId = self.nextInfraId + 1
    
    -- Infrastruktur-Daten speichern
    local infrastructure = {
        id = infraId,
        type = infraType,
        owner = player.UserId,
        startPos = startPos,
        endPos = endPos,
        distance = distance,
        segments = segments,
        cost = totalCost,
        buildTime = tick(),
        isActive = true,
        signals = {},
        model = nil
    }
    
    -- 3D-Modell erstellen
    local model = self:CreateInfrastructureModel(infrastructure, infraConfig)
    infrastructure.model = model
    
    self.infrastructure[infraId] = infrastructure
    
    -- Kosten abziehen
    if economyManager then
        economyManager:DeductPlayerMoney(player.UserId, totalCost)
    end
    
    print("🛤️ Infrastruktur gebaut:", infraConfig.name, "von", startPos, "nach", endPos, "für", totalCost)
    return true
end

-- Infrastruktur-Modell erstellen
function InfrastructureManager:CreateInfrastructureModel(infrastructure, config)
    local model = Instance.new("Model")
    model.Name = infrastructure.id
    
    -- Ziel-Ordner bestimmen
    local targetFolder = Workspace.Infrastructure.Roads
    if infrastructure.type == "RAILWAY" then
        targetFolder = Workspace.Infrastructure.Railways
    elseif infrastructure.type == "WATERWAY" then
        targetFolder = Workspace.Infrastructure.Waterways
    elseif infrastructure.type == "BRIDGE" then
        targetFolder = Workspace.Infrastructure.Bridges
    elseif infrastructure.type == "TUNNEL" then
        targetFolder = Workspace.Infrastructure.Tunnels
    end
    
    model.Parent = targetFolder
    
    -- Segmente erstellen
    local direction = (infrastructure.endPos - infrastructure.startPos).Unit
    local segmentLength = infrastructure.distance / infrastructure.segments
    
    for i = 0, infrastructure.segments - 1 do
        local segmentPos = infrastructure.startPos + direction * (i * segmentLength + segmentLength/2)
        
        -- Terrain-Höhe anpassen
        local raycast = Workspace:Raycast(segmentPos + Vector3.new(0, 100, 0), Vector3.new(0, -200, 0))
        if raycast then
            segmentPos = Vector3.new(segmentPos.X, raycast.Position.Y + config.height, segmentPos.Z)
        end
        
        local segment = Instance.new("Part")
        segment.Name = "Segment_" .. i
        segment.Size = Vector3.new(segmentLength, config.height, config.width)
        segment.Position = segmentPos
        segment.Material = config.material
        segment.BrickColor = BrickColor.new(config.color)
        segment.Anchored = true
        segment.CanCollide = infrastructure.type ~= "WATERWAY"
        segment.Parent = model
        
        -- Rotation zur Ausrichtung
        local lookDirection = direction
        segment.CFrame = CFrame.lookAt(segmentPos, segmentPos + lookDirection)
        
        -- Spezielle Behandlung für verschiedene Typen
        if infrastructure.type == "RAILWAY" then
            self:AddRailwayDetails(segment, config)
        elseif infrastructure.type == "ROAD" then
            self:AddRoadDetails(segment, config)
        elseif infrastructure.type == "WATERWAY" then
            self:AddWaterwayDetails(segment, config)
        end
    end
    
    return model
end

-- Schienen-Details hinzufügen
function InfrastructureManager:AddRailwayDetails(segment, config)
    -- Schienen
    for i = -1, 1, 2 do
        local rail = Instance.new("Part")
        rail.Name = "Rail"
        rail.Size = Vector3.new(segment.Size.X, 0.2, 0.3)
        rail.Position = segment.Position + segment.CFrame.RightVector * i * 1.5
        rail.Material = Enum.Material.Metal
        rail.BrickColor = BrickColor.new("Really black")
        rail.Anchored = true
        rail.Parent = segment.Parent
    end
    
    -- Schwellen
    for i = 0, segment.Size.X, 2 do
        local tie = Instance.new("Part")
        tie.Name = "Tie"
        tie.Size = Vector3.new(0.3, 0.3, 4)
        tie.Position = segment.Position + segment.CFrame.LookVector * (i - segment.Size.X/2)
        tie.Material = Enum.Material.Wood
        tie.BrickColor = BrickColor.new("Brown")
        tie.Anchored = true
        tie.Parent = segment.Parent
    end
end

-- Straßen-Details hinzufügen
function InfrastructureManager:AddRoadDetails(segment, config)
    -- Mittelstreifen
    local centerLine = Instance.new("Part")
    centerLine.Name = "CenterLine"
    centerLine.Size = Vector3.new(segment.Size.X, 0.1, 0.2)
    centerLine.Position = segment.Position + Vector3.new(0, config.height/2 + 0.05, 0)
    centerLine.Material = Enum.Material.Neon
    centerLine.BrickColor = BrickColor.new("Bright yellow")
    centerLine.Anchored = true
    centerLine.Parent = segment.Parent
end

-- Wasserweg-Details hinzufügen
function InfrastructureManager:AddWaterwayDetails(segment, config)
    -- Wasser-Effekt
    segment.Material = Enum.Material.Water
    segment.Transparency = 0.3
    segment.CanCollide = false
    
    -- Ufer-Markierungen
    for i = -1, 1, 2 do
        local shore = Instance.new("Part")
        shore.Name = "Shore"
        shore.Size = Vector3.new(segment.Size.X, 1, 1)
        shore.Position = segment.Position + segment.CFrame.RightVector * i * (config.width/2 + 0.5)
        shore.Material = Enum.Material.Rock
        shore.BrickColor = BrickColor.new("Brown")
        shore.Anchored = true
        shore.Parent = segment.Parent
    end
end

-- Signal platzieren
function InfrastructureManager:PlaceSignal(player, signalType, position, infraId)
    local signalConfig = SIGNAL_TYPES[signalType]
    if not signalConfig then
        warn("Unbekannter Signal-Typ:", signalType)
        return false
    end
    
    local infrastructure = self.infrastructure[infraId]
    if not infrastructure then
        warn("Infrastruktur nicht gefunden:", infraId)
        return false
    end
    
    -- Prüfen ob Signal-Typ zur Infrastruktur passt
    local isCompatible = false
    for _, compatibleType in ipairs(signalConfig.applicableInfra) do
        if infrastructure.type == compatibleType then
            isCompatible = true
            break
        end
    end
    
    if not isCompatible then
        warn("Signal-Typ nicht kompatibel mit Infrastruktur")
        return false
    end
    
    -- Kosten prüfen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager and not economyManager:CanPlayerAfford(player.UserId, signalConfig.cost) then
        return false
    end
    
    -- Signal-ID generieren
    local signalId = "signal_" .. self.nextSignalId
    self.nextSignalId = self.nextSignalId + 1
    
    -- Signal-Daten speichern
    local signal = {
        id = signalId,
        type = signalType,
        owner = player.UserId,
        position = position,
        infraId = infraId,
        cost = signalConfig.cost,
        isActive = true,
        state = "GREEN", -- GREEN, YELLOW, RED
        model = nil
    }
    
    -- 3D-Modell erstellen
    local model = self:CreateSignalModel(signal, signalConfig)
    signal.model = model
    
    self.signals[signalId] = signal
    table.insert(infrastructure.signals, signalId)
    
    -- Kosten abziehen
    if economyManager then
        economyManager:DeductPlayerMoney(player.UserId, signalConfig.cost)
    end
    
    print("🚦 Signal platziert:", signalConfig.name, "an Position", position)
    return true
end

-- Signal-Modell erstellen
function InfrastructureManager:CreateSignalModel(signal, config)
    local model = Instance.new("Model")
    model.Name = signal.id
    model.Parent = Workspace.Infrastructure.Signals
    
    -- Signal-Mast
    local pole = Instance.new("Part")
    pole.Name = "Pole"
    pole.Size = Vector3.new(0.5, config.size.Y, 0.5)
    pole.Position = signal.position
    pole.Material = Enum.Material.Metal
    pole.BrickColor = BrickColor.new("Dark stone grey")
    pole.Anchored = true
    pole.Parent = model
    
    -- Signal-Kopf
    local head = Instance.new("Part")
    head.Name = "Head"
    head.Size = config.size
    head.Position = signal.position + Vector3.new(0, config.size.Y/2, 0)
    head.Material = Enum.Material.Plastic
    head.BrickColor = BrickColor.new(config.color)
    head.Anchored = true
    head.Parent = model
    
    -- Signal-Lichter (für Ampeln)
    if signal.type == "TRAFFIC_LIGHT" then
        local colors = {Color3.fromRGB(255, 0, 0), Color3.fromRGB(255, 255, 0), Color3.fromRGB(0, 255, 0)}
        for i, color in ipairs(colors) do
            local light = Instance.new("Part")
            light.Name = "Light_" .. i
            light.Size = Vector3.new(1, 1, 0.2)
            light.Position = head.Position + Vector3.new(0, 2 - i, 0.6)
            light.Material = Enum.Material.Neon
            light.Color = color
            light.Anchored = true
            light.Parent = model
        end
    end
    
    return model
end

-- Infrastruktur-Daten abrufen
function InfrastructureManager:GetInfrastructureData(player)
    local playerInfra = {}
    local playerSignals = {}
    
    -- Spieler-Infrastruktur filtern
    for infraId, infrastructure in pairs(self.infrastructure) do
        if infrastructure.owner == player.UserId then
            playerInfra[infraId] = {
                id = infrastructure.id,
                type = infrastructure.type,
                startPos = infrastructure.startPos,
                endPos = infrastructure.endPos,
                distance = infrastructure.distance,
                cost = infrastructure.cost,
                signals = infrastructure.signals,
                isActive = infrastructure.isActive
            }
        end
    end
    
    -- Spieler-Signale filtern
    for signalId, signal in pairs(self.signals) do
        if signal.owner == player.UserId then
            playerSignals[signalId] = {
                id = signal.id,
                type = signal.type,
                position = signal.position,
                infraId = signal.infraId,
                state = signal.state,
                isActive = signal.isActive
            }
        end
    end
    
    return {
        infrastructure = playerInfra,
        signals = playerSignals,
        infrastructureTypes = INFRASTRUCTURE_TYPES,
        signalTypes = SIGNAL_TYPES
    }
end

-- Update-Funktion
function InfrastructureManager:Update(deltaTime)
    -- Signal-Steuerung
    for signalId, signal in pairs(self.signals) do
        if signal.isActive and signal.type == "TRAFFIC_LIGHT" then
            self:UpdateTrafficLight(signal, deltaTime)
        end
    end
end

-- Ampel-Steuerung
function InfrastructureManager:UpdateTrafficLight(signal, deltaTime)
    -- Vereinfachte Ampel-Logik
    local time = tick()
    local cycle = time % 30 -- 30 Sekunden Zyklus
    
    if cycle < 10 then
        signal.state = "GREEN"
    elseif cycle < 13 then
        signal.state = "YELLOW"
    else
        signal.state = "RED"
    end
    
    -- Lichter aktualisieren
    if signal.model then
        for i, light in ipairs(signal.model:GetChildren()) do
            if light.Name:find("Light_") then
                local lightIndex = tonumber(light.Name:match("Light_(%d+)"))
                if (lightIndex == 1 and signal.state == "RED") or
                   (lightIndex == 2 and signal.state == "YELLOW") or
                   (lightIndex == 3 and signal.state == "GREEN") then
                    light.Transparency = 0
                else
                    light.Transparency = 0.8
                end
            end
        end
    end
end

return InfrastructureManager
