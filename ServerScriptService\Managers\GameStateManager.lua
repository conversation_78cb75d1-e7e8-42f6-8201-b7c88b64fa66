-- ServerScriptService/Managers/GameStateManager.lua
-- Zentraler Manager für Spielzustand und Zeitverlauf

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local GameStateManager = {}
GameStateManager.GameState = "menu" -- "menu", "loading", "playing", "paused"
GameStateManager.GameSpeed = 1 -- 0=Pause, 1=Normal, 2=Fast, 3=Fastest
GameStateManager.GameTime = {
    year = 1850,
    month = 1,
    day = 1,
    totalDays = 0,
    realTimeElapsed = 0
}
GameStateManager.Players = {} -- Spieler-spezifische Zustände

-- Spieler-Zustand initialisieren
function GameStateManager:InitializePlayer(player)
    local playerId = tostring(player.UserId)
    
    self.Players[playerId] = {
        player = player,
        state = "menu", -- "menu", "playing", "paused"
        joinedAt = os.time(),
        lastSave = 0,
        settings = {
            autoSave = true,
            notifications = true,
            soundEnabled = true
        }
    }
    
    print("🎮 Spieler-Zustand initialisiert:", player.Name)
end

-- Spieler entfernen
function GameStateManager:RemovePlayer(player)
    local playerId = tostring(player.UserId)
    
    if self.Players[playerId] then
        -- Auto-Save vor dem Entfernen
        if self.Players[playerId].settings.autoSave then
            self:TriggerAutoSave(player)
        end
        
        self.Players[playerId] = nil
        print("👋 Spieler-Zustand entfernt:", player.Name)
    end
end

-- Spiel starten
function GameStateManager:StartGame(player, mapConfig)
    local playerId = tostring(player.UserId)
    local playerState = self.Players[playerId]
    
    if not playerState then
        warn("Spieler-Zustand nicht gefunden:", player.Name)
        return false
    end
    
    -- Spielzeit aus Konfiguration setzen
    if mapConfig and mapConfig.startYear then
        self.GameTime.year = mapConfig.startYear
        self.GameTime.month = 1
        self.GameTime.day = 1
        self.GameTime.totalDays = 0
    end
    
    -- Spieler-Zustand aktualisieren
    playerState.state = "playing"
    
    -- Globaler Spielzustand
    if self.GameState == "menu" then
        self.GameState = "playing"
        self.GameSpeed = 1
    end
    
    print("🎮 Spiel gestartet für:", player.Name)
    return true
end

-- Spiel pausieren/fortsetzen
function GameStateManager:TogglePause(player)
    local playerId = tostring(player.UserId)
    local playerState = self.Players[playerId]
    
    if not playerState then return false end
    
    if playerState.state == "playing" then
        playerState.state = "paused"
        self.GameSpeed = 0
        print("⏸️ Spiel pausiert für:", player.Name)
    elseif playerState.state == "paused" then
        playerState.state = "playing"
        self.GameSpeed = 1
        print("▶️ Spiel fortgesetzt für:", player.Name)
    end
    
    return true
end

-- Spielgeschwindigkeit ändern
function GameStateManager:SetGameSpeed(player, speed)
    local playerId = tostring(player.UserId)
    local playerState = self.Players[playerId]
    
    if not playerState or playerState.state ~= "playing" then
        return false
    end
    
    -- Geschwindigkeit validieren
    if speed < 0 or speed > 3 then
        speed = 1
    end
    
    self.GameSpeed = speed
    
    local speedNames = {"Pausiert", "Normal", "Schnell", "Sehr schnell"}
    print("⏱️ Spielgeschwindigkeit geändert:", speedNames[speed + 1])
    
    -- Alle Spieler über Geschwindigkeitsänderung informieren
    self:BroadcastSpeedChange(speed)
    
    return true
end

-- Geschwindigkeitsänderung an alle Clients senden
function GameStateManager:BroadcastSpeedChange(speed)
    local Events = ReplicatedStorage:FindFirstChild("Events")
    if not Events then return end
    
    local SpeedChangedEvent = Events:FindFirstChild("SpeedChangedEvent")
    if not SpeedChangedEvent then
        SpeedChangedEvent = Instance.new("RemoteEvent")
        SpeedChangedEvent.Name = "SpeedChangedEvent"
        SpeedChangedEvent.Parent = Events
    end
    
    SpeedChangedEvent:FireAllClients(speed)
end

-- Spielzeit aktualisieren
function GameStateManager:Update(deltaTime)
    if self.GameState ~= "playing" or self.GameSpeed == 0 then
        return
    end
    
    -- Reale Zeit verfolgen
    self.GameTime.realTimeElapsed = self.GameTime.realTimeElapsed + deltaTime
    
    -- Spielzeit basierend auf Geschwindigkeit aktualisieren
    local timeMultiplier = self.GameSpeed * GameConfig.Game.TimeScale
    local gameTimeElapsed = deltaTime * timeMultiplier
    
    -- Tage hinzufügen (1 Sekunde = 1 Tag bei TimeScale = 1)
    local daysToAdd = math.floor(gameTimeElapsed)
    
    if daysToAdd > 0 then
        self:AdvanceTime(daysToAdd)
    end
    
    -- Auto-Save prüfen
    self:CheckAutoSave()
end

-- Zeit voranschreiten
function GameStateManager:AdvanceTime(days)
    for i = 1, days do
        self.GameTime.day = self.GameTime.day + 1
        self.GameTime.totalDays = self.GameTime.totalDays + 1
        
        -- Monat wechseln
        local daysInMonth = self:GetDaysInMonth(self.GameTime.year, self.GameTime.month)
        if self.GameTime.day > daysInMonth then
            self.GameTime.day = 1
            self.GameTime.month = self.GameTime.month + 1
            
            -- Jahr wechseln
            if self.GameTime.month > 12 then
                self.GameTime.month = 1
                self.GameTime.year = self.GameTime.year + 1
                
                print("🎊 Neues Jahr:", self.GameTime.year)
                self:BroadcastYearChange(self.GameTime.year)
            end
            
            print("📅 Neuer Monat:", self.GameTime.month .. "/" .. self.GameTime.year)
            self:BroadcastMonthChange(self.GameTime.month, self.GameTime.year)
        end
    end
end

-- Tage im Monat berechnen
function GameStateManager:GetDaysInMonth(year, month)
    local daysInMonth = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}
    
    -- Schaltjahr prüfen
    if month == 2 and self:IsLeapYear(year) then
        return 29
    end
    
    return daysInMonth[month] or 30
end

-- Schaltjahr prüfen
function GameStateManager:IsLeapYear(year)
    return (year % 4 == 0 and year % 100 ~= 0) or (year % 400 == 0)
end

-- Jahreswechsel an Clients senden
function GameStateManager:BroadcastYearChange(year)
    local Events = ReplicatedStorage:FindFirstChild("Events")
    if not Events then return end
    
    local YearChangedEvent = Events:FindFirstChild("YearChangedEvent")
    if not YearChangedEvent then
        YearChangedEvent = Instance.new("RemoteEvent")
        YearChangedEvent.Name = "YearChangedEvent"
        YearChangedEvent.Parent = Events
    end
    
    YearChangedEvent:FireAllClients(year)
end

-- Monatswechsel an Clients senden
function GameStateManager:BroadcastMonthChange(month, year)
    local Events = ReplicatedStorage:FindFirstChild("Events")
    if not Events then return end
    
    local MonthChangedEvent = Events:FindFirstChild("MonthChangedEvent")
    if not MonthChangedEvent then
        MonthChangedEvent = Instance.new("RemoteEvent")
        MonthChangedEvent.Name = "MonthChangedEvent"
        MonthChangedEvent.Parent = Events
    end
    
    MonthChangedEvent:FireAllClients(month, year)
end

-- Auto-Save prüfen
function GameStateManager:CheckAutoSave()
    local currentTime = os.time()
    
    for playerId, playerState in pairs(self.Players) do
        if playerState.settings.autoSave and 
           playerState.state == "playing" and
           currentTime - playerState.lastSave >= GameConfig.Game.AutoSaveInterval then
            
            self:TriggerAutoSave(playerState.player)
            playerState.lastSave = currentTime
        end
    end
end

-- Auto-Save auslösen
function GameStateManager:TriggerAutoSave(player)
    local SaveManager = require(script.Parent.SaveManager)
    
    if SaveManager then
        local success = SaveManager:AutoSave(player)
        if success then
            print("💾 Auto-Save erfolgreich für:", player.Name)
            self:SendNotification(player, "Spiel automatisch gespeichert")
        else
            warn("💾 Auto-Save fehlgeschlagen für:", player.Name)
        end
    end
end

-- Benachrichtigung an Spieler senden
function GameStateManager:SendNotification(player, message)
    local Events = ReplicatedStorage:FindFirstChild("Events")
    if not Events then return end
    
    local ShowNotificationEvent = Events:FindFirstChild("ShowNotificationEvent")
    if not ShowNotificationEvent then
        ShowNotificationEvent = Instance.new("RemoteEvent")
        ShowNotificationEvent.Name = "ShowNotificationEvent"
        ShowNotificationEvent.Parent = Events
    end
    
    ShowNotificationEvent:FireClient(player, message)
end

-- Spielzeit-Daten abrufen
function GameStateManager:GetGameTime()
    return {
        year = self.GameTime.year,
        month = self.GameTime.month,
        day = self.GameTime.day,
        totalDays = self.GameTime.totalDays,
        speed = self.GameSpeed,
        state = self.GameState
    }
end

-- Spieler-Einstellungen aktualisieren
function GameStateManager:UpdatePlayerSettings(player, settings)
    local playerId = tostring(player.UserId)
    local playerState = self.Players[playerId]
    
    if not playerState then return false end
    
    -- Einstellungen zusammenführen
    for key, value in pairs(settings) do
        if playerState.settings[key] ~= nil then
            playerState.settings[key] = value
        end
    end
    
    print("⚙️ Einstellungen aktualisiert für:", player.Name)
    return true
end

-- Spielstatistiken abrufen
function GameStateManager:GetGameStatistics()
    local totalPlayers = 0
    local activePlayers = 0
    
    for _, playerState in pairs(self.Players) do
        totalPlayers = totalPlayers + 1
        if playerState.state == "playing" then
            activePlayers = activePlayers + 1
        end
    end
    
    return {
        totalPlayers = totalPlayers,
        activePlayers = activePlayers,
        gameTime = self.GameTime,
        gameSpeed = self.GameSpeed,
        gameState = self.GameState,
        uptime = self.GameTime.realTimeElapsed
    }
end

-- Spiel zurücksetzen
function GameStateManager:ResetGame()
    self.GameState = "menu"
    self.GameSpeed = 1
    self.GameTime = {
        year = 1850,
        month = 1,
        day = 1,
        totalDays = 0,
        realTimeElapsed = 0
    }
    
    -- Alle Spieler zurücksetzen
    for playerId, playerState in pairs(self.Players) do
        playerState.state = "menu"
    end
    
    print("🔄 Spiel zurückgesetzt")
end

-- Spieler-Events verbinden
function GameStateManager:ConnectPlayerEvents()
    Players.PlayerAdded:Connect(function(player)
        self:InitializePlayer(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:RemovePlayer(player)
    end)
end

-- Initialisierung
function GameStateManager:Initialize()
    -- Bestehende Spieler initialisieren
    for _, player in pairs(Players:GetPlayers()) do
        self:InitializePlayer(player)
    end
    
    -- Player-Events verbinden
    self:ConnectPlayerEvents()
    
    print("🎮 GameStateManager initialisiert")
    print("📅 Startzeit:", self.GameTime.month .. "/" .. self.GameTime.year)
end

return GameStateManager
