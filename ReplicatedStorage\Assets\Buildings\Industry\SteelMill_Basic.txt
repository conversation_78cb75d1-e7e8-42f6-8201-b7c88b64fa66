# Stahlwerk - Grundstufe
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "STEEL_MILL_BASIC"
Name = "Stahlwerk"
Category = "Industry"
IndustryType = "Heavy_Industry"
TechLevel = 1
Era_Years = {1870, 2000}

[MODEL_DATA]
ModelId = "rbxassetid://STEEL_MILL_BASIC"
Scale = Vector3(40, 15, 60)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.4, 0.4, 0.4)      # Stahl/Beton
Secondary = Color3(0.6, 0.3, 0.1)    # Rost/Backstein
Accent = Color3(1, 0.5, 0)           # Glühende Öfen
Trim = Color3(0.2, 0.2, 0.2)         # Schwarzer Ruß

[GAMEPLAY_STATS]
Workers = 80
BuildCost = 25000
MaintenanceCost = 250
BuildTime = 120
PowerConsumption = 20
WaterConsumption = 25
LandSize = Vector2(8, 12)  # 8x12 Felder

[PRODUCTION_DATA]
OutputResource = "Steel"
ProductionRate = 80  # Tonnen/Monat
InputResources = {"Iron_Ore", "Coal"}
InputRatios = {
    Iron_Ore = 60,  # Tonnen/Monat
    Coal = 40       # Tonnen/Monat
}
StorageCapacity = 400

[REQUIREMENTS]
MinPopulation = 2000
MinYear = 1870
RequiredTech = {"Steel_Production"}
RequiredResources = {"Iron_Ore", "Coal"}
UnlockCost = 5000

[TECH_UPGRADES]
Level_1 = {
    Name = "STEEL_MILL_BASIC",
    ProductionRate = 80,
    Workers = 80,
    Texture = "Basic_Blast_Furnaces",
    Efficiency = 0.75
}
Level_2 = {
    Name = "STEEL_MILL_IMPROVED",
    ProductionRate = 120,
    Workers = 90,
    Texture = "Improved_Furnaces",
    RequiredTech = {"Improved_Steel_Production"},
    UpgradeCost = 12000,
    UnlockYear = 1900,
    Efficiency = 0.85
}
Level_3 = {
    Name = "STEEL_MILL_ELECTRIC",
    ProductionRate = 180,
    Workers = 70,
    Texture = "Electric_Arc_Furnaces",
    RequiredTech = {"Electric_Steel_Production"},
    UpgradeCost = 20000,
    UnlockYear = 1950,
    Efficiency = 0.95,
    PowerConsumption = 80
}
Level_4 = {
    Name = "STEEL_MILL_AUTOMATED",
    ProductionRate = 300,
    Workers = 40,
    Texture = "Automated_Systems",
    RequiredTech = {"Automated_Steel_Production"},
    UpgradeCost = 40000,
    UnlockYear = 1980,
    Efficiency = 0.98,
    PowerConsumption = 150
}

[FEATURES]
HasBlastFurnaces = true
HasSmokestacks = true
HasRailAccess = true
HasCoolingTowers = false  # Wird bei Level 3 hinzugefügt
HasAutomation = false     # Wird bei Level 4 hinzugefügt

[ENVIRONMENTAL_DATA]
PollutionLevel = 9
NoiseLevel = 8
WaterPollution = 6
AirPollution = 9

[ECONOMIC_DATA]
Employment = 80
TaxRevenue = 200
ResourceValue = 12  # Pro Tonne Steel
ExportPotential = true

[SUPPLY_CHAIN]
Suppliers = {"COAL_MINE_BASIC", "IRON_MINE_BASIC"}
Customers = {"MACHINE_FACTORY", "CONSTRUCTION_INDUSTRY"}

[DESCRIPTION]
ShortDesc = "Traditionelles Stahlwerk mit Hochöfen"
LongDesc = "Ein grundlegendes Stahlwerk mit Hochöfen zur Verarbeitung von Eisenerz und Kohle zu Stahl. Kann durch technologische Fortschritte zu einem hochmodernen, automatisierten Werk ausgebaut werden."
