# RemoteEvents Configuration
# Diese Datei definiert alle RemoteEvents und RemoteFunctions für die Client-Server Kommunikation

## RemoteEvents (Server -> Client und Client -> Server)

### Spiel-Management
- GenerateMapEvent: Client -> Server (Karte generieren)
- MapGeneratedEvent: Server -> Client (Karte wurde generiert)
- SaveGameEvent: Client -> Server (Spiel speichern)
- GameSavedEvent: Server -> Client (Spiel wurde gespeichert)
- LoadGameEvent: Client -> Server (Spiel laden)
- GameLoadedEvent: Server -> Client (Spiel wurde geladen)
- PlayerJoinedEvent: Server -> Client (Spieler beigetreten)

### Transport-System
- BuildRailwayEvent: Client -> Server (Eisenbahn bauen)
- BuildRoadEvent: Client -> Server (Straße bauen)
- BuildStationEvent: Client -> Server (Bahnhof/Depot bauen)
- CreateVehicleEvent: Client -> Server (Fahrzeug erstellen)
- CreateRouteEvent: Client -> Server (Route erstellen)
- UpdateRouteEvent: Client -> Server (Route aktualisieren)
- DeleteRouteEvent: Client -> Server (Route löschen)

### Wirtschafts-System
- UpdateEconomyEvent: Server -> Client (Wirtschaftsdaten aktualisiert)
- CityGrowthEvent: Server -> Client (Stadt ist gewachsen)
- IndustryUpdateEvent: Server -> Client (Industrie aktualisiert)
- CargoDeliveredEvent: Server -> Client (Waren geliefert)

### GUI-Events
- OpenMenuEvent: Client -> Server (Menü öffnen)
- CloseMenuEvent: Client -> Server (Menü schließen)
- UpdateUIEvent: Server -> Client (UI aktualisieren)
- ShowNotificationEvent: Server -> Client (Benachrichtigung anzeigen)

## RemoteFunctions (Request-Response)

### Daten-Abfragen
- GetMapDataFunction: Client -> Server (Kartendaten abrufen)
- GetEconomyDataFunction: Client -> Server (Wirtschaftsdaten abrufen)
- GetVehicleDataFunction: Client -> Server (Fahrzeugdaten abrufen)
- GetRouteDataFunction: Client -> Server (Routendaten abrufen)
- GetSaveDataFunction: Client -> Server (Speicherdaten abrufen)

### Validierung
- ValidateBuildFunction: Client -> Server (Bau validieren)
- ValidateRouteFunction: Client -> Server (Route validieren)
- CalculateCostFunction: Client -> Server (Kosten berechnen)

## Event-Parameter

### GenerateMapEvent
```lua
{
    seed = "string", -- Seed für Kartengenerierung
    mapSize = "Small|Medium|Large",
    mapType = "European|American|Asian",
    hilliness = 0.5, -- 0.0 - 1.0
    waterAmount = 0.3, -- 0.0 - 1.0
    startYear = 1850
}
```

### BuildRailwayEvent
```lua
{
    startPos = Vector3,
    endPos = Vector3,
    railType = "Standard|HighSpeed",
    cost = number
}
```

### CreateVehicleEvent
```lua
{
    vehicleType = "Train|Truck|Ship",
    model = "string",
    routeId = "string",
    cost = number
}
```

### UpdateEconomyEvent
```lua
{
    cities = {
        {id = "string", population = number, demands = {}},
        ...
    },
    industries = {
        {id = "string", production = number, goods = {}},
        ...
    },
    playerMoney = number,
    monthlyIncome = number,
    monthlyExpenses = number
}
```
