# Vehicle Assets für Transport Empire
# Detaillierte 3D-Modell-Spezifikationen für alle Fahrzeuge

## ZÜGE (Trains)

### Dampflokomotiven (1850-1900)
[TRAIN_STEAM_BASIC]
Name = "Grundlegende Dampflok"
Era = 1850-1880
ModelId = "rbxassetid://STEAM_LOCO_BASIC"
Scale = Vector3(4, 3, 12)
Speed = 25
Capacity = 0 (nur Lokomotive)
Cost = 15000
MaintenanceCost = 200
FuelType = "Coal"
FuelConsumption = 5
Colors = {
    Primary = Color3(0.2, 0.2, 0.2),    # Dunkelgrau
    Secondary = Color3(0.8, 0.1, 0.1),  # Rot
    Accent = Color3(0.9, 0.8, 0.3)      # Gold
}
Sounds = {
    Engine = "rbxassetid://STEAM_ENGINE_SOUND",
    Horn = "rbxassetid://STEAM_WHISTLE",
    Brake = "rbxassetid://STEAM_BRAKE"
}
Effects = {
    Smoke = "rbxassetid://STEAM_SMOKE_EFFECT",
    Steam = "rbxassetid://STEAM_EFFECT"
}

[TRAIN_STEAM_ADVANCED]
Name = "Fortgeschrittene Dampflok"
Era = 1880-1920
ModelId = "rbxassetid://STEAM_LOCO_ADVANCED"
Scale = Vector3(4.5, 3.5, 14)
Speed = 45
Capacity = 0
Cost = 35000
MaintenanceCost = 350
FuelType = "Coal"
FuelConsumption = 8
Colors = {
    Primary = Color3(0.1, 0.3, 0.1),
    Secondary = Color3(0.9, 0.9, 0.9),
    Accent = Color3(0.8, 0.6, 0.2)
}

### Diesellokomotiven (1920-1960)
[TRAIN_DIESEL_EARLY]
Name = "Frühe Diesellok"
Era = 1920-1950
ModelId = "rbxassetid://DIESEL_LOCO_EARLY"
Scale = Vector3(4, 3.5, 15)
Speed = 65
Capacity = 0
Cost = 75000
MaintenanceCost = 500
FuelType = "Diesel"
FuelConsumption = 12
Colors = {
    Primary = Color3(0.8, 0.4, 0.1),    # Orange
    Secondary = Color3(0.2, 0.2, 0.2),  # Schwarz
    Accent = Color3(0.9, 0.9, 0.9)      # Weiß
}

[TRAIN_DIESEL_MODERN]
Name = "Moderne Diesellok"
Era = 1950-2000
ModelId = "rbxassetid://DIESEL_LOCO_MODERN"
Scale = Vector3(4.2, 4, 16)
Speed = 85
Capacity = 0
Cost = 150000
MaintenanceCost = 800
FuelType = "Diesel"
FuelConsumption = 15

### Elektrolokomotiven (1960+)
[TRAIN_ELECTRIC_MODERN]
Name = "Elektrolokomotive"
Era = 1960-2000
ModelId = "rbxassetid://ELECTRIC_LOCO_MODERN"
Scale = Vector3(4, 4, 18)
Speed = 120
Capacity = 0
Cost = 250000
MaintenanceCost = 600
FuelType = "Electric"
FuelConsumption = 20
RequiresElectrification = true

## WAGGONS (Wagons)

### Passagierwaggons
[WAGON_PASSENGER_BASIC]
Name = "Einfacher Passagierwaggon"
Era = 1850-1900
ModelId = "rbxassetid://PASSENGER_WAGON_BASIC"
Scale = Vector3(3.5, 3, 10)
Capacity = 40
CargoType = "Passengers"
Cost = 8000
MaintenanceCost = 100
Colors = {
    Primary = Color3(0.6, 0.3, 0.1),    # Braun
    Secondary = Color3(0.9, 0.9, 0.7),  # Beige
    Accent = Color3(0.8, 0.6, 0.2)      # Gold
}

[WAGON_PASSENGER_LUXURY]
Name = "Luxus-Passagierwaggon"
Era = 1900-1950
ModelId = "rbxassetid://PASSENGER_WAGON_LUXURY"
Scale = Vector3(3.8, 3.2, 12)
Capacity = 30
CargoType = "Passengers"
Cost = 25000
MaintenanceCost = 200
IncomeMultiplier = 1.5

### Frachtwaggons
[WAGON_FREIGHT_BASIC]
Name = "Offener Güterwagen"
Era = 1850-2000
ModelId = "rbxassetid://FREIGHT_WAGON_OPEN"
Scale = Vector3(3.5, 2.5, 8)
Capacity = 20
CargoType = "General"
Cost = 5000
MaintenanceCost = 50

[WAGON_FREIGHT_COVERED]
Name = "Gedeckter Güterwagen"
Era = 1880-2000
ModelId = "rbxassetid://FREIGHT_WAGON_COVERED"
Scale = Vector3(3.5, 3, 10)
Capacity = 25
CargoType = "General"
Cost = 8000
MaintenanceCost = 75
WeatherProtection = true

[WAGON_TANK]
Name = "Tankwagen"
Era = 1900-2000
ModelId = "rbxassetid://TANK_WAGON"
Scale = Vector3(3.5, 3.5, 12)
Capacity = 30
CargoType = "Liquids"
Cost = 15000
MaintenanceCost = 150

## LASTKRAFTWAGEN (Trucks)

### Frühe LKWs (1920-1950)
[TRUCK_EARLY_SMALL]
Name = "Kleiner Lieferwagen"
Era = 1920-1950
ModelId = "rbxassetid://TRUCK_EARLY_SMALL"
Scale = Vector3(2, 1.8, 4)
Speed = 35
Capacity = 5
Cost = 8000
MaintenanceCost = 150
FuelType = "Gasoline"
FuelConsumption = 8
Colors = {
    Primary = Color3(0.2, 0.4, 0.6),    # Blau
    Secondary = Color3(0.9, 0.9, 0.9),  # Weiß
    Accent = Color3(0.8, 0.8, 0.8)      # Silber
}

[TRUCK_EARLY_MEDIUM]
Name = "Mittlerer LKW"
Era = 1930-1960
ModelId = "rbxassetid://TRUCK_EARLY_MEDIUM"
Scale = Vector3(2.5, 2.2, 6)
Speed = 45
Capacity = 12
Cost = 15000
MaintenanceCost = 250
FuelType = "Gasoline"
FuelConsumption = 12

### Moderne LKWs (1950+)
[TRUCK_MODERN_SMALL]
Name = "Moderner Kleintransporter"
Era = 1950-2000
ModelId = "rbxassetid://TRUCK_MODERN_SMALL"
Scale = Vector3(2.2, 2, 5)
Speed = 65
Capacity = 8
Cost = 25000
MaintenanceCost = 200
FuelType = "Diesel"
FuelConsumption = 6

[TRUCK_MODERN_LARGE]
Name = "Großer Sattelschlepper"
Era = 1960-2000
ModelId = "rbxassetid://TRUCK_MODERN_LARGE"
Scale = Vector3(3, 3, 12)
Speed = 75
Capacity = 25
Cost = 65000
MaintenanceCost = 400
FuelType = "Diesel"
FuelConsumption = 18

## SCHIFFE (Ships)

### Dampfschiffe (1850-1920)
[SHIP_STEAM_SMALL]
Name = "Kleines Dampfschiff"
Era = 1850-1920
ModelId = "rbxassetid://SHIP_STEAM_SMALL"
Scale = Vector3(6, 4, 20)
Speed = 15
Capacity = 50
Cost = 50000
MaintenanceCost = 800
FuelType = "Coal"
FuelConsumption = 15
RequiresWater = true
Colors = {
    Primary = Color3(0.3, 0.2, 0.1),    # Braun
    Secondary = Color3(0.9, 0.9, 0.9),  # Weiß
    Accent = Color3(0.8, 0.6, 0.2)      # Gold
}

[SHIP_STEAM_LARGE]
Name = "Großes Dampfschiff"
Era = 1880-1930
ModelId = "rbxassetid://SHIP_STEAM_LARGE"
Scale = Vector3(8, 6, 35)
Speed = 20
Capacity = 150
Cost = 150000
MaintenanceCost = 1500
FuelType = "Coal"
FuelConsumption = 30

### Motorschiffe (1920+)
[SHIP_MOTOR_CARGO]
Name = "Motorfrachtschiff"
Era = 1920-2000
ModelId = "rbxassetid://SHIP_MOTOR_CARGO"
Scale = Vector3(10, 8, 50)
Speed = 25
Capacity = 300
Cost = 300000
MaintenanceCost = 2000
FuelType = "Diesel"
FuelConsumption = 40

[SHIP_CONTAINER]
Name = "Containerschiff"
Era = 1960-2000
ModelId = "rbxassetid://SHIP_CONTAINER"
Scale = Vector3(12, 10, 80)
Speed = 30
Capacity = 500
Cost = 800000
MaintenanceCost = 4000
FuelType = "Diesel"
FuelConsumption = 60
CargoType = "Containers"

## SPEZIALFAHRZEUGE

### Busse
[BUS_EARLY]
Name = "Früher Omnibus"
Era = 1920-1950
ModelId = "rbxassetid://BUS_EARLY"
Scale = Vector3(2.5, 2.5, 8)
Speed = 40
Capacity = 25
CargoType = "Passengers"
Cost = 12000
MaintenanceCost = 200
FuelType = "Gasoline"
FuelConsumption = 10

[BUS_MODERN]
Name = "Moderner Stadtbus"
Era = 1960-2000
ModelId = "rbxassetid://BUS_MODERN"
Scale = Vector3(2.8, 3, 12)
Speed = 55
Capacity = 40
CargoType = "Passengers"
Cost = 35000
MaintenanceCost = 300
FuelType = "Diesel"
FuelConsumption = 12

### Flugzeuge (vereinfacht)
[PLANE_EARLY]
Name = "Frühe Passagiermaschine"
Era = 1930-1960
ModelId = "rbxassetid://PLANE_EARLY"
Scale = Vector3(15, 4, 12)
Speed = 200
Capacity = 20
CargoType = "Passengers"
Cost = 200000
MaintenanceCost = 2000
FuelType = "Aviation"
FuelConsumption = 50
RequiresAirport = true

## FAHRZEUG-KATEGORIEN

[VEHICLE_CATEGORIES]
Trains = {
    "TRAIN_STEAM_BASIC",
    "TRAIN_STEAM_ADVANCED", 
    "TRAIN_DIESEL_EARLY",
    "TRAIN_DIESEL_MODERN",
    "TRAIN_ELECTRIC_MODERN"
}

Wagons = {
    "WAGON_PASSENGER_BASIC",
    "WAGON_PASSENGER_LUXURY",
    "WAGON_FREIGHT_BASIC",
    "WAGON_FREIGHT_COVERED",
    "WAGON_TANK"
}

Trucks = {
    "TRUCK_EARLY_SMALL",
    "TRUCK_EARLY_MEDIUM",
    "TRUCK_MODERN_SMALL",
    "TRUCK_MODERN_LARGE"
}

Ships = {
    "SHIP_STEAM_SMALL",
    "SHIP_STEAM_LARGE",
    "SHIP_MOTOR_CARGO",
    "SHIP_CONTAINER"
}

Buses = {
    "BUS_EARLY",
    "BUS_MODERN"
}

Planes = {
    "PLANE_EARLY"
}

## PERFORMANCE-EINSTELLUNGEN

[LOD_SETTINGS]
# Level of Detail für Performance-Optimierung
Distance_High = 50      # Volle Details bis 50 Studs
Distance_Medium = 150   # Mittlere Details bis 150 Studs  
Distance_Low = 300      # Niedrige Details bis 300 Studs
Distance_Cull = 500     # Ausblenden ab 500 Studs

[ANIMATION_SETTINGS]
WheelRotation = true
SmokeEffects = true
LightEffects = true
SoundEffects = true
MaxActiveVehicles = 100  # Maximale Anzahl aktiver Fahrzeuge

## UPGRADE-SYSTEM

[VEHICLE_UPGRADES]
Engine = {
    Level1 = {SpeedBonus = 0.1, Cost = 5000},
    Level2 = {SpeedBonus = 0.2, Cost = 15000},
    Level3 = {SpeedBonus = 0.3, Cost = 35000}
}

Cargo = {
    Level1 = {CapacityBonus = 0.15, Cost = 3000},
    Level2 = {CapacityBonus = 0.3, Cost = 8000},
    Level3 = {CapacityBonus = 0.5, Cost = 20000}
}

Efficiency = {
    Level1 = {FuelSaving = 0.1, Cost = 4000},
    Level2 = {FuelSaving = 0.2, Cost = 12000},
    Level3 = {FuelSaving = 0.35, Cost = 25000}
}
