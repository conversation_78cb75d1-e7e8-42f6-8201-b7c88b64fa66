-- StarterPlayerScripts/GUI/ModernStatsGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Statistiken-GUI mit Diagrammen und detaillierter Analyse

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetStatisticsFunction = Events:WaitForChild("GetStatisticsFunction")
local GetCityStatsFunction = Events:WaitForChild("GetCityStatsFunction")
local GetTransportStatsFunction = Events:WaitForChild("GetTransportStatsFunction")

local ModernStatsGUI = {}
ModernStatsGUI.__index = ModernStatsGUI

-- Konstruktor
function ModernStatsGUI.new()
    local self = setmetatable({}, ModernStatsGUI)
    
    self.isOpen = false
    self.isDocked = false
    self.currentTab = "OVERVIEW" -- OVERVIEW, TRANSPORT, CITIES, ECONOMY, ENVIRONMENT
    self.statsData = {}
    self.updateConnection = nil
    
    return self
end

-- GUI erstellen
function ModernStatsGUI:CreateGUI()
    if self.screenGui then return end
    
    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernStatsGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Hauptfenster
    local mainWindow = Instance.new("Frame")
    mainWindow.Size = UDim2.new(0, 1400, 0, 900)
    mainWindow.Position = UDim2.new(0.5, -700, 0.5, -450)
    mainWindow.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    mainWindow.BorderSizePixel = 0
    mainWindow.Visible = false
    mainWindow.Parent = self.screenGui
    
    local windowCorner = Instance.new("UICorner")
    windowCorner.CornerRadius = UDim.new(0, 15)
    windowCorner.Parent = mainWindow
    
    -- Fenster-Gradient
    local windowGradient = Instance.new("UIGradient")
    windowGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(15, 20, 25)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(5, 10, 15))
    }
    windowGradient.Rotation = 45
    windowGradient.Parent = mainWindow
    
    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 60)
    titleBar.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainWindow
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 15)
    titleCorner.Parent = titleBar
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -200, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "📊 STATISTIK-ZENTRALE"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Aktualisierungs-Status
    local updateStatus = Instance.new("TextLabel")
    updateStatus.Size = UDim2.new(0, 150, 0, 30)
    updateStatus.Position = UDim2.new(1, -300, 0, 15)
    updateStatus.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
    updateStatus.Text = "🔄 LIVE"
    updateStatus.TextColor3 = Color3.fromRGB(255, 255, 255)
    updateStatus.TextScaled = true
    updateStatus.Font = Enum.Font.SourceSansBold
    updateStatus.BorderSizePixel = 0
    updateStatus.Parent = titleBar
    
    local statusCorner = Instance.new("UICorner")
    statusCorner.CornerRadius = UDim.new(0, 8)
    statusCorner.Parent = updateStatus
    
    -- Dock-Button
    local dockButton = Instance.new("TextButton")
    dockButton.Size = UDim2.new(0, 45, 0, 45)
    dockButton.Position = UDim2.new(1, -100, 0, 7.5)
    dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    dockButton.Text = "📌"
    dockButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    dockButton.TextScaled = true
    dockButton.Font = Enum.Font.SourceSans
    dockButton.BorderSizePixel = 0
    dockButton.Parent = titleBar
    
    local dockCorner = Instance.new("UICorner")
    dockCorner.CornerRadius = UDim.new(0, 10)
    dockCorner.Parent = dockButton
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 45, 0, 45)
    closeButton.Position = UDim2.new(1, -50, 0, 7.5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 10)
    closeCorner.Parent = closeButton
    
    -- Tab-Navigation
    local tabContainer = Instance.new("Frame")
    tabContainer.Size = UDim2.new(1, -20, 0, 60)
    tabContainer.Position = UDim2.new(0, 10, 0, 70)
    tabContainer.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    tabContainer.BorderSizePixel = 0
    tabContainer.Parent = mainWindow
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 12)
    tabCorner.Parent = tabContainer
    
    local tabLayout = Instance.new("UIListLayout")
    tabLayout.FillDirection = Enum.FillDirection.Horizontal
    tabLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    tabLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    tabLayout.Padding = UDim.new(0, 10)
    tabLayout.Parent = tabContainer
    
    local tabPadding = Instance.new("UIPadding")
    tabPadding.PaddingAll = UDim.new(0, 15)
    tabPadding.Parent = tabContainer
    
    -- Tab-Buttons erstellen
    local tabs = {
        {text = "📈 ÜBERSICHT", id = "OVERVIEW", color = Color3.fromRGB(100, 150, 255)},
        {text = "🚂 TRANSPORT", id = "TRANSPORT", color = Color3.fromRGB(100, 255, 100)},
        {text = "🏙️ STÄDTE", id = "CITIES", color = Color3.fromRGB(255, 200, 100)},
        {text = "💰 WIRTSCHAFT", id = "ECONOMY", color = Color3.fromRGB(255, 150, 100)},
        {text = "🌍 UMWELT", id = "ENVIRONMENT", color = Color3.fromRGB(150, 255, 150)}
    }
    
    self.tabButtons = {}
    
    for _, tabData in ipairs(tabs) do
        local tabButton = Instance.new("TextButton")
        tabButton.Size = UDim2.new(0, 180, 0, 40)
        tabButton.BackgroundColor3 = tabData.color
        tabButton.Text = tabData.text
        tabButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        tabButton.TextScaled = true
        tabButton.Font = Enum.Font.SourceSansBold
        tabButton.BorderSizePixel = 0
        tabButton.Parent = tabContainer
        
        local tabButtonCorner = Instance.new("UICorner")
        tabButtonCorner.CornerRadius = UDim.new(0, 8)
        tabButtonCorner.Parent = tabButton
        
        -- Hover-Effekt
        tabButton.MouseEnter:Connect(function()
            if self.currentTab ~= tabData.id then
                TweenService:Create(tabButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.new(
                        math.min(tabData.color.R + 0.1, 1),
                        math.min(tabData.color.G + 0.1, 1),
                        math.min(tabData.color.B + 0.1, 1)
                    )
                }):Play()
            end
        end)
        
        tabButton.MouseLeave:Connect(function()
            if self.currentTab ~= tabData.id then
                TweenService:Create(tabButton, TweenInfo.new(0.2), {
                    BackgroundColor3 = tabData.color
                }):Play()
            end
        end)
        
        -- Click-Handler
        tabButton.MouseButton1Click:Connect(function()
            self:SwitchTab(tabData.id)
        end)
        
        self.tabButtons[tabData.id] = {button = tabButton, color = tabData.color}
    end
    
    -- Content-Bereich
    self.contentFrame = Instance.new("Frame")
    self.contentFrame.Size = UDim2.new(1, -20, 1, -150)
    self.contentFrame.Position = UDim2.new(0, 10, 0, 140)
    self.contentFrame.BackgroundColor3 = Color3.fromRGB(5, 10, 15)
    self.contentFrame.BorderSizePixel = 0
    self.contentFrame.Parent = mainWindow
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 12)
    contentCorner.Parent = self.contentFrame
    
    -- Referenzen speichern
    self.mainWindow = mainWindow
    self.titleBar = titleBar
    self.updateStatus = updateStatus
    self.dockButton = dockButton
    self.closeButton = closeButton
    
    -- Event-Handler
    dockButton.MouseButton1Click:Connect(function()
        self:ToggleDock()
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Drag-Funktionalität
    self:SetupDragging()
    
    return self.screenGui
end

-- Tab wechseln
function ModernStatsGUI:SwitchTab(tabId)
    if self.currentTab == tabId then return end
    
    -- Alten Tab deaktivieren
    if self.tabButtons[self.currentTab] then
        local oldButton = self.tabButtons[self.currentTab].button
        local oldColor = self.tabButtons[self.currentTab].color
        TweenService:Create(oldButton, TweenInfo.new(0.2), {
            BackgroundColor3 = oldColor,
            BackgroundTransparency = 0.3
        }):Play()
    end
    
    -- Neuen Tab aktivieren
    if self.tabButtons[tabId] then
        local newButton = self.tabButtons[tabId].button
        TweenService:Create(newButton, TweenInfo.new(0.2), {
            BackgroundTransparency = 0,
            Size = UDim2.new(0, 185, 0, 42)
        }):Play()
    end
    
    self.currentTab = tabId
    
    -- Content aktualisieren
    self:UpdateTabContent()
end

-- Tab-Inhalt aktualisieren
function ModernStatsGUI:UpdateTabContent()
    -- Alten Inhalt löschen
    for _, child in pairs(self.contentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    -- Neuen Inhalt basierend auf Tab erstellen
    if self.currentTab == "OVERVIEW" then
        self:CreateOverviewContent()
    elseif self.currentTab == "TRANSPORT" then
        self:CreateTransportContent()
    elseif self.currentTab == "CITIES" then
        self:CreateCitiesContent()
    elseif self.currentTab == "ECONOMY" then
        self:CreateEconomyContent()
    elseif self.currentTab == "ENVIRONMENT" then
        self:CreateEnvironmentContent()
    end
end

-- Übersicht-Inhalt erstellen
function ModernStatsGUI:CreateOverviewContent()
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "📈 UNTERNEHMENS-ÜBERSICHT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame
    
    -- KPI-Karten Container
    local kpiContainer = Instance.new("Frame")
    kpiContainer.Size = UDim2.new(1, -40, 0, 150)
    kpiContainer.Position = UDim2.new(0, 20, 0, 80)
    kpiContainer.BackgroundTransparency = 1
    kpiContainer.Parent = self.contentFrame
    
    local kpiLayout = Instance.new("UIListLayout")
    kpiLayout.FillDirection = Enum.FillDirection.Horizontal
    kpiLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    kpiLayout.VerticalAlignment = Enum.VerticalAlignment.Top
    kpiLayout.Padding = UDim.new(0, 20)
    kpiLayout.Parent = kpiContainer
    
    -- KPI-Karten
    local kpis = {
        {title = "🚂 FAHRZEUGE", value = "247", subtitle = "Aktive Fahrzeuge", color = Color3.fromRGB(100, 150, 255)},
        {title = "🏙️ STÄDTE", value = "18", subtitle = "Versorgte Städte", color = Color3.fromRGB(100, 255, 100)},
        {title = "📈 GEWINN", value = "+2.1M $", subtitle = "Monatlich", color = Color3.fromRGB(255, 200, 100)},
        {title = "👥 PASSAGIERE", value = "45.2K", subtitle = "Pro Monat", color = Color3.fromRGB(150, 255, 150)}
    }
    
    for _, kpiData in ipairs(kpis) do
        local kpiCard = Instance.new("Frame")
        kpiCard.Size = UDim2.new(0, 300, 1, 0)
        kpiCard.BackgroundColor3 = kpiData.color
        kpiCard.BorderSizePixel = 0
        kpiCard.Parent = kpiContainer
        
        local kpiCorner = Instance.new("UICorner")
        kpiCorner.CornerRadius = UDim.new(0, 12)
        kpiCorner.Parent = kpiCard
        
        local kpiGradient = Instance.new("UIGradient")
        kpiGradient.Color = ColorSequence.new{
            ColorSequenceKeypoint.new(0, kpiData.color),
            ColorSequenceKeypoint.new(1, Color3.new(
                math.max(kpiData.color.R - 0.1, 0),
                math.max(kpiData.color.G - 0.1, 0),
                math.max(kpiData.color.B - 0.1, 0)
            ))
        }
        kpiGradient.Rotation = 45
        kpiGradient.Parent = kpiCard
        
        -- KPI-Titel
        local kpiTitle = Instance.new("TextLabel")
        kpiTitle.Size = UDim2.new(1, -20, 0, 30)
        kpiTitle.Position = UDim2.new(0, 10, 0, 15)
        kpiTitle.BackgroundTransparency = 1
        kpiTitle.Text = kpiData.title
        kpiTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
        kpiTitle.TextScaled = true
        kpiTitle.Font = Enum.Font.SourceSansBold
        kpiTitle.TextXAlignment = Enum.TextXAlignment.Left
        kpiTitle.Parent = kpiCard
        
        -- KPI-Wert
        local kpiValue = Instance.new("TextLabel")
        kpiValue.Size = UDim2.new(1, -20, 0, 50)
        kpiValue.Position = UDim2.new(0, 10, 0, 45)
        kpiValue.BackgroundTransparency = 1
        kpiValue.Text = kpiData.value
        kpiValue.TextColor3 = Color3.fromRGB(255, 255, 255)
        kpiValue.TextScaled = true
        kpiValue.Font = Enum.Font.SourceSansBold
        kpiValue.TextXAlignment = Enum.TextXAlignment.Left
        kpiValue.Parent = kpiCard
        
        -- KPI-Untertitel
        local kpiSubtitle = Instance.new("TextLabel")
        kpiSubtitle.Size = UDim2.new(1, -20, 0, 25)
        kpiSubtitle.Position = UDim2.new(0, 10, 0, 95)
        kpiSubtitle.BackgroundTransparency = 1
        kpiSubtitle.Text = kpiData.subtitle
        kpiSubtitle.TextColor3 = Color3.fromRGB(220, 220, 220)
        kpiSubtitle.TextScaled = true
        kpiSubtitle.Font = Enum.Font.SourceSans
        kpiSubtitle.TextXAlignment = Enum.TextXAlignment.Left
        kpiSubtitle.Parent = kpiCard
    end
    
    -- Diagramm-Bereich
    local chartContainer = Instance.new("Frame")
    chartContainer.Size = UDim2.new(1, -40, 1, -250)
    chartContainer.Position = UDim2.new(0, 20, 0, 250)
    chartContainer.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    chartContainer.BorderSizePixel = 0
    chartContainer.Parent = self.contentFrame
    
    local chartCorner = Instance.new("UICorner")
    chartCorner.CornerRadius = UDim.new(0, 12)
    chartCorner.Parent = chartContainer
    
    local chartTitle = Instance.new("TextLabel")
    chartTitle.Size = UDim2.new(1, -20, 0, 40)
    chartTitle.Position = UDim2.new(0, 10, 0, 10)
    chartTitle.BackgroundTransparency = 1
    chartTitle.Text = "📊 GEWINN-ENTWICKLUNG (12 MONATE)"
    chartTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    chartTitle.TextScaled = true
    chartTitle.Font = Enum.Font.SourceSansBold
    chartTitle.TextXAlignment = Enum.TextXAlignment.Left
    chartTitle.Parent = chartContainer
    
    local chartPlaceholder = Instance.new("TextLabel")
    chartPlaceholder.Size = UDim2.new(1, -20, 1, -60)
    chartPlaceholder.Position = UDim2.new(0, 10, 0, 50)
    chartPlaceholder.BackgroundTransparency = 1
    chartPlaceholder.Text = "🚧 Interaktive Diagramme\n\nHier werden detaillierte Diagramme\nzur Gewinn-Entwicklung,\nPassagier-Aufkommen und\nFracht-Volumen angezeigt."
    chartPlaceholder.TextColor3 = Color3.fromRGB(150, 150, 150)
    chartPlaceholder.TextScaled = true
    chartPlaceholder.Font = Enum.Font.SourceSans
    chartPlaceholder.Parent = chartContainer
end

-- Transport-Inhalt erstellen
function ModernStatsGUI:CreateTransportContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🚂 TRANSPORT-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Transport-Statistiken\n\n• Fahrzeug-Auslastung\n• Linien-Performance\n• Passagier-Aufkommen\n• Fracht-Volumen\n• Pünktlichkeits-Statistiken\n\nDetaillierte Transport-Analysen werden hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Städte-Inhalt erstellen
function ModernStatsGUI:CreateCitiesContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🏙️ STADT-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Stadt-Statistiken\n\n• Bevölkerungswachstum\n• Zufriedenheits-Index\n• Wirtschafts-Entwicklung\n• Transport-Nachfrage\n• Umwelt-Qualität\n\nDetaillierte Stadt-Analysen werden hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Wirtschafts-Inhalt erstellen
function ModernStatsGUI:CreateEconomyContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "💰 WIRTSCHAFTS-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Wirtschafts-Statistiken\n\n• Gewinn-/Verlust-Entwicklung\n• Cashflow-Analyse\n• ROI-Berechnungen\n• Marktanteils-Entwicklung\n• Konkurrenz-Vergleich\n\nDetaillierte Wirtschafts-Analysen werden hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Umwelt-Inhalt erstellen
function ModernStatsGUI:CreateEnvironmentContent()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 50)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🌍 UMWELT-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = self.contentFrame

    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -90)
    placeholder.Position = UDim2.new(0, 20, 0, 70)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Umwelt-Statistiken\n\n• CO2-Emissionen\n• Luftqualitäts-Index\n• Lärmbelastung\n• Wasserqualität\n• Umwelt-Verbesserungen\n\nDetaillierte Umwelt-Analysen werden hier angezeigt."
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.contentFrame
end

-- Docking umschalten
function ModernStatsGUI:ToggleDock()
    self.isDocked = not self.isDocked

    if self.isDocked then
        -- An obere Seite andocken
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(1, -20, 0, 400),
            Position = UDim2.new(0, 10, 0, 10)
        }):Play()
        self.dockButton.Text = "🔓"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    else
        -- Floating
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            Size = UDim2.new(0, 1400, 0, 900),
            Position = UDim2.new(0.5, -700, 0.5, -450)
        }):Play()
        self.dockButton.Text = "📌"
        self.dockButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    end
end

-- Dragging Setup
function ModernStatsGUI:SetupDragging()
    local dragging = false
    local dragStart = nil
    local startPos = nil

    self.titleBar.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 and not self.isDocked then
            dragging = true
            dragStart = input.Position
            startPos = self.mainWindow.Position
        end
    end)

    UserInputService.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            local delta = input.Position - dragStart
            self.mainWindow.Position = UDim2.new(startPos.X.Scale, startPos.X.Offset + delta.X, startPos.Y.Scale, startPos.Y.Offset + delta.Y)
        end
    end)

    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
end

-- Statistiken aktualisieren
function ModernStatsGUI:UpdateStatistics()
    local success, data = pcall(function()
        return GetStatisticsFunction:InvokeServer()
    end)

    if success and data then
        self.statsData = data

        -- Update-Status aktualisieren
        if self.updateStatus then
            self.updateStatus.Text = "🔄 " .. os.date("%H:%M:%S")

            -- Kurz grün blinken
            self.updateStatus.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
            wait(0.1)
            TweenService:Create(self.updateStatus, TweenInfo.new(0.5), {
                BackgroundColor3 = Color3.fromRGB(100, 150, 255)
            }):Play()
        end

        -- Tab-Inhalt aktualisieren falls nötig
        if self.currentTab == "OVERVIEW" then
            self:UpdateTabContent()
        end
    else
        warn("❌ Fehler beim Laden der Statistiken:", data)

        if self.updateStatus then
            self.updateStatus.Text = "❌ FEHLER"
            self.updateStatus.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
        end
    end
end

-- GUI öffnen
function ModernStatsGUI:OpenGUI()
    if not self.screenGui then
        self:CreateGUI()
    end

    self.mainWindow.Visible = true
    self.isOpen = true

    -- Smooth fade-in
    self.mainWindow.BackgroundTransparency = 1
    TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    }):Play()

    -- Standard-Tab setzen und Daten laden
    self:SwitchTab("OVERVIEW")
    self:UpdateStatistics()

    -- Auto-Update starten
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end

    self.updateConnection = RunService.Heartbeat:Connect(function()
        -- Alle 10 Sekunden aktualisieren
        if tick() % 10 < 0.1 then
            self:UpdateStatistics()
        end
    end)
end

-- GUI schließen
function ModernStatsGUI:CloseGUI()
    if self.mainWindow then
        TweenService:Create(self.mainWindow, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        }):Play()

        wait(0.3)
        self.mainWindow.Visible = false
        self.isOpen = false

        -- Auto-Update stoppen
        if self.updateConnection then
            self.updateConnection:Disconnect()
            self.updateConnection = nil
        end
    end
end

-- Singleton-Instanz
local ModernStatsGUIInstance = ModernStatsGUI.new()

return ModernStatsGUIInstance
