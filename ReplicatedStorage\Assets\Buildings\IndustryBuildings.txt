# Industrie-Gebäude Assets für Transport Empire
# 3D-Modell-Spezifikationen für Industriegebäude

## ROHSTOFF-INDUSTRIEN

### Bergbau
[COAL_MINE]
Name = "Kohlemine"
Era = 1850-1980
ModelId = "rbxassetid://COAL_MINE"
Scale = Vector3(30, 15, 40)
IndustryType = "Mining"
Production = {"Coal"}
ProductionRate = 100  # Tonnen/Monat
Workers = 50
Features = {"Mine_Shaft", "Conveyor_Belt", "Storage_Silos"}

[IRON_MINE]
Name = "Eisenmine"
Era = 1850-2000
ModelId = "rbxassetid://IRON_MINE"
Scale = Vector3(35, 20, 45)
IndustryType = "Mining"
Production = {"Iron_Ore"}
ProductionRate = 80
Workers = 60

[OIL_WELL]
Name = "Ölquelle"
Era = 1900-2000
ModelId = "rbxassetid://OIL_WELL"
Scale = Vector3(15, 25, 15)
IndustryType = "Oil"
Production = {"Crude_Oil"}
ProductionRate = 200  # Barrel/Monat
Workers = 20
Features = {"Drilling_Tower", "Pumps", "Storage_Tanks"}

### Landwirtschaft
[FARM_GRAIN]
Name = "Getreidefarm"
Era = 1850-2000
ModelId = "rbxassetid://FARM_GRAIN"
Scale = Vector3(50, 8, 60)
IndustryType = "Agriculture"
Production = {"Grain"}
ProductionRate = 150
Workers = 15
Features = {"Barn", "Silos", "Fields"}

[FARM_LIVESTOCK]
Name = "Viehfarm"
Era = 1850-2000
ModelId = "rbxassetid://FARM_LIVESTOCK"
Scale = Vector3(40, 6, 50)
IndustryType = "Agriculture"
Production = {"Meat", "Milk"}
ProductionRate = 80
Workers = 20
Features = {"Stables", "Pastures", "Feed_Storage"}

[FOREST]
Name = "Sägewerk"
Era = 1850-2000
ModelId = "rbxassetid://FOREST"
Scale = Vector3(60, 12, 80)
IndustryType = "Forestry"
Production = {"Wood"}
ProductionRate = 120
Workers = 25
Features = {"Sawmill", "Log_Storage", "Drying_Sheds"}

## VERARBEITENDE INDUSTRIEN

### Schwerindustrie
[STEEL_MILL]
Name = "Stahlwerk"
Era = 1870-2000
ModelId = "rbxassetid://STEEL_MILL"
Scale = Vector3(80, 25, 100)
IndustryType = "Heavy_Industry"
Inputs = {"Iron_Ore", "Coal"}
Production = {"Steel"}
ProductionRate = 200
Workers = 200
Features = {"Blast_Furnaces", "Smokestacks", "Rail_Access"}

[MACHINE_FACTORY]
Name = "Maschinenfabrik"
Era = 1880-2000
ModelId = "rbxassetid://MACHINE_FACTORY"
Scale = Vector3(60, 12, 80)
IndustryType = "Manufacturing"
Inputs = {"Steel"}
Production = {"Machines"}
ProductionRate = 50
Workers = 150

### Chemische Industrie
[CHEMICAL_PLANT]
Name = "Chemiewerk"
Era = 1900-2000
ModelId = "rbxassetid://CHEMICAL_PLANT"
Scale = Vector3(70, 20, 90)
IndustryType = "Chemical"
Inputs = {"Crude_Oil"}
Production = {"Chemicals", "Plastics"}
ProductionRate = 100
Workers = 80
Features = {"Distillation_Towers", "Pipelines", "Safety_Systems"}

[REFINERY]
Name = "Ölraffinerie"
Era = 1920-2000
ModelId = "rbxassetid://REFINERY"
Scale = Vector3(100, 30, 120)
IndustryType = "Oil_Processing"
Inputs = {"Crude_Oil"}
Production = {"Fuel", "Gasoline", "Diesel"}
ProductionRate = 300
Workers = 100
Features = {"Cracking_Towers", "Storage_Tanks", "Flare_Stacks"}

### Lebensmittelindustrie
[FLOUR_MILL]
Name = "Getreidemühle"
Era = 1850-2000
ModelId = "rbxassetid://FLOUR_MILL"
Scale = Vector3(20, 15, 25)
IndustryType = "Food_Processing"
Inputs = {"Grain"}
Production = {"Flour"}
ProductionRate = 120
Workers = 15
Features = {"Windmill", "Grinding_Stones", "Storage_Silos"}

[BREWERY]
Name = "Brauerei"
Era = 1850-2000
ModelId = "rbxassetid://BREWERY"
Scale = Vector3(30, 12, 35)
IndustryType = "Food_Processing"
Inputs = {"Grain"}
Production = {"Beer"}
ProductionRate = 80
Workers = 25
Features = {"Brewing_Tanks", "Fermentation_Halls", "Bottling_Line"}

[MEAT_PACKING]
Name = "Fleischverarbeitung"
Era = 1880-2000
ModelId = "rbxassetid://MEAT_PACKING"
Scale = Vector3(40, 8, 50)
IndustryType = "Food_Processing"
Inputs = {"Meat"}
Production = {"Processed_Meat"}
ProductionRate = 60
Workers = 40
Features = {"Cold_Storage", "Processing_Lines", "Loading_Docks"}

### Textil- und Konsumgüter
[TEXTILE_MILL]
Name = "Textilfabrik"
Era = 1850-1980
ModelId = "rbxassetid://TEXTILE_MILL"
Scale = Vector3(50, 10, 70)
IndustryType = "Textile"
Inputs = {"Cotton", "Wool"}
Production = {"Textiles"}
ProductionRate = 100
Workers = 120
Features = {"Spinning_Machines", "Looms", "Dyeing_Vats"}

[FURNITURE_FACTORY]
Name = "Möbelfabrik"
Era = 1880-2000
ModelId = "rbxassetid://FURNITURE_FACTORY"
Scale = Vector3(40, 8, 60)
IndustryType = "Manufacturing"
Inputs = {"Wood"}
Production = {"Furniture"}
ProductionRate = 40
Workers = 60

[ELECTRONICS_FACTORY]
Name = "Elektronikfabrik"
Era = 1950-2000
ModelId = "rbxassetid://ELECTRONICS_FACTORY"
Scale = Vector3(80, 6, 100)
IndustryType = "High_Tech"
Inputs = {"Metals", "Plastics"}
Production = {"Electronics"}
ProductionRate = 200
Workers = 300
Features = {"Clean_Rooms", "Assembly_Lines", "Quality_Control"}

## KRAFTWERKE

[COAL_POWER_PLANT]
Name = "Kohlekraftwerk"
Era = 1880-2000
ModelId = "rbxassetid://COAL_POWER_PLANT"
Scale = Vector3(60, 30, 80)
IndustryType = "Power_Generation"
Inputs = {"Coal"}
Production = {"Electricity"}
ProductionRate = 500  # MW
Workers = 50
Features = {"Cooling_Towers", "Smokestacks", "Turbine_Hall"}

[NUCLEAR_POWER_PLANT]
Name = "Atomkraftwerk"
Era = 1960-2000
ModelId = "rbxassetid://NUCLEAR_POWER_PLANT"
Scale = Vector3(100, 40, 120)
IndustryType = "Power_Generation"
Inputs = {"Uranium"}
Production = {"Electricity"}
ProductionRate = 1000
Workers = 200
Features = {"Reactor_Dome", "Cooling_Towers", "Security_Perimeter"}

## INDUSTRIE-KATEGORIEN

[INDUSTRY_CATEGORIES]
Primary = {
    "COAL_MINE",
    "IRON_MINE", 
    "OIL_WELL",
    "FARM_GRAIN",
    "FARM_LIVESTOCK",
    "FOREST"
}

Secondary = {
    "STEEL_MILL",
    "MACHINE_FACTORY",
    "CHEMICAL_PLANT",
    "REFINERY",
    "FLOUR_MILL",
    "BREWERY",
    "MEAT_PACKING",
    "TEXTILE_MILL",
    "FURNITURE_FACTORY"
}

Tertiary = {
    "ELECTRONICS_FACTORY",
    "NUCLEAR_POWER_PLANT"
}

## PRODUKTIONSKETTEN

[PRODUCTION_CHAINS]
Steel_Chain = {
    Raw = {"COAL_MINE", "IRON_MINE"},
    Processing = {"STEEL_MILL"},
    Final = {"MACHINE_FACTORY"}
}

Food_Chain = {
    Raw = {"FARM_GRAIN", "FARM_LIVESTOCK"},
    Processing = {"FLOUR_MILL", "MEAT_PACKING"},
    Final = {"BREWERY"}
}

Oil_Chain = {
    Raw = {"OIL_WELL"},
    Processing = {"REFINERY"},
    Final = {"CHEMICAL_PLANT"}
}

Wood_Chain = {
    Raw = {"FOREST"},
    Processing = {"FURNITURE_FACTORY"}
}

## INDUSTRIE-ENTWICKLUNG

[INDUSTRY_ERAS]
Industrial_Age = {
    Years = 1850-1900,
    Industries = {"COAL_MINE", "STEEL_MILL", "TEXTILE_MILL", "FLOUR_MILL"}
}

Early_Modern = {
    Years = 1900-1950,
    Industries = {"OIL_WELL", "REFINERY", "CHEMICAL_PLANT", "COAL_POWER_PLANT"}
}

Modern_Era = {
    Years = 1950-2000,
    Industries = {"ELECTRONICS_FACTORY", "NUCLEAR_POWER_PLANT"}
}
