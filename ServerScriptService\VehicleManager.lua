-- ServerScriptService/VehicleManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vollständiges 3D-Fahrzeugsystem mit Physik und KI-Navigation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")
local PathfindingService = game:GetService("PathfindingService")

local VehicleManager = {}
VehicleManager.__index = VehicleManager

function VehicleManager.new()
    local self = setmetatable({}, VehicleManager)
    
    -- Vehicle instances
    self.vehicles = {} -- All active vehicles
    self.vehicleModels = {} -- 3D models in workspace
    self.playerVehicles = {} -- Player-owned vehicles
    
    -- Vehicle definitions
    self.vehicleTypes = {
        trains = {
            {id = "steam_locomotive", name = "Dampflok", cost = 50000, speed = 60, capacity = 200, maintenance = 500, era = 1850, fuel = "coal"},
            {id = "electric_train", name = "Elektrozug", cost = 120000, speed = 120, capacity = 300, maintenance = 800, era = 1920, fuel = "electric"},
            {id = "diesel_train", name = "Dieselzug", cost = 80000, speed = 100, capacity = 250, maintenance = 600, era = 1950, fuel = "diesel"},
            {id = "highspeed_train", name = "Hochgeschwindigkeitszug", cost = 300000, speed = 250, capacity = 400, maintenance = 1500, era = 1980, fuel = "electric"},
            {id = "maglev_train", name = "Magnetschwebebahn", cost = 500000, speed = 400, capacity = 500, maintenance = 2000, era = 2000, fuel = "magnetic"}
        },
        trucks = {
            {id = "small_truck", name = "Kleiner LKW", cost = 25000, speed = 80, capacity = 10, maintenance = 200, era = 1920, fuel = "diesel"},
            {id = "medium_truck", name = "Mittlerer LKW", cost = 45000, speed = 90, capacity = 20, maintenance = 300, era = 1950, fuel = "diesel"},
            {id = "large_truck", name = "Großer LKW", cost = 75000, speed = 85, capacity = 35, maintenance = 450, era = 1970, fuel = "diesel"},
            {id = "electric_truck", name = "Elektro-LKW", cost = 90000, speed = 95, capacity = 25, maintenance = 250, era = 2010, fuel = "electric"}
        },
        ships = {
            {id = "cargo_ship", name = "Frachtschiff", cost = 200000, speed = 25, capacity = 1000, maintenance = 2000, era = 1850, fuel = "coal"},
            {id = "container_ship", name = "Containerschiff", cost = 500000, speed = 35, capacity = 2500, maintenance = 3500, era = 1960, fuel = "diesel"},
            {id = "ferry", name = "Fähre", cost = 150000, speed = 30, capacity = 200, maintenance = 1200, era = 1900, fuel = "diesel"}
        },
        planes = {
            {id = "small_plane", name = "Kleinflugzeug", cost = 100000, speed = 200, capacity = 50, maintenance = 1000, era = 1930, fuel = "aviation"},
            {id = "airliner", name = "Verkehrsflugzeug", cost = 2000000, speed = 800, capacity = 300, maintenance = 5000, era = 1960, fuel = "aviation"},
            {id = "jumbo_jet", name = "Jumbo-Jet", cost = 5000000, speed = 900, capacity = 500, maintenance = 8000, era = 1970, fuel = "aviation"}
        }
    }
    
    -- Vehicle AI states
    self.aiStates = {
        idle = "idle",
        loading = "loading",
        traveling = "traveling",
        unloading = "unloading",
        maintenance = "maintenance",
        broken = "broken"
    }
    
    -- Setup vehicle folder in workspace
    self.vehicleFolder = Workspace:FindFirstChild("Vehicles") or Instance.new("Folder")
    self.vehicleFolder.Name = "Vehicles"
    self.vehicleFolder.Parent = Workspace
    
    return self
end

-- Buy vehicle
function VehicleManager:BuyVehicle(player, vehicleType, vehicleId)
    local vehicleData = self:GetVehicleData(vehicleType, vehicleId)
    if not vehicleData then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Fahrzeug nicht gefunden!", "error")
        return
    end
    
    local playerData = self:GetPlayerData(player)
    
    -- Check if player has enough money
    if playerData.money < vehicleData.cost then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Nicht genügend Geld!", "error")
        return
    end
    
    -- Check era requirements
    local currentYear = self:GetCurrentYear()
    if currentYear < vehicleData.era then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Fahrzeug noch nicht verfügbar!", "error")
        return
    end
    
    -- Create vehicle instance
    local vehicleInstanceId = HttpService:GenerateGUID(false)
    local vehicle = {
        id = vehicleInstanceId,
        type = vehicleType,
        vehicleId = vehicleId,
        owner = player.UserId,
        name = vehicleData.name .. " #" .. math.random(100, 999),
        data = vehicleData,
        position = Vector3.new(0, 10, 0), -- Default spawn position
        rotation = Vector3.new(0, 0, 0),
        speed = 0,
        currentLine = nil,
        cargo = {},
        passengers = 0,
        condition = 100,
        fuel = 100,
        aiState = self.aiStates.idle,
        lastMaintenance = os.time(),
        totalDistance = 0,
        totalRevenue = 0,
        created = os.time()
    }
    
    -- Deduct money
    self:GetEconomyManager():AddTransaction(player, -vehicleData.cost, "Fahrzeug gekauft: " .. vehicleData.name, "expense")
    
    -- Add to player vehicles
    if not self.playerVehicles[player.UserId] then
        self.playerVehicles[player.UserId] = {}
    end
    self.playerVehicles[player.UserId][vehicleInstanceId] = vehicle
    self.vehicles[vehicleInstanceId] = vehicle
    
    -- Create 3D model
    self:CreateVehicleModel(vehicle)
    
    -- Notify client
    ReplicatedStorage.Events.VehiclePurchasedEvent:FireClient(player, vehicle)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, vehicleData.name .. " gekauft!", "success")
    
    print("🚂 Vehicle purchased:", player.Name, vehicleData.name)
    return vehicleInstanceId
end

-- Sell vehicle
function VehicleManager:SellVehicle(player, vehicleInstanceId)
    local vehicle = self.vehicles[vehicleInstanceId]
    if not vehicle or vehicle.owner ~= player.UserId then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Fahrzeug nicht gefunden!", "error")
        return
    end
    
    -- Calculate sell price (50-80% of original cost based on condition)
    local sellPrice = math.floor(vehicle.data.cost * (0.5 + (vehicle.condition / 100) * 0.3))
    
    -- Add money
    self:GetEconomyManager():AddTransaction(player, sellPrice, "Fahrzeug verkauft: " .. vehicle.name, "income")
    
    -- Remove from collections
    self.vehicles[vehicleInstanceId] = nil
    if self.playerVehicles[player.UserId] then
        self.playerVehicles[player.UserId][vehicleInstanceId] = nil
    end
    
    -- Remove 3D model
    self:RemoveVehicleModel(vehicleInstanceId)
    
    -- Notify client
    ReplicatedStorage.Events.VehicleSoldEvent:FireClient(player, vehicleInstanceId, sellPrice)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Fahrzeug verkauft für " .. sellPrice .. "$", "success")
    
    print("💰 Vehicle sold:", player.Name, vehicle.name, "for", sellPrice)
end

-- Create 3D vehicle model
function VehicleManager:CreateVehicleModel(vehicle)
    local model = self:GetVehicleModel(vehicle.type, vehicle.vehicleId)
    if not model then return end
    
    model.Name = vehicle.id
    model:SetPrimaryPartCFrame(CFrame.new(vehicle.position) * CFrame.Angles(math.rad(vehicle.rotation.X), math.rad(vehicle.rotation.Y), math.rad(vehicle.rotation.Z)))
    model.Parent = self.vehicleFolder
    
    -- Add vehicle script for movement
    local vehicleScript = script.VehicleScript:Clone()
    vehicleScript.VehicleId.Value = vehicle.id
    vehicleScript.Parent = model
    vehicleScript.Disabled = false
    
    -- Store model reference
    self.vehicleModels[vehicle.id] = model
    
    print("🎨 Vehicle model created:", vehicle.name)
end

-- Get vehicle 3D model template
function VehicleManager:GetVehicleModel(vehicleType, vehicleId)
    -- Create basic vehicle models (placeholder implementation)
    local model = Instance.new("Model")
    
    -- Main body
    local body = Instance.new("Part")
    body.Name = "Body"
    body.Material = Enum.Material.Metal
    body.BrickColor = BrickColor.new("Really red")
    body.CanCollide = true
    body.Anchored = false
    body.Parent = model
    
    -- Set size based on vehicle type
    if vehicleType == "trains" then
        body.Size = Vector3.new(4, 3, 12)
    elseif vehicleType == "trucks" then
        body.Size = Vector3.new(3, 2, 6)
    elseif vehicleType == "ships" then
        body.Size = Vector3.new(8, 4, 20)
    elseif vehicleType == "planes" then
        body.Size = Vector3.new(6, 3, 15)
    end
    
    -- Add BodyVelocity for movement
    local bodyVelocity = Instance.new("BodyVelocity")
    bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
    bodyVelocity.Velocity = Vector3.new(0, 0, 0)
    bodyVelocity.Parent = body
    
    -- Add BodyAngularVelocity for rotation
    local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
    bodyAngularVelocity.MaxTorque = Vector3.new(4000, 4000, 4000)
    bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
    bodyAngularVelocity.Parent = body
    
    -- Set primary part
    model.PrimaryPart = body
    
    -- Add wheels/details based on type
    if vehicleType == "trains" or vehicleType == "trucks" then
        for i = 1, 4 do
            local wheel = Instance.new("Part")
            wheel.Name = "Wheel" .. i
            wheel.Shape = Enum.PartType.Cylinder
            wheel.Material = Enum.Material.Rubber
            wheel.BrickColor = BrickColor.new("Really black")
            wheel.Size = Vector3.new(1, 2, 2)
            wheel.CanCollide = false
            wheel.Parent = model
            
            local weld = Instance.new("WeldConstraint")
            weld.Part0 = body
            weld.Part1 = wheel
            weld.Parent = body
            
            -- Position wheels
            local xOffset = (i <= 2) and -body.Size.Z/3 or body.Size.Z/3
            local zOffset = (i % 2 == 1) and -body.Size.X/2.5 or body.Size.X/2.5
            wheel.CFrame = body.CFrame * CFrame.new(xOffset, -body.Size.Y/2, zOffset) * CFrame.Angles(0, 0, math.rad(90))
        end
    end
    
    return model
end

-- Vehicle AI and movement
function VehicleManager:UpdateVehicleAI(vehicle)
    if vehicle.aiState == self.aiStates.idle then
        -- Check if vehicle has a line assigned
        if vehicle.currentLine then
            vehicle.aiState = self.aiStates.loading
        end
    elseif vehicle.aiState == self.aiStates.loading then
        -- Load passengers/cargo
        self:LoadVehicle(vehicle)
        vehicle.aiState = self.aiStates.traveling
    elseif vehicle.aiState == self.aiStates.traveling then
        -- Move vehicle along route
        self:MoveVehicle(vehicle)
    elseif vehicle.aiState == self.aiStates.unloading then
        -- Unload passengers/cargo
        self:UnloadVehicle(vehicle)
        vehicle.aiState = self.aiStates.loading
    elseif vehicle.aiState == self.aiStates.maintenance then
        -- Vehicle in maintenance
        self:MaintenanceVehicle(vehicle)
    elseif vehicle.aiState == self.aiStates.broken then
        -- Vehicle broken down
        self:RepairVehicle(vehicle)
    end
end

-- Move vehicle
function VehicleManager:MoveVehicle(vehicle)
    local model = self.vehicleModels[vehicle.id]
    if not model or not model.PrimaryPart then return end
    
    -- Get current line and route
    local line = self:GetTransportLine(vehicle.currentLine)
    if not line or not line.route or #line.route < 2 then return end
    
    -- Simple movement along route points
    local currentTarget = line.route[vehicle.routeIndex or 1]
    if not currentTarget then return end
    
    local currentPos = model.PrimaryPart.Position
    local targetPos = Vector3.new(currentTarget.x, currentTarget.y or currentPos.Y, currentTarget.z)
    
    -- Calculate direction and distance
    local direction = (targetPos - currentPos).Unit
    local distance = (targetPos - currentPos).Magnitude
    
    -- Move vehicle
    local speed = vehicle.data.speed / 10 -- Scale down for Roblox
    local bodyVelocity = model.PrimaryPart:FindFirstChild("BodyVelocity")
    
    if bodyVelocity then
        if distance > 5 then
            bodyVelocity.Velocity = direction * speed
            vehicle.speed = speed
        else
            -- Reached waypoint
            bodyVelocity.Velocity = Vector3.new(0, 0, 0)
            vehicle.speed = 0
            vehicle.routeIndex = (vehicle.routeIndex or 1) + 1
            
            if vehicle.routeIndex > #line.route then
                vehicle.routeIndex = 1
                vehicle.aiState = self.aiStates.unloading
            end
        end
    end
    
    -- Update vehicle position
    vehicle.position = currentPos
    vehicle.totalDistance = vehicle.totalDistance + speed * 0.1
end

-- Load vehicle with passengers/cargo
function VehicleManager:LoadVehicle(vehicle)
    -- Simulate loading time
    wait(2)
    
    -- Load based on vehicle type and capacity
    if vehicle.type == "trains" or vehicle.type == "planes" then
        vehicle.passengers = math.min(vehicle.data.capacity, math.random(50, vehicle.data.capacity))
    else
        -- Load cargo
        local cargoAmount = math.min(vehicle.data.capacity, math.random(10, vehicle.data.capacity))
        vehicle.cargo = {
            type = "goods",
            amount = cargoAmount
        }
    end
    
    print("📦 Vehicle loaded:", vehicle.name, "Passengers:", vehicle.passengers, "Cargo:", vehicle.cargo.amount or 0)
end

-- Unload vehicle
function VehicleManager:UnloadVehicle(vehicle)
    -- Calculate revenue
    local revenue = 0
    
    if vehicle.passengers > 0 then
        revenue = vehicle.passengers * 5 -- 5$ per passenger
        vehicle.passengers = 0
    end
    
    if vehicle.cargo and vehicle.cargo.amount > 0 then
        revenue = revenue + (vehicle.cargo.amount * 10) -- 10$ per cargo unit
        vehicle.cargo = {}
    end
    
    -- Add revenue to player
    if revenue > 0 then
        local player = Players:GetPlayerByUserId(vehicle.owner)
        if player then
            self:GetEconomyManager():AddTransaction(player, revenue, "Transport-Einnahmen: " .. vehicle.name, "income")
        end
        vehicle.totalRevenue = vehicle.totalRevenue + revenue
    end
    
    print("💰 Vehicle unloaded:", vehicle.name, "Revenue:", revenue)
end

-- Vehicle maintenance
function VehicleManager:MaintenanceVehicle(vehicle)
    -- Restore condition
    vehicle.condition = math.min(100, vehicle.condition + 10)
    vehicle.fuel = 100
    vehicle.lastMaintenance = os.time()
    
    -- Maintenance cost
    local player = Players:GetPlayerByUserId(vehicle.owner)
    if player then
        self:GetEconomyManager():AddTransaction(player, -vehicle.data.maintenance, "Wartung: " .. vehicle.name, "expense")
    end
    
    vehicle.aiState = self.aiStates.idle
    print("🔧 Vehicle maintained:", vehicle.name)
end

-- Get vehicle data
function VehicleManager:GetVehicleData(vehicleType, vehicleId)
    if not self.vehicleTypes[vehicleType] then return nil end
    
    for _, vehicle in ipairs(self.vehicleTypes[vehicleType]) do
        if vehicle.id == vehicleId then
            return vehicle
        end
    end
    return nil
end

-- Get player vehicles
function VehicleManager:GetPlayerVehicles(player)
    return self.playerVehicles[player.UserId] or {}
end

-- Update function (called by GameManager)
function VehicleManager:Update(deltaTime)
    -- Update all vehicles
    for vehicleId, vehicle in pairs(self.vehicles) do
        -- Update condition based on usage
        if vehicle.speed > 0 then
            vehicle.condition = vehicle.condition - 0.01 -- Wear and tear
            vehicle.fuel = vehicle.fuel - 0.05 -- Fuel consumption
        end
        
        -- Check if maintenance needed
        if vehicle.condition < 20 or vehicle.fuel < 10 then
            vehicle.aiState = self.aiStates.maintenance
        end
        
        -- Update AI
        self:UpdateVehicleAI(vehicle)
    end
end

-- Helper functions
function VehicleManager:GetPlayerData(player)
    local GameManager = require(script.Parent.GameManager)
    return GameManager:GetPlayerData(player)
end

function VehicleManager:GetEconomyManager()
    local EconomyManager = require(script.Parent.EconomyManager)
    return EconomyManager.new()
end

function VehicleManager:GetTransportLine(lineId)
    local TransportManager = require(script.Parent.TransportManager)
    return TransportManager.new():GetLine(lineId)
end

function VehicleManager:GetCurrentYear()
    local GameManager = require(script.Parent.GameManager)
    return GameManager.new().gameState.currentDate.year
end

function VehicleManager:RemoveVehicleModel(vehicleId)
    local model = self.vehicleModels[vehicleId]
    if model then
        model:Destroy()
        self.vehicleModels[vehicleId] = nil
    end
end

return VehicleManager
