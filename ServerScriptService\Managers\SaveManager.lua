-- ServerScriptService/Managers/SaveManager.lua
-- Speicher- und Ladesystem für Transport Empire

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local SaveManager = {}
SaveManager.DataStore = nil
SaveManager.SaveSlots = {} -- Cache für Speicherslots

-- DataStore initialisieren
function SaveManager:InitializeDataStore()
    local success, result = pcall(function()
        self.DataStore = DataStoreService:GetDataStore("TransportEmpireSaves_v1")
    end)
    
    if success then
        print("💾 DataStore erfolgreich initialisiert")
    else
        warn("💾 DataStore-Initialisierung fehlgeschlagen:", result)
        -- Fallback: Lokale Speicherung (nur für Tests)
        self.DataStore = nil
    end
end

-- Spielstand speichern
function SaveManager:SaveGame(player, saveSlot)
    local playerId = tostring(player.UserId)
    local saveKey = self:GetSaveKey(playerId, saveSlot)
    
    -- Alle Manager-Daten sammeln
    local saveData = self:CollectSaveData(player)
    
    if not saveData then
        warn("💾 Keine Speicherdaten verfügbar für:", player.Name)
        return false
    end
    
    -- Metadaten hinzufügen
    saveData.metadata = {
        playerName = player.Name,
        playerId = playerId,
        saveSlot = saveSlot,
        savedAt = os.time(),
        gameVersion = GameConfig.Game.Version,
        totalPlayTime = saveData.gameTime.realTimeElapsed or 0
    }
    
    -- JSON-String erstellen
    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(saveData)
    end)
    
    if not success then
        warn("💾 JSON-Encoding fehlgeschlagen:", jsonData)
        return false
    end
    
    -- In DataStore speichern
    if self.DataStore then
        local success, result = pcall(function()
            self.DataStore:SetAsync(saveKey, jsonData)
        end)
        
        if success then
            print("💾 Spiel gespeichert für:", player.Name, "Slot:", saveSlot)
            self:UpdateSaveSlotCache(playerId, saveSlot, saveData.metadata)
            return true
        else
            warn("💾 DataStore-Speicherung fehlgeschlagen:", result)
        end
    else
        -- Fallback: Lokale Speicherung (nur für Tests)
        print("💾 Lokale Speicherung (Test-Modus):", saveKey)
        return true
    end
    
    return false
end

-- Spielstand laden
function SaveManager:LoadGame(player, saveSlot)
    local playerId = tostring(player.UserId)
    local saveKey = self:GetSaveKey(playerId, saveSlot)
    
    local jsonData = nil
    
    -- Aus DataStore laden
    if self.DataStore then
        local success, result = pcall(function()
            return self.DataStore:GetAsync(saveKey)
        end)
        
        if success and result then
            jsonData = result
        else
            warn("💾 DataStore-Laden fehlgeschlagen:", result)
            return false
        end
    else
        -- Fallback: Keine Daten verfügbar
        warn("💾 Keine DataStore verfügbar")
        return false
    end
    
    if not jsonData then
        warn("💾 Keine Speicherdaten gefunden für Slot:", saveSlot)
        return false
    end
    
    -- JSON dekodieren
    local success, saveData = pcall(function()
        return HttpService:JSONDecode(jsonData)
    end)
    
    if not success then
        warn("💾 JSON-Decoding fehlgeschlagen:", saveData)
        return false
    end
    
    -- Daten in Manager laden
    local loadSuccess = self:LoadSaveData(player, saveData)
    
    if loadSuccess then
        print("📂 Spiel geladen für:", player.Name, "Slot:", saveSlot)
        return true
    else
        warn("📂 Laden der Speicherdaten fehlgeschlagen")
        return false
    end
end

-- Auto-Save
function SaveManager:AutoSave(player)
    return self:SaveGame(player, "autosave")
end

-- Speicherdaten sammeln
function SaveManager:CollectSaveData(player)
    local playerId = tostring(player.UserId)
    
    -- Manager laden
    local GameStateManager = require(script.Parent.GameStateManager)
    local EconomyManager = require(script.Parent.EconomyManager)
    local TransportManager = require(script.Parent.TransportManager)
    local MapGenerator = require(script.Parent.MapGenerator)
    
    local saveData = {
        -- Spielzeit und Zustand
        gameTime = GameStateManager:GetGameTime(),
        gameState = GameStateManager.GameState,
        
        -- Wirtschaftsdaten
        economy = EconomyManager:GetEconomyData(playerId),
        
        -- Transport-Daten
        transport = TransportManager:GetTransportData(playerId),
        
        -- Karten-Daten
        map = MapGenerator:GetMapData(),
        
        -- Spieler-spezifische Einstellungen
        playerSettings = GameStateManager.Players[playerId] and GameStateManager.Players[playerId].settings or {}
    }
    
    return saveData
end

-- Speicherdaten laden
function SaveManager:LoadSaveData(player, saveData)
    local playerId = tostring(player.UserId)
    
    -- Manager laden
    local GameStateManager = require(script.Parent.GameStateManager)
    local EconomyManager = require(script.Parent.EconomyManager)
    local TransportManager = require(script.Parent.TransportManager)
    local MapGenerator = require(script.Parent.MapGenerator)
    
    local success = true
    
    -- Spielzeit und Zustand wiederherstellen
    if saveData.gameTime then
        GameStateManager.GameTime = saveData.gameTime
        GameStateManager.GameState = saveData.gameState or "playing"
    end
    
    -- Wirtschaftsdaten wiederherstellen
    if saveData.economy then
        EconomyManager.PlayerData[playerId] = saveData.economy.player
        EconomyManager.GlobalEconomy = saveData.economy.global
    end
    
    -- Transport-Daten wiederherstellen
    if saveData.transport then
        TransportManager.PlayerTransport[playerId] = saveData.transport
    end
    
    -- Karten-Daten wiederherstellen
    if saveData.map then
        MapGenerator.CurrentMap = saveData.map
        -- Welt-Objekte neu erstellen
        MapGenerator:CreateWorldObjects()
    end
    
    -- Spieler-Einstellungen wiederherstellen
    if saveData.playerSettings and GameStateManager.Players[playerId] then
        GameStateManager.Players[playerId].settings = saveData.playerSettings
    end
    
    return success
end

-- Speicherslots abrufen
function SaveManager:GetSaveSlots(player)
    local playerId = tostring(player.UserId)
    local saveSlots = {}
    
    -- Cache prüfen
    if self.SaveSlots[playerId] then
        return self.SaveSlots[playerId]
    end
    
    -- Alle möglichen Slots prüfen
    for i = 1, GameConfig.Game.MaxSaveSlots do
        local slotName = "slot" .. i
        local saveKey = self:GetSaveKey(playerId, slotName)
        
        if self.DataStore then
            local success, jsonData = pcall(function()
                return self.DataStore:GetAsync(saveKey)
            end)
            
            if success and jsonData then
                local success2, saveData = pcall(function()
                    return HttpService:JSONDecode(jsonData)
                end)
                
                if success2 and saveData.metadata then
                    table.insert(saveSlots, {
                        slot = slotName,
                        metadata = saveData.metadata,
                        exists = true
                    })
                end
            end
        end
    end
    
    -- Auto-Save prüfen
    local autoSaveKey = self:GetSaveKey(playerId, "autosave")
    if self.DataStore then
        local success, jsonData = pcall(function()
            return self.DataStore:GetAsync(autoSaveKey)
        end)
        
        if success and jsonData then
            local success2, saveData = pcall(function()
                return HttpService:JSONDecode(jsonData)
            end)
            
            if success2 and saveData.metadata then
                table.insert(saveSlots, {
                    slot = "autosave",
                    metadata = saveData.metadata,
                    exists = true
                })
            end
        end
    end
    
    -- Cache aktualisieren
    self.SaveSlots[playerId] = saveSlots
    
    return saveSlots
end

-- Speicherslot löschen
function SaveManager:DeleteSaveSlot(player, saveSlot)
    local playerId = tostring(player.UserId)
    local saveKey = self:GetSaveKey(playerId, saveSlot)
    
    if self.DataStore then
        local success, result = pcall(function()
            self.DataStore:RemoveAsync(saveKey)
        end)
        
        if success then
            print("🗑️ Speicherslot gelöscht:", saveSlot)
            -- Cache aktualisieren
            self:ClearSaveSlotCache(playerId)
            return true
        else
            warn("🗑️ Löschen fehlgeschlagen:", result)
        end
    end
    
    return false
end

-- Speicher-Key generieren
function SaveManager:GetSaveKey(playerId, saveSlot)
    return "player_" .. playerId .. "_" .. saveSlot
end

-- Speicherslot-Cache aktualisieren
function SaveManager:UpdateSaveSlotCache(playerId, saveSlot, metadata)
    if not self.SaveSlots[playerId] then
        self.SaveSlots[playerId] = {}
    end
    
    -- Bestehenden Slot finden oder neuen hinzufügen
    local found = false
    for _, slot in pairs(self.SaveSlots[playerId]) do
        if slot.slot == saveSlot then
            slot.metadata = metadata
            slot.exists = true
            found = true
            break
        end
    end
    
    if not found then
        table.insert(self.SaveSlots[playerId], {
            slot = saveSlot,
            metadata = metadata,
            exists = true
        })
    end
end

-- Speicherslot-Cache leeren
function SaveManager:ClearSaveSlotCache(playerId)
    self.SaveSlots[playerId] = nil
end

-- Speicher-Statistiken
function SaveManager:GetSaveStatistics(player)
    local playerId = tostring(player.UserId)
    local saveSlots = self:GetSaveSlots(player)
    
    local totalSaves = #saveSlots
    local totalSize = 0 -- Vereinfacht, da DataStore keine Größeninfo liefert
    local oldestSave = nil
    local newestSave = nil
    
    for _, slot in pairs(saveSlots) do
        if slot.metadata and slot.metadata.savedAt then
            if not oldestSave or slot.metadata.savedAt < oldestSave then
                oldestSave = slot.metadata.savedAt
            end
            if not newestSave or slot.metadata.savedAt > newestSave then
                newestSave = slot.metadata.savedAt
            end
        end
    end
    
    return {
        totalSaves = totalSaves,
        totalSize = totalSize,
        oldestSave = oldestSave,
        newestSave = newestSave,
        maxSlots = GameConfig.Game.MaxSaveSlots
    }
end

-- Backup erstellen (vereinfacht)
function SaveManager:CreateBackup(player)
    local playerId = tostring(player.UserId)
    local backupSlot = "backup_" .. os.time()
    
    return self:SaveGame(player, backupSlot)
end

-- Initialisierung
function SaveManager:Initialize()
    self:InitializeDataStore()
    print("💾 SaveManager initialisiert")
end

return SaveManager
