-- ServerScriptService/Managers/SaveManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- <PERSON><PERSON><PERSON><PERSON>tes Savegame-Management mit mehreren Slots, Metadaten, Vorschaubildern und Import/Export

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local DataStoreService = game:GetService("DataStoreService")
local HttpService = game:GetService("HttpService")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- DataStores
local SaveDataStore = DataStoreService:GetDataStore("TransportFeverSaves_v3")
local MetadataStore = DataStoreService:GetDataStore("TransportFeverMetadata_v3")
local PreviewStore = DataStoreService:GetDataStore("TransportFeverPreviews_v3")

-- Konfiguration
local MAX_SAVE_SLOTS = 10
local AUTO_SAVE_INTERVAL = 300 -- 5 Minuten
local SAVE_VERSION = "3.0"
local MAX_PREVIEW_SIZE = 100000 -- 100KB für Vorschaubilder

local SaveManager = {}
SaveManager.__index = SaveManager

-- Konstruktor
function SaveManager.new()
    local self = setmetatable({}, SaveManager)

    self.saveSlots = {} -- [playerId] = {slot1, slot2, ...}
    self.currentSaveSlot = {} -- [playerId] = currentSlotNumber
    self.autoSaveEnabled = {} -- [playerId] = boolean
    self.lastAutoSave = {} -- [playerId] = timestamp

    self:InitializeEvents()
    self:StartAutoSaveLoop()

    return self
end

-- Events initialisieren
function SaveManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")

    -- Save/Load Events
    if not Events:FindFirstChild("SaveGameEvent") then
        local saveGameEvent = Instance.new("RemoteEvent")
        saveGameEvent.Name = "SaveGameEvent"
        saveGameEvent.Parent = Events
    end

    if not Events:FindFirstChild("LoadGameEvent") then
        local loadGameEvent = Instance.new("RemoteEvent")
        loadGameEvent.Name = "LoadGameEvent"
        loadGameEvent.Parent = Events
    end

    if not Events:FindFirstChild("GetSaveSlotsFunction") then
        local getSaveSlotsFunction = Instance.new("RemoteFunction")
        getSaveSlotsFunction.Name = "GetSaveSlotsFunction"
        getSaveSlotsFunction.Parent = Events
    end

    if not Events:FindFirstChild("DeleteSaveSlotEvent") then
        local deleteSaveSlotEvent = Instance.new("RemoteEvent")
        deleteSaveSlotEvent.Name = "DeleteSaveSlotEvent"
        deleteSaveSlotEvent.Parent = Events
    end

    if not Events:FindFirstChild("ExportSaveEvent") then
        local exportSaveEvent = Instance.new("RemoteEvent")
        exportSaveEvent.Name = "ExportSaveEvent"
        exportSaveEvent.Parent = Events
    end

    if not Events:FindFirstChild("ImportSaveEvent") then
        local importSaveEvent = Instance.new("RemoteEvent")
        importSaveEvent.Name = "ImportSaveEvent"
        importSaveEvent.Parent = Events
    end

    -- Zusätzliche Events für erweiterte Funktionalität
    if not Events:FindFirstChild("SaveGameCompleteEvent") then
        local saveGameCompleteEvent = Instance.new("RemoteEvent")
        saveGameCompleteEvent.Name = "SaveGameCompleteEvent"
        saveGameCompleteEvent.Parent = Events
    end

    if not Events:FindFirstChild("SaveSlotDeletedEvent") then
        local saveSlotDeletedEvent = Instance.new("RemoteEvent")
        saveSlotDeletedEvent.Name = "SaveSlotDeletedEvent"
        saveSlotDeletedEvent.Parent = Events
    end

    if not Events:FindFirstChild("SaveExportedEvent") then
        local saveExportedEvent = Instance.new("RemoteEvent")
        saveExportedEvent.Name = "SaveExportedEvent"
        saveExportedEvent.Parent = Events
    end

    if not Events:FindFirstChild("SaveImportedEvent") then
        local saveImportedEvent = Instance.new("RemoteEvent")
        saveImportedEvent.Name = "SaveImportedEvent"
        saveImportedEvent.Parent = Events
    end

    -- Event-Handler
    Events.SaveGameEvent.OnServerEvent:Connect(function(player, slotNumber, slotName, description)
        self:SaveGame(player, slotNumber, slotName, description)
    end)

    Events.LoadGameEvent.OnServerEvent:Connect(function(player, slotNumber)
        self:LoadGame(player, slotNumber)
    end)

    Events.GetSaveSlotsFunction.OnServerInvoke = function(player)
        return self:GetSaveSlots(player)
    end

    Events.DeleteSaveSlotEvent.OnServerEvent:Connect(function(player, slotNumber)
        self:DeleteSaveSlot(player, slotNumber)
    end)

    Events.ExportSaveEvent.OnServerEvent:Connect(function(player, slotNumber)
        self:ExportSave(player, slotNumber)
    end)

    Events.ImportSaveEvent.OnServerEvent:Connect(function(player, saveData)
        self:ImportSave(player, saveData)
    end)
end

-- Auto-Save Loop starten
function SaveManager:StartAutoSaveLoop()
    self.autoSaveConnection = RunService.Heartbeat:Connect(function()
        local currentTime = tick()

        for _, player in pairs(Players:GetPlayers()) do
            local playerId = tostring(player.UserId)

            if self.autoSaveEnabled[playerId] ~= false then -- Standard: aktiviert
                local lastSave = self.lastAutoSave[playerId] or 0

                if currentTime - lastSave >= AUTO_SAVE_INTERVAL then
                    self:AutoSave(player)
                    self.lastAutoSave[playerId] = currentTime
                end
            end
        end
    end)
end

-- DataStore initialisieren
function SaveManager:InitializeDataStore()
    local success, result = pcall(function()
        self.DataStore = DataStoreService:GetDataStore("TransportEmpireSaves_v1")
    end)
    
    if success then
        print("💾 DataStore erfolgreich initialisiert")
    else
        warn("💾 DataStore-Initialisierung fehlgeschlagen:", result)
        -- Fallback: Lokale Speicherung (nur für Tests)
        self.DataStore = nil
    end
end

-- Spielstand speichern (erweitert)
function SaveManager:SaveGame(player, slotNumber, slotName, description)
    local playerId = tostring(player.UserId)

    -- Slot-Validierung
    if slotNumber < 1 or slotNumber > MAX_SAVE_SLOTS then
        warn("💾 Ungültiger Save-Slot:", slotNumber)
        return false
    end

    -- Alle Manager-Daten sammeln
    local saveData = self:CollectSaveData(player)

    if not saveData then
        warn("💾 Keine Speicherdaten verfügbar für:", player.Name)
        return false
    end

    -- Erweiterte Metadaten
    local currentTime = os.time()
    local metadata = {
        -- Basis-Informationen
        playerName = player.Name,
        playerId = playerId,
        slotNumber = slotNumber,
        slotName = slotName or ("Spielstand " .. slotNumber),
        description = description or "",

        -- Zeit-Informationen
        savedAt = currentTime,
        savedAtFormatted = os.date("%d.%m.%Y %H:%M", currentTime),
        gameYear = saveData.gameTime and saveData.gameTime.year or 1850,
        totalPlayTime = saveData.gameTime and saveData.gameTime.realTimeElapsed or 0,

        -- Spiel-Informationen
        gameVersion = SAVE_VERSION,
        mapSeed = saveData.map and saveData.map.seed or 0,
        mapSize = saveData.map and saveData.map.size or "medium",

        -- Wirtschafts-Informationen
        playerMoney = saveData.economy and saveData.economy.player and saveData.economy.player.money or 0,
        companyName = saveData.economy and saveData.economy.player and saveData.economy.player.companyName or "Transport Empire",

        -- Transport-Statistiken
        totalVehicles = saveData.transport and saveData.transport.vehicles and #saveData.transport.vehicles or 0,
        totalLines = saveData.transport and saveData.transport.lines and #saveData.transport.lines or 0,

        -- Städte-Statistiken
        totalCities = saveData.cities and #saveData.cities or 0,
        totalPopulation = self:CalculateTotalPopulation(saveData.cities or {}),

        -- Achievements
        achievements = saveData.achievements or {},

        -- Schwierigkeitsgrad
        difficulty = saveData.gameSettings and saveData.gameSettings.difficulty or "normal"
    }

    -- Vorschaubild generieren
    local previewImage = self:GeneratePreviewImage(player, saveData)

    -- Speicher-Schlüssel
    local saveKey = self:GetSaveKey(playerId, slotNumber)
    local metadataKey = self:GetMetadataKey(playerId, slotNumber)
    local previewKey = self:GetPreviewKey(playerId, slotNumber)

    -- JSON-String erstellen
    local success, jsonData = pcall(function()
        return HttpService:JSONEncode(saveData)
    end)

    if not success then
        warn("💾 JSON-Encoding fehlgeschlagen:", jsonData)
        return false
    end

    -- In DataStores speichern
    local saveSuccess = false
    local metadataSuccess = false
    local previewSuccess = false

    -- Hauptdaten speichern
    local success1, result1 = pcall(function()
        SaveDataStore:SetAsync(saveKey, jsonData)
    end)

    if success1 then
        saveSuccess = true
        print("💾 Spielstand gespeichert:", saveKey)
    else
        warn("💾 Spielstand-Speicherung fehlgeschlagen:", result1)
    end

    -- Metadaten speichern
    local success2, result2 = pcall(function()
        MetadataStore:SetAsync(metadataKey, HttpService:JSONEncode(metadata))
    end)

    if success2 then
        metadataSuccess = true
        print("📋 Metadaten gespeichert:", metadataKey)
    else
        warn("📋 Metadaten-Speicherung fehlgeschlagen:", result2)
    end

    -- Vorschaubild speichern
    if previewImage then
        local success3, result3 = pcall(function()
            PreviewStore:SetAsync(previewKey, previewImage)
        end)

        if success3 then
            previewSuccess = true
            print("🖼️ Vorschaubild gespeichert:", previewKey)
        else
            warn("🖼️ Vorschaubild-Speicherung fehlgeschlagen:", result3)
        end
    end

    -- Cache aktualisieren
    if saveSuccess and metadataSuccess then
        self:UpdateSaveSlotCache(playerId, slotNumber, metadata, previewSuccess)

        -- Aktuellen Slot setzen
        self.currentSaveSlot[playerId] = slotNumber

        -- Event an Client senden
        local Events = ReplicatedStorage:WaitForChild("Events")
        if Events:FindFirstChild("SaveGameCompleteEvent") then
            Events.SaveGameCompleteEvent:FireClient(player, {
                success = true,
                slotNumber = slotNumber,
                slotName = slotName,
                metadata = metadata
            })
        end

        return true
    end

    return false
end

-- Spielstand laden
function SaveManager:LoadGame(player, saveSlot)
    local playerId = tostring(player.UserId)
    local saveKey = self:GetSaveKey(playerId, saveSlot)
    
    local jsonData = nil
    
    -- Aus DataStore laden
    if self.DataStore then
        local success, result = pcall(function()
            return self.DataStore:GetAsync(saveKey)
        end)
        
        if success and result then
            jsonData = result
        else
            warn("💾 DataStore-Laden fehlgeschlagen:", result)
            return false
        end
    else
        -- Fallback: Keine Daten verfügbar
        warn("💾 Keine DataStore verfügbar")
        return false
    end
    
    if not jsonData then
        warn("💾 Keine Speicherdaten gefunden für Slot:", saveSlot)
        return false
    end
    
    -- JSON dekodieren
    local success, saveData = pcall(function()
        return HttpService:JSONDecode(jsonData)
    end)
    
    if not success then
        warn("💾 JSON-Decoding fehlgeschlagen:", saveData)
        return false
    end
    
    -- Daten in Manager laden
    local loadSuccess = self:LoadSaveData(player, saveData)
    
    if loadSuccess then
        print("📂 Spiel geladen für:", player.Name, "Slot:", saveSlot)
        return true
    else
        warn("📂 Laden der Speicherdaten fehlgeschlagen")
        return false
    end
end

-- Auto-Save (erweitert)
function SaveManager:AutoSave(player)
    local playerId = tostring(player.UserId)
    local currentSlot = self.currentSaveSlot[playerId]

    if currentSlot then
        -- In aktuellen Slot speichern
        local slotData = self:GetSaveSlotData(player, currentSlot)
        local slotName = slotData and slotData.metadata and slotData.metadata.slotName or ("Spielstand " .. currentSlot)

        return self:SaveGame(player, currentSlot, slotName .. " (Auto)", "Automatische Speicherung")
    else
        -- Neuen Auto-Save-Slot erstellen
        return self:SaveGame(player, 0, "Auto-Save", "Automatische Speicherung")
    end
end

-- Vorschaubild generieren
function SaveManager:GeneratePreviewImage(player, saveData)
    -- Vereinfachtes Vorschaubild als JSON-Daten
    -- In einer echten Implementierung würde hier ein Screenshot oder eine Minimap generiert

    local previewData = {
        type = "mapPreview",
        mapSeed = saveData.map and saveData.map.seed or 0,
        mapSize = saveData.map and saveData.map.size or "medium",
        cities = {},
        routes = {},
        timestamp = tick()
    }

    -- Städte für Vorschau
    if saveData.cities then
        for i, city in ipairs(saveData.cities) do
            if i <= 10 then -- Maximal 10 Städte für Vorschau
                table.insert(previewData.cities, {
                    name = city.name,
                    position = city.position,
                    population = city.population,
                    size = city.size
                })
            end
        end
    end

    -- Transport-Routen für Vorschau
    if saveData.transport and saveData.transport.lines then
        for i, line in ipairs(saveData.transport.lines) do
            if i <= 5 then -- Maximal 5 Linien für Vorschau
                table.insert(previewData.routes, {
                    name = line.name,
                    type = line.vehicleType,
                    stations = line.stations and #line.stations or 0
                })
            end
        end
    end

    -- Als JSON-String zurückgeben
    local success, jsonString = pcall(function()
        return HttpService:JSONEncode(previewData)
    end)

    if success and #jsonString <= MAX_PREVIEW_SIZE then
        return jsonString
    else
        warn("🖼️ Vorschaubild zu groß oder Encoding fehlgeschlagen")
        return nil
    end
end

-- Gesamtbevölkerung berechnen
function SaveManager:CalculateTotalPopulation(cities)
    local totalPopulation = 0

    for _, city in ipairs(cities) do
        totalPopulation = totalPopulation + (city.population or 0)
    end

    return totalPopulation
end

-- Speicher-Schlüssel generieren
function SaveManager:GetSaveKey(playerId, slotNumber)
    if slotNumber == 0 then
        return "autosave_" .. playerId
    else
        return "save_" .. playerId .. "_slot_" .. slotNumber
    end
end

-- Metadaten-Schlüssel generieren
function SaveManager:GetMetadataKey(playerId, slotNumber)
    if slotNumber == 0 then
        return "metadata_autosave_" .. playerId
    else
        return "metadata_" .. playerId .. "_slot_" .. slotNumber
    end
end

-- Vorschau-Schlüssel generieren
function SaveManager:GetPreviewKey(playerId, slotNumber)
    if slotNumber == 0 then
        return "preview_autosave_" .. playerId
    else
        return "preview_" .. playerId .. "_slot_" .. slotNumber
    end
end

-- Speicherdaten sammeln
function SaveManager:CollectSaveData(player)
    local playerId = tostring(player.UserId)
    
    -- Manager laden
    local GameStateManager = require(script.Parent.GameStateManager)
    local EconomyManager = require(script.Parent.EconomyManager)
    local TransportManager = require(script.Parent.TransportManager)
    local MapGenerator = require(script.Parent.MapGenerator)
    
    local saveData = {
        -- Spielzeit und Zustand
        gameTime = GameStateManager:GetGameTime(),
        gameState = GameStateManager.GameState,
        
        -- Wirtschaftsdaten
        economy = EconomyManager:GetEconomyData(playerId),
        
        -- Transport-Daten
        transport = TransportManager:GetTransportData(playerId),
        
        -- Karten-Daten
        map = MapGenerator:GetMapData(),
        
        -- Spieler-spezifische Einstellungen
        playerSettings = GameStateManager.Players[playerId] and GameStateManager.Players[playerId].settings or {}
    }
    
    return saveData
end

-- Speicherdaten laden
function SaveManager:LoadSaveData(player, saveData)
    local playerId = tostring(player.UserId)
    
    -- Manager laden
    local GameStateManager = require(script.Parent.GameStateManager)
    local EconomyManager = require(script.Parent.EconomyManager)
    local TransportManager = require(script.Parent.TransportManager)
    local MapGenerator = require(script.Parent.MapGenerator)
    
    local success = true
    
    -- Spielzeit und Zustand wiederherstellen
    if saveData.gameTime then
        GameStateManager.GameTime = saveData.gameTime
        GameStateManager.GameState = saveData.gameState or "playing"
    end
    
    -- Wirtschaftsdaten wiederherstellen
    if saveData.economy then
        EconomyManager.PlayerData[playerId] = saveData.economy.player
        EconomyManager.GlobalEconomy = saveData.economy.global
    end
    
    -- Transport-Daten wiederherstellen
    if saveData.transport then
        TransportManager.PlayerTransport[playerId] = saveData.transport
    end
    
    -- Karten-Daten wiederherstellen
    if saveData.map then
        MapGenerator.CurrentMap = saveData.map
        -- Welt-Objekte neu erstellen
        MapGenerator:CreateWorldObjects()
    end
    
    -- Spieler-Einstellungen wiederherstellen
    if saveData.playerSettings and GameStateManager.Players[playerId] then
        GameStateManager.Players[playerId].settings = saveData.playerSettings
    end
    
    return success
end

-- Speicherslots abrufen (erweitert)
function SaveManager:GetSaveSlots(player)
    local playerId = tostring(player.UserId)
    local saveSlots = {}

    -- Cache prüfen (optional, für bessere Performance)
    if self.saveSlots[playerId] and tick() - (self.saveSlots[playerId].lastUpdate or 0) < 30 then
        return self.saveSlots[playerId].slots
    end

    -- Auto-Save Slot (Slot 0)
    local autoSaveData = self:GetSaveSlotData(player, 0)
    if autoSaveData then
        table.insert(saveSlots, autoSaveData)
    else
        -- Leeren Auto-Save Slot hinzufügen
        table.insert(saveSlots, {
            slotNumber = 0,
            slotName = "Auto-Save",
            exists = false,
            metadata = nil,
            preview = nil
        })
    end

    -- Normale Save-Slots (1-10)
    for i = 1, MAX_SAVE_SLOTS do
        local slotData = self:GetSaveSlotData(player, i)
        if slotData then
            table.insert(saveSlots, slotData)
        else
            -- Leeren Slot hinzufügen
            table.insert(saveSlots, {
                slotNumber = i,
                slotName = "Leerer Slot",
                exists = false,
                metadata = nil,
                preview = nil
            })
        end
    end

    -- Cache aktualisieren
    self.saveSlots[playerId] = {
        slots = saveSlots,
        lastUpdate = tick()
    }

    return saveSlots
end

-- Einzelnen Save-Slot-Daten abrufen
function SaveManager:GetSaveSlotData(player, slotNumber)
    local playerId = tostring(player.UserId)

    local saveKey = self:GetSaveKey(playerId, slotNumber)
    local metadataKey = self:GetMetadataKey(playerId, slotNumber)
    local previewKey = self:GetPreviewKey(playerId, slotNumber)

    -- Metadaten laden
    local metadata = nil
    local success1, metadataJson = pcall(function()
        return MetadataStore:GetAsync(metadataKey)
    end)

    if success1 and metadataJson then
        local success2, metadataData = pcall(function()
            return HttpService:JSONDecode(metadataJson)
        end)

        if success2 then
            metadata = metadataData
        end
    end

    -- Wenn keine Metadaten vorhanden, Slot existiert nicht
    if not metadata then
        return nil
    end

    -- Vorschaubild laden
    local preview = nil
    local success3, previewData = pcall(function()
        return PreviewStore:GetAsync(previewKey)
    end)

    if success3 and previewData then
        preview = previewData
    end

    -- Slot-Daten zusammenstellen
    return {
        slotNumber = slotNumber,
        slotName = metadata.slotName or ("Spielstand " .. slotNumber),
        exists = true,
        metadata = metadata,
        preview = preview,

        -- Zusätzliche Informationen für GUI
        displayInfo = {
            savedAt = metadata.savedAtFormatted or "Unbekannt",
            gameYear = metadata.gameYear or 1850,
            playTime = self:FormatPlayTime(metadata.totalPlayTime or 0),
            money = self:FormatMoney(metadata.playerMoney or 0),
            companyName = metadata.companyName or "Transport Empire",
            mapInfo = (metadata.mapSize or "medium") .. " (" .. (metadata.mapSeed or 0) .. ")",
            cities = metadata.totalCities or 0,
            population = self:FormatNumber(metadata.totalPopulation or 0),
            vehicles = metadata.totalVehicles or 0,
            lines = metadata.totalLines or 0,
            difficulty = metadata.difficulty or "normal"
        }
    }
end

-- Spielzeit formatieren
function SaveManager:FormatPlayTime(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)

    if hours > 0 then
        return string.format("%dh %dm", hours, minutes)
    else
        return string.format("%dm", minutes)
    end
end

-- Geld formatieren
function SaveManager:FormatMoney(amount)
    if amount >= 1000000 then
        return string.format("%.1fM $", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.1fK $", amount / 1000)
    else
        return string.format("%d $", amount)
    end
end

-- Zahlen formatieren
function SaveManager:FormatNumber(number)
    if number >= 1000000 then
        return string.format("%.1fM", number / 1000000)
    elseif number >= 1000 then
        return string.format("%.1fK", number / 1000)
    else
        return tostring(number)
    end
end

-- Speicherslot löschen
function SaveManager:DeleteSaveSlot(player, saveSlot)
    local playerId = tostring(player.UserId)
    local saveKey = self:GetSaveKey(playerId, saveSlot)
    
    if self.DataStore then
        local success, result = pcall(function()
            self.DataStore:RemoveAsync(saveKey)
        end)
        
        if success then
            print("🗑️ Speicherslot gelöscht:", saveSlot)
            -- Cache aktualisieren
            self:ClearSaveSlotCache(playerId)
            return true
        else
            warn("🗑️ Löschen fehlgeschlagen:", result)
        end
    end
    
    return false
end

-- Speicher-Key generieren
function SaveManager:GetSaveKey(playerId, saveSlot)
    return "player_" .. playerId .. "_" .. saveSlot
end

-- Speicherslot-Cache aktualisieren
function SaveManager:UpdateSaveSlotCache(playerId, saveSlot, metadata)
    if not self.SaveSlots[playerId] then
        self.SaveSlots[playerId] = {}
    end
    
    -- Bestehenden Slot finden oder neuen hinzufügen
    local found = false
    for _, slot in pairs(self.SaveSlots[playerId]) do
        if slot.slot == saveSlot then
            slot.metadata = metadata
            slot.exists = true
            found = true
            break
        end
    end
    
    if not found then
        table.insert(self.SaveSlots[playerId], {
            slot = saveSlot,
            metadata = metadata,
            exists = true
        })
    end
end

-- Speicherslot-Cache leeren
function SaveManager:ClearSaveSlotCache(playerId)
    self.SaveSlots[playerId] = nil
end

-- Speicher-Statistiken
function SaveManager:GetSaveStatistics(player)
    local playerId = tostring(player.UserId)
    local saveSlots = self:GetSaveSlots(player)
    
    local totalSaves = #saveSlots
    local totalSize = 0 -- Vereinfacht, da DataStore keine Größeninfo liefert
    local oldestSave = nil
    local newestSave = nil
    
    for _, slot in pairs(saveSlots) do
        if slot.metadata and slot.metadata.savedAt then
            if not oldestSave or slot.metadata.savedAt < oldestSave then
                oldestSave = slot.metadata.savedAt
            end
            if not newestSave or slot.metadata.savedAt > newestSave then
                newestSave = slot.metadata.savedAt
            end
        end
    end
    
    return {
        totalSaves = totalSaves,
        totalSize = totalSize,
        oldestSave = oldestSave,
        newestSave = newestSave,
        maxSlots = GameConfig.Game.MaxSaveSlots
    }
end

-- Save exportieren
function SaveManager:ExportSave(player, slotNumber)
    local playerId = tostring(player.UserId)

    -- Save-Daten laden
    local saveKey = self:GetSaveKey(playerId, slotNumber)
    local metadataKey = self:GetMetadataKey(playerId, slotNumber)

    local saveData = nil
    local metadata = nil

    -- Hauptdaten laden
    local success1, saveJson = pcall(function()
        return SaveDataStore:GetAsync(saveKey)
    end)

    if success1 and saveJson then
        local success2, data = pcall(function()
            return HttpService:JSONDecode(saveJson)
        end)

        if success2 then
            saveData = data
        end
    end

    -- Metadaten laden
    local success3, metadataJson = pcall(function()
        return MetadataStore:GetAsync(metadataKey)
    end)

    if success3 and metadataJson then
        local success4, data = pcall(function()
            return HttpService:JSONDecode(metadataJson)
        end)

        if success4 then
            metadata = data
        end
    end

    if not saveData or not metadata then
        warn("📤 Export fehlgeschlagen: Keine Daten gefunden für Slot", slotNumber)
        return false
    end

    -- Export-Paket erstellen
    local exportPackage = {
        version = SAVE_VERSION,
        exportedAt = os.time(),
        exportedBy = player.Name,

        -- Daten
        saveData = saveData,
        metadata = metadata,

        -- Verifikation
        checksum = self:CalculateChecksum(saveData)
    }

    -- Als JSON exportieren
    local success, exportJson = pcall(function()
        return HttpService:JSONEncode(exportPackage)
    end)

    if success then
        print("📤 Save exportiert für:", player.Name, "Slot:", slotNumber)

        -- Export-Event an Client senden
        local Events = ReplicatedStorage:WaitForChild("Events")
        if Events:FindFirstChild("SaveExportedEvent") then
            Events.SaveExportedEvent:FireClient(player, {
                success = true,
                slotNumber = slotNumber,
                exportData = exportJson,
                filename = string.format("TransportFever_%s_Slot%d_%s.json",
                    player.Name, slotNumber, os.date("%Y%m%d_%H%M%S"))
            })
        end

        return true
    else
        warn("📤 Export-JSON-Encoding fehlgeschlagen:", exportJson)
        return false
    end
end

-- Save importieren
function SaveManager:ImportSave(player, importData)
    local playerId = tostring(player.UserId)

    -- Import-Daten validieren
    local success, importPackage = pcall(function()
        return HttpService:JSONDecode(importData)
    end)

    if not success then
        warn("📥 Import fehlgeschlagen: Ungültiges JSON")
        return false
    end

    -- Version prüfen
    if not importPackage.version or importPackage.version ~= SAVE_VERSION then
        warn("📥 Import fehlgeschlagen: Inkompatible Version", importPackage.version)
        return false
    end

    -- Checksum prüfen
    local calculatedChecksum = self:CalculateChecksum(importPackage.saveData)
    if calculatedChecksum ~= importPackage.checksum then
        warn("📥 Import fehlgeschlagen: Checksum-Fehler")
        return false
    end

    -- Freien Slot finden
    local targetSlot = nil
    for i = 1, MAX_SAVE_SLOTS do
        local slotData = self:GetSaveSlotData(player, i)
        if not slotData then
            targetSlot = i
            break
        end
    end

    if not targetSlot then
        warn("📥 Import fehlgeschlagen: Keine freien Slots verfügbar")
        return false
    end

    -- Metadaten aktualisieren
    local metadata = importPackage.metadata
    metadata.importedAt = os.time()
    metadata.importedBy = player.Name
    metadata.originalPlayer = metadata.playerName
    metadata.playerName = player.Name
    metadata.playerId = playerId
    metadata.slotNumber = targetSlot
    metadata.slotName = metadata.slotName .. " (Importiert)"

    -- Speichern
    local saveKey = self:GetSaveKey(playerId, targetSlot)
    local metadataKey = self:GetMetadataKey(playerId, targetSlot)

    -- Hauptdaten speichern
    local success1, result1 = pcall(function()
        SaveDataStore:SetAsync(saveKey, HttpService:JSONEncode(importPackage.saveData))
    end)

    -- Metadaten speichern
    local success2, result2 = pcall(function()
        MetadataStore:SetAsync(metadataKey, HttpService:JSONEncode(metadata))
    end)

    if success1 and success2 then
        print("📥 Save importiert für:", player.Name, "in Slot:", targetSlot)

        -- Cache leeren
        self.saveSlots[playerId] = nil

        -- Import-Event an Client senden
        local Events = ReplicatedStorage:WaitForChild("Events")
        if Events:FindFirstChild("SaveImportedEvent") then
            Events.SaveImportedEvent:FireClient(player, {
                success = true,
                targetSlot = targetSlot,
                metadata = metadata
            })
        end

        return true
    else
        warn("📥 Import-Speicherung fehlgeschlagen")
        return false
    end
end

-- Checksum berechnen (vereinfacht)
function SaveManager:CalculateChecksum(data)
    local jsonString = HttpService:JSONEncode(data)
    local checksum = 0

    for i = 1, #jsonString do
        checksum = checksum + string.byte(jsonString, i)
    end

    return checksum % 1000000 -- 6-stellige Checksum
end

-- Backup erstellen
function SaveManager:CreateBackup(player, slotNumber)
    local playerId = tostring(player.UserId)

    -- Backup-Slot finden (negative Nummern für Backups)
    local backupSlot = -math.abs(slotNumber or 1)
    local backupName = "Backup " .. os.date("%d.%m.%Y %H:%M")

    -- Aktuellen Spielstand als Backup speichern
    return self:SaveGame(player, backupSlot, backupName, "Automatisches Backup")
end

-- Speicherslot löschen (erweitert)
function SaveManager:DeleteSaveSlot(player, slotNumber)
    local playerId = tostring(player.UserId)

    -- Auto-Save kann nicht gelöscht werden
    if slotNumber == 0 then
        warn("🗑️ Auto-Save kann nicht gelöscht werden")
        return false
    end

    local saveKey = self:GetSaveKey(playerId, slotNumber)
    local metadataKey = self:GetMetadataKey(playerId, slotNumber)
    local previewKey = self:GetPreviewKey(playerId, slotNumber)

    local success1, success2, success3 = false, false, false

    -- Hauptdaten löschen
    local result1 = pcall(function()
        SaveDataStore:RemoveAsync(saveKey)
    end)

    if result1 then
        success1 = true
        print("🗑️ Spielstand gelöscht:", saveKey)
    end

    -- Metadaten löschen
    local result2 = pcall(function()
        MetadataStore:RemoveAsync(metadataKey)
    end)

    if result2 then
        success2 = true
        print("🗑️ Metadaten gelöscht:", metadataKey)
    end

    -- Vorschaubild löschen
    local result3 = pcall(function()
        PreviewStore:RemoveAsync(previewKey)
    end)

    if result3 then
        success3 = true
        print("🗑️ Vorschaubild gelöscht:", previewKey)
    end

    if success1 then
        -- Cache leeren
        self.saveSlots[playerId] = nil

        -- Event an Client senden
        local Events = ReplicatedStorage:WaitForChild("Events")
        if Events:FindFirstChild("SaveSlotDeletedEvent") then
            Events.SaveSlotDeletedEvent:FireClient(player, {
                success = true,
                slotNumber = slotNumber
            })
        end

        return true
    else
        warn("🗑️ Löschen fehlgeschlagen für Slot:", slotNumber)
        return false
    end
end

-- Cache aktualisieren
function SaveManager:UpdateSaveSlotCache(playerId, slotNumber, metadata, hasPreview)
    if not self.saveSlots[playerId] then
        self.saveSlots[playerId] = {
            slots = {},
            lastUpdate = 0
        }
    end

    -- Cache leeren, damit beim nächsten Aufruf neu geladen wird
    self.saveSlots[playerId].lastUpdate = 0

    print("💾 Cache aktualisiert für Spieler:", playerId, "Slot:", slotNumber)
end

-- Auto-Save-Einstellungen
function SaveManager:SetAutoSaveEnabled(player, enabled)
    local playerId = tostring(player.UserId)
    self.autoSaveEnabled[playerId] = enabled

    print("⚙️ Auto-Save", enabled and "aktiviert" or "deaktiviert", "für:", player.Name)
end

function SaveManager:IsAutoSaveEnabled(player)
    local playerId = tostring(player.UserId)
    return self.autoSaveEnabled[playerId] ~= false -- Standard: aktiviert
end

-- Spieler-Events
function SaveManager:OnPlayerAdded(player)
    local playerId = tostring(player.UserId)

    -- Spieler-spezifische Daten initialisieren
    self.saveSlots[playerId] = nil
    self.currentSaveSlot[playerId] = nil
    self.autoSaveEnabled[playerId] = true
    self.lastAutoSave[playerId] = tick()

    print("👤 SaveManager initialisiert für:", player.Name)
end

function SaveManager:OnPlayerRemoving(player)
    local playerId = tostring(player.UserId)

    -- Auto-Save vor dem Verlassen
    if self.autoSaveEnabled[playerId] then
        self:AutoSave(player)
    end

    -- Cleanup
    self.saveSlots[playerId] = nil
    self.currentSaveSlot[playerId] = nil
    self.autoSaveEnabled[playerId] = nil
    self.lastAutoSave[playerId] = nil

    print("👤 SaveManager cleanup für:", player.Name)
end

-- Cleanup
function SaveManager:Cleanup()
    if self.autoSaveConnection then
        self.autoSaveConnection:Disconnect()
        self.autoSaveConnection = nil
    end

    print("🧹 SaveManager cleanup abgeschlossen")
end

-- Initialisierung
function SaveManager:Initialize()
    print("💾 SaveManager initialisiert - Mehrere Save-Slots mit Metadaten und Vorschaubildern")
end

-- Singleton-Instanz erstellen
local SaveManagerInstance = SaveManager.new()

-- Spieler-Events verbinden
game.Players.PlayerAdded:Connect(function(player)
    SaveManagerInstance:OnPlayerAdded(player)
end)

game.Players.PlayerRemoving:Connect(function(player)
    SaveManagerInstance:OnPlayerRemoving(player)
end)

-- Cleanup bei Server-Shutdown
game:BindToClose(function()
    SaveManagerInstance:Cleanup()
end)

return SaveManagerInstance
