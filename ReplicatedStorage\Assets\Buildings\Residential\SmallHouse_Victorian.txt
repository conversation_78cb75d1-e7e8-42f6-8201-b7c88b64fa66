# Kleines Viktorianisches Haus
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "SMALL_HOUSE_VICTORIAN"
Name = "Kleines Viktorianisches Haus"
Category = "Residential"
Size = "Small"
Era = "Victorian"
Era_Years = {1850, 1900}

[MODEL_DATA]
ModelId = "rbxassetid://SMALL_HOUSE_VICTORIAN"
Scale = Vector3(8, 6, 10)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.6, 0.4, 0.2)      # Dunkles Holz
Secondary = Color3(0.8, 0.2, 0.2)    # Rotes Ziegeldach
Accent = Color3(0.9, 0.9, 0.7)       # Cremefarbene Verzierungen
Trim = Color3(0.95, 0.95, 0.95)      # Weiße Fensterrahmen

[GAMEPLAY_STATS]
Population = 4
BuildCost = 2500
MaintenanceCost = 25
BuildTime = 30
PowerConsumption = 0
WaterConsumption = 2
LandSize = Vector2(2, 2)  # 2x2 Felder

[REQUIREMENTS]
MinPopulation = 0
MinYear = 1850
RequiredTech = {}
RequiredResources = {"Wood", "Stone"}
UnlockCost = 0

[FEATURES]
ArchitecturalStyle = "Victorian"
HasGarden = true
HasChimney = true
HasBasement = false
Floors = 1
WindowStyle = "Bay_Windows"
RoofStyle = "Steep_Gabled"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "MEDIUM_HOUSE_VICTORIAN"
UpgradeCost = 1500
UpgradeTime = 20
UpgradeRequirements = {"Population_Growth"}

[ECONOMIC_DATA]
TaxRevenue = 15
PropertyValue = 2500
MaintenanceJobs = 0
ConstructionJobs = 5

[DESCRIPTION]
ShortDesc = "Gemütliches kleines Haus im viktorianischen Stil"
LongDesc = "Ein charmantes kleines Holzhaus mit typisch viktorianischen Elementen wie Erkerfenstern und verzierten Holzarbeiten. Perfekt für eine kleine Familie am Anfang der industriellen Entwicklung."
