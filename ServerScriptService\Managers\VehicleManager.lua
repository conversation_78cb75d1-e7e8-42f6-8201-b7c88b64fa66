-- ServerScriptService/Managers/VehicleManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Verwaltet alle Fahrzeuge und deren Bewegung

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local VehicleManager = {}
VehicleManager.__index = VehicleManager

-- Erweiterte Fahrzeug-Definitionen
local VEHICLE_TYPES = {
    -- ZÜGE - LOKOMOTIVEN
    STEAM_LOCOMOTIVE = {
        name = "Dampflok 'Pioneer'",
        type = "TRAIN",
        era = 1850,
        cost = 50000,
        maxSpeed = 60, -- km/h
        power = 800, -- PS
        capacity = {passengers = 0, cargo = 0}, -- Nur Zugmaschine
        fuelType = "COAL",
        fuelConsumption = 15, -- pro Stunde
        reliability = 0.7,
        maintenanceCost = 500, -- pro Monat
        lifespan = 25, -- Jahre
        size = Vector3.new(12, 4, 3),
        color = Color3.fromRGB(50, 50, 50),
        description = "Klassische Dampflokomotive für schwere Lasten",
        unlockYear = 1850
    },
    DIESEL_LOCOMOTIVE = {
        name = "Diesellok 'Powerhouse'",
        type = "TRAIN",
        era = 1930,
        cost = 120000,
        maxSpeed = 120,
        power = 2000,
        capacity = {passengers = 0, cargo = 0},
        fuelType = "DIESEL",
        fuelConsumption = 8,
        reliability = 0.85,
        maintenanceCost = 800,
        lifespan = 30,
        size = Vector3.new(15, 4, 3),
        color = Color3.fromRGB(200, 50, 50),
        description = "Moderne Diesellokomotive mit hoher Leistung",
        unlockYear = 1930
    },
    ELECTRIC_LOCOMOTIVE = {
        name = "E-Lok 'Lightning'",
        type = "TRAIN",
        era = 1960,
        cost = 200000,
        maxSpeed = 200,
        power = 4000,
        capacity = {passengers = 0, cargo = 0},
        fuelType = "ELECTRICITY",
        fuelConsumption = 12, -- kWh pro km
        reliability = 0.95,
        maintenanceCost = 600,
        lifespan = 40,
        size = Vector3.new(18, 4, 3),
        color = Color3.fromRGB(50, 100, 200),
        description = "Hochmoderne Elektrolokomotive",
        unlockYear = 1960
    },
    HIGH_SPEED_TRAIN = {
        name = "Hochgeschwindigkeitszug 'Bullet'",
        type = "TRAIN",
        era = 1990,
        cost = 800000,
        maxSpeed = 320,
        power = 8000,
        capacity = {passengers = 400, cargo = 0},
        fuelType = "ELECTRICITY",
        fuelConsumption = 20,
        reliability = 0.98,
        maintenanceCost = 1500,
        lifespan = 35,
        size = Vector3.new(200, 4, 3), -- Ganzer Zug
        color = Color3.fromRGB(255, 255, 255),
        description = "Ultramoderner Hochgeschwindigkeitszug",
        unlockYear = 1990
    },

    -- WAGGONS
    PASSENGER_CAR_BASIC = {
        name = "Personenwagen Standard",
        type = "TRAIN_CAR",
        era = 1850,
        cost = 15000,
        maxSpeed = 120,
        power = 0,
        capacity = {passengers = 80, cargo = 0},
        fuelType = "NONE",
        fuelConsumption = 0,
        reliability = 0.9,
        maintenanceCost = 200,
        lifespan = 35,
        size = Vector3.new(20, 4, 3),
        color = Color3.fromRGB(150, 100, 50),
        description = "Komfortabler Personenwagen",
        unlockYear = 1850
    },
    PASSENGER_CAR_LUXURY = {
        name = "Luxus-Personenwagen",
        type = "TRAIN_CAR",
        era = 1920,
        cost = 35000,
        maxSpeed = 140,
        power = 0,
        capacity = {passengers = 60, cargo = 0}, -- Weniger Plätze, mehr Komfort
        fuelType = "NONE",
        fuelConsumption = 0,
        reliability = 0.95,
        maintenanceCost = 400,
        lifespan = 40,
        size = Vector3.new(22, 4, 3),
        color = Color3.fromRGB(100, 50, 150),
        description = "Luxuriöser Personenwagen mit Premium-Service",
        unlockYear = 1920
    },
    CARGO_CAR_BASIC = {
        name = "Güterwagen Standard",
        type = "TRAIN_CAR",
        era = 1850,
        cost = 12000,
        maxSpeed = 100,
        power = 0,
        capacity = {passengers = 0, cargo = 25}, -- Tonnen
        fuelType = "NONE",
        fuelConsumption = 0,
        reliability = 0.95,
        maintenanceCost = 150,
        lifespan = 40,
        size = Vector3.new(15, 4, 3),
        color = Color3.fromRGB(100, 100, 100),
        description = "Robuster Güterwagen für alle Waren",
        unlockYear = 1850
    },
    CARGO_CAR_HEAVY = {
        name = "Schwerer Güterwagen",
        type = "TRAIN_CAR",
        era = 1920,
        cost = 25000,
        maxSpeed = 80,
        power = 0,
        capacity = {passengers = 0, cargo = 50},
        fuelType = "NONE",
        fuelConsumption = 0,
        reliability = 0.9,
        maintenanceCost = 250,
        lifespan = 45,
        size = Vector3.new(18, 5, 3),
        color = Color3.fromRGB(80, 80, 80),
        description = "Verstärkter Güterwagen für schwere Lasten",
        unlockYear = 1920
    },
    TANKER_CAR = {
        name = "Tankwagen",
        type = "TRAIN_CAR",
        era = 1900,
        cost = 20000,
        maxSpeed = 90,
        power = 0,
        capacity = {passengers = 0, cargo = 40}, -- Nur Flüssigkeiten
        fuelType = "NONE",
        fuelConsumption = 0,
        reliability = 0.92,
        maintenanceCost = 300,
        lifespan = 30,
        size = Vector3.new(16, 4, 3),
        color = Color3.fromRGB(200, 200, 50),
        description = "Spezieller Tankwagen für Flüssigkeiten",
        unlockYear = 1900,
        cargoTypes = {"OIL", "FUEL", "CHEMICALS"}
    }
}

-- Fortsetzung der Fahrzeug-Definitionen
-- LKWs
local TRUCK_TYPES = {
    SMALL_TRUCK = {
        name = "Kleiner LKW 'Courier'",
        type = "TRUCK",
        era = 1920,
        cost = 25000,
        maxSpeed = 80,
        power = 150,
        capacity = {passengers = 2, cargo = 5},
        fuelType = "GASOLINE",
        fuelConsumption = 12, -- Liter pro 100km
        reliability = 0.8,
        maintenanceCost = 300,
        lifespan = 15,
        size = Vector3.new(6, 3, 2.5),
        color = Color3.fromRGB(100, 150, 100),
        description = "Wendiger LKW für kurze Strecken",
        unlockYear = 1920
    },
    MEDIUM_TRUCK = {
        name = "Mittlerer LKW 'Workhorse'",
        type = "TRUCK",
        era = 1940,
        cost = 45000,
        maxSpeed = 85,
        power = 250,
        capacity = {passengers = 2, cargo = 15},
        fuelType = "DIESEL",
        fuelConsumption = 18,
        reliability = 0.85,
        maintenanceCost = 450,
        lifespan = 18,
        size = Vector3.new(8, 3.5, 2.5),
        color = Color3.fromRGB(150, 100, 50),
        description = "Zuverlässiger LKW für mittlere Distanzen",
        unlockYear = 1940
    },
    HEAVY_TRUCK = {
        name = "Schwerer LKW 'Titan'",
        type = "TRUCK",
        era = 1950,
        cost = 80000,
        maxSpeed = 90,
        power = 400,
        capacity = {passengers = 2, cargo = 40},
        fuelType = "DIESEL",
        fuelConsumption = 25,
        reliability = 0.85,
        maintenanceCost = 600,
        lifespan = 20,
        size = Vector3.new(12, 4, 3),
        color = Color3.fromRGB(200, 100, 50),
        description = "Leistungsstarker LKW für schwere Lasten",
        unlockYear = 1950
    },
    ARTICULATED_TRUCK = {
        name = "Sattelschlepper 'Giant'",
        type = "TRUCK",
        era = 1970,
        cost = 150000,
        maxSpeed = 110,
        power = 600,
        capacity = {passengers = 2, cargo = 80},
        fuelType = "DIESEL",
        fuelConsumption = 35,
        reliability = 0.9,
        maintenanceCost = 800,
        lifespan = 25,
        size = Vector3.new(18, 4, 3),
        color = Color3.fromRGB(50, 100, 200),
        description = "Moderner Sattelschlepper für Fernverkehr",
        unlockYear = 1970
    }
}

-- SCHIFFE
local SHIP_TYPES = {
    CARGO_SHIP_SMALL = {
        name = "Kleines Frachtschiff 'Merchant'",
        type = "SHIP",
        era = 1900,
        cost = 300000,
        maxSpeed = 20,
        power = 1500,
        capacity = {passengers = 10, cargo = 200},
        fuelType = "HEAVY_OIL",
        fuelConsumption = 30,
        reliability = 0.85,
        maintenanceCost = 1200,
        lifespan = 25,
        size = Vector3.new(50, 12, 8),
        color = Color3.fromRGB(50, 50, 100),
        description = "Kompaktes Frachtschiff für Küstenverkehr",
        unlockYear = 1900
    },
    CARGO_SHIP_LARGE = {
        name = "Großes Frachtschiff 'Oceanic'",
        type = "SHIP",
        era = 1950,
        cost = 800000,
        maxSpeed = 25,
        power = 3000,
        capacity = {passengers = 20, cargo = 800},
        fuelType = "HEAVY_OIL",
        fuelConsumption = 60,
        reliability = 0.9,
        maintenanceCost = 2500,
        lifespan = 30,
        size = Vector3.new(120, 18, 15),
        color = Color3.fromRGB(30, 30, 80),
        description = "Großes Frachtschiff für Überseetransport",
        unlockYear = 1950
    },
    CONTAINER_SHIP = {
        name = "Containerschiff 'Global'",
        type = "SHIP",
        era = 1980,
        cost = 2000000,
        maxSpeed = 30,
        power = 5000,
        capacity = {passengers = 25, cargo = 2000},
        fuelType = "HEAVY_OIL",
        fuelConsumption = 100,
        reliability = 0.95,
        maintenanceCost = 4000,
        lifespan = 35,
        size = Vector3.new(200, 25, 20),
        color = Color3.fromRGB(200, 50, 50),
        description = "Modernes Containerschiff für Welthandel",
        unlockYear = 1980
    }
}

-- BUSSE
local BUS_TYPES = {
    CITY_BUS = {
        name = "Stadtbus 'Urban'",
        type = "BUS",
        era = 1930,
        cost = 40000,
        maxSpeed = 60,
        power = 200,
        capacity = {passengers = 50, cargo = 0},
        fuelType = "DIESEL",
        fuelConsumption = 20,
        reliability = 0.85,
        maintenanceCost = 400,
        lifespan = 12,
        size = Vector3.new(12, 3, 2.5),
        color = Color3.fromRGB(255, 200, 50),
        description = "Zuverlässiger Stadtbus",
        unlockYear = 1930
    },
    ARTICULATED_BUS = {
        name = "Gelenkbus 'Metro'",
        type = "BUS",
        era = 1960,
        cost = 80000,
        maxSpeed = 65,
        power = 300,
        capacity = {passengers = 120, cargo = 0},
        fuelType = "DIESEL",
        fuelConsumption = 30,
        reliability = 0.88,
        maintenanceCost = 600,
        lifespan = 15,
        size = Vector3.new(18, 3, 2.5),
        color = Color3.fromRGB(200, 150, 50),
        description = "Großer Gelenkbus für Hauptlinien",
        unlockYear = 1960
    },
    ELECTRIC_BUS = {
        name = "Elektrobus 'EcoRide'",
        type = "BUS",
        era = 2000,
        cost = 120000,
        maxSpeed = 70,
        power = 250,
        capacity = {passengers = 80, cargo = 0},
        fuelType = "ELECTRICITY",
        fuelConsumption = 15, -- kWh pro 100km
        reliability = 0.95,
        maintenanceCost = 300,
        lifespan = 18,
        size = Vector3.new(12, 3, 2.5),
        color = Color3.fromRGB(50, 200, 50),
        description = "Umweltfreundlicher Elektrobus",
        unlockYear = 2000
    }
}

-- Alle Fahrzeugtypen zusammenfassen
for k, v in pairs(TRUCK_TYPES) do
    VEHICLE_TYPES[k] = v
end
for k, v in pairs(SHIP_TYPES) do
    VEHICLE_TYPES[k] = v
end
for k, v in pairs(BUS_TYPES) do
    VEHICLE_TYPES[k] = v
end

function VehicleManager.new()
    local self = setmetatable({}, VehicleManager)

    self.vehicles = {} -- Alle Fahrzeuge
    self.depots = {} -- Fahrzeug-Depots
    self.nextVehicleId = 1
    self.nextDepotId = 1

    self:InitializeEvents()
    self:CreateVehicleFolder()

    -- Update-Loop starten
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:Update(deltaTime)
    end)

    return self
end

-- Fahrzeug erstellen
function VehicleManager:CreateVehicle(playerId, vehicleType, startPosition)
    local vehicleData = VehicleTypes[vehicleType]
    if not vehicleData then
        return false, "Unbekannter Fahrzeugtyp"
    end
    
    local vehicleId = "VEHICLE_" .. self.NextVehicleId
    self.NextVehicleId = self.NextVehicleId + 1
    
    -- Fahrzeug-Objekt erstellen
    local vehicle = {
        Id = vehicleId,
        Type = vehicleType,
        PlayerId = playerId,
        Position = startPosition,
        Rotation = Vector3.new(0, 0, 0),
        Speed = vehicleData.Speed,
        Capacity = vehicleData.Capacity,
        CurrentLoad = 0,
        Cargo = {},
        Route = nil,
        CurrentWaypoint = 1,
        IsMoving = false,
        Model = nil
    }
    
    -- 3D-Modell erstellen
    vehicle.Model = self:CreateVehicleModel(vehicle, vehicleData)
    
    self.ActiveVehicles[vehicleId] = vehicle
    
    print("🚂 Fahrzeug erstellt:", vehicleType, "für Spieler:", playerId)
    return true, vehicleId
end

-- 3D-Modell erstellen
function VehicleManager:CreateVehicleModel(vehicle, vehicleData)
    local model = Instance.new("Model")
    model.Name = vehicle.Id
    model.Parent = workspace
    
    -- Haupt-Part
    local mainPart = Instance.new("Part")
    mainPart.Name = "Main"
    mainPart.Size = Vector3.new(8, 4, 20)
    mainPart.Position = vehicle.Position
    mainPart.Anchored = true
    mainPart.BrickColor = BrickColor.new("Really black")
    mainPart.Parent = model
    
    -- Schornstein für Dampffahrzeuge
    if vehicle.Type == "STEAM_TRAIN" or vehicle.Type == "STEAMSHIP" then
        local chimney = Instance.new("Part")
        chimney.Name = "Chimney"
        chimney.Size = Vector3.new(2, 8, 2)
        chimney.Position = mainPart.Position + Vector3.new(0, 6, -6)
        chimney.Anchored = true
        chimney.BrickColor = BrickColor.new("Dark stone grey")
        chimney.Parent = model
        
        -- Rauch-Effekt
        local smoke = Instance.new("Smoke")
        smoke.Size = 5
        smoke.Opacity = 0.3
        smoke.RiseVelocity = 10
        smoke.Parent = chimney
    end
    
    -- Räder für Landfahrzeuge
    if vehicle.Type == "STEAM_TRAIN" or vehicle.Type == "TRUCK_EARLY" then
        for i = 1, 4 do
            local wheel = Instance.new("Part")
            wheel.Name = "Wheel" .. i
            wheel.Size = Vector3.new(3, 3, 1)
            wheel.Shape = Enum.PartType.Cylinder
            wheel.Position = mainPart.Position + Vector3.new(
                (i <= 2) and -3 or 3,
                -2,
                (i % 2 == 1) and 6 or -6
            )
            wheel.Anchored = true
            wheel.BrickColor = BrickColor.new("Really black")
            wheel.Parent = model
        end
    end
    
    return model
end

-- Route setzen
function VehicleManager:SetVehicleRoute(vehicleId, waypoints)
    local vehicle = self.ActiveVehicles[vehicleId]
    if not vehicle then
        return false, "Fahrzeug nicht gefunden"
    end
    
    vehicle.Route = waypoints
    vehicle.CurrentWaypoint = 1
    vehicle.IsMoving = true
    
    print("🗺️ Route gesetzt für Fahrzeug:", vehicleId, "Wegpunkte:", #waypoints)
    return true
end

-- Fahrzeug bewegen
function VehicleManager:MoveVehicle(vehicle, deltaTime)
    if not vehicle.IsMoving or not vehicle.Route or #vehicle.Route == 0 then
        return
    end
    
    local targetWaypoint = vehicle.Route[vehicle.CurrentWaypoint]
    if not targetWaypoint then
        vehicle.IsMoving = false
        return
    end
    
    local currentPos = vehicle.Position
    local targetPos = targetWaypoint.Position
    local distance = (targetPos - currentPos).Magnitude
    
    -- Geschwindigkeit in Studs/Sekunde umrechnen
    local speedInStuds = vehicle.Speed * 2.8 -- Ungefähr km/h zu Studs/s
    local moveDistance = speedInStuds * deltaTime
    
    if distance <= moveDistance then
        -- Wegpunkt erreicht
        vehicle.Position = targetPos
        vehicle.CurrentWaypoint = vehicle.CurrentWaypoint + 1
        
        if vehicle.CurrentWaypoint > #vehicle.Route then
            -- Route abgeschlossen
            vehicle.CurrentWaypoint = 1
            print("🏁 Fahrzeug", vehicle.Id, "hat Route abgeschlossen")
        end
    else
        -- Zum Ziel bewegen
        local direction = (targetPos - currentPos).Unit
        vehicle.Position = currentPos + direction * moveDistance
        
        -- Rotation berechnen
        vehicle.Rotation = Vector3.new(0, math.atan2(direction.X, direction.Z), 0)
    end
    
    -- 3D-Modell aktualisieren
    if vehicle.Model and vehicle.Model.Main then
        vehicle.Model.Main.Position = vehicle.Position
        vehicle.Model.Main.Rotation = vehicle.Rotation
        
        -- Alle anderen Teile mitbewegen
        for _, part in pairs(vehicle.Model:GetChildren()) do
            if part:IsA("BasePart") and part.Name ~= "Main" then
                local offset = part.Position - vehicle.Model.Main.Position
                part.Position = vehicle.Position + offset
            end
        end
    end
end

-- Alle Fahrzeuge aktualisieren
function VehicleManager:UpdateVehicles(deltaTime)
    for vehicleId, vehicle in pairs(self.ActiveVehicles) do
        self:MoveVehicle(vehicle, deltaTime)
    end
end

-- Verfügbare Fahrzeuge für Jahr abrufen
function VehicleManager:GetAvailableVehicles(currentYear)
    local available = {}
    
    for vehicleType, data in pairs(VehicleTypes) do
        if currentYear >= data.UnlockYear then
            available[vehicleType] = data
        end
    end
    
    return available
end

-- Fahrzeug entfernen
function VehicleManager:RemoveVehicle(vehicleId)
    local vehicle = self.ActiveVehicles[vehicleId]
    if vehicle and vehicle.Model then
        vehicle.Model:Destroy()
    end
    
    self.ActiveVehicles[vehicleId] = nil
    print("🗑️ Fahrzeug entfernt:", vehicleId)
end

return VehicleManager
