-- ServerScriptService/Managers/VehicleManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Verwaltet alle Fahrzeuge und deren Bewegung

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local VehicleManager = {}
VehicleManager.__index = VehicleManager

-- Fahrzeug-Daten
local VehicleTypes = {
    STEAM_TRAIN = {
        Name = "Dampflokomotive",
        Speed = 40, -- km/h
        Capacity = 200, -- Tonnen
        Cost = 15000,
        UnlockYear = 1850,
        FuelType = "Coal",
        ModelId = "rbxassetid://STEAM_TRAIN"
    },
    TRUCK_EARLY = {
        Name = "Früher LKW",
        Speed = 25,
        Capacity = 5,
        Cost = 3000,
        UnlockYear = 1920,
        FuelType = "Gasoline",
        ModelId = "rbxassetid://TRUCK_EARLY"
    },
    STEAMSHIP = {
        Name = "Dampfschiff",
        Speed = 15,
        Capacity = 500,
        Cost = 25000,
        UnlockYear = 1850,
        FuelType = "Coal",
        ModelId = "rbxassetid://STEAMSHIP"
    }
}

function VehicleManager.new()
    local self = setmetatable({}, VehicleManager)
    
    self.ActiveVehicles = {}
    self.VehicleRoutes = {}
    self.NextVehicleId = 1
    
    return self
end

-- Fahrzeug erstellen
function VehicleManager:CreateVehicle(playerId, vehicleType, startPosition)
    local vehicleData = VehicleTypes[vehicleType]
    if not vehicleData then
        return false, "Unbekannter Fahrzeugtyp"
    end
    
    local vehicleId = "VEHICLE_" .. self.NextVehicleId
    self.NextVehicleId = self.NextVehicleId + 1
    
    -- Fahrzeug-Objekt erstellen
    local vehicle = {
        Id = vehicleId,
        Type = vehicleType,
        PlayerId = playerId,
        Position = startPosition,
        Rotation = Vector3.new(0, 0, 0),
        Speed = vehicleData.Speed,
        Capacity = vehicleData.Capacity,
        CurrentLoad = 0,
        Cargo = {},
        Route = nil,
        CurrentWaypoint = 1,
        IsMoving = false,
        Model = nil
    }
    
    -- 3D-Modell erstellen
    vehicle.Model = self:CreateVehicleModel(vehicle, vehicleData)
    
    self.ActiveVehicles[vehicleId] = vehicle
    
    print("🚂 Fahrzeug erstellt:", vehicleType, "für Spieler:", playerId)
    return true, vehicleId
end

-- 3D-Modell erstellen
function VehicleManager:CreateVehicleModel(vehicle, vehicleData)
    local model = Instance.new("Model")
    model.Name = vehicle.Id
    model.Parent = workspace
    
    -- Haupt-Part
    local mainPart = Instance.new("Part")
    mainPart.Name = "Main"
    mainPart.Size = Vector3.new(8, 4, 20)
    mainPart.Position = vehicle.Position
    mainPart.Anchored = true
    mainPart.BrickColor = BrickColor.new("Really black")
    mainPart.Parent = model
    
    -- Schornstein für Dampffahrzeuge
    if vehicle.Type == "STEAM_TRAIN" or vehicle.Type == "STEAMSHIP" then
        local chimney = Instance.new("Part")
        chimney.Name = "Chimney"
        chimney.Size = Vector3.new(2, 8, 2)
        chimney.Position = mainPart.Position + Vector3.new(0, 6, -6)
        chimney.Anchored = true
        chimney.BrickColor = BrickColor.new("Dark stone grey")
        chimney.Parent = model
        
        -- Rauch-Effekt
        local smoke = Instance.new("Smoke")
        smoke.Size = 5
        smoke.Opacity = 0.3
        smoke.RiseVelocity = 10
        smoke.Parent = chimney
    end
    
    -- Räder für Landfahrzeuge
    if vehicle.Type == "STEAM_TRAIN" or vehicle.Type == "TRUCK_EARLY" then
        for i = 1, 4 do
            local wheel = Instance.new("Part")
            wheel.Name = "Wheel" .. i
            wheel.Size = Vector3.new(3, 3, 1)
            wheel.Shape = Enum.PartType.Cylinder
            wheel.Position = mainPart.Position + Vector3.new(
                (i <= 2) and -3 or 3,
                -2,
                (i % 2 == 1) and 6 or -6
            )
            wheel.Anchored = true
            wheel.BrickColor = BrickColor.new("Really black")
            wheel.Parent = model
        end
    end
    
    return model
end

-- Route setzen
function VehicleManager:SetVehicleRoute(vehicleId, waypoints)
    local vehicle = self.ActiveVehicles[vehicleId]
    if not vehicle then
        return false, "Fahrzeug nicht gefunden"
    end
    
    vehicle.Route = waypoints
    vehicle.CurrentWaypoint = 1
    vehicle.IsMoving = true
    
    print("🗺️ Route gesetzt für Fahrzeug:", vehicleId, "Wegpunkte:", #waypoints)
    return true
end

-- Fahrzeug bewegen
function VehicleManager:MoveVehicle(vehicle, deltaTime)
    if not vehicle.IsMoving or not vehicle.Route or #vehicle.Route == 0 then
        return
    end
    
    local targetWaypoint = vehicle.Route[vehicle.CurrentWaypoint]
    if not targetWaypoint then
        vehicle.IsMoving = false
        return
    end
    
    local currentPos = vehicle.Position
    local targetPos = targetWaypoint.Position
    local distance = (targetPos - currentPos).Magnitude
    
    -- Geschwindigkeit in Studs/Sekunde umrechnen
    local speedInStuds = vehicle.Speed * 2.8 -- Ungefähr km/h zu Studs/s
    local moveDistance = speedInStuds * deltaTime
    
    if distance <= moveDistance then
        -- Wegpunkt erreicht
        vehicle.Position = targetPos
        vehicle.CurrentWaypoint = vehicle.CurrentWaypoint + 1
        
        if vehicle.CurrentWaypoint > #vehicle.Route then
            -- Route abgeschlossen
            vehicle.CurrentWaypoint = 1
            print("🏁 Fahrzeug", vehicle.Id, "hat Route abgeschlossen")
        end
    else
        -- Zum Ziel bewegen
        local direction = (targetPos - currentPos).Unit
        vehicle.Position = currentPos + direction * moveDistance
        
        -- Rotation berechnen
        vehicle.Rotation = Vector3.new(0, math.atan2(direction.X, direction.Z), 0)
    end
    
    -- 3D-Modell aktualisieren
    if vehicle.Model and vehicle.Model.Main then
        vehicle.Model.Main.Position = vehicle.Position
        vehicle.Model.Main.Rotation = vehicle.Rotation
        
        -- Alle anderen Teile mitbewegen
        for _, part in pairs(vehicle.Model:GetChildren()) do
            if part:IsA("BasePart") and part.Name ~= "Main" then
                local offset = part.Position - vehicle.Model.Main.Position
                part.Position = vehicle.Position + offset
            end
        end
    end
end

-- Alle Fahrzeuge aktualisieren
function VehicleManager:UpdateVehicles(deltaTime)
    for vehicleId, vehicle in pairs(self.ActiveVehicles) do
        self:MoveVehicle(vehicle, deltaTime)
    end
end

-- Verfügbare Fahrzeuge für Jahr abrufen
function VehicleManager:GetAvailableVehicles(currentYear)
    local available = {}
    
    for vehicleType, data in pairs(VehicleTypes) do
        if currentYear >= data.UnlockYear then
            available[vehicleType] = data
        end
    end
    
    return available
end

-- Fahrzeug entfernen
function VehicleManager:RemoveVehicle(vehicleId)
    local vehicle = self.ActiveVehicles[vehicleId]
    if vehicle and vehicle.Model then
        vehicle.Model:Destroy()
    end
    
    self.ActiveVehicles[vehicleId] = nil
    print("🗑️ Fahrzeug entfernt:", vehicleId)
end

return VehicleManager
