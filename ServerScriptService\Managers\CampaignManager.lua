-- ServerScriptService/Managers/CampaignManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Kampagnen-System mit Story, Zielen und Achievements

local CampaignManager = {}
CampaignManager.__index = CampaignManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Kampagnen-Definitionen
local CAMPAIGNS = {
    TUTORIAL = {
        id = "TUTORIAL",
        name = "Transport-Pionier",
        description = "Lerne die Grundlagen des Transportwesens in einer kleinen Stadt.",
        difficulty = "EASY",
        estimatedTime = "30 Minuten",
        unlocked = true,
        
        startConditions = {
            money = 500000,
            year = 1850,
            mapSize = "SMALL",
            cities = {"Pionierstadt"},
            industries = {"Kohlemine", "Sägewerk"}
        },
        
        objectives = {
            {
                id = "first_route",
                name = "Erste Route",
                description = "Erstelle deine erste Transportroute zwischen zwei Städten",
                type = "CREATE_ROUTE",
                target = 1,
                reward = {money = 50000, experience = 100},
                mandatory = true
            },
            {
                id = "transport_passengers",
                name = "Passagier-Transport",
                description = "Transportiere 100 Passagiere",
                type = "TRANSPORT_PASSENGERS",
                target = 100,
                reward = {money = 25000, experience = 50},
                mandatory = true
            },
            {
                id = "earn_money",
                name = "Gewinn erzielen",
                description = "Erwirtschafte einen Gewinn von $100,000",
                type = "EARN_MONEY",
                target = 100000,
                reward = {money = 100000, experience = 200},
                mandatory = true
            }
        },
        
        story = {
            intro = "Willkommen in der Welt des Transports! Du beginnst als kleiner Unternehmer in einer aufstrebenden Region.",
            chapters = {
                {
                    title = "Die ersten Schritte",
                    text = "Baue deine erste Transportroute und verbinde die Städte miteinander.",
                    trigger = "START"
                },
                {
                    title = "Wachsender Erfolg",
                    text = "Dein Unternehmen wächst! Die Menschen vertrauen auf deine Dienste.",
                    trigger = "OBJECTIVE_COMPLETE:transport_passengers"
                },
                {
                    title = "Erfolgreicher Unternehmer",
                    text = "Gratulation! Du hast bewiesen, dass du ein erfolgreicher Transporteur bist.",
                    trigger = "CAMPAIGN_COMPLETE"
                }
            }
        }
    },
    
    INDUSTRIAL_REVOLUTION = {
        id = "INDUSTRIAL_REVOLUTION",
        name = "Industrielle Revolution",
        description = "Führe eine Region durch die industrielle Revolution des 19. Jahrhunderts.",
        difficulty = "NORMAL",
        estimatedTime = "2 Stunden",
        unlocked = false,
        
        startConditions = {
            money = 1000000,
            year = 1840,
            mapSize = "MEDIUM",
            cities = {"Manchester", "Birmingham", "Liverpool"},
            industries = {"Kohlemine", "Eisenmine", "Stahlwerk", "Textilfabrik"}
        },
        
        objectives = {
            {
                id = "connect_industries",
                name = "Industrie verbinden",
                description = "Verbinde alle Industrien mit Transportrouten",
                type = "CONNECT_INDUSTRIES",
                target = 4,
                reward = {money = 200000, experience = 300},
                mandatory = true
            },
            {
                id = "city_growth",
                name = "Stadtentwicklung",
                description = "Lasse eine Stadt auf 10,000 Einwohner wachsen",
                type = "CITY_POPULATION",
                target = 10000,
                reward = {money = 150000, experience = 250},
                mandatory = true
            },
            {
                id = "railway_network",
                name = "Eisenbahn-Netzwerk",
                description = "Baue 50 km Eisenbahnstrecke",
                type = "BUILD_RAILWAY",
                target = 50000, -- in Metern
                reward = {money = 300000, experience = 400},
                mandatory = false
            }
        }
    },
    
    MODERN_LOGISTICS = {
        id = "MODERN_LOGISTICS",
        name = "Moderne Logistik",
        description = "Meistere die Herausforderungen der modernen Logistik im 21. Jahrhundert.",
        difficulty = "HARD",
        estimatedTime = "3 Stunden",
        unlocked = false,
        
        startConditions = {
            money = 2000000,
            year = 2000,
            mapSize = "LARGE",
            cities = {"Metropolis", "Tech City", "Port Harbor", "Airport City"},
            industries = {"Container-Terminal", "Logistikzentrum", "Technologie-Park"}
        },
        
        objectives = {
            {
                id = "multimodal_transport",
                name = "Multimodaler Transport",
                description = "Nutze alle Transportmittel: Zug, LKW, Schiff und Flugzeug",
                type = "USE_ALL_TRANSPORT_MODES",
                target = 4,
                reward = {money = 500000, experience = 600},
                mandatory = true
            },
            {
                id = "environmental_goals",
                name = "Umweltziele",
                description = "Reduziere CO2-Emissionen um 30%",
                type = "REDUCE_EMISSIONS",
                target = 30, -- Prozent
                reward = {money = 400000, experience = 500},
                mandatory = true
            }
        }
    }
}

-- Achievement-Definitionen
local ACHIEVEMENTS = {
    FIRST_MILLION = {
        id = "FIRST_MILLION",
        name = "Millionär",
        description = "Verdiene deine erste Million",
        icon = "💰",
        type = "MONEY",
        target = 1000000,
        reward = {experience = 500, title = "Millionär"}
    },
    TRANSPORT_MASTER = {
        id = "TRANSPORT_MASTER",
        name = "Transport-Meister",
        description = "Transportiere 1 Million Passagiere",
        icon = "🚂",
        type = "PASSENGERS_TRANSPORTED",
        target = 1000000,
        reward = {experience = 1000, title = "Transport-Meister"}
    },
    CITY_BUILDER = {
        id = "CITY_BUILDER",
        name = "Stadtentwickler",
        description = "Lasse eine Stadt auf 50,000 Einwohner wachsen",
        icon = "🏙️",
        type = "CITY_POPULATION",
        target = 50000,
        reward = {experience = 750, title = "Stadtentwickler"}
    },
    SPEED_DEMON = {
        id = "SPEED_DEMON",
        name = "Geschwindigkeits-Teufel",
        description = "Erreiche 300 km/h mit einem Fahrzeug",
        icon = "⚡",
        type = "MAX_SPEED",
        target = 300,
        reward = {experience = 300, title = "Geschwindigkeits-Teufel"}
    }
}

-- Konstruktor
function CampaignManager.new()
    local self = setmetatable({}, CampaignManager)
    
    self.playerCampaigns = {} -- [playerId] = campaign data
    self.playerAchievements = {} -- [playerId] = achievements
    self.playerProgress = {} -- [playerId] = progress data
    
    self:InitializeEvents()
    
    -- Progress-Update-Loop
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdateProgress(deltaTime)
    end)
    
    return self
end

-- Events initialisieren
function CampaignManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("StartCampaignEvent") then
        local startCampaignEvent = Instance.new("RemoteEvent")
        startCampaignEvent.Name = "StartCampaignEvent"
        startCampaignEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("GetCampaignDataFunction") then
        local getCampaignDataFunction = Instance.new("RemoteFunction")
        getCampaignDataFunction.Name = "GetCampaignDataFunction"
        getCampaignDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("CompleteObjectiveEvent") then
        local completeObjectiveEvent = Instance.new("RemoteEvent")
        completeObjectiveEvent.Name = "CompleteObjectiveEvent"
        completeObjectiveEvent.Parent = Events
    end
    
    -- Event-Handler
    Events.StartCampaignEvent.OnServerEvent:Connect(function(player, campaignId)
        self:StartCampaign(player, campaignId)
    end)
    
    Events.GetCampaignDataFunction.OnServerInvoke = function(player)
        return self:GetPlayerCampaignData(player)
    end
    
    Events.CompleteObjectiveEvent.OnServerEvent:Connect(function(player, objectiveId)
        self:CompleteObjective(player, objectiveId)
    end)
end

-- Kampagne starten
function CampaignManager:StartCampaign(player, campaignId)
    local playerId = player.UserId
    local campaign = CAMPAIGNS[campaignId]
    
    if not campaign then
        warn("Unbekannte Kampagne:", campaignId)
        return false
    end
    
    if not campaign.unlocked then
        print("❌ Kampagne nicht freigeschaltet:", campaign.name)
        return false
    end
    
    -- Kampagnen-Fortschritt initialisieren
    self.playerCampaigns[playerId] = {
        campaignId = campaignId,
        startTime = tick(),
        currentChapter = 1,
        completedObjectives = {},
        activeObjectives = {},
        status = "ACTIVE" -- ACTIVE, COMPLETED, FAILED
    }
    
    -- Erste Ziele aktivieren
    for _, objective in ipairs(campaign.objectives) do
        if objective.mandatory then
            table.insert(self.playerCampaigns[playerId].activeObjectives, {
                id = objective.id,
                progress = 0,
                target = objective.target,
                completed = false
            })
        end
    end
    
    -- Spieler-Fortschritt initialisieren
    if not self.playerProgress[playerId] then
        self.playerProgress[playerId] = {
            experience = 0,
            level = 1,
            titles = {},
            statistics = {
                passengersTransported = 0,
                cargoTransported = 0,
                moneyEarned = 0,
                routesCreated = 0,
                citiesConnected = 0
            }
        }
    end
    
    -- Achievements initialisieren
    if not self.playerAchievements[playerId] then
        self.playerAchievements[playerId] = {}
    end
    
    print("🎯 Kampagne gestartet:", campaign.name, "für Spieler:", player.Name)
    
    -- Story-Intro zeigen
    self:ShowStoryChapter(player, campaign.story.intro)
    
    return true
end

-- Ziel abschließen
function CampaignManager:CompleteObjective(player, objectiveId)
    local playerId = player.UserId
    local campaignData = self.playerCampaigns[playerId]
    
    if not campaignData then
        warn("Spieler hat keine aktive Kampagne")
        return false
    end
    
    local campaign = CAMPAIGNS[campaignData.campaignId]
    local objective = nil
    
    -- Ziel finden
    for _, obj in ipairs(campaign.objectives) do
        if obj.id == objectiveId then
            objective = obj
            break
        end
    end
    
    if not objective then
        warn("Unbekanntes Ziel:", objectiveId)
        return false
    end
    
    -- Ziel als abgeschlossen markieren
    table.insert(campaignData.completedObjectives, objectiveId)
    
    -- Belohnung geben
    if objective.reward then
        if objective.reward.money then
            -- Geld hinzufügen (über EconomyManager)
            local economyManager = require(script.Parent.EconomyManager)
            if economyManager then
                economyManager:AddPlayerMoney(playerId, objective.reward.money)
            end
        end
        
        if objective.reward.experience then
            self.playerProgress[playerId].experience = self.playerProgress[playerId].experience + objective.reward.experience
            self:CheckLevelUp(playerId)
        end
    end
    
    print("✅ Ziel abgeschlossen:", objective.name, "von Spieler:", player.Name)
    
    -- Prüfen ob Kampagne abgeschlossen
    self:CheckCampaignCompletion(player)
    
    return true
end

-- Kampagnen-Abschluss prüfen
function CampaignManager:CheckCampaignCompletion(player)
    local playerId = player.UserId
    local campaignData = self.playerCampaigns[playerId]
    local campaign = CAMPAIGNS[campaignData.campaignId]
    
    -- Alle Pflicht-Ziele abgeschlossen?
    local mandatoryObjectives = {}
    for _, objective in ipairs(campaign.objectives) do
        if objective.mandatory then
            table.insert(mandatoryObjectives, objective.id)
        end
    end
    
    local completedMandatory = 0
    for _, objectiveId in ipairs(campaignData.completedObjectives) do
        for _, mandatoryId in ipairs(mandatoryObjectives) do
            if objectiveId == mandatoryId then
                completedMandatory = completedMandatory + 1
                break
            end
        end
    end
    
    if completedMandatory >= #mandatoryObjectives then
        -- Kampagne abgeschlossen!
        campaignData.status = "COMPLETED"
        campaignData.completionTime = tick()
        
        -- Nächste Kampagne freischalten
        self:UnlockNextCampaign(campaignData.campaignId)
        
        -- Abschluss-Story zeigen
        self:ShowStoryChapter(player, "Gratulation! Du hast die Kampagne '" .. campaign.name .. "' erfolgreich abgeschlossen!")
        
        print("🏆 Kampagne abgeschlossen:", campaign.name, "von Spieler:", player.Name)
    end
end

-- Nächste Kampagne freischalten
function CampaignManager:UnlockNextCampaign(completedCampaignId)
    if completedCampaignId == "TUTORIAL" then
        CAMPAIGNS.INDUSTRIAL_REVOLUTION.unlocked = true
    elseif completedCampaignId == "INDUSTRIAL_REVOLUTION" then
        CAMPAIGNS.MODERN_LOGISTICS.unlocked = true
    end
end

-- Level-Up prüfen
function CampaignManager:CheckLevelUp(playerId)
    local progress = self.playerProgress[playerId]
    local requiredExp = progress.level * 1000 -- 1000 EXP pro Level
    
    if progress.experience >= requiredExp then
        progress.level = progress.level + 1
        progress.experience = progress.experience - requiredExp
        
        print("🎉 Level Up! Spieler:", playerId, "Level:", progress.level)
    end
end

-- Fortschritt aktualisieren
function CampaignManager:UpdateProgress(deltaTime)
    -- Hier würden kontinuierliche Fortschritts-Updates stattfinden
    -- z.B. Statistiken sammeln, Achievements prüfen
    
    for playerId, campaignData in pairs(self.playerCampaigns) do
        if campaignData.status == "ACTIVE" then
            self:UpdateCampaignProgress(playerId, campaignData)
        end
    end
end

-- Kampagnen-Fortschritt aktualisieren
function CampaignManager:UpdateCampaignProgress(playerId, campaignData)
    -- Hier würden spezifische Kampagnen-Ziele überwacht
    -- z.B. Geld verdient, Passagiere transportiert, etc.
    
    -- Beispiel: Geld-Ziel prüfen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager and economyManager.PlayerData[tostring(playerId)] then
        local playerMoney = economyManager.PlayerData[tostring(playerId)].money
        
        -- Prüfe Geld-basierte Ziele
        for _, objective in ipairs(campaignData.activeObjectives) do
            local campaign = CAMPAIGNS[campaignData.campaignId]
            local objectiveData = nil
            
            for _, obj in ipairs(campaign.objectives) do
                if obj.id == objective.id then
                    objectiveData = obj
                    break
                end
            end
            
            if objectiveData and objectiveData.type == "EARN_MONEY" then
                if playerMoney >= objectiveData.target and not objective.completed then
                    objective.completed = true
                    self:CompleteObjective({UserId = playerId}, objective.id)
                end
            end
        end
    end
end

-- Story-Kapitel anzeigen
function CampaignManager:ShowStoryChapter(player, text)
    -- Hier würde eine Story-GUI angezeigt werden
    print("📖 Story für", player.Name .. ":", text)
end

-- Spieler-Kampagnen-Daten abrufen
function CampaignManager:GetPlayerCampaignData(player)
    local playerId = player.UserId
    
    return {
        campaigns = CAMPAIGNS,
        currentCampaign = self.playerCampaigns[playerId],
        progress = self.playerProgress[playerId],
        achievements = self.playerAchievements[playerId] or {},
        availableAchievements = ACHIEVEMENTS
    }
end

-- Achievement freischalten
function CampaignManager:UnlockAchievement(playerId, achievementId)
    if not self.playerAchievements[playerId] then
        self.playerAchievements[playerId] = {}
    end
    
    if not self.playerAchievements[playerId][achievementId] then
        local achievement = ACHIEVEMENTS[achievementId]
        if achievement then
            self.playerAchievements[playerId][achievementId] = {
                unlockedAt = tick(),
                progress = achievement.target
            }
            
            -- Belohnung geben
            if achievement.reward then
                if achievement.reward.experience then
                    self.playerProgress[playerId].experience = self.playerProgress[playerId].experience + achievement.reward.experience
                end
                
                if achievement.reward.title then
                    table.insert(self.playerProgress[playerId].titles, achievement.reward.title)
                end
            end
            
            print("🏆 Achievement freigeschaltet:", achievement.name, "für Spieler:", playerId)
        end
    end
end

return CampaignManager
