-- ReplicatedStorage/Modules/PathfindingModule.lua
-- A* Pathfinding für Fahrzeug-Navigation

local PathfindingModule = {}

-- Node-Struktur für A*
local function CreateNode(x, z, gCost, hCost, parent)
    return {
        x = x,
        z = z,
        gCost = gCost or 0, -- <PERSON><PERSON> vom Start
        hCost = hCost or 0, -- Heuristische Kosten zum Ziel
        fCost = (gCost or 0) + (hCost or 0), -- Gesamtkosten
        parent = parent
    }
end

-- Heuristische Distanz (Manhattan-Distanz)
local function GetHeuristicDistance(node1, node2)
    return math.abs(node1.x - node2.x) + math.abs(node1.z - node2.z)
end

-- Euklidische Distanz
local function GetDistance(node1, node2)
    return math.sqrt((node1.x - node2.x)^2 + (node1.z - node2.z)^2)
end

-- <PERSON><PERSON><PERSON><PERSON> eines Nodes finden
local function GetNeighbors(node, mapWidth, mapHeight)
    local neighbors = {}
    local directions = {
        {x = 0, z = 1},   -- <PERSON>
        {x = 1, z = 0},   -- Ost
        {x = 0, z = -1},  -- Süd
        {x = -1, z = 0},  -- West
        {x = 1, z = 1},   -- Nordost
        {x = 1, z = -1},  -- Südost
        {x = -1, z = -1}, -- Südwest
        {x = -1, z = 1}   -- Nordwest
    }
    
    for _, dir in pairs(directions) do
        local newX = node.x + dir.x
        local newZ = node.z + dir.z
        
        -- Grenzen prüfen
        if newX >= 1 and newX <= mapWidth and newZ >= 1 and newZ <= mapHeight then
            table.insert(neighbors, {x = newX, z = newZ})
        end
    end
    
    return neighbors
end

-- Prüfen ob Node in Liste enthalten ist
local function ContainsNode(nodeList, node)
    for _, listNode in pairs(nodeList) do
        if listNode.x == node.x and listNode.z == node.z then
            return listNode
        end
    end
    return nil
end

-- Node aus Liste entfernen
local function RemoveNode(nodeList, node)
    for i, listNode in pairs(nodeList) do
        if listNode.x == node.x and listNode.z == node.z then
            table.remove(nodeList, i)
            return
        end
    end
end

-- Node mit niedrigsten fCost finden
local function GetLowestFCostNode(nodeList)
    local lowestNode = nodeList[1]
    
    for _, node in pairs(nodeList) do
        if node.fCost < lowestNode.fCost or 
           (node.fCost == lowestNode.fCost and node.hCost < lowestNode.hCost) then
            lowestNode = node
        end
    end
    
    return lowestNode
end

-- Pfad rekonstruieren
local function ReconstructPath(endNode)
    local path = {}
    local currentNode = endNode
    
    while currentNode do
        table.insert(path, 1, {x = currentNode.x, z = currentNode.z})
        currentNode = currentNode.parent
    end
    
    return path
end

-- Hauptfunktion: A* Pathfinding
function PathfindingModule:FindPath(startPos, endPos, mapData, vehicleType)
    local mapWidth = mapData.size.width
    local mapHeight = mapData.size.height
    local heightMap = mapData.heightMap
    local waterMap = mapData.waterMap
    
    -- Start- und End-Nodes erstellen
    local startNode = CreateNode(
        math.floor(startPos.x), 
        math.floor(startPos.z), 
        0, 
        GetHeuristicDistance({x = startPos.x, z = startPos.z}, {x = endPos.x, z = endPos.z})
    )
    
    local endNode = CreateNode(math.floor(endPos.x), math.floor(endPos.z))
    
    -- Open und Closed Lists
    local openList = {startNode}
    local closedList = {}
    
    -- Maximale Iterationen (Performance-Schutz)
    local maxIterations = mapWidth * mapHeight
    local iterations = 0
    
    while #openList > 0 and iterations < maxIterations do
        iterations = iterations + 1
        
        -- Node mit niedrigsten fCost
        local currentNode = GetLowestFCostNode(openList)
        
        -- Von Open zu Closed verschieben
        RemoveNode(openList, currentNode)
        table.insert(closedList, currentNode)
        
        -- Ziel erreicht?
        if currentNode.x == endNode.x and currentNode.z == endNode.z then
            return ReconstructPath(currentNode)
        end
        
        -- Nachbarn untersuchen
        local neighbors = GetNeighbors(currentNode, mapWidth, mapHeight)
        
        for _, neighborPos in pairs(neighbors) do
            -- Prüfen ob Nachbar begehbar ist
            if not self:IsWalkable(neighborPos, mapData, vehicleType) then
                goto continue
            end
            
            -- Prüfen ob bereits in Closed List
            if ContainsNode(closedList, neighborPos) then
                goto continue
            end
            
            -- Bewegungskosten berechnen
            local moveCost = GetDistance(currentNode, neighborPos)
            
            -- Terrain-Kosten hinzufügen
            local terrainCost = self:GetTerrainCost(neighborPos, mapData, vehicleType)
            moveCost = moveCost + terrainCost
            
            local newGCost = currentNode.gCost + moveCost
            
            -- Prüfen ob bereits in Open List
            local existingNode = ContainsNode(openList, neighborPos)
            
            if not existingNode then
                -- Neuen Node zur Open List hinzufügen
                local newNode = CreateNode(
                    neighborPos.x,
                    neighborPos.z,
                    newGCost,
                    GetHeuristicDistance(neighborPos, endNode),
                    currentNode
                )
                table.insert(openList, newNode)
            elseif newGCost < existingNode.gCost then
                -- Besseren Pfad gefunden
                existingNode.gCost = newGCost
                existingNode.fCost = existingNode.gCost + existingNode.hCost
                existingNode.parent = currentNode
            end
            
            ::continue::
        end
    end
    
    -- Kein Pfad gefunden
    return nil
end

-- Prüfen ob Position begehbar ist
function PathfindingModule:IsWalkable(pos, mapData, vehicleType)
    local x, z = pos.x, pos.z
    
    -- Grenzen prüfen
    if x < 1 or x > mapData.size.width or z < 1 or z > mapData.size.height then
        return false
    end
    
    -- Wasser prüfen (nur Schiffe können über Wasser)
    if mapData.waterMap[x] and mapData.waterMap[x][z] then
        return vehicleType == "Ship"
    end
    
    -- Höhe prüfen (zu steile Hänge)
    local height = mapData.heightMap[x] and mapData.heightMap[x][z] or 0
    if height > 15 and vehicleType ~= "Ship" then -- Zu hohe Berge
        return false
    end
    
    return true
end

-- Terrain-Kosten berechnen
function PathfindingModule:GetTerrainCost(pos, mapData, vehicleType)
    local x, z = pos.x, pos.z
    local baseCost = 1
    
    -- Höhen-basierte Kosten
    local height = mapData.heightMap[x] and mapData.heightMap[x][z] or 0
    local heightCost = height * 0.1 -- Höhere Gebiete sind teurer
    
    -- Fahrzeug-spezifische Kosten
    local vehicleCost = 0
    if vehicleType == "Train" then
        -- Züge mögen flaches Terrain
        vehicleCost = height > 5 and height * 0.2 or 0
    elseif vehicleType == "Truck" then
        -- LKWs sind flexibler
        vehicleCost = height > 10 and height * 0.1 or 0
    elseif vehicleType == "Ship" then
        -- Schiffe nur auf Wasser
        if not (mapData.waterMap[x] and mapData.waterMap[x][z]) then
            return 1000 -- Sehr hohe Kosten für Land
        end
    end
    
    return baseCost + heightCost + vehicleCost
end

-- Pfad glätten (Waypoints reduzieren)
function PathfindingModule:SmoothPath(path, mapData, vehicleType)
    if #path <= 2 then return path end
    
    local smoothedPath = {path[1]} -- Startpunkt
    local currentIndex = 1
    
    while currentIndex < #path do
        local farthestIndex = currentIndex + 1
        
        -- Finde den weitesten Punkt mit direkter Sichtlinie
        for i = currentIndex + 2, #path do
            if self:HasDirectPath(path[currentIndex], path[i], mapData, vehicleType) then
                farthestIndex = i
            else
                break
            end
        end
        
        table.insert(smoothedPath, path[farthestIndex])
        currentIndex = farthestIndex
    end
    
    return smoothedPath
end

-- Prüfen ob direkter Pfad möglich ist
function PathfindingModule:HasDirectPath(startPos, endPos, mapData, vehicleType)
    local dx = endPos.x - startPos.x
    local dz = endPos.z - startPos.z
    local distance = math.sqrt(dx^2 + dz^2)
    
    if distance == 0 then return true end
    
    local steps = math.floor(distance)
    local stepX = dx / steps
    local stepZ = dz / steps
    
    -- Jeden Schritt prüfen
    for i = 1, steps do
        local checkPos = {
            x = math.floor(startPos.x + stepX * i),
            z = math.floor(startPos.z + stepZ * i)
        }
        
        if not self:IsWalkable(checkPos, mapData, vehicleType) then
            return false
        end
    end
    
    return true
end

-- Pfad-Distanz berechnen
function PathfindingModule:GetPathDistance(path)
    if #path <= 1 then return 0 end
    
    local totalDistance = 0
    
    for i = 1, #path - 1 do
        local distance = GetDistance(path[i], path[i + 1])
        totalDistance = totalDistance + distance
    end
    
    return totalDistance
end

return PathfindingModule
