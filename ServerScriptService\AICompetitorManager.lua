-- ServerScriptService/AICompetitorManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- KI-Konkurrenten System für realistische Markt-Konkurrenz

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local AICompetitorManager = {}
AICompetitorManager.__index = AICompetitorManager

function AICompetitorManager.new()
    local self = setmetatable({}, AICompetitorManager)
    
    -- AI Companies
    self.aiCompanies = {}
    
    -- Company name templates
    self.companyNames = {
        prefixes = {"Trans", "Euro", "Global", "Express", "Rapid", "Swift", "Prime", "Elite", "Metro", "Inter"},
        suffixes = {"Transport", "Logistics", "Express", "Lines", "Cargo", "Rail", "Airways", "Shipping", "Freight", "Connect"},
        countries = {"German", "European", "International", "Continental", "Regional", "National", "City", "Local"}
    }
    
    -- AI personality types
    self.personalityTypes = {
        aggressive = {
            name = "Aggressiv",
            expansionRate = 1.5,
            priceCompetition = 1.3,
            riskTolerance = 1.4,
            investmentRate = 1.2
        },
        conservative = {
            name = "Konservativ",
            expansionRate = 0.7,
            priceCompetition = 0.8,
            riskTolerance = 0.6,
            investmentRate = 0.9
        },
        balanced = {
            name = "Ausgewogen",
            expansionRate = 1.0,
            priceCompetition = 1.0,
            riskTolerance = 1.0,
            investmentRate = 1.0
        },
        innovative = {
            name = "Innovativ",
            expansionRate = 1.2,
            priceCompetition = 0.9,
            riskTolerance = 1.1,
            investmentRate = 1.4
        }
    }
    
    -- Market strategies
    self.strategies = {
        price_leader = "Preisführer",
        quality_leader = "Qualitätsführer",
        niche_player = "Nischenspieler",
        cost_cutter = "Kostensenkung",
        growth_focused = "Wachstumsorientiert"
    }
    
    return self
end

-- Initialize AI competitors
function AICompetitorManager:InitializeCompetitors(numCompetitors, gameSettings)
    self.aiCompanies = {}
    
    for i = 1, numCompetitors do
        local company = self:CreateAICompany(gameSettings)
        self.aiCompanies[company.id] = company
    end
    
    print("🤖 Initialized", numCompetitors, "AI competitors")
end

-- Create AI company
function AICompetitorManager:CreateAICompany(gameSettings)
    local companyId = HttpService:GenerateGUID(false)
    
    -- Generate company name
    local prefix = self.companyNames.prefixes[math.random(#self.companyNames.prefixes)]
    local suffix = self.companyNames.suffixes[math.random(#self.companyNames.suffixes)]
    local country = self.companyNames.countries[math.random(#self.companyNames.countries)]
    
    local name = prefix .. " " .. suffix
    if math.random() < 0.3 then
        name = country .. " " .. name
    end
    
    -- Select personality and strategy
    local personalityKeys = {}
    for key, _ in pairs(self.personalityTypes) do
        table.insert(personalityKeys, key)
    end
    local personalityKey = personalityKeys[math.random(#personalityKeys)]
    local personality = self.personalityTypes[personalityKey]
    
    local strategyKeys = {}
    for key, _ in pairs(self.strategies) do
        table.insert(strategyKeys, key)
    end
    local strategy = strategyKeys[math.random(#strategyKeys)]
    
    local company = {
        id = companyId,
        name = name,
        personality = personalityKey,
        personalityData = personality,
        strategy = strategy,
        
        -- Financial data
        cash = math.random(1000000, 5000000),
        revenue = 0,
        expenses = 0,
        profit = 0,
        companyValue = 0,
        
        -- Assets
        vehicles = {},
        routes = {},
        infrastructure = {},
        
        -- Market data
        marketShare = 0,
        reputation = math.random(50, 80),
        customerSatisfaction = math.random(60, 90),
        
        -- AI state
        lastDecisionTime = 0,
        decisionCooldown = 30, -- seconds between major decisions
        currentGoals = {},
        
        -- Performance metrics
        efficiency = math.random(70, 95),
        reliability = math.random(75, 95),
        innovation = math.random(40, 90),
        
        -- Specializations
        specializations = self:GenerateSpecializations(),
        
        -- Active in these transport types
        activeTransportTypes = {"rail", "road"}, -- Start with basic types
        
        -- AI difficulty scaling
        difficultyMultiplier = gameSettings.aiDifficulty or 1.0
    }
    
    -- Apply difficulty scaling
    company.cash = company.cash * company.difficultyMultiplier
    company.efficiency = math.min(100, company.efficiency * company.difficultyMultiplier)
    
    return company
end

-- Generate AI specializations
function AICompetitorManager:GenerateSpecializations()
    local allSpecializations = {
        "passenger_transport", "cargo_transport", "mail_delivery", 
        "express_service", "bulk_cargo", "container_shipping",
        "regional_service", "intercity_service", "urban_transport"
    }
    
    local specializations = {}
    local numSpecializations = math.random(2, 4)
    
    for i = 1, numSpecializations do
        local spec = allSpecializations[math.random(#allSpecializations)]
        if not specializations[spec] then
            specializations[spec] = math.random(60, 95) -- Skill level in this specialization
        end
    end
    
    return specializations
end

-- Update AI companies
function AICompetitorManager:UpdateAICompanies(deltaTime, gameData)
    for companyId, company in pairs(self.aiCompanies) do
        self:UpdateAICompany(company, deltaTime, gameData)
    end
end

-- Update single AI company
function AICompetitorManager:UpdateAICompany(company, deltaTime, gameData)
    -- Decision making cooldown
    company.lastDecisionTime = company.lastDecisionTime + deltaTime
    
    if company.lastDecisionTime >= company.decisionCooldown then
        self:MakeAIDecisions(company, gameData)
        company.lastDecisionTime = 0
        
        -- Randomize next decision time
        company.decisionCooldown = math.random(20, 60)
    end
    
    -- Update financial performance
    self:UpdateAIFinances(company, deltaTime, gameData)
    
    -- Update market position
    self:UpdateMarketPosition(company, gameData)
    
    -- Update reputation based on performance
    self:UpdateReputation(company, deltaTime)
end

-- AI decision making
function AICompetitorManager:MakeAIDecisions(company, gameData)
    local personality = company.personalityData
    
    -- Evaluate current situation
    local situation = self:EvaluateCompanySituation(company, gameData)
    
    -- Make decisions based on personality and situation
    if situation.needsVehicles and company.cash > 500000 then
        self:AIBuyVehicle(company, gameData)
    end
    
    if situation.needsRoutes and company.cash > 200000 then
        self:AICreateRoute(company, gameData)
    end
    
    if situation.profitability < 0.1 and personality.riskTolerance > 1.0 then
        self:AIOptimizeOperations(company)
    end
    
    if situation.marketOpportunity and company.cash > 1000000 then
        self:AIExpandToNewMarket(company, gameData)
    end
    
    -- Strategic decisions based on company strategy
    if company.strategy == "price_leader" then
        self:AIImplementPriceStrategy(company, gameData)
    elseif company.strategy == "quality_leader" then
        self:AIImplementQualityStrategy(company, gameData)
    elseif company.strategy == "growth_focused" then
        self:AIImplementGrowthStrategy(company, gameData)
    end
end

-- Evaluate company situation
function AICompetitorManager:EvaluateCompanySituation(company, gameData)
    return {
        needsVehicles = #company.vehicles < 5,
        needsRoutes = #company.routes < 3,
        profitability = company.revenue > 0 and company.profit / company.revenue or 0,
        marketOpportunity = math.random() < 0.3, -- Simplified
        cashFlow = company.profit > 0,
        competitionLevel = self:GetCompetitionLevel(gameData),
        marketGrowth = math.random() < 0.4
    }
end

-- AI buy vehicle
function AICompetitorManager:AIBuyVehicle(company, gameData)
    local vehicleTypes = {"train", "truck", "ship"}
    local vehicleType = vehicleTypes[math.random(#vehicleTypes)]
    
    local vehicleCost = math.random(100000, 800000)
    
    if company.cash >= vehicleCost then
        local vehicle = {
            id = HttpService:GenerateGUID(false),
            type = vehicleType,
            purchasePrice = vehicleCost,
            age = 0,
            condition = 100,
            efficiency = math.random(80, 95),
            capacity = math.random(50, 200),
            speed = math.random(60, 120),
            assignedRoute = nil
        }
        
        table.insert(company.vehicles, vehicle)
        company.cash = company.cash - vehicleCost
        
        print("🤖", company.name, "bought", vehicleType, "for", vehicleCost)
    end
end

-- AI create route
function AICompetitorManager:AICreateRoute(company, gameData)
    local routeCost = math.random(50000, 300000)
    
    if company.cash >= routeCost then
        local route = {
            id = HttpService:GenerateGUID(false),
            name = "Route " .. (#company.routes + 1),
            type = math.random() < 0.6 and "passenger" or "cargo",
            distance = math.random(50, 500),
            demand = math.random(100, 1000),
            competition = math.random(0, 3),
            profitability = math.random(0.05, 0.25),
            establishmentCost = routeCost
        }
        
        table.insert(company.routes, route)
        company.cash = company.cash - routeCost
        
        print("🤖", company.name, "established new route:", route.name)
    end
end

-- Update AI finances
function AICompetitorManager:UpdateAIFinances(company, deltaTime, gameData)
    local baseRevenue = 0
    local baseExpenses = 0
    
    -- Calculate revenue from routes
    for _, route in pairs(company.routes) do
        local routeRevenue = route.demand * route.profitability * deltaTime * 0.1
        baseRevenue = baseRevenue + routeRevenue
    end
    
    -- Calculate expenses
    for _, vehicle in pairs(company.vehicles) do
        local maintenanceCost = vehicle.purchasePrice * 0.001 * deltaTime -- 0.1% per update
        baseExpenses = baseExpenses + maintenanceCost
    end
    
    -- Apply personality modifiers
    local personality = company.personalityData
    baseRevenue = baseRevenue * personality.investmentRate
    baseExpenses = baseExpenses * (2 - personality.investmentRate) -- Better investment = lower relative expenses
    
    -- Apply market conditions
    if gameData.marketConditions then
        baseRevenue = baseRevenue * gameData.marketConditions.demandMultiplier
        baseExpenses = baseExpenses * gameData.marketConditions.fuelPrices
    end
    
    -- Update finances
    company.revenue = company.revenue + baseRevenue
    company.expenses = company.expenses + baseExpenses
    company.profit = baseRevenue - baseExpenses
    company.cash = company.cash + company.profit
    
    -- Prevent bankruptcy (AI gets emergency funding)
    if company.cash < 0 then
        company.cash = math.random(100000, 500000)
        print("🤖", company.name, "received emergency funding")
    end
end

-- Update market position
function AICompetitorManager:UpdateMarketPosition(company, gameData)
    -- Calculate market share based on revenue and routes
    local totalMarketRevenue = company.revenue
    
    -- Add player and other AI revenues (simplified)
    if gameData.playerRevenue then
        totalMarketRevenue = totalMarketRevenue + gameData.playerRevenue
    end
    
    for otherCompanyId, otherCompany in pairs(self.aiCompanies) do
        if otherCompanyId ~= company.id then
            totalMarketRevenue = totalMarketRevenue + otherCompany.revenue
        end
    end
    
    if totalMarketRevenue > 0 then
        company.marketShare = (company.revenue / totalMarketRevenue) * 100
    else
        company.marketShare = 0
    end
    
    -- Update company value
    company.companyValue = company.cash + (company.revenue * 3) -- 3x revenue multiple
    for _, vehicle in pairs(company.vehicles) do
        company.companyValue = company.companyValue + (vehicle.purchasePrice * 0.7) -- Depreciated value
    end
end

-- Update reputation
function AICompetitorManager:UpdateReputation(company, deltaTime)
    local reputationChange = 0
    
    -- Performance-based reputation
    if company.profit > 0 then
        reputationChange = reputationChange + 0.1
    else
        reputationChange = reputationChange - 0.2
    end
    
    -- Efficiency bonus
    if company.efficiency > 90 then
        reputationChange = reputationChange + 0.05
    end
    
    -- Random events
    if math.random() < 0.01 * deltaTime then -- Rare events
        reputationChange = reputationChange + math.random(-5, 5)
    end
    
    company.reputation = math.max(0, math.min(100, company.reputation + reputationChange))
    
    -- Customer satisfaction follows reputation with some lag
    local targetSatisfaction = company.reputation * 0.9
    local satisfactionDiff = targetSatisfaction - company.customerSatisfaction
    company.customerSatisfaction = company.customerSatisfaction + (satisfactionDiff * 0.1)
end

-- Get competition level
function AICompetitorManager:GetCompetitionLevel(gameData)
    local totalCompetitors = 0
    for _ in pairs(self.aiCompanies) do
        totalCompetitors = totalCompetitors + 1
    end
    
    if gameData.playerActive then
        totalCompetitors = totalCompetitors + 1
    end
    
    return totalCompetitors
end

-- AI implement price strategy
function AICompetitorManager:AIImplementPriceStrategy(company, gameData)
    -- Price leaders try to undercut competition
    for _, route in pairs(company.routes) do
        if route.competition > 0 then
            route.profitability = route.profitability * 0.95 -- Reduce prices by 5%
        end
    end
end

-- AI implement quality strategy
function AICompetitorManager:AIImplementQualityStrategy(company, gameData)
    -- Quality leaders invest in better vehicles and service
    if company.cash > 1000000 and math.random() < 0.3 then
        company.cash = company.cash - 200000
        company.efficiency = math.min(100, company.efficiency + 2)
        company.customerSatisfaction = math.min(100, company.customerSatisfaction + 1)
    end
end

-- AI implement growth strategy
function AICompetitorManager:AIImplementGrowthStrategy(company, gameData)
    -- Growth-focused companies expand aggressively
    if company.cash > 500000 and #company.routes < 10 then
        self:AICreateRoute(company, gameData)
    end
end

-- Get AI company data for display
function AICompetitorManager:GetCompanyData(companyId)
    local company = self.aiCompanies[companyId]
    if not company then return nil end
    
    return {
        id = company.id,
        name = company.name,
        personality = company.personalityData.name,
        strategy = self.strategies[company.strategy],
        cash = company.cash,
        revenue = company.revenue,
        profit = company.profit,
        marketShare = company.marketShare,
        reputation = company.reputation,
        customerSatisfaction = company.customerSatisfaction,
        vehicleCount = #company.vehicles,
        routeCount = #company.routes,
        companyValue = company.companyValue,
        specializations = company.specializations
    }
end

-- Get all AI companies data
function AICompetitorManager:GetAllCompaniesData()
    local companiesData = {}
    
    for companyId, company in pairs(self.aiCompanies) do
        companiesData[companyId] = self:GetCompanyData(companyId)
    end
    
    return companiesData
end

-- Get market overview
function AICompetitorManager:GetMarketOverview(gameData)
    local totalRevenue = 0
    local totalCompanies = 0
    
    for _, company in pairs(self.aiCompanies) do
        totalRevenue = totalRevenue + company.revenue
        totalCompanies = totalCompanies + 1
    end
    
    if gameData.playerRevenue then
        totalRevenue = totalRevenue + gameData.playerRevenue
        totalCompanies = totalCompanies + 1
    end
    
    return {
        totalMarketRevenue = totalRevenue,
        totalCompanies = totalCompanies,
        averageRevenue = totalCompanies > 0 and totalRevenue / totalCompanies or 0,
        competitionLevel = self:GetCompetitionLevel(gameData),
        marketGrowth = math.random(0.95, 1.05) -- Simplified market growth
    }
end

return AICompetitorManager
