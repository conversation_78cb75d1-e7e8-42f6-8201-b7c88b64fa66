-- StarterPlayerScripts/GUI/ModernMainMenuGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Modernes Hauptmenü mit verbessertem Design, Animationen und Navigation

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local StartNewGameEvent = Events:WaitForChild("StartNewGameEvent")

local ModernMainMenuGUI = {}
ModernMainMenuGUI.__index = ModernMainMenuGUI

-- Konstruktor
function ModernMainMenuGUI.new()
    local self = setmetatable({}, ModernMainMenuGUI)
    
    self.isOpen = true
    self.currentPage = "MAIN"
    self.animationQueue = {}
    
    -- Spielkonfiguration
    self.gameConfig = {
        -- Karten-Einstellungen
        mapSeed = math.random(1, 999999),
        mapSize = "MEDIUM",
        startYear = 1950,
        climate = "TEMPERATE",
        difficulty = "NORMAL",
        
        -- Erweiterte Optionen
        aiCompetitors = true,
        aiCount = 2,
        economicCrises = true,
        naturalDisasters = true,
        randomEvents = true,
        
        -- Finanz-Optionen
        startingMoney = 2000000,
        loanAvailable = true,
        bankruptcy = true
    }
    
    self:CreateGUI()
    self:ShowMainPage()
    
    return self
end

-- GUI erstellen
function ModernMainMenuGUI:CreateGUI()
    -- ScreenGui
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernMainMenuGUI"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Hintergrund mit Gradient
    local background = Instance.new("Frame")
    background.Size = UDim2.new(1, 0, 1, 0)
    background.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    background.BorderSizePixel = 0
    background.Parent = self.screenGui
    
    local bgGradient = Instance.new("UIGradient")
    bgGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(15, 25, 35)),
        ColorSequenceKeypoint.new(0.5, Color3.fromRGB(10, 15, 20)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(5, 10, 15))
    }
    bgGradient.Rotation = 45
    bgGradient.Parent = background
    
    -- Animierter Hintergrund-Effekt
    self:CreateAnimatedBackground(background)
    
    -- Haupt-Container
    local mainContainer = Instance.new("Frame")
    mainContainer.Size = UDim2.new(0, 1400, 0, 900)
    mainContainer.Position = UDim2.new(0.5, -700, 0.5, -450)
    mainContainer.BackgroundTransparency = 1
    mainContainer.Parent = self.screenGui
    
    -- Logo-Bereich
    local logoContainer = Instance.new("Frame")
    logoContainer.Size = UDim2.new(1, 0, 0, 150)
    logoContainer.BackgroundTransparency = 1
    logoContainer.Parent = mainContainer
    
    local logo = Instance.new("TextLabel")
    logo.Size = UDim2.new(1, 0, 0, 80)
    logo.Position = UDim2.new(0, 0, 0, 20)
    logo.BackgroundTransparency = 1
    logo.Text = "🚂 TRANSPORT FEVER"
    logo.TextColor3 = Color3.fromRGB(100, 150, 255)
    logo.TextScaled = true
    logo.Font = Enum.Font.SourceSansBold
    logo.Parent = logoContainer
    
    local subtitle = Instance.new("TextLabel")
    subtitle.Size = UDim2.new(1, 0, 0, 30)
    subtitle.Position = UDim2.new(0, 0, 0, 100)
    subtitle.BackgroundTransparency = 1
    subtitle.Text = "Transport Empire Builder"
    subtitle.TextColor3 = Color3.fromRGB(150, 150, 150)
    subtitle.TextScaled = true
    subtitle.Font = Enum.Font.SourceSansItalic
    subtitle.Parent = logoContainer
    
    -- Content-Bereich
    self.contentFrame = Instance.new("Frame")
    self.contentFrame.Size = UDim2.new(1, 0, 1, -170)
    self.contentFrame.Position = UDim2.new(0, 0, 0, 170)
    self.contentFrame.BackgroundTransparency = 1
    self.contentFrame.Parent = mainContainer
    
    -- Navigation
    self:CreateNavigation()
    
    -- Referenzen speichern
    self.background = background
    self.mainContainer = mainContainer
    self.logoContainer = logoContainer
    
    -- Logo-Animation
    self:AnimateLogo()
end

-- Animierten Hintergrund erstellen
function ModernMainMenuGUI:CreateAnimatedBackground(parent)
    -- Bewegende Partikel-Effekte
    for i = 1, 20 do
        local particle = Instance.new("Frame")
        particle.Size = UDim2.new(0, math.random(2, 6), 0, math.random(2, 6))
        particle.Position = UDim2.new(math.random(), 0, math.random(), 0)
        particle.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        particle.BackgroundTransparency = 0.8
        particle.BorderSizePixel = 0
        particle.Parent = parent
        
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(1, 0)
        corner.Parent = particle
        
        -- Bewegungsanimation
        local function animateParticle()
            local targetX = math.random()
            local targetY = math.random()
            local duration = math.random(10, 20)
            
            TweenService:Create(particle, TweenInfo.new(duration, Enum.EasingStyle.Linear), {
                Position = UDim2.new(targetX, 0, targetY, 0)
            }):Play()
            
            wait(duration)
            animateParticle()
        end
        
        spawn(animateParticle)
    end
end

-- Logo-Animation
function ModernMainMenuGUI:AnimateLogo()
    local logo = self.logoContainer:FindFirstChild("TextLabel")
    if not logo then return end
    
    -- Pulsierender Effekt
    local function pulseLogo()
        TweenService:Create(logo, TweenInfo.new(2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
            TextColor3 = Color3.fromRGB(150, 200, 255)
        }):Play()
    end
    
    pulseLogo()
end

-- Navigation erstellen
function ModernMainMenuGUI:CreateNavigation()
    local navContainer = Instance.new("Frame")
    navContainer.Size = UDim2.new(0, 300, 1, 0)
    navContainer.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    navContainer.BorderSizePixel = 0
    navContainer.Parent = self.contentFrame
    
    local navCorner = Instance.new("UICorner")
    navCorner.CornerRadius = UDim.new(0, 15)
    navCorner.Parent = navContainer
    
    local navGradient = Instance.new("UIGradient")
    navGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 30, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 20, 25))
    }
    navGradient.Rotation = 90
    navGradient.Parent = navContainer
    
    -- Navigation-Layout
    local navLayout = Instance.new("UIListLayout")
    navLayout.SortOrder = Enum.SortOrder.LayoutOrder
    navLayout.Padding = UDim.new(0, 10)
    navLayout.Parent = navContainer
    
    local navPadding = Instance.new("UIPadding")
    navPadding.PaddingAll = UDim.new(0, 20)
    navPadding.Parent = navContainer
    
    -- Navigation-Buttons
    local navButtons = {
        {text = "🎮 NEUES SPIEL", id = "NEW_GAME", order = 1},
        {text = "📂 SPIEL LADEN", id = "LOAD_GAME", order = 2},
        {text = "🏆 KAMPAGNEN", id = "CAMPAIGNS", order = 3},
        {text = "⚙️ EINSTELLUNGEN", id = "SETTINGS", order = 4},
        {text = "❓ HILFE", id = "HELP", order = 5},
        {text = "🚪 BEENDEN", id = "EXIT", order = 6}
    }
    
    self.navButtons = {}
    
    for _, buttonData in ipairs(navButtons) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, 0, 0, 60)
        button.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
        button.Text = buttonData.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.LayoutOrder = buttonData.order
        button.Parent = navContainer
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 10)
        buttonCorner.Parent = button
        
        -- Hover-Effekt
        button.MouseEnter:Connect(function()
            TweenService:Create(button, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(60, 70, 80),
                Size = UDim2.new(1, 5, 0, 65)
            }):Play()
        end)
        
        button.MouseLeave:Connect(function()
            if self.currentPage ~= buttonData.id then
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.fromRGB(40, 50, 60),
                    Size = UDim2.new(1, 0, 0, 60)
                }):Play()
            end
        end)
        
        -- Click-Handler
        button.MouseButton1Click:Connect(function()
            self:NavigateToPage(buttonData.id)
        end)
        
        self.navButtons[buttonData.id] = button
    end
    
    -- Content-Bereich
    self.pageContent = Instance.new("Frame")
    self.pageContent.Size = UDim2.new(1, -320, 1, 0)
    self.pageContent.Position = UDim2.new(0, 320, 0, 0)
    self.pageContent.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    self.pageContent.BorderSizePixel = 0
    self.pageContent.Parent = self.contentFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 15)
    contentCorner.Parent = self.pageContent
    
    self.navContainer = navContainer
end

-- Zu Seite navigieren
function ModernMainMenuGUI:NavigateToPage(pageId)
    if self.currentPage == pageId then return end
    
    -- Aktuelle Seite ausblenden
    self:HideCurrentPage()
    
    -- Navigation-Button-Status aktualisieren
    for id, button in pairs(self.navButtons) do
        if id == pageId then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
        end
    end
    
    self.currentPage = pageId
    
    -- Neue Seite anzeigen
    if pageId == "NEW_GAME" then
        self:ShowNewGamePage()
    elseif pageId == "LOAD_GAME" then
        self:ShowLoadGamePage()
    elseif pageId == "CAMPAIGNS" then
        self:ShowCampaignsPage()
    elseif pageId == "SETTINGS" then
        self:ShowSettingsPage()
    elseif pageId == "HELP" then
        self:ShowHelpPage()
    elseif pageId == "EXIT" then
        self:ExitGame()
    end
end

-- Aktuelle Seite ausblenden
function ModernMainMenuGUI:HideCurrentPage()
    for _, child in pairs(self.pageContent:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
end

-- Hauptseite anzeigen
function ModernMainMenuGUI:ShowMainPage()
    self:NavigateToPage("NEW_GAME")
end

-- Neues Spiel Seite
function ModernMainMenuGUI:ShowNewGamePage()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 60)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🎮 NEUES SPIEL ERSTELLEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.pageContent
    
    -- Hier würde der vollständige Neues-Spiel-Inhalt kommen
    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -100)
    placeholder.Position = UDim2.new(0, 20, 0, 80)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Neues Spiel Konfiguration\n\nHier werden alle Einstellungen für\nein neues Spiel konfiguriert:\n\n• Kartengröße und Seed\n• Schwierigkeitsgrad\n• KI-Konkurrenten\n• Wirtschafts-Events\n• Naturkatastrophen"
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.pageContent
end

-- Spiel Laden Seite
function ModernMainMenuGUI:ShowLoadGamePage()
    -- SaveGameGUI öffnen
    local SaveGameGUI = require(script.Parent.SaveGameGUI)
    SaveGameGUI:OpenGUI("LOAD")
end

-- Kampagnen Seite
function ModernMainMenuGUI:ShowCampaignsPage()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 60)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "🏆 KAMPAGNEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.pageContent
    
    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -100)
    placeholder.Position = UDim2.new(0, 20, 0, 80)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Kampagnen-Modus\n\nVerfügbare Kampagnen:\n\n• Tutorial-Kampagne\n• Industrielle Revolution\n• Moderne Logistik\n• Benutzerdefinierte Szenarien"
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.pageContent
end

-- Einstellungen Seite
function ModernMainMenuGUI:ShowSettingsPage()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 60)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "⚙️ EINSTELLUNGEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.pageContent

    -- Einstellungen-Button
    local settingsButton = Instance.new("TextButton")
    settingsButton.Size = UDim2.new(0, 300, 0, 80)
    settingsButton.Position = UDim2.new(0.5, -150, 0.5, -40)
    settingsButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    settingsButton.Text = "🎛️ EINSTELLUNGEN ÖFFNEN"
    settingsButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    settingsButton.TextScaled = true
    settingsButton.Font = Enum.Font.SourceSansBold
    settingsButton.BorderSizePixel = 0
    settingsButton.Parent = self.pageContent

    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 15)
    buttonCorner.Parent = settingsButton

    -- Click-Handler
    settingsButton.MouseButton1Click:Connect(function()
        print("⚙️ Einstellungen öffnen")
        -- Modernes Einstellungen-GUI öffnen
        local ModernSettingsGUI = require(script.Parent.ModernSettingsGUI)
        ModernSettingsGUI:OpenGUI()
    end)

    -- Info-Text
    local infoText = Instance.new("TextLabel")
    infoText.Size = UDim2.new(1, -40, 0, 100)
    infoText.Position = UDim2.new(0, 20, 1, -120)
    infoText.BackgroundTransparency = 1
    infoText.Text = "Konfiguriere Grafik, Audio, Steuerung und Gameplay-Optionen\nnach deinen Wünschen."
    infoText.TextColor3 = Color3.fromRGB(180, 180, 180)
    infoText.TextScaled = true
    infoText.Font = Enum.Font.SourceSans
    infoText.Parent = self.pageContent
end

-- Hilfe Seite
function ModernMainMenuGUI:ShowHelpPage()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -40, 0, 60)
    title.Position = UDim2.new(0, 20, 0, 20)
    title.BackgroundTransparency = 1
    title.Text = "❓ HILFE & TUTORIAL"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.pageContent
    
    local placeholder = Instance.new("TextLabel")
    placeholder.Size = UDim2.new(1, -40, 1, -100)
    placeholder.Position = UDim2.new(0, 20, 0, 80)
    placeholder.BackgroundTransparency = 1
    placeholder.Text = "🚧 Hilfe & Anleitungen\n\n• Spielanleitung\n• Hotkeys\n• FAQ\n• Community\n• Support"
    placeholder.TextColor3 = Color3.fromRGB(180, 180, 180)
    placeholder.TextScaled = true
    placeholder.Font = Enum.Font.SourceSans
    placeholder.Parent = self.pageContent
end

-- Spiel beenden
function ModernMainMenuGUI:ExitGame()
    -- Bestätigungsdialog
    print("🚪 Spiel beenden")
end

-- GUI schließen
function ModernMainMenuGUI:CloseGUI()
    if self.screenGui then
        self.screenGui:Destroy()
        self.isOpen = false
    end
end

-- Singleton-Instanz
local ModernMainMenuInstance = ModernMainMenuGUI.new()

return ModernMainMenuInstance
