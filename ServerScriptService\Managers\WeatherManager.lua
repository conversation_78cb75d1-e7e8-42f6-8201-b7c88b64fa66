-- ServerScriptService/Managers/WeatherManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Umwelt & Wetter System: Wetter, Tag/Nacht, Jahreszeiten, Naturkatastrophen

local WeatherManager = {}
WeatherManager.__index = WeatherManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Lighting = game:GetService("Lighting")
local TweenService = game:GetService("TweenService")

-- Wetter-Typen
local WEATHER_TYPES = {
    SUNNY = {
        name = "Sonnig",
        icon = "☀️",
        probability = 0.4,
        effects = {
            visibility = 1.0,
            transportSpeed = 1.0,
            constructionSpeed = 1.2,
            touristBonus = 1.3,
            energyConsumption = 0.9
        },
        lighting = {
            brightness = 2,
            ambientColor = Color3.fromRGB(255, 255, 255),
            outdoorAmbient = Color3.fromRGB(128, 128, 128),
            fogEnd = 100000,
            fogStart = 0
        }
    },
    
    CLOUDY = {
        name = "Bewölkt",
        icon = "☁️",
        probability = 0.3,
        effects = {
            visibility = 0.8,
            transportSpeed = 0.95,
            constructionSpeed = 1.0,
            touristBonus = 0.9,
            energyConsumption = 1.0
        },
        lighting = {
            brightness = 1.5,
            ambientColor = Color3.fromRGB(200, 200, 200),
            outdoorAmbient = Color3.fromRGB(100, 100, 100),
            fogEnd = 50000,
            fogStart = 0
        }
    },
    
    RAINY = {
        name = "Regnerisch",
        icon = "🌧️",
        probability = 0.15,
        effects = {
            visibility = 0.6,
            transportSpeed = 0.8,
            constructionSpeed = 0.7,
            touristBonus = 0.5,
            energyConsumption = 1.1,
            accidentRisk = 1.5
        },
        lighting = {
            brightness = 1,
            ambientColor = Color3.fromRGB(150, 150, 180),
            outdoorAmbient = Color3.fromRGB(80, 80, 100),
            fogEnd = 20000,
            fogStart = 100
        }
    },
    
    STORMY = {
        name = "Stürmisch",
        icon = "⛈️",
        probability = 0.05,
        effects = {
            visibility = 0.4,
            transportSpeed = 0.6,
            constructionSpeed = 0.5,
            touristBonus = 0.2,
            energyConsumption = 1.3,
            accidentRisk = 2.0,
            airportClosure = true
        },
        lighting = {
            brightness = 0.8,
            ambientColor = Color3.fromRGB(100, 100, 120),
            outdoorAmbient = Color3.fromRGB(60, 60, 80),
            fogEnd = 10000,
            fogStart = 200
        }
    },
    
    SNOWY = {
        name = "Schnee",
        icon = "❄️",
        probability = 0.1, -- Abhängig von Jahreszeit
        effects = {
            visibility = 0.7,
            transportSpeed = 0.7,
            constructionSpeed = 0.6,
            touristBonus = 1.1, -- Winter-Tourismus
            energyConsumption = 1.4,
            accidentRisk = 1.3
        },
        lighting = {
            brightness = 1.8,
            ambientColor = Color3.fromRGB(220, 220, 255),
            outdoorAmbient = Color3.fromRGB(120, 120, 140),
            fogEnd = 30000,
            fogStart = 50
        }
    }
}

-- Jahreszeiten
local SEASONS = {
    SPRING = {
        name = "Frühling",
        icon = "🌸",
        months = {3, 4, 5},
        effects = {
            constructionBonus = 1.1,
            touristBonus = 1.2,
            energyConsumption = 0.9
        },
        weatherProbabilities = {
            SUNNY = 0.4,
            CLOUDY = 0.35,
            RAINY = 0.2,
            STORMY = 0.05,
            SNOWY = 0.0
        }
    },
    
    SUMMER = {
        name = "Sommer",
        icon = "☀️",
        months = {6, 7, 8},
        effects = {
            constructionBonus = 1.2,
            touristBonus = 1.5,
            energyConsumption = 1.2 -- Klimaanlagen
        },
        weatherProbabilities = {
            SUNNY = 0.6,
            CLOUDY = 0.25,
            RAINY = 0.1,
            STORMY = 0.05,
            SNOWY = 0.0
        }
    },
    
    AUTUMN = {
        name = "Herbst",
        icon = "🍂",
        months = {9, 10, 11},
        effects = {
            constructionBonus = 1.0,
            touristBonus = 1.1,
            energyConsumption = 1.0
        },
        weatherProbabilities = {
            SUNNY = 0.3,
            CLOUDY = 0.4,
            RAINY = 0.25,
            STORMY = 0.05,
            SNOWY = 0.0
        }
    },
    
    WINTER = {
        name = "Winter",
        icon = "❄️",
        months = {12, 1, 2},
        effects = {
            constructionBonus = 0.8,
            touristBonus = 0.9,
            energyConsumption = 1.5 -- Heizung
        },
        weatherProbabilities = {
            SUNNY = 0.2,
            CLOUDY = 0.3,
            RAINY = 0.1,
            STORMY = 0.05,
            SNOWY = 0.35
        }
    }
}

-- Naturkatastrophen
local DISASTERS = {
    EARTHQUAKE = {
        name = "Erdbeben",
        icon = "🌍",
        probability = 0.001, -- 0.1% pro Tag
        duration = 60, -- Sekunden
        effects = {
            infrastructureDamage = 0.1,
            transportDisruption = 0.5,
            constructionHalt = true,
            economicImpact = -50000
        }
    },
    
    FLOOD = {
        name = "Überschwemmung",
        icon = "🌊",
        probability = 0.002,
        duration = 300, -- 5 Minuten
        effects = {
            infrastructureDamage = 0.05,
            transportDisruption = 0.8,
            constructionHalt = true,
            economicImpact = -30000
        }
    },
    
    WILDFIRE = {
        name = "Waldbrand",
        icon = "🔥",
        probability = 0.001,
        duration = 600, -- 10 Minuten
        effects = {
            infrastructureDamage = 0.15,
            transportDisruption = 0.6,
            touristImpact = -0.5,
            economicImpact = -40000
        }
    },
    
    BLIZZARD = {
        name = "Schneesturm",
        icon = "🌨️",
        probability = 0.003, -- Nur im Winter
        duration = 180, -- 3 Minuten
        effects = {
            transportDisruption = 0.9,
            constructionHalt = true,
            energyConsumption = 2.0,
            economicImpact = -20000
        }
    }
}

-- Konstruktor
function WeatherManager.new()
    local self = setmetatable({}, WeatherManager)
    
    -- Zustand
    self.currentWeather = "SUNNY"
    self.currentSeason = "SPRING"
    self.currentDisaster = nil
    
    -- Zeit-System
    self.gameTime = {
        year = 1850,
        month = 1,
        day = 1,
        hour = 8,
        minute = 0
    }
    
    self.timeScale = 60 -- 1 Spielminute = 1 Realzeit-Sekunde
    self.lastTimeUpdate = tick()
    
    -- Wetter-System
    self.weatherDuration = 0
    self.weatherChangeInterval = 300 -- 5 Minuten Realzeit
    self.lastWeatherChange = tick()
    
    -- Katastrophen
    self.activeDisasters = {}
    self.disastersEnabled = true
    
    self:InitializeEvents()
    self:StartUpdateLoop()
    
    return self
end

-- Events initialisieren
function WeatherManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetWeatherDataFunction") then
        local getWeatherDataFunction = Instance.new("RemoteFunction")
        getWeatherDataFunction.Name = "GetWeatherDataFunction"
        getWeatherDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("WeatherChangedEvent") then
        local weatherChangedEvent = Instance.new("RemoteEvent")
        weatherChangedEvent.Name = "WeatherChangedEvent"
        weatherChangedEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("DisasterEvent") then
        local disasterEvent = Instance.new("RemoteEvent")
        disasterEvent.Name = "DisasterEvent"
        disasterEvent.Parent = Events
    end
    
    -- Event-Handler
    Events.GetWeatherDataFunction.OnServerInvoke = function(player)
        return self:GetWeatherData()
    end
end

-- Update-Loop starten
function WeatherManager:StartUpdateLoop()
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdateTime(deltaTime)
        self:UpdateWeather(deltaTime)
        self:UpdateDisasters(deltaTime)
        self:UpdateLighting()
    end)
end

-- Zeit aktualisieren
function WeatherManager:UpdateTime(deltaTime)
    local currentTime = tick()
    local realTimePassed = currentTime - self.lastTimeUpdate
    
    if realTimePassed >= 1 then -- Jede Sekunde
        local gameMinutesPassed = realTimePassed * self.timeScale / 60
        
        self.gameTime.minute = self.gameTime.minute + gameMinutesPassed
        
        -- Minuten zu Stunden
        if self.gameTime.minute >= 60 then
            self.gameTime.hour = self.gameTime.hour + math.floor(self.gameTime.minute / 60)
            self.gameTime.minute = self.gameTime.minute % 60
        end
        
        -- Stunden zu Tagen
        if self.gameTime.hour >= 24 then
            self.gameTime.day = self.gameTime.day + math.floor(self.gameTime.hour / 24)
            self.gameTime.hour = self.gameTime.hour % 24
        end
        
        -- Tage zu Monaten (vereinfacht: 30 Tage pro Monat)
        if self.gameTime.day > 30 then
            self.gameTime.month = self.gameTime.month + math.floor(self.gameTime.day / 30)
            self.gameTime.day = ((self.gameTime.day - 1) % 30) + 1
        end
        
        -- Monate zu Jahren
        if self.gameTime.month > 12 then
            self.gameTime.year = self.gameTime.year + math.floor((self.gameTime.month - 1) / 12)
            self.gameTime.month = ((self.gameTime.month - 1) % 12) + 1
        end
        
        -- Jahreszeit aktualisieren
        self:UpdateSeason()
        
        self.lastTimeUpdate = currentTime
    end
end

-- Jahreszeit aktualisieren
function WeatherManager:UpdateSeason()
    local newSeason = nil
    
    for seasonId, seasonData in pairs(SEASONS) do
        for _, month in ipairs(seasonData.months) do
            if month == self.gameTime.month then
                newSeason = seasonId
                break
            end
        end
        if newSeason then break end
    end
    
    if newSeason and newSeason ~= self.currentSeason then
        self.currentSeason = newSeason
        print("🌸 Jahreszeit geändert:", SEASONS[newSeason].name)
        
        -- Wetter-Wahrscheinlichkeiten anpassen
        self:ChangeWeatherBasedOnSeason()
    end
end

-- Wetter aktualisieren
function WeatherManager:UpdateWeather(deltaTime)
    local currentTime = tick()
    
    -- Wetter-Wechsel prüfen
    if currentTime - self.lastWeatherChange >= self.weatherChangeInterval then
        self:ChangeWeather()
        self.lastWeatherChange = currentTime
    end
end

-- Wetter wechseln
function WeatherManager:ChangeWeather()
    local seasonData = SEASONS[self.currentSeason]
    if not seasonData then return end
    
    -- Zufälliges Wetter basierend auf Jahreszeit
    local random = math.random()
    local cumulative = 0
    
    for weatherType, probability in pairs(seasonData.weatherProbabilities) do
        cumulative = cumulative + probability
        if random <= cumulative then
            if weatherType ~= self.currentWeather then
                self.currentWeather = weatherType
                print("🌤️ Wetter geändert:", WEATHER_TYPES[weatherType].name)
                
                -- Event an Clients senden
                local Events = ReplicatedStorage:WaitForChild("Events")
                Events.WeatherChangedEvent:FireAllClients(weatherType, self.currentSeason)
            end
            break
        end
    end
end

-- Wetter basierend auf Jahreszeit ändern
function WeatherManager:ChangeWeatherBasedOnSeason()
    -- Sofortiger Wetter-Wechsel bei Jahreszeit-Änderung
    self:ChangeWeather()
end

-- Naturkatastrophen aktualisieren
function WeatherManager:UpdateDisasters(deltaTime)
    if not self.disastersEnabled then return end
    
    -- Aktive Katastrophen aktualisieren
    for disasterId, disasterData in pairs(self.activeDisasters) do
        disasterData.timeRemaining = disasterData.timeRemaining - deltaTime
        
        if disasterData.timeRemaining <= 0 then
            self:EndDisaster(disasterId)
        end
    end
    
    -- Neue Katastrophen prüfen
    for disasterId, disasterData in pairs(DISASTERS) do
        if not self.activeDisasters[disasterId] then
            local probability = disasterData.probability
            
            -- Jahreszeit-Modifikationen
            if disasterId == "BLIZZARD" and self.currentSeason ~= "WINTER" then
                probability = 0 -- Schneesturm nur im Winter
            elseif disasterId == "WILDFIRE" and self.currentSeason == "SUMMER" then
                probability = probability * 2 -- Höhere Waldbrand-Gefahr im Sommer
            end
            
            if math.random() < probability then
                self:StartDisaster(disasterId)
            end
        end
    end
end

-- Katastrophe starten
function WeatherManager:StartDisaster(disasterId)
    local disasterData = DISASTERS[disasterId]
    if not disasterData then return end
    
    self.activeDisasters[disasterId] = {
        timeRemaining = disasterData.duration,
        effects = disasterData.effects
    }
    
    print("⚠️ Naturkatastrophe:", disasterData.name)
    
    -- Event an Clients senden
    local Events = ReplicatedStorage:WaitForChild("Events")
    Events.DisasterEvent:FireAllClients("START", disasterId, disasterData)
    
    -- Effekte anwenden
    self:ApplyDisasterEffects(disasterId, disasterData.effects)
end

-- Katastrophe beenden
function WeatherManager:EndDisaster(disasterId)
    local disasterData = DISASTERS[disasterId]
    if not disasterData then return end
    
    self.activeDisasters[disasterId] = nil
    
    print("✅ Naturkatastrophe beendet:", disasterData.name)
    
    -- Event an Clients senden
    local Events = ReplicatedStorage:WaitForChild("Events")
    Events.DisasterEvent:FireAllClients("END", disasterId, disasterData)
end

-- Katastrophen-Effekte anwenden
function WeatherManager:ApplyDisasterEffects(disasterId, effects)
    -- Hier würden die Effekte auf andere Manager angewendet
    -- Z.B. EconomyManager für wirtschaftliche Auswirkungen
    -- TransportManager für Transport-Störungen
    -- InfrastructureManager für Schäden
    
    if effects.economicImpact then
        -- Wirtschaftliche Auswirkungen
        print("💰 Wirtschaftlicher Schaden:", effects.economicImpact)
    end
    
    if effects.infrastructureDamage then
        -- Infrastruktur-Schäden
        print("🏗️ Infrastruktur-Schäden:", effects.infrastructureDamage * 100 .. "%")
    end
end

-- Beleuchtung aktualisieren
function WeatherManager:UpdateLighting()
    local weatherData = WEATHER_TYPES[self.currentWeather]
    if not weatherData then return end
    
    -- Tag/Nacht-Zyklus
    local timeOfDay = self.gameTime.hour + (self.gameTime.minute / 60)
    local dayProgress = timeOfDay / 24
    
    -- Sonnenstand berechnen
    local sunAngle = (dayProgress - 0.25) * 360 -- 6 Uhr = Sonnenaufgang
    
    -- Helligkeit basierend auf Tageszeit
    local baseBrightness = 0.5
    if timeOfDay >= 6 and timeOfDay <= 18 then
        -- Tag (6-18 Uhr)
        local dayTime = (timeOfDay - 6) / 12
        baseBrightness = 0.5 + (math.sin(dayTime * math.pi) * 1.5)
    else
        -- Nacht
        baseBrightness = 0.1
    end
    
    -- Wetter-Modifikation
    local finalBrightness = baseBrightness * (weatherData.lighting.brightness / 2)
    
    -- Lighting-Eigenschaften setzen
    Lighting.Brightness = finalBrightness
    Lighting.Ambient = weatherData.lighting.ambientColor
    Lighting.OutdoorAmbient = weatherData.lighting.outdoorAmbient
    Lighting.FogEnd = weatherData.lighting.fogEnd
    Lighting.FogStart = weatherData.lighting.fogStart
    
    -- Sonnenposition (vereinfacht)
    if Lighting:FindFirstChild("Sun") then
        Lighting.Sun.CFrame = CFrame.Angles(math.rad(sunAngle), 0, 0)
    end
end

-- Wetter-Daten abrufen
function WeatherManager:GetWeatherData()
    return {
        currentWeather = self.currentWeather,
        weatherData = WEATHER_TYPES[self.currentWeather],
        currentSeason = self.currentSeason,
        seasonData = SEASONS[self.currentSeason],
        gameTime = self.gameTime,
        activeDisasters = self.activeDisasters,
        timeScale = self.timeScale
    }
end

-- Katastrophen aktivieren/deaktivieren
function WeatherManager:SetDisastersEnabled(enabled)
    self.disastersEnabled = enabled
    
    if not enabled then
        -- Alle aktiven Katastrophen beenden
        for disasterId in pairs(self.activeDisasters) do
            self:EndDisaster(disasterId)
        end
    end
    
    print("⚠️ Naturkatastrophen:", enabled and "aktiviert" or "deaktiviert")
end

-- Zeit-Skala ändern
function WeatherManager:SetTimeScale(scale)
    self.timeScale = math.max(1, math.min(3600, scale)) -- 1x bis 3600x
    print("⏰ Zeit-Skala geändert:", self.timeScale .. "x")
end

-- Cleanup
function WeatherManager:Destroy()
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end
end

return WeatherManager
