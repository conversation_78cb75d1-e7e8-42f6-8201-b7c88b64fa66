-- ServerScriptService/Managers/MultiplayerManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vollständiges Multiplayer-System mit Chat, Allianzen und Kooperation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local HttpService = game:GetService("HttpService")

local MultiplayerManager = {}
MultiplayerManager.__index = MultiplayerManager

function MultiplayerManager.new()
    local self = setmetatable({}, MultiplayerManager)
    
    -- Chat-System
    self.chatSystem = {
        globalChat = {},
        privateChats = {},
        allianceChats = {},
        maxMessageLength = 200,
        messageHistory = 100
    }
    
    -- Allianz-System
    self.alliances = {}
    self.playerAlliances = {}
    
    -- Kooperations-System
    self.cooperationProjects = {}
    self.tradeOffers = {}
    
    -- Synchronisations-System
    self.syncSystem = {
        playerStates = {},
        lastSync = os.time()
    }
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function MultiplayerManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Chat-Events
    if not Events:FindFirstChild("SendChatMessageEvent") then
        local sendChatEvent = Instance.new("RemoteEvent")
        sendChatEvent.Name = "SendChatMessageEvent"
        sendChatEvent.Parent = Events
        
        sendChatEvent.OnServerEvent:Connect(function(player, message, chatType, targetId)
            self:ProcessChatMessage(player, message, chatType, targetId)
        end)
    end
    
    -- Allianz-Events
    if not Events:FindFirstChild("CreateAllianceEvent") then
        local createAllianceEvent = Instance.new("RemoteEvent")
        createAllianceEvent.Name = "CreateAllianceEvent"
        createAllianceEvent.Parent = Events
        
        createAllianceEvent.OnServerEvent:Connect(function(player, allianceName)
            self:CreateAlliance(player.UserId, allianceName)
        end)
    end
    
    if not Events:FindFirstChild("JoinAllianceEvent") then
        local joinAllianceEvent = Instance.new("RemoteEvent")
        joinAllianceEvent.Name = "JoinAllianceEvent"
        joinAllianceEvent.Parent = Events
        
        joinAllianceEvent.OnServerEvent:Connect(function(player, allianceId)
            self:JoinAlliance(player.UserId, allianceId)
        end)
    end
    
    -- Kooperations-Events
    if not Events:FindFirstChild("CreateCooperationProjectEvent") then
        local createCoopEvent = Instance.new("RemoteEvent")
        createCoopEvent.Name = "CreateCooperationProjectEvent"
        createCoopEvent.Parent = Events
        
        createCoopEvent.OnServerEvent:Connect(function(player, projectData)
            self:CreateCooperationProject(player.UserId, projectData)
        end)
    end
end

-- Chat-Nachricht verarbeiten
function MultiplayerManager:ProcessChatMessage(player, message, chatType, targetId)
    if #message > self.chatSystem.maxMessageLength then
        return false, "Nachricht zu lang"
    end
    
    -- Nachricht erstellen
    local chatMessage = {
        id = HttpService:GenerateGUID(false),
        playerId = player.UserId,
        playerName = player.Name,
        message = message,
        timestamp = os.time(),
        chatType = chatType or "global"
    }
    
    -- Je nach Chat-Typ verarbeiten
    if chatType == "global" then
        table.insert(self.chatSystem.globalChat, chatMessage)
        
        -- An alle Spieler senden
        local Events = ReplicatedStorage.Events
        if Events:FindFirstChild("ChatMessageReceivedEvent") then
            Events.ChatMessageReceivedEvent:FireAllClients(chatMessage)
        end
        
    elseif chatType == "alliance" then
        local allianceId = self.playerAlliances[player.UserId]
        if allianceId then
            if not self.chatSystem.allianceChats[allianceId] then
                self.chatSystem.allianceChats[allianceId] = {}
            end
            
            table.insert(self.chatSystem.allianceChats[allianceId], chatMessage)
            
            -- An Allianz-Mitglieder senden
            local alliance = self.alliances[allianceId]
            if alliance then
                for _, memberId in pairs(alliance.members) do
                    local memberPlayer = Players:GetPlayerByUserId(memberId)
                    if memberPlayer then
                        local Events = ReplicatedStorage.Events
                        if Events:FindFirstChild("ChatMessageReceivedEvent") then
                            Events.ChatMessageReceivedEvent:FireClient(memberPlayer, chatMessage)
                        end
                    end
                end
            end
        end
        
    elseif chatType == "private" and targetId then
        local chatKey = player.UserId < targetId and (player.UserId .. "_" .. targetId) or (targetId .. "_" .. player.UserId)
        
        if not self.chatSystem.privateChats[chatKey] then
            self.chatSystem.privateChats[chatKey] = {}
        end
        
        table.insert(self.chatSystem.privateChats[chatKey], chatMessage)
        
        -- An Ziel-Spieler senden
        local targetPlayer = Players:GetPlayerByUserId(targetId)
        if targetPlayer then
            local Events = ReplicatedStorage.Events
            if Events:FindFirstChild("ChatMessageReceivedEvent") then
                Events.ChatMessageReceivedEvent:FireClient(targetPlayer, chatMessage)
            end
        end
    end
    
    -- Chat-Historie begrenzen
    self:LimitChatHistory()
    
    return true
end

-- Chat-Historie begrenzen
function MultiplayerManager:LimitChatHistory()
    -- Global Chat
    while #self.chatSystem.globalChat > self.chatSystem.messageHistory do
        table.remove(self.chatSystem.globalChat, 1)
    end
    
    -- Allianz Chats
    for allianceId, chat in pairs(self.chatSystem.allianceChats) do
        while #chat > self.chatSystem.messageHistory do
            table.remove(chat, 1)
        end
    end
    
    -- Private Chats
    for chatKey, chat in pairs(self.chatSystem.privateChats) do
        while #chat > self.chatSystem.messageHistory do
            table.remove(chat, 1)
        end
    end
end

-- Allianz erstellen
function MultiplayerManager:CreateAlliance(playerId, allianceName)
    -- Prüfen ob Spieler bereits in Allianz ist
    if self.playerAlliances[playerId] then
        return false, "Bereits in einer Allianz"
    end
    
    local allianceId = HttpService:GenerateGUID(false)
    local alliance = {
        id = allianceId,
        name = allianceName,
        leaderId = playerId,
        members = {playerId},
        created = os.time(),
        description = "",
        treasury = 0,
        projects = {}
    }
    
    self.alliances[allianceId] = alliance
    self.playerAlliances[playerId] = allianceId
    
    print("🤝 Allianz erstellt:", allianceName, "von Spieler:", playerId)
    return true, allianceId
end

-- Allianz beitreten
function MultiplayerManager:JoinAlliance(playerId, allianceId)
    -- Prüfen ob Spieler bereits in Allianz ist
    if self.playerAlliances[playerId] then
        return false, "Bereits in einer Allianz"
    end
    
    local alliance = self.alliances[allianceId]
    if not alliance then
        return false, "Allianz nicht gefunden"
    end
    
    -- Zur Allianz hinzufügen
    table.insert(alliance.members, playerId)
    self.playerAlliances[playerId] = allianceId
    
    print("🤝 Spieler", playerId, "ist Allianz beigetreten:", alliance.name)
    return true
end

-- Kooperations-Projekt erstellen
function MultiplayerManager:CreateCooperationProject(playerId, projectData)
    local projectId = HttpService:GenerateGUID(false)
    local project = {
        id = projectId,
        name = projectData.name,
        description = projectData.description,
        creatorId = playerId,
        participants = {playerId},
        requiredInvestment = projectData.requiredInvestment or 0,
        currentInvestment = 0,
        status = "planning", -- planning, active, completed, cancelled
        created = os.time(),
        rewards = projectData.rewards or {}
    }
    
    self.cooperationProjects[projectId] = project
    
    print("🏗️ Kooperations-Projekt erstellt:", project.name, "von Spieler:", playerId)
    return true, projectId
end

-- Spieler-Daten synchronisieren
function MultiplayerManager:SyncPlayerData(playerId, playerData)
    self.syncSystem.playerStates[playerId] = {
        data = playerData,
        lastUpdate = os.time()
    }
end

-- Alle Spieler-Daten abrufen
function MultiplayerManager:GetAllPlayerStates()
    return self.syncSystem.playerStates
end

-- Chat-Daten für Spieler abrufen
function MultiplayerManager:GetChatData(playerId)
    local chatData = {
        globalChat = self.chatSystem.globalChat,
        privateChats = {},
        allianceChat = {}
    }
    
    -- Private Chats für diesen Spieler
    for chatKey, chat in pairs(self.chatSystem.privateChats) do
        local participants = string.split(chatKey, "_")
        if tonumber(participants[1]) == playerId or tonumber(participants[2]) == playerId then
            chatData.privateChats[chatKey] = chat
        end
    end
    
    -- Allianz Chat
    local allianceId = self.playerAlliances[playerId]
    if allianceId and self.chatSystem.allianceChats[allianceId] then
        chatData.allianceChat = self.chatSystem.allianceChats[allianceId]
    end
    
    return chatData
end

-- Allianz-Daten für Spieler abrufen
function MultiplayerManager:GetAllianceData(playerId)
    local allianceId = self.playerAlliances[playerId]
    if allianceId then
        return self.alliances[allianceId]
    end
    return nil
end

-- Alle verfügbaren Allianzen abrufen
function MultiplayerManager:GetAvailableAlliances()
    local availableAlliances = {}
    for allianceId, alliance in pairs(self.alliances) do
        table.insert(availableAlliances, {
            id = allianceId,
            name = alliance.name,
            memberCount = #alliance.members,
            description = alliance.description
        })
    end
    return availableAlliances
end

-- Update-Funktion
function MultiplayerManager:Update(deltaTime)
    -- Regelmäßige Synchronisation
    if os.time() - self.syncSystem.lastSync > 30 then -- Alle 30 Sekunden
        self:BroadcastPlayerStates()
        self.syncSystem.lastSync = os.time()
    end
end

-- Spieler-Zustände an alle senden
function MultiplayerManager:BroadcastPlayerStates()
    local Events = ReplicatedStorage.Events
    if Events:FindFirstChild("PlayerStatesUpdateEvent") then
        Events.PlayerStatesUpdateEvent:FireAllClients(self.syncSystem.playerStates)
    end
end

return MultiplayerManager
