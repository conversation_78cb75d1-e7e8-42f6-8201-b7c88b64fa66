-- StarterPlayerScripts/GUI/AdvancedInfrastructureGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- <PERSON><PERSON>weiterte Infrastruktur-GUI mit Kategorien und modernem Design

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local BuildInfrastructureEvent = Events:WaitForChild("BuildInfrastructureEvent")

local AdvancedInfrastructureGUI = {}
AdvancedInfrastructureGUI.IsOpen = false
AdvancedInfrastructureGUI.CurrentCategory = "BASIC"
AdvancedInfrastructureGUI.SelectedType = nil

-- Infrastruktur-Kategorien
local CATEGORIES = {
    BASIC = {
        name = "🛣️ Grundlagen",
        types = {"ROAD", "RAILWAY", "WATERWAY", "BRIDGE", "TUNNEL"},
        color = Color3.fromRGB(100, 150, 255)
    },
    AVIATION = {
        name = "✈️ Luftfahrt",
        types = {"AIRPORT"},
        color = Color3.fromRGB(255, 200, 100)
    },
    URBAN_RAIL = {
        name = "🚇 Nahverkehr",
        types = {"SUBWAY_STATION", "TRAM_STOP", "MONORAIL_STATION"},
        color = Color3.fromRGB(150, 255, 150)
    },
    RAIL_UPGRADE = {
        name = "⚡ Bahn-Upgrades",
        types = {"ELECTRIFICATION", "DOUBLE_TRACK"},
        color = Color3.fromRGB(255, 255, 100)
    },
    HIGH_SPEED = {
        name = "🚄 Hochgeschwindigkeit",
        types = {"HIGH_SPEED_RAIL"},
        color = Color3.fromRGB(255, 150, 255)
    },
    LOGISTICS = {
        name = "📦 Logistik",
        types = {"CARGO_HUB"},
        color = Color3.fromRGB(255, 150, 100)
    }
}

-- Infrastruktur-Daten
local INFRASTRUCTURE_DATA = {
    ROAD = {name = "Straße", cost = 100, icon = "🛣️", description = "Grundlegende Straße für Fahrzeuge"},
    RAILWAY = {name = "Schiene", cost = 500, icon = "🛤️", description = "Eisenbahnstrecke für Züge"},
    WATERWAY = {name = "Wasserweg", cost = 300, icon = "🌊", description = "Schifffahrtsroute für Schiffe"},
    BRIDGE = {name = "Brücke", cost = 2000, icon = "🌉", description = "Überquerung von Gewässern und Tälern"},
    TUNNEL = {name = "Tunnel", cost = 5000, icon = "🚇", description = "Unterirdische Passage durch Berge"},
    AIRPORT = {name = "Flughafen", cost = 5000000, icon = "✈️", description = "Großer Flughafen für Passagier- und Frachtflüge"},
    SUBWAY_STATION = {name = "U-Bahn-Station", cost = 2000000, icon = "🚇", description = "Unterirdische Bahnstation für Stadtverkehr"},
    TRAM_STOP = {name = "Straßenbahn-Haltestelle", cost = 100000, icon = "🚋", description = "Oberirdische Haltestelle für Straßenbahnen"},
    ELECTRIFICATION = {name = "Elektrifizierung", cost = 500000, icon = "⚡", description = "Elektrifizierung bestehender Bahnstrecken"},
    DOUBLE_TRACK = {name = "Zweigleisiger Ausbau", cost = 300000, icon = "🛤️", description = "Ausbau auf zwei Gleise für höhere Kapazität"},
    HIGH_SPEED_RAIL = {name = "Hochgeschwindigkeitsstrecke", cost = 15000000, icon = "🚄", description = "Spezielle Strecke für Hochgeschwindigkeitszüge"},
    CARGO_HUB = {name = "Fracht-Drehkreuz", cost = 3000000, icon = "📦", description = "Großes Logistikzentrum für Güterumschlag"},
    MONORAIL_STATION = {name = "Einschienenbahn-Station", cost = 1500000, icon = "🚝", description = "Futuristische Einschienenbahn-Station"}
}

-- GUI erstellen
function AdvancedInfrastructureGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "AdvancedInfrastructureGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe mit modernem Design
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1000, 0, 700)
    mainFrame.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Moderne Ecken
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 20)
    corner.Parent = mainFrame
    
    -- Schatten-Effekt
    local shadow = Instance.new("ImageLabel")
    shadow.Size = UDim2.new(1, 20, 1, 20)
    shadow.Position = UDim2.new(0, -10, 0, -10)
    shadow.BackgroundTransparency = 1
    shadow.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
    shadow.ImageColor3 = Color3.fromRGB(0, 0, 0)
    shadow.ImageTransparency = 0.8
    shadow.ZIndex = -1
    shadow.Parent = mainFrame
    
    -- Header
    local header = Instance.new("Frame")
    header.Size = UDim2.new(1, 0, 0, 80)
    header.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    header.BorderSizePixel = 0
    header.Parent = mainFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 20)
    headerCorner.Parent = header
    
    -- Titel mit Gradient
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -100, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "🏗️ ERWEITERTE INFRASTRUKTUR"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = header
    
    -- Schließen-Button mit Hover-Effekt
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -70, 0, 15)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = header
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 25)
    closeCorner.Parent = closeButton
    
    -- Hover-Effekt für Close-Button
    closeButton.MouseEnter:Connect(function()
        TweenService:Create(closeButton, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(255, 80, 80)
        }):Play()
    end)
    
    closeButton.MouseLeave:Connect(function()
        TweenService:Create(closeButton, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(220, 60, 60)
        }):Play()
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Content-Bereich
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -40, 1, -120)
    contentFrame.Position = UDim2.new(0, 20, 0, 100)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = mainFrame
    
    -- Kategorie-Sidebar
    local sidebarFrame = Instance.new("Frame")
    sidebarFrame.Size = UDim2.new(0, 250, 1, 0)
    sidebarFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    sidebarFrame.BorderSizePixel = 0
    sidebarFrame.Parent = contentFrame
    
    local sidebarCorner = Instance.new("UICorner")
    sidebarCorner.CornerRadius = UDim.new(0, 15)
    sidebarCorner.Parent = sidebarFrame
    
    -- Kategorie-Titel
    local categoryTitle = Instance.new("TextLabel")
    categoryTitle.Size = UDim2.new(1, 0, 0, 50)
    categoryTitle.BackgroundTransparency = 1
    categoryTitle.Text = "KATEGORIEN"
    categoryTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    categoryTitle.TextScaled = true
    categoryTitle.Font = Enum.Font.SourceSansBold
    categoryTitle.Parent = sidebarFrame
    
    -- Kategorie-Buttons Container
    local categoryContainer = Instance.new("ScrollingFrame")
    categoryContainer.Size = UDim2.new(1, -20, 1, -70)
    categoryContainer.Position = UDim2.new(0, 10, 0, 60)
    categoryContainer.BackgroundTransparency = 1
    categoryContainer.ScrollBarThickness = 6
    categoryContainer.Parent = sidebarFrame
    
    -- Hauptinhalt-Bereich
    local mainContentFrame = Instance.new("Frame")
    mainContentFrame.Size = UDim2.new(1, -270, 1, 0)
    mainContentFrame.Position = UDim2.new(0, 270, 0, 0)
    mainContentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    mainContentFrame.BorderSizePixel = 0
    mainContentFrame.Parent = contentFrame
    
    local mainContentCorner = Instance.new("UICorner")
    mainContentCorner.CornerRadius = UDim.new(0, 15)
    mainContentCorner.Parent = mainContentFrame
    
    -- Referenzen speichern
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.SidebarFrame = sidebarFrame
    self.CategoryContainer = categoryContainer
    self.MainContentFrame = mainContentFrame
    
    -- Kategorie-Buttons erstellen
    self:CreateCategoryButtons()
    
    return screenGui
end

-- Kategorie-Buttons erstellen
function AdvancedInfrastructureGUI:CreateCategoryButtons()
    local yPos = 0
    
    for categoryId, categoryData in pairs(CATEGORIES) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, -10, 0, 70)
        button.Position = UDim2.new(0, 5, 0, yPos)
        button.BackgroundColor3 = categoryId == self.CurrentCategory and categoryData.color or Color3.fromRGB(40, 45, 50)
        button.Text = categoryData.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = self.CategoryContainer
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 10)
        buttonCorner.Parent = button
        
        -- Hover-Effekt
        button.MouseEnter:Connect(function()
            if categoryId ~= self.CurrentCategory then
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.fromRGB(60, 65, 70)
                }):Play()
            end
        end)
        
        button.MouseLeave:Connect(function()
            if categoryId ~= self.CurrentCategory then
                TweenService:Create(button, TweenInfo.new(0.2), {
                    BackgroundColor3 = Color3.fromRGB(40, 45, 50)
                }):Play()
            end
        end)
        
        button.MouseButton1Click:Connect(function()
            self:SelectCategory(categoryId)
        end)
        
        yPos = yPos + 80
    end
    
    -- Canvas-Größe setzen
    self.CategoryContainer.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Kategorie auswählen
function AdvancedInfrastructureGUI:SelectCategory(categoryId)
    self.CurrentCategory = categoryId
    self:UpdateCategoryButtons()
    self:ShowInfrastructureTypes()
end

-- Kategorie-Buttons aktualisieren
function AdvancedInfrastructureGUI:UpdateCategoryButtons()
    for _, button in pairs(self.CategoryContainer:GetChildren()) do
        if button:IsA("TextButton") then
            local categoryId = nil
            for id, data in pairs(CATEGORIES) do
                if button.Text == data.name then
                    categoryId = id
                    break
                end
            end
            
            if categoryId then
                local categoryData = CATEGORIES[categoryId]
                button.BackgroundColor3 = categoryId == self.CurrentCategory and categoryData.color or Color3.fromRGB(40, 45, 50)
            end
        end
    end
end

-- Infrastruktur-Typen anzeigen
function AdvancedInfrastructureGUI:ShowInfrastructureTypes()
    -- Content löschen
    for _, child in pairs(self.MainContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    local categoryData = CATEGORIES[self.CurrentCategory]
    if not categoryData then return end
    
    -- Kategorie-Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 60)
    title.BackgroundTransparency = 1
    title.Text = categoryData.name
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.MainContentFrame
    
    -- Scroll-Container für Infrastruktur-Karten
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -20, 1, -80)
    scrollFrame.Position = UDim2.new(0, 10, 0, 70)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.MainContentFrame
    
    -- Infrastruktur-Karten erstellen
    local xPos = 10
    local yPos = 10
    local cardWidth = 280
    local cardHeight = 180
    local spacing = 20
    local cardsPerRow = 2
    
    for i, infrastructureType in ipairs(categoryData.types) do
        local infraData = INFRASTRUCTURE_DATA[infrastructureType]
        if infraData then
            self:CreateInfrastructureCard(scrollFrame, infrastructureType, infraData, xPos, yPos, cardWidth, cardHeight)
            
            -- Position für nächste Karte
            xPos = xPos + cardWidth + spacing
            if (i % cardsPerRow) == 0 then
                xPos = 10
                yPos = yPos + cardHeight + spacing
            end
        end
    end
    
    -- Canvas-Größe setzen
    local totalRows = math.ceil(#categoryData.types / cardsPerRow)
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + (totalRows > 0 and cardHeight or 0) + 20)
end

-- Infrastruktur-Karte erstellen
function AdvancedInfrastructureGUI:CreateInfrastructureCard(parent, infrastructureType, infraData, x, y, width, height)
    local card = Instance.new("Frame")
    card.Size = UDim2.new(0, width, 0, height)
    card.Position = UDim2.new(0, x, 0, y)
    card.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    card.BorderSizePixel = 0
    card.Parent = parent
    
    local cardCorner = Instance.new("UICorner")
    cardCorner.CornerRadius = UDim.new(0, 12)
    cardCorner.Parent = card
    
    -- Icon
    local icon = Instance.new("TextLabel")
    icon.Size = UDim2.new(0, 60, 0, 60)
    icon.Position = UDim2.new(0, 15, 0, 15)
    icon.BackgroundTransparency = 1
    icon.Text = infraData.icon
    icon.TextColor3 = Color3.fromRGB(255, 255, 255)
    icon.TextScaled = true
    icon.Font = Enum.Font.SourceSans
    icon.Parent = card
    
    -- Name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, -90, 0, 30)
    nameLabel.Position = UDim2.new(0, 85, 0, 15)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = infraData.name
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = card
    
    -- Kosten
    local costLabel = Instance.new("TextLabel")
    costLabel.Size = UDim2.new(1, -90, 0, 25)
    costLabel.Position = UDim2.new(0, 85, 0, 45)
    costLabel.BackgroundTransparency = 1
    costLabel.Text = "💰 $" .. string.format("%,d", infraData.cost):gsub(",", ".")
    costLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
    costLabel.TextScaled = true
    costLabel.Font = Enum.Font.SourceSans
    costLabel.TextXAlignment = Enum.TextXAlignment.Left
    costLabel.Parent = card
    
    -- Beschreibung
    local descLabel = Instance.new("TextLabel")
    descLabel.Size = UDim2.new(1, -20, 0, 60)
    descLabel.Position = UDim2.new(0, 10, 0, 85)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = infraData.description
    descLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.SourceSans
    descLabel.TextWrapped = true
    descLabel.TextXAlignment = Enum.TextXAlignment.Left
    descLabel.TextYAlignment = Enum.TextYAlignment.Top
    descLabel.Parent = card
    
    -- Bauen-Button
    local buildButton = Instance.new("TextButton")
    buildButton.Size = UDim2.new(1, -20, 0, 35)
    buildButton.Position = UDim2.new(0, 10, 1, -45)
    buildButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    buildButton.Text = "🏗️ BAUEN"
    buildButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    buildButton.TextScaled = true
    buildButton.Font = Enum.Font.SourceSansBold
    buildButton.BorderSizePixel = 0
    buildButton.Parent = card
    
    local buildCorner = Instance.new("UICorner")
    buildCorner.CornerRadius = UDim.new(0, 8)
    buildCorner.Parent = buildButton
    
    -- Hover-Effekt für Karte
    card.MouseEnter:Connect(function()
        TweenService:Create(card, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(45, 50, 55)
        }):Play()
    end)
    
    card.MouseLeave:Connect(function()
        TweenService:Create(card, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(35, 40, 45)
        }):Play()
    end)
    
    -- Hover-Effekt für Button
    buildButton.MouseEnter:Connect(function()
        TweenService:Create(buildButton, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(120, 170, 255)
        }):Play()
    end)
    
    buildButton.MouseLeave:Connect(function()
        TweenService:Create(buildButton, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        }):Play()
    end)
    
    -- Click-Event
    buildButton.MouseButton1Click:Connect(function()
        self:SelectInfrastructureType(infrastructureType)
    end)
end

-- Infrastruktur-Typ auswählen
function AdvancedInfrastructureGUI:SelectInfrastructureType(infrastructureType)
    self.SelectedType = infrastructureType
    local infraData = INFRASTRUCTURE_DATA[infrastructureType]
    
    if infraData then
        print("🏗️ Infrastruktur ausgewählt:", infraData.name, "Kosten:", infraData.cost)
        
        -- Event an Server senden
        BuildInfrastructureEvent:FireServer(infrastructureType, Vector3.new(0, 0, 0), {})
        
        self:CloseGUI()
    end
end

-- GUI öffnen
function AdvancedInfrastructureGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in mit Scale-Effekt
    self.MainFrame.Size = UDim2.new(0, 800, 0, 560)
    self.MainFrame.BackgroundTransparency = 1
    
    local sizeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.4, Enum.EasingStyle.Back), {
        Size = UDim2.new(0, 1000, 0, 700)
    })
    
    local fadeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    
    sizeTween:Play()
    fadeTween:Play()
    
    self:ShowInfrastructureTypes()
end

-- GUI schließen
function AdvancedInfrastructureGUI:CloseGUI()
    if self.MainFrame then
        local sizeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Size = UDim2.new(0, 800, 0, 560)
        })
        
        local fadeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        
        sizeTween:Play()
        fadeTween:Play()
        
        fadeTween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.I then -- 'I' für Infrastructure
        if AdvancedInfrastructureGUI.IsOpen then
            AdvancedInfrastructureGUI:CloseGUI()
        else
            AdvancedInfrastructureGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function AdvancedInfrastructureGUI:Initialize()
    print("🏗️ AdvancedInfrastructureGUI initialisiert - Drücke 'I' zum Öffnen")
end

-- Auto-Start
AdvancedInfrastructureGUI:Initialize()

return AdvancedInfrastructureGUI
