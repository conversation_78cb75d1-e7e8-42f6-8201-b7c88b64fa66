# Mittleres Viktorianisches Haus
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "MEDIUM_HOUSE_VICTORIAN"
Name = "Mittleres Viktorianisches Haus"
Category = "Residential"
Size = "Medium"
Era = "Victorian"
Era_Years = {1860, 1900}

[MODEL_DATA]
ModelId = "rbxassetid://MEDIUM_HOUSE_VICTORIAN"
Scale = Vector3(12, 8, 14)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.7, 0.3, 0.2)      # Roter Backstein
Secondary = Color3(0.3, 0.3, 0.3)    # Schieferdach
Accent = Color3(0.9, 0.9, 0.9)       # Weiße Verzierungen
Trim = Color3(0.2, 0.2, 0.2)         # Schwarze Eisenverzierungen

[GAMEPLAY_STATS]
Population = 8
BuildCost = 4500
MaintenanceCost = 45
BuildTime = 45
PowerConsumption = 0
WaterConsumption = 4
LandSize = Vector2(3, 3)  # 3x3 Felder

[REQUIREMENTS]
MinPopulation = 200
MinYear = 1860
RequiredTech = {"Brick_Construction"}
RequiredResources = {"Brick", "Stone", "Iron"}
UnlockCost = 500

[FEATURES]
ArchitecturalStyle = "Victorian"
HasGarden = true
HasChimney = true
HasBasement = true
Floors = 2
WindowStyle = "Sash_Windows"
RoofStyle = "Complex_Gabled"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "LARGE_HOUSE_VICTORIAN"
UpgradeCost = 3000
UpgradeTime = 40
UpgradeRequirements = {"Wealthy_Population"}

[ECONOMIC_DATA]
TaxRevenue = 35
PropertyValue = 4500
MaintenanceJobs = 1
ConstructionJobs = 8

[DESCRIPTION]
ShortDesc = "Elegantes zweistöckiges Backsteinhaus"
LongDesc = "Ein solides Backsteinhaus mit zwei Stockwerken und einem Keller. Zeigt den wachsenden Wohlstand der viktorianischen Mittelschicht mit aufwendigen Verzierungen und modernen Annehmlichkeiten."
