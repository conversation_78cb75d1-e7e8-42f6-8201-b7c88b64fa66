-- StarterPlayerScripts/GUI/CompetitorGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- KI-Konkurrenten Übersicht und Marktanalyse

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetCompetitorDataFunction = Events:WaitForChild("GetCompetitorDataFunction")

local CompetitorGUI = {}
CompetitorGUI.IsOpen = false
CompetitorGUI.CurrentTab = "OVERVIEW" -- OVERVIEW, MARKET, ANALYSIS
CompetitorGUI.CompetitorData = {}

-- Persönlichkeits-Farben
local PERSONALITY_COLORS = {
    AGGRESSIVE = Color3.fromRGB(255, 100, 100),
    CONSERVATIVE = Color3.fromRGB(100, 150, 255),
    INNOVATIVE = Color3.fromRGB(150, 255, 100),
    OPPORTUNISTIC = Color3.fromRGB(255, 200, 100)
}

-- GUI erstellen
function CompetitorGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CompetitorGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1200, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 60)
    title.BackgroundTransparency = 1
    title.Text = "🤖 KONKURRENZ-ANALYSE"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 70)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "OVERVIEW", text = "📊 Übersicht", icon = "📊"},
        {name = "MARKET", text = "📈 Marktanteile", icon = "📈"},
        {name = "ANALYSIS", text = "🔍 Detailanalyse", icon = "🔍"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 660)
    contentFrame.Position = UDim2.new(0, 10, 0, 130)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function CompetitorGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function CompetitorGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        end
    end
end

-- Content aktualisieren
function CompetitorGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "OVERVIEW" then
        self:CreateOverviewContent()
    elseif self.CurrentTab == "MARKET" then
        self:CreateMarketContent()
    elseif self.CurrentTab == "ANALYSIS" then
        self:CreateAnalysisContent()
    end
end

-- Übersicht-Content erstellen
function CompetitorGUI:CreateOverviewContent()
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    local yPos = 10
    
    -- Markt-Übersicht
    local marketFrame = Instance.new("Frame")
    marketFrame.Size = UDim2.new(1, -20, 0, 100)
    marketFrame.Position = UDim2.new(0, 10, 0, yPos)
    marketFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    marketFrame.BorderSizePixel = 0
    marketFrame.Parent = scrollFrame
    
    local marketCorner = Instance.new("UICorner")
    marketCorner.CornerRadius = UDim.new(0, 10)
    marketCorner.Parent = marketFrame
    
    local marketTitle = Instance.new("TextLabel")
    marketTitle.Size = UDim2.new(1, 0, 0, 30)
    marketTitle.Position = UDim2.new(0, 15, 0, 10)
    marketTitle.BackgroundTransparency = 1
    marketTitle.Text = "📊 MARKT-ÜBERSICHT"
    marketTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    marketTitle.TextScaled = true
    marketTitle.Font = Enum.Font.SourceSansBold
    marketTitle.TextXAlignment = Enum.TextXAlignment.Left
    marketTitle.Parent = marketFrame
    
    -- Markt-Statistiken
    local marketStats = {
        {label = "Gesamtumsatz:", value = "$" .. string.format("%,d", self.CompetitorData.marketData and self.CompetitorData.marketData.totalRevenue or 0)},
        {label = "Konkurrenten:", value = tostring(self:GetCompetitorCount())},
        {label = "Wettbewerbsintensität:", value = string.format("%.0f%%", (self.CompetitorData.marketData and self.CompetitorData.marketData.competitionLevel or 0) * 100)}
    }
    
    for i, stat in ipairs(marketStats) do
        local statLabel = Instance.new("TextLabel")
        statLabel.Size = UDim2.new(0.3, 0, 0, 25)
        statLabel.Position = UDim2.new((i-1) * 0.33, 15, 0, 45)
        statLabel.BackgroundTransparency = 1
        statLabel.Text = stat.label .. " " .. stat.value
        statLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        statLabel.TextScaled = true
        statLabel.Font = Enum.Font.SourceSans
        statLabel.TextXAlignment = Enum.TextXAlignment.Left
        statLabel.Parent = marketFrame
    end
    
    yPos = yPos + 120
    
    -- Konkurrenten-Liste
    if self.CompetitorData.competitors then
        for competitorId, competitor in pairs(self.CompetitorData.competitors) do
            local competitorFrame = Instance.new("Frame")
            competitorFrame.Size = UDim2.new(1, -20, 0, 120)
            competitorFrame.Position = UDim2.new(0, 10, 0, yPos)
            competitorFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
            competitorFrame.BorderSizePixel = 0
            competitorFrame.Parent = scrollFrame
            
            local competitorCorner = Instance.new("UICorner")
            competitorCorner.CornerRadius = UDim.new(0, 10)
            competitorCorner.Parent = competitorFrame
            
            -- Persönlichkeits-Indikator
            local personalityBar = Instance.new("Frame")
            personalityBar.Size = UDim2.new(0, 5, 1, -20)
            personalityBar.Position = UDim2.new(0, 10, 0, 10)
            personalityBar.BackgroundColor3 = PERSONALITY_COLORS[competitor.personality] or Color3.fromRGB(100, 100, 100)
            personalityBar.BorderSizePixel = 0
            personalityBar.Parent = competitorFrame
            
            local personalityCorner = Instance.new("UICorner")
            personalityCorner.CornerRadius = UDim.new(0, 2)
            personalityCorner.Parent = personalityBar
            
            -- Firmenname
            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(0.4, 0, 0, 30)
            nameLabel.Position = UDim2.new(0, 25, 0, 10)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = competitor.name
            nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.SourceSansBold
            nameLabel.TextXAlignment = Enum.TextXAlignment.Left
            nameLabel.Parent = competitorFrame
            
            -- Persönlichkeit
            local personalityLabel = Instance.new("TextLabel")
            personalityLabel.Size = UDim2.new(0.3, 0, 0, 20)
            personalityLabel.Position = UDim2.new(0, 25, 0, 40)
            personalityLabel.BackgroundTransparency = 1
            personalityLabel.Text = "Typ: " .. competitor.personality
            personalityLabel.TextColor3 = PERSONALITY_COLORS[competitor.personality] or Color3.fromRGB(200, 200, 200)
            personalityLabel.TextScaled = true
            personalityLabel.Font = Enum.Font.SourceSans
            personalityLabel.TextXAlignment = Enum.TextXAlignment.Left
            personalityLabel.Parent = competitorFrame
            
            -- Statistiken
            local stats = {
                {label = "💰", value = "$" .. string.format("%,d", competitor.money)},
                {label = "📊", value = string.format("%.1f%%", competitor.marketShare * 100)},
                {label = "🚗", value = tostring(competitor.vehicleCount)},
                {label = "⭐", value = string.format("%.0f%%", competitor.reputation)}
            }
            
            for i, stat in ipairs(stats) do
                local statFrame = Instance.new("Frame")
                statFrame.Size = UDim2.new(0.15, 0, 0, 40)
                statFrame.Position = UDim2.new(0.5 + (i-1) * 0.12, 0, 0, 65)
                statFrame.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
                statFrame.BorderSizePixel = 0
                statFrame.Parent = competitorFrame
                
                local statCorner = Instance.new("UICorner")
                statCorner.CornerRadius = UDim.new(0, 5)
                statCorner.Parent = statFrame
                
                local statIcon = Instance.new("TextLabel")
                statIcon.Size = UDim2.new(1, 0, 0.5, 0)
                statIcon.BackgroundTransparency = 1
                statIcon.Text = stat.label
                statIcon.TextColor3 = Color3.fromRGB(255, 255, 255)
                statIcon.TextScaled = true
                statIcon.Font = Enum.Font.SourceSans
                statIcon.Parent = statFrame
                
                local statValue = Instance.new("TextLabel")
                statValue.Size = UDim2.new(1, 0, 0.5, 0)
                statValue.Position = UDim2.new(0, 0, 0.5, 0)
                statValue.BackgroundTransparency = 1
                statValue.Text = stat.value
                statValue.TextColor3 = Color3.fromRGB(200, 200, 200)
                statValue.TextScaled = true
                statValue.Font = Enum.Font.SourceSans
                statValue.Parent = statFrame
            end
            
            yPos = yPos + 130
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Markt-Content erstellen
function CompetitorGUI:CreateMarketContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "📈 MARKTANTEILE\n\nDetaillierte Marktanalyse\nund Wettbewerbsposition."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Analyse-Content erstellen
function CompetitorGUI:CreateAnalysisContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🔍 DETAILANALYSE\n\nTiefgehende Konkurrenz-\nanalyse und Strategien."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Konkurrenten-Anzahl abrufen
function CompetitorGUI:GetCompetitorCount()
    if not self.CompetitorData.competitors then
        return 0
    end
    
    local count = 0
    for _ in pairs(self.CompetitorData.competitors) do
        count = count + 1
    end
    return count
end

-- GUI öffnen
function CompetitorGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadCompetitorData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function CompetitorGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Konkurrenten-Daten laden
function CompetitorGUI:LoadCompetitorData()
    local success, data = pcall(function()
        return GetCompetitorDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.CompetitorData = data
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Konkurrenten-Daten")
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.C then
        if CompetitorGUI.IsOpen then
            CompetitorGUI:CloseGUI()
        else
            CompetitorGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function CompetitorGUI:Initialize()
    print("🤖 CompetitorGUI initialisiert - Drücke 'C' zum Öffnen")
end

-- Auto-Start
CompetitorGUI:Initialize()

return CompetitorGUI
