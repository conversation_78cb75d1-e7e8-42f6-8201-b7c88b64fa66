# Stadt-Geb<PERSON>ude nach Epochen strukturiert
# Historisch korrekte Stadtentwicklung für Transport Empire

## ===== VIKTORIANISCHE ÄRA (1850-1900) =====

### WOHNGEBÄUDE - Viktorianisch
[HOUSE_VICTORIAN_COTTAGE]
Name = "Viktorianisches Cottage"
Era = 1850-1900
ModelId = "rbxassetid://HOUSE_VICTORIAN_COTTAGE"
Scale = Vector3(6, 5, 8)
Population = 4
BuildingType = "Residential"
ArchitecturalStyle = "Victorian"
Features = {"Bay_Windows", "Ornate_Trim", "Steep_Roof", "Front_Garden"}

[HOUSE_VICTORIAN_TOWNHOUSE]
Name = "Viktorianisches Reihenhaus"
Era = 1860-1900
ModelId = "rbxassetid://HOUSE_VICTORIAN_TOWNHOUSE"
Scale = Vector3(7, 8, 12)
Population = 8
BuildingType = "Residential"
ArchitecturalStyle = "Victorian"
Features = {"Multi_Story", "Shared_Walls", "Front_Steps", "Basement"}

### GESCHÄFTE - Viktorianisch
[SHOP_VICTORIAN_GENERAL]
Name = "Viktorianischer Gemischtwarenladen"
Era = 1850-1900
ModelId = "rbxassetid://SHOP_VICTORIAN_GENERAL"
Scale = Vector3(8, 6, 12)
BuildingType = "Commercial"
ArchitecturalStyle = "Victorian"
Services = {"General_Goods"}
Workers = 3
Features = {"Large_Windows", "Wooden_Signage", "Living_Quarters_Above"}

[BANK_VICTORIAN]
Name = "Viktorianische Bank"
Era = 1860-1900
ModelId = "rbxassetid://BANK_VICTORIAN"
Scale = Vector3(15, 10, 20)
BuildingType = "Financial"
ArchitecturalStyle = "Victorian"
Services = {"Banking", "Loans"}
Workers = 8
Features = {"Stone_Facade", "Columns", "Vault", "Clock"}

### ÖFFENTLICHE GEBÄUDE - Viktorianisch
[CITY_HALL_VICTORIAN]
Name = "Viktorianisches Rathaus"
Era = 1850-1900
ModelId = "rbxassetid://CITY_HALL_VICTORIAN"
Scale = Vector3(25, 12, 30)
BuildingType = "Government"
ArchitecturalStyle = "Victorian"
Workers = 15
Features = {"Clock_Tower", "Gothic_Revival", "Grand_Entrance", "Council_Chamber"}

## ===== JAHRHUNDERTWENDE (1900-1920) =====

### WOHNGEBÄUDE - Edwardianisch
[HOUSE_EDWARDIAN_VILLA]
Name = "Edwardianische Villa"
Era = 1900-1920
ModelId = "rbxassetid://HOUSE_EDWARDIAN_VILLA"
Scale = Vector3(12, 8, 15)
Population = 12
BuildingType = "Residential"
ArchitecturalStyle = "Edwardian"
Features = {"Symmetrical_Design", "Large_Gardens", "Servants_Quarters", "Electric_Lighting"}

[APARTMENT_EDWARDIAN]
Name = "Edwardianisches Mehrfamilienhaus"
Era = 1900-1920
ModelId = "rbxassetid://APARTMENT_EDWARDIAN"
Scale = Vector3(15, 12, 20)
Population = 24
BuildingType = "Residential"
ArchitecturalStyle = "Edwardian"
Floors = 3
Features = {"Brick_Construction", "Sash_Windows", "Shared_Courtyard", "Central_Heating"}

### GESCHÄFTE - Jahrhundertwende
[DEPARTMENT_STORE_EARLY]
Name = "Frühes Kaufhaus"
Era = 1900-1920
ModelId = "rbxassetid://DEPARTMENT_STORE_EARLY"
Scale = Vector3(25, 12, 35)
BuildingType = "Commercial"
ArchitecturalStyle = "Edwardian"
Services = {"Department_Store", "Fashion", "Home_Goods"}
Workers = 30
Features = {"Large_Display_Windows", "Multiple_Floors", "Electric_Lighting", "Elevator"}

## ===== ZWISCHENKRIEGSZEIT (1920-1940) =====

### WOHNGEBÄUDE - Art Deco
[APARTMENT_ART_DECO]
Name = "Art Deco Apartment"
Era = 1920-1940
ModelId = "rbxassetid://APARTMENT_ART_DECO"
Scale = Vector3(18, 15, 25)
Population = 40
BuildingType = "Residential"
ArchitecturalStyle = "Art_Deco"
Floors = 5
Features = {"Geometric_Patterns", "Stepped_Facade", "Modern_Amenities", "Radio_Antennas"}

[HOUSE_SUBURBAN]
Name = "Vorstadthaus"
Era = 1920-1940
ModelId = "rbxassetid://HOUSE_SUBURBAN"
Scale = Vector3(10, 6, 12)
Population = 6
BuildingType = "Residential"
ArchitecturalStyle = "Colonial_Revival"
Features = {"Front_Porch", "Garage", "Lawn", "Telephone_Lines"}

### GESCHÄFTE - Art Deco
[CINEMA_ART_DECO]
Name = "Art Deco Kino"
Era = 1920-1940
ModelId = "rbxassetid://CINEMA_ART_DECO"
Scale = Vector3(20, 12, 30)
BuildingType = "Entertainment"
ArchitecturalStyle = "Art_Deco"
Capacity = 300
Workers = 15
Features = {"Neon_Signs", "Streamlined_Design", "Marquee", "Sound_System"}

## ===== NACHKRIEGSZEIT (1945-1960) =====

### WOHNGEBÄUDE - Modernistisch
[HOUSE_RANCH_STYLE]
Name = "Ranch-Style Haus"
Era = 1945-1960
ModelId = "rbxassetid://HOUSE_RANCH_STYLE"
Scale = Vector3(12, 4, 16)
Population = 6
BuildingType = "Residential"
ArchitecturalStyle = "Mid_Century_Modern"
Features = {"Single_Story", "Large_Windows", "Open_Floor_Plan", "Carport", "TV_Antenna"}

[APARTMENT_MODERNIST]
Name = "Modernistischer Wohnblock"
Era = 1945-1960
ModelId = "rbxassetid://APARTMENT_MODERNIST"
Scale = Vector3(25, 18, 30)
Population = 60
BuildingType = "Residential"
ArchitecturalStyle = "Modernist"
Floors = 6
Features = {"Clean_Lines", "Flat_Roof", "Ribbon_Windows", "Concrete_Construction"}

### GESCHÄFTE - Modernistisch
[SHOPPING_CENTER_EARLY]
Name = "Frühes Einkaufszentrum"
Era = 1950-1960
ModelId = "rbxassetid://SHOPPING_CENTER_EARLY"
Scale = Vector3(40, 6, 60)
BuildingType = "Commercial"
ArchitecturalStyle = "Mid_Century_Modern"
Services = {"Multiple_Stores", "Parking", "Food_Court"}
Workers = 80
Features = {"Strip_Mall_Design", "Large_Parking_Lot", "Neon_Signs", "Air_Conditioning"}

## ===== MODERNE ÄRA (1960-1980) =====

### WOHNGEBÄUDE - Spätmoderne
[APARTMENT_HIGH_RISE]
Name = "Hochhaus-Wohnanlage"
Era = 1960-1980
ModelId = "rbxassetid://APARTMENT_HIGH_RISE"
Scale = Vector3(20, 30, 20)
Population = 120
BuildingType = "Residential"
ArchitecturalStyle = "Brutalist"
Floors = 15
Features = {"Concrete_Construction", "Repetitive_Windows", "Minimal_Decoration", "Elevators"}

### GESCHÄFTE - Spätmoderne
[MALL_ENCLOSED]
Name = "Überdachtes Einkaufszentrum"
Era = 1960-1980
ModelId = "rbxassetid://MALL_ENCLOSED"
Scale = Vector3(80, 8, 120)
BuildingType = "Commercial"
ArchitecturalStyle = "Late_Modern"
Services = {"Department_Stores", "Specialty_Shops", "Food_Court", "Entertainment"}
Workers = 200
Features = {"Climate_Control", "Skylights", "Central_Court", "Anchor_Stores"}

## ===== POSTMODERNE (1980-2000) =====

### WOHNGEBÄUDE - Postmodern
[APARTMENT_LUXURY]
Name = "Luxus-Apartmenthaus"
Era = 1980-2000
ModelId = "rbxassetid://APARTMENT_LUXURY"
Scale = Vector3(25, 20, 30)
Population = 80
BuildingType = "Residential"
ArchitecturalStyle = "Postmodern"
Floors = 8
Features = {"Mixed_Materials", "Decorative_Elements", "Balconies", "Amenities", "Cable_TV"}

### GESCHÄFTE - Postmodern
[MEGAMALL]
Name = "Mega-Einkaufszentrum"
Era = 1980-2000
ModelId = "rbxassetid://MEGAMALL"
Scale = Vector3(120, 10, 150)
BuildingType = "Commercial"
ArchitecturalStyle = "Postmodern"
Services = {"Multiple_Levels", "Entertainment_Complex", "Restaurants", "Services"}
Workers = 500
Features = {"Themed_Areas", "Atriums", "Escalators", "Parking_Structures"}

## ===== EPOCHEN-KATEGORIEN =====

[ERA_CATEGORIES]
Victorian_1850_1900 = {
    Style = "Ornate, Gothic Revival, Bay Windows",
    Materials = {"Wood", "Brick", "Stone"},
    Colors = {"Dark_Wood", "Red_Brick", "White_Trim"},
    Buildings = {
        "HOUSE_VICTORIAN_COTTAGE",
        "HOUSE_VICTORIAN_TOWNHOUSE", 
        "SHOP_VICTORIAN_GENERAL",
        "BANK_VICTORIAN",
        "CITY_HALL_VICTORIAN"
    }
}

Edwardian_1900_1920 = {
    Style = "Symmetrical, Classical Elements, Electric Lighting",
    Materials = {"Brick", "Stone", "Steel"},
    Colors = {"Red_Brick", "White_Stone", "Green_Copper"},
    Buildings = {
        "HOUSE_EDWARDIAN_VILLA",
        "APARTMENT_EDWARDIAN",
        "DEPARTMENT_STORE_EARLY"
    }
}

Art_Deco_1920_1940 = {
    Style = "Geometric, Streamlined, Vertical Emphasis",
    Materials = {"Concrete", "Steel", "Glass"},
    Colors = {"Cream", "Gold", "Black"},
    Buildings = {
        "APARTMENT_ART_DECO",
        "HOUSE_SUBURBAN",
        "CINEMA_ART_DECO"
    }
}

Mid_Century_1945_1960 = {
    Style = "Clean Lines, Glass, Horizontal Emphasis",
    Materials = {"Steel", "Glass", "Concrete"},
    Colors = {"White", "Beige", "Pastels"},
    Buildings = {
        "HOUSE_RANCH_STYLE",
        "APARTMENT_MODERNIST",
        "SHOPPING_CENTER_EARLY"
    }
}

Late_Modern_1960_1980 = {
    Style = "Brutalist, Concrete, Minimal Decoration",
    Materials = {"Concrete", "Steel", "Glass"},
    Colors = {"Gray", "Brown", "Orange"},
    Buildings = {
        "APARTMENT_HIGH_RISE",
        "MALL_ENCLOSED"
    }
}

Postmodern_1980_2000 = {
    Style = "Eclectic, Colorful, Historical References",
    Materials = {"Mixed", "Synthetic", "Glass"},
    Colors = {"Varied", "Bright", "Metallic"},
    Buildings = {
        "APARTMENT_LUXURY",
        "MEGAMALL"
    }
}
