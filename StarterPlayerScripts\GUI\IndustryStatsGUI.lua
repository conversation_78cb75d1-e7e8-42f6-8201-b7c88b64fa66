-- StarterPlayerScripts/GUI/IndustryStatsGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Detaillierte Industrie-Statistiken GUI

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetIndustryDataFunction = Events:WaitForChild("GetIndustryDataFunction")
local RenameIndustryEvent = Events:WaitForChild("RenameIndustryEvent")

local IndustryStatsGUI = {}
IndustryStatsGUI.IsOpen = false
IndustryStatsGUI.SelectedIndustry = nil
IndustryStatsGUI.IndustryData = {}

-- Industrie-Icons
local IndustryIcons = {
    CoalMine = "⛏️",
    IronMine = "🔨",
    SteelMill = "🏭",
    Farm = "🚜",
    Sawmill = "🪵",
    Factory = "🏗️",
    PowerPlant = "⚡",
    OilRig = "🛢️",
    Refinery = "⚗️",
    Default = "🏭"
}

-- GUI erstellen
function IndustryStatsGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "IndustryStatsGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 900, 0, 650)
    mainFrame.Position = UDim2.new(0.5, -450, 0.5, -325)
    mainFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🏭 INDUSTRIE-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Industrie-Liste (links)
    local industryListFrame = Instance.new("ScrollingFrame")
    industryListFrame.Size = UDim2.new(0.35, 0, 0.85, 0)
    industryListFrame.Position = UDim2.new(0.02, 0, 0.1, 0)
    industryListFrame.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    industryListFrame.BorderSizePixel = 0
    industryListFrame.ScrollBarThickness = 8
    industryListFrame.Parent = mainFrame
    
    local listCorner = Instance.new("UICorner")
    listCorner.CornerRadius = UDim.new(0, 8)
    listCorner.Parent = industryListFrame
    
    -- Details-Panel (rechts)
    local detailsFrame = Instance.new("Frame")
    detailsFrame.Size = UDim2.new(0.6, 0, 0.85, 0)
    detailsFrame.Position = UDim2.new(0.38, 0, 0.1, 0)
    detailsFrame.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    detailsFrame.BorderSizePixel = 0
    detailsFrame.Parent = mainFrame
    
    local detailsCorner = Instance.new("UICorner")
    detailsCorner.CornerRadius = UDim.new(0, 8)
    detailsCorner.Parent = detailsFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.IndustryListFrame = industryListFrame
    self.DetailsFrame = detailsFrame
    
    return screenGui
end

-- Industrie-Liste erstellen
function IndustryStatsGUI:UpdateIndustryList()
    -- Alte Einträge löschen
    for _, child in pairs(self.IndustryListFrame:GetChildren()) do
        if child:IsA("Frame") and child.Name == "IndustryEntry" then
            child:Destroy()
        end
    end
    
    local yPosition = 5
    local entryHeight = 90
    
    for industryId, industryData in pairs(self.IndustryData) do
        local entry = Instance.new("Frame")
        entry.Name = "IndustryEntry"
        entry.Size = UDim2.new(1, -10, 0, entryHeight)
        entry.Position = UDim2.new(0, 5, 0, yPosition)
        entry.BackgroundColor3 = Color3.fromRGB(45, 50, 55)
        entry.BorderSizePixel = 0
        entry.Parent = self.IndustryListFrame
        
        local entryCorner = Instance.new("UICorner")
        entryCorner.CornerRadius = UDim.new(0, 5)
        entryCorner.Parent = entry
        
        -- Industrie-Icon
        local icon = Instance.new("TextLabel")
        icon.Size = UDim2.new(0, 50, 0, 50)
        icon.Position = UDim2.new(0, 10, 0, 10)
        icon.BackgroundTransparency = 1
        icon.Text = IndustryIcons[industryData.type] or IndustryIcons.Default
        icon.TextScaled = true
        icon.Font = Enum.Font.SourceSans
        icon.Parent = entry
        
        -- Industrie-Name
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(0.65, 0, 0, 25)
        nameLabel.Position = UDim2.new(0, 70, 0, 5)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = industryData.name or "Unbekannte Industrie"
        nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        nameLabel.TextScaled = true
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Parent = entry
        
        -- Industrie-Typ
        local typeLabel = Instance.new("TextLabel")
        typeLabel.Size = UDim2.new(0.65, 0, 0, 20)
        typeLabel.Position = UDim2.new(0, 70, 0, 25)
        typeLabel.BackgroundTransparency = 1
        typeLabel.Text = "🏷️ " .. (industryData.type or "Unbekannt")
        typeLabel.TextColor3 = Color3.fromRGB(180, 180, 180)
        typeLabel.TextScaled = true
        typeLabel.Font = Enum.Font.SourceSans
        typeLabel.TextXAlignment = Enum.TextXAlignment.Left
        typeLabel.Parent = entry
        
        -- Produktion
        local productionLabel = Instance.new("TextLabel")
        productionLabel.Size = UDim2.new(0.65, 0, 0, 20)
        productionLabel.Position = UDim2.new(0, 70, 0, 45)
        productionLabel.BackgroundTransparency = 1
        local productionRate = industryData.productionRate or 0
        productionLabel.Text = "📦 " .. string.format("%.1f/Monat", productionRate)
        productionLabel.TextColor3 = productionRate > 0 and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(255, 100, 100)
        productionLabel.TextScaled = true
        productionLabel.Font = Enum.Font.SourceSans
        productionLabel.TextXAlignment = Enum.TextXAlignment.Left
        productionLabel.Parent = entry
        
        -- Status-Indikator
        local statusIndicator = Instance.new("Frame")
        statusIndicator.Size = UDim2.new(0, 12, 0, 12)
        statusIndicator.Position = UDim2.new(1, -20, 0, 10)
        statusIndicator.BorderSizePixel = 0
        statusIndicator.BackgroundColor3 = industryData.isActive and Color3.fromRGB(0, 255, 0) or Color3.fromRGB(255, 0, 0)
        statusIndicator.Parent = entry
        
        local statusCorner = Instance.new("UICorner")
        statusCorner.CornerRadius = UDim.new(0, 6)
        statusCorner.Parent = statusIndicator
        
        -- Klick-Handler
        local clickButton = Instance.new("TextButton")
        clickButton.Size = UDim2.new(1, 0, 1, 0)
        clickButton.BackgroundTransparency = 1
        clickButton.Text = ""
        clickButton.Parent = entry
        
        clickButton.MouseButton1Click:Connect(function()
            self:SelectIndustry(industryId, industryData)
        end)
        
        -- Hover-Effekt
        entry.MouseEnter:Connect(function()
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(65, 70, 75)
            })
            tween:Play()
        end)
        
        entry.MouseLeave:Connect(function()
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(45, 50, 55)
            })
            tween:Play()
        end)
        
        yPosition = yPosition + entryHeight + 5
    end
    
    self.IndustryListFrame.CanvasSize = UDim2.new(0, 0, 0, yPosition)
end

-- Industrie auswählen
function IndustryStatsGUI:SelectIndustry(industryId, industryData)
    self.SelectedIndustry = industryId
    self:UpdateDetailsPanel(industryData)
end

-- Details-Panel aktualisieren
function IndustryStatsGUI:UpdateDetailsPanel(industryData)
    -- Alte Details löschen
    for _, child in pairs(self.DetailsFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if not industryData then
        local noSelection = Instance.new("TextLabel")
        noSelection.Size = UDim2.new(1, 0, 1, 0)
        noSelection.BackgroundTransparency = 1
        noSelection.Text = "Wähle eine Industrie aus der Liste"
        noSelection.TextColor3 = Color3.fromRGB(150, 150, 150)
        noSelection.TextScaled = true
        noSelection.Font = Enum.Font.SourceSans
        noSelection.Parent = self.DetailsFrame
        return
    end
    
    -- Scroll-Container für Details
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 6
    scrollFrame.Parent = self.DetailsFrame
    
    local yPos = 10
    
    -- Industrie-Name (editierbar)
    self:CreateEditableField(scrollFrame, "🏭 Industrie-Name:", yPos, industryData.name or "Unbekannte Industrie", function(newName)
        RenameIndustryEvent:FireServer(self.SelectedIndustry, newName)
        industryData.name = newName
    end)
    yPos = yPos + 60
    
    -- Grundlegende Informationen
    self:CreateStatSection(scrollFrame, "ℹ️ GRUNDLEGENDE INFORMATIONEN", yPos)
    yPos = yPos + 40
    
    self:CreateStatField(scrollFrame, "🏷️ Typ:", yPos, industryData.type or "Unbekannt")
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "📍 Position:", yPos, string.format("X: %.0f, Z: %.0f", industryData.position.X or 0, industryData.position.Z or 0))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "🔄 Status:", yPos, industryData.isActive and "🟢 Aktiv" or "🔴 Inaktiv")
    yPos = yPos + 30
    
    -- Produktions-Statistiken
    self:CreateStatSection(scrollFrame, "📦 PRODUKTION", yPos)
    yPos = yPos + 40
    
    self:CreateStatField(scrollFrame, "⚡ Produktionsrate:", yPos, string.format("%.1f pro Monat", industryData.productionRate or 0))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "📦 Aktueller Lagerbestand:", yPos, string.format("%.0f Einheiten", industryData.currentStock or 0))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "🏪 Maximales Lager:", yPos, string.format("%.0f Einheiten", industryData.maxStock or 0))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "📤 Produziert:", yPos, industryData.outputResource or "Unbekannt")
    yPos = yPos + 30
    
    -- Ressourcen-Anforderungen
    if industryData.inputResources and next(industryData.inputResources) then
        self:CreateStatSection(scrollFrame, "📥 RESSOURCEN-ANFORDERUNGEN", yPos)
        yPos = yPos + 40
        
        for resource, amount in pairs(industryData.inputResources) do
            self:CreateStatField(scrollFrame, resource .. ":", yPos, string.format("%.1f pro Monat", amount))
            yPos = yPos + 30
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
end

-- Editierbares Feld erstellen (gleiche Funktion wie in CityStatsGUI)
function IndustryStatsGUI:CreateEditableField(parent, label, yPos, currentValue, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.3, 0, 0, 30)
    labelText.Position = UDim2.new(0, 10, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSansBold
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    local textBox = Instance.new("TextBox")
    textBox.Size = UDim2.new(0.6, 0, 0, 30)
    textBox.Position = UDim2.new(0.35, 0, 0, yPos)
    textBox.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    textBox.Text = currentValue
    textBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    textBox.TextScaled = true
    textBox.Font = Enum.Font.SourceSans
    textBox.BorderSizePixel = 0
    textBox.Parent = parent

    local boxCorner = Instance.new("UICorner")
    boxCorner.CornerRadius = UDim.new(0, 5)
    boxCorner.Parent = textBox

    textBox.FocusLost:Connect(function()
        if callback then callback(textBox.Text) end
    end)
end

-- Statistik-Sektion erstellen
function IndustryStatsGUI:CreateStatSection(parent, title, yPos)
    local section = Instance.new("TextLabel")
    section.Size = UDim2.new(1, -20, 0, 30)
    section.Position = UDim2.new(0, 10, 0, yPos)
    section.BackgroundColor3 = Color3.fromRGB(180, 120, 60)
    section.Text = title
    section.TextColor3 = Color3.fromRGB(255, 255, 255)
    section.TextScaled = true
    section.Font = Enum.Font.SourceSansBold
    section.BorderSizePixel = 0
    section.Parent = parent

    local sectionCorner = Instance.new("UICorner")
    sectionCorner.CornerRadius = UDim.new(0, 5)
    sectionCorner.Parent = section
end

-- Statistik-Feld erstellen
function IndustryStatsGUI:CreateStatField(parent, label, yPos, value)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.5, 0, 0, 25)
    labelText.Position = UDim2.new(0, 20, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(200, 200, 200)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    local valueText = Instance.new("TextLabel")
    valueText.Size = UDim2.new(0.45, 0, 0, 25)
    valueText.Position = UDim2.new(0.5, 0, 0, yPos)
    valueText.BackgroundTransparency = 1
    valueText.Text = value
    valueText.TextColor3 = Color3.fromRGB(255, 255, 255)
    valueText.TextScaled = true
    valueText.Font = Enum.Font.SourceSans
    valueText.TextXAlignment = Enum.TextXAlignment.Right
    valueText.Parent = parent
end

-- Daten laden
function IndustryStatsGUI:LoadIndustryData()
    local success, data = pcall(function()
        return GetIndustryDataFunction:InvokeServer()
    end)

    if success and data then
        self.IndustryData = data
        self:UpdateIndustryList()
    else
        warn("Fehler beim Laden der Industrie-Daten")
    end
end

-- GUI öffnen
function IndustryStatsGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end

    self:LoadIndustryData()
    self.MainFrame.Visible = true
    self.IsOpen = true

    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function IndustryStatsGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()

        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.I then
        if IndustryStatsGUI.IsOpen then
            IndustryStatsGUI:CloseGUI()
        else
            IndustryStatsGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function IndustryStatsGUI:Initialize()
    print("🏭 IndustryStatsGUI initialisiert - Drücke 'I' zum Öffnen")
end

-- Auto-Start
IndustryStatsGUI:Initialize()

return IndustryStatsGUI
