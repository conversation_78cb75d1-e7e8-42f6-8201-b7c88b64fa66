-- StarterPlayerScripts/GUI/CityStatsGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Detaillierte Stadt-Statistiken GUI

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetCityDataFunction = Events:WaitForChild("GetCityDataFunction")
local RenameCityEvent = Events:WaitForChild("RenameCityEvent")

local CityStatsGUI = {}
CityStatsGUI.IsOpen = false
CityStatsGUI.SelectedCity = nil
CityStatsGUI.CityData = {}

-- GUI erstellen
function CityStatsGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CityStatsGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 800, 0, 600)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🏙️ STADT-STATISTIKEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Stadt-Liste (links)
    local cityListFrame = Instance.new("ScrollingFrame")
    cityListFrame.Size = UDim2.new(0.3, 0, 0.85, 0)
    cityListFrame.Position = UDim2.new(0.02, 0, 0.1, 0)
    cityListFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    cityListFrame.BorderSizePixel = 0
    cityListFrame.ScrollBarThickness = 8
    cityListFrame.Parent = mainFrame
    
    local listCorner = Instance.new("UICorner")
    listCorner.CornerRadius = UDim.new(0, 8)
    listCorner.Parent = cityListFrame
    
    -- Details-Panel (rechts)
    local detailsFrame = Instance.new("Frame")
    detailsFrame.Size = UDim2.new(0.65, 0, 0.85, 0)
    detailsFrame.Position = UDim2.new(0.33, 0, 0.1, 0)
    detailsFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    detailsFrame.BorderSizePixel = 0
    detailsFrame.Parent = mainFrame
    
    local detailsCorner = Instance.new("UICorner")
    detailsCorner.CornerRadius = UDim.new(0, 8)
    detailsCorner.Parent = detailsFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.CityListFrame = cityListFrame
    self.DetailsFrame = detailsFrame
    
    return screenGui
end

-- Stadt-Liste erstellen
function CityStatsGUI:UpdateCityList()
    -- Alte Einträge löschen
    for _, child in pairs(self.CityListFrame:GetChildren()) do
        if child:IsA("Frame") and child.Name == "CityEntry" then
            child:Destroy()
        end
    end
    
    local yPosition = 5
    local entryHeight = 80
    
    for cityId, cityData in pairs(self.CityData) do
        local entry = Instance.new("Frame")
        entry.Name = "CityEntry"
        entry.Size = UDim2.new(1, -10, 0, entryHeight)
        entry.Position = UDim2.new(0, 5, 0, yPosition)
        entry.BackgroundColor3 = Color3.fromRGB(50, 55, 60)
        entry.BorderSizePixel = 0
        entry.Parent = self.CityListFrame
        
        local entryCorner = Instance.new("UICorner")
        entryCorner.CornerRadius = UDim.new(0, 5)
        entryCorner.Parent = entry
        
        -- Stadt-Icon
        local icon = Instance.new("TextLabel")
        icon.Size = UDim2.new(0, 50, 0, 50)
        icon.Position = UDim2.new(0, 10, 0, 15)
        icon.BackgroundTransparency = 1
        icon.Text = "🏙️"
        icon.TextScaled = true
        icon.Font = Enum.Font.SourceSans
        icon.Parent = entry
        
        -- Stadt-Name
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(0.6, 0, 0, 25)
        nameLabel.Position = UDim2.new(0, 70, 0, 10)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = cityData.name or "Unbekannte Stadt"
        nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        nameLabel.TextScaled = true
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Parent = entry
        
        -- Bevölkerung
        local popLabel = Instance.new("TextLabel")
        popLabel.Size = UDim2.new(0.6, 0, 0, 20)
        popLabel.Position = UDim2.new(0, 70, 0, 35)
        popLabel.BackgroundTransparency = 1
        popLabel.Text = "👥 " .. (cityData.population or 0) .. " Einwohner"
        popLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        popLabel.TextScaled = true
        popLabel.Font = Enum.Font.SourceSans
        popLabel.TextXAlignment = Enum.TextXAlignment.Left
        popLabel.Parent = entry
        
        -- Wachstum
        local growthLabel = Instance.new("TextLabel")
        growthLabel.Size = UDim2.new(0.6, 0, 0, 20)
        growthLabel.Position = UDim2.new(0, 70, 0, 55)
        growthLabel.BackgroundTransparency = 1
        local growthRate = (cityData.growthRate or 0) * 100
        growthLabel.Text = "📈 " .. string.format("%.1f%%", growthRate) .. " Wachstum"
        growthLabel.TextColor3 = growthRate > 0 and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(255, 100, 100)
        growthLabel.TextScaled = true
        growthLabel.Font = Enum.Font.SourceSans
        growthLabel.TextXAlignment = Enum.TextXAlignment.Left
        growthLabel.Parent = entry
        
        -- Klick-Handler
        local clickButton = Instance.new("TextButton")
        clickButton.Size = UDim2.new(1, 0, 1, 0)
        clickButton.BackgroundTransparency = 1
        clickButton.Text = ""
        clickButton.Parent = entry
        
        clickButton.MouseButton1Click:Connect(function()
            self:SelectCity(cityId, cityData)
        end)
        
        -- Hover-Effekt
        entry.MouseEnter:Connect(function()
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(70, 75, 80)
            })
            tween:Play()
        end)
        
        entry.MouseLeave:Connect(function()
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(50, 55, 60)
            })
            tween:Play()
        end)
        
        yPosition = yPosition + entryHeight + 5
    end
    
    self.CityListFrame.CanvasSize = UDim2.new(0, 0, 0, yPosition)
end

-- Stadt auswählen
function CityStatsGUI:SelectCity(cityId, cityData)
    self.SelectedCity = cityId
    self:UpdateDetailsPanel(cityData)
end

-- Details-Panel aktualisieren
function CityStatsGUI:UpdateDetailsPanel(cityData)
    -- Alte Details löschen
    for _, child in pairs(self.DetailsFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if not cityData then
        local noSelection = Instance.new("TextLabel")
        noSelection.Size = UDim2.new(1, 0, 1, 0)
        noSelection.BackgroundTransparency = 1
        noSelection.Text = "Wähle eine Stadt aus der Liste"
        noSelection.TextColor3 = Color3.fromRGB(150, 150, 150)
        noSelection.TextScaled = true
        noSelection.Font = Enum.Font.SourceSans
        noSelection.Parent = self.DetailsFrame
        return
    end
    
    -- Scroll-Container für Details
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 6
    scrollFrame.Parent = self.DetailsFrame
    
    local yPos = 10
    
    -- Stadt-Name (editierbar)
    self:CreateEditableField(scrollFrame, "🏙️ Stadt-Name:", yPos, cityData.name or "Unbekannte Stadt", function(newName)
        RenameCityEvent:FireServer(self.SelectedCity, newName)
        cityData.name = newName
    end)
    yPos = yPos + 60
    
    -- Grundlegende Statistiken
    self:CreateStatSection(scrollFrame, "📊 GRUNDLEGENDE STATISTIKEN", yPos)
    yPos = yPos + 40
    
    self:CreateStatField(scrollFrame, "👥 Bevölkerung:", yPos, tostring(cityData.population or 0))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "📈 Wachstumsrate:", yPos, string.format("%.2f%% pro Monat", (cityData.growthRate or 0) * 100))
    yPos = yPos + 30
    
    self:CreateStatField(scrollFrame, "📍 Position:", yPos, string.format("X: %.0f, Z: %.0f", cityData.position.X or 0, cityData.position.Z or 0))
    yPos = yPos + 30
    
    -- Nachfrage-Statistiken
    self:CreateStatSection(scrollFrame, "📦 NACHFRAGE", yPos)
    yPos = yPos + 40
    
    if cityData.demand then
        for resource, amount in pairs(cityData.demand) do
            self:CreateStatField(scrollFrame, resource .. ":", yPos, string.format("%.0f pro Monat", amount))
            yPos = yPos + 30
        end
    end
    
    -- Angebot-Statistiken
    self:CreateStatSection(scrollFrame, "📤 ANGEBOT", yPos)
    yPos = yPos + 40
    
    if cityData.supply then
        for resource, amount in pairs(cityData.supply) do
            self:CreateStatField(scrollFrame, resource .. ":", yPos, string.format("%.0f verfügbar", amount))
            yPos = yPos + 30
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
end

-- Editierbares Feld erstellen
function CityStatsGUI:CreateEditableField(parent, label, yPos, currentValue, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.3, 0, 0, 30)
    labelText.Position = UDim2.new(0, 10, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSansBold
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    local textBox = Instance.new("TextBox")
    textBox.Size = UDim2.new(0.6, 0, 0, 30)
    textBox.Position = UDim2.new(0.35, 0, 0, yPos)
    textBox.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    textBox.Text = currentValue
    textBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    textBox.TextScaled = true
    textBox.Font = Enum.Font.SourceSans
    textBox.BorderSizePixel = 0
    textBox.Parent = parent

    local boxCorner = Instance.new("UICorner")
    boxCorner.CornerRadius = UDim.new(0, 5)
    boxCorner.Parent = textBox

    textBox.FocusLost:Connect(function()
        if callback then callback(textBox.Text) end
    end)
end

-- Statistik-Sektion erstellen
function CityStatsGUI:CreateStatSection(parent, title, yPos)
    local section = Instance.new("TextLabel")
    section.Size = UDim2.new(1, -20, 0, 30)
    section.Position = UDim2.new(0, 10, 0, yPos)
    section.BackgroundColor3 = Color3.fromRGB(60, 120, 180)
    section.Text = title
    section.TextColor3 = Color3.fromRGB(255, 255, 255)
    section.TextScaled = true
    section.Font = Enum.Font.SourceSansBold
    section.BorderSizePixel = 0
    section.Parent = parent

    local sectionCorner = Instance.new("UICorner")
    sectionCorner.CornerRadius = UDim.new(0, 5)
    sectionCorner.Parent = section
end

-- Statistik-Feld erstellen
function CityStatsGUI:CreateStatField(parent, label, yPos, value)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.5, 0, 0, 25)
    labelText.Position = UDim2.new(0, 20, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(200, 200, 200)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    local valueText = Instance.new("TextLabel")
    valueText.Size = UDim2.new(0.45, 0, 0, 25)
    valueText.Position = UDim2.new(0.5, 0, 0, yPos)
    valueText.BackgroundTransparency = 1
    valueText.Text = value
    valueText.TextColor3 = Color3.fromRGB(255, 255, 255)
    valueText.TextScaled = true
    valueText.Font = Enum.Font.SourceSans
    valueText.TextXAlignment = Enum.TextXAlignment.Right
    valueText.Parent = parent
end

-- Daten laden
function CityStatsGUI:LoadCityData()
    local success, data = pcall(function()
        return GetCityDataFunction:InvokeServer()
    end)

    if success and data then
        self.CityData = data
        self:UpdateCityList()
    else
        warn("Fehler beim Laden der Stadt-Daten")
    end
end

-- GUI öffnen
function CityStatsGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end

    self:LoadCityData()
    self.MainFrame.Visible = true
    self.IsOpen = true

    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function CityStatsGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()

        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.C then
        if CityStatsGUI.IsOpen then
            CityStatsGUI:CloseGUI()
        else
            CityStatsGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function CityStatsGUI:Initialize()
    print("🏙️ CityStatsGUI initialisiert - Drücke 'C' zum Öffnen")
end

-- Auto-Start
CityStatsGUI:Initialize()

return CityStatsGUI
