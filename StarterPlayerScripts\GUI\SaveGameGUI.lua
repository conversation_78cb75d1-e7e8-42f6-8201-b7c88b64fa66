-- StarterPlayerScripts/GUI/SaveGameGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Erweiterte Save/Load GUI mit mehreren Slots, Vorschaubildern und Metadaten

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local SaveGameEvent = Events:WaitForChild("SaveGameEvent")
local LoadGameEvent = Events:WaitForChild("LoadGameEvent")
local GetSaveSlotsFunction = Events:WaitForChild("GetSaveSlotsFunction")
local DeleteSaveSlotEvent = Events:WaitForChild("DeleteSaveSlotEvent")
local ExportSaveEvent = Events:WaitForChild("ExportSaveEvent")
local ImportSaveEvent = Events:WaitForChild("ImportSaveEvent")

local SaveGameGUI = {}
SaveGameGUI.IsOpen = false
SaveGameGUI.CurrentMode = "LOAD" -- "LOAD" oder "SAVE"
SaveGameGUI.SaveSlots = {}

-- GUI erstellen
function SaveGameGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "SaveGameGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1200, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local mainCorner = Instance.new("UICorner")
    mainCorner.CornerRadius = UDim.new(0, 20)
    mainCorner.Parent = mainFrame
    
    -- Header
    local header = Instance.new("Frame")
    header.Size = UDim2.new(1, 0, 0, 80)
    header.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    header.BorderSizePixel = 0
    header.Parent = mainFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 20)
    headerCorner.Parent = header
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -200, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "💾 SPIELSTÄNDE VERWALTEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = header
    
    -- Mode-Buttons
    local loadButton = Instance.new("TextButton")
    loadButton.Size = UDim2.new(0, 80, 0, 50)
    loadButton.Position = UDim2.new(1, -180, 0, 15)
    loadButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    loadButton.Text = "📂 LADEN"
    loadButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    loadButton.TextScaled = true
    loadButton.Font = Enum.Font.SourceSansBold
    loadButton.BorderSizePixel = 0
    loadButton.Parent = header
    
    local loadCorner = Instance.new("UICorner")
    loadCorner.CornerRadius = UDim.new(0, 10)
    loadCorner.Parent = loadButton
    
    local saveButton = Instance.new("TextButton")
    saveButton.Size = UDim2.new(0, 80, 0, 50)
    saveButton.Position = UDim2.new(1, -90, 0, 15)
    saveButton.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
    saveButton.Text = "💾 SPEICHERN"
    saveButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    saveButton.TextScaled = true
    saveButton.Font = Enum.Font.SourceSansBold
    saveButton.BorderSizePixel = 0
    saveButton.Parent = header
    
    local saveCorner = Instance.new("UICorner")
    saveCorner.CornerRadius = UDim.new(0, 10)
    saveCorner.Parent = saveButton
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 50, 0, 50)
    closeButton.Position = UDim2.new(1, -70, 0, 15)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = header
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 25)
    closeCorner.Parent = closeButton
    
    -- Content-Bereich
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -40, 1, -120)
    contentFrame.Position = UDim2.new(0, 20, 0, 100)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = mainFrame
    
    -- Slots-Container
    local slotsContainer = Instance.new("ScrollingFrame")
    slotsContainer.Size = UDim2.new(1, 0, 1, -80)
    slotsContainer.BackgroundTransparency = 1
    slotsContainer.ScrollBarThickness = 8
    slotsContainer.Parent = contentFrame
    
    -- Aktions-Bereich
    local actionsFrame = Instance.new("Frame")
    actionsFrame.Size = UDim2.new(1, 0, 0, 60)
    actionsFrame.Position = UDim2.new(0, 0, 1, -60)
    actionsFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    actionsFrame.BorderSizePixel = 0
    actionsFrame.Parent = contentFrame
    
    local actionsCorner = Instance.new("UICorner")
    actionsCorner.CornerRadius = UDim.new(0, 10)
    actionsCorner.Parent = actionsFrame
    
    -- Import/Export Buttons
    local importButton = Instance.new("TextButton")
    importButton.Size = UDim2.new(0, 120, 0, 40)
    importButton.Position = UDim2.new(0, 10, 0, 10)
    importButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
    importButton.Text = "📥 IMPORTIEREN"
    importButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    importButton.TextScaled = true
    importButton.Font = Enum.Font.SourceSansBold
    importButton.BorderSizePixel = 0
    importButton.Parent = actionsFrame
    
    local importCorner = Instance.new("UICorner")
    importCorner.CornerRadius = UDim.new(0, 8)
    importCorner.Parent = importButton
    
    local exportButton = Instance.new("TextButton")
    exportButton.Size = UDim2.new(0, 120, 0, 40)
    exportButton.Position = UDim2.new(0, 140, 0, 10)
    exportButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
    exportButton.Text = "📤 EXPORTIEREN"
    exportButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    exportButton.TextScaled = true
    exportButton.Font = Enum.Font.SourceSansBold
    exportButton.BorderSizePixel = 0
    exportButton.Parent = actionsFrame
    
    local exportCorner = Instance.new("UICorner")
    exportCorner.CornerRadius = UDim.new(0, 8)
    exportCorner.Parent = exportButton
    
    -- Backup-Button
    local backupButton = Instance.new("TextButton")
    backupButton.Size = UDim2.new(0, 120, 0, 40)
    backupButton.Position = UDim2.new(1, -130, 0, 10)
    backupButton.BackgroundColor3 = Color3.fromRGB(255, 200, 100)
    backupButton.Text = "🔄 BACKUP"
    backupButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    backupButton.TextScaled = true
    backupButton.Font = Enum.Font.SourceSansBold
    backupButton.BorderSizePixel = 0
    backupButton.Parent = actionsFrame
    
    local backupCorner = Instance.new("UICorner")
    backupCorner.CornerRadius = UDim.new(0, 8)
    backupCorner.Parent = backupButton
    
    -- Referenzen speichern
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.Title = title
    self.LoadButton = loadButton
    self.SaveButton = saveButton
    self.CloseButton = closeButton
    self.SlotsContainer = slotsContainer
    self.ActionsFrame = actionsFrame
    self.ImportButton = importButton
    self.ExportButton = exportButton
    self.BackupButton = backupButton
    
    -- Event-Handler
    loadButton.MouseButton1Click:Connect(function()
        self:SetMode("LOAD")
    end)
    
    saveButton.MouseButton1Click:Connect(function()
        self:SetMode("SAVE")
    end)
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    importButton.MouseButton1Click:Connect(function()
        self:ShowImportDialog()
    end)
    
    exportButton.MouseButton1Click:Connect(function()
        self:ShowExportDialog()
    end)
    
    backupButton.MouseButton1Click:Connect(function()
        self:CreateBackup()
    end)
    
    return screenGui
end

-- Modus setzen
function SaveGameGUI:SetMode(mode)
    self.CurrentMode = mode
    
    if mode == "LOAD" then
        self.LoadButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        self.SaveButton.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
        self.Title.Text = "📂 SPIELSTAND LADEN"
    else
        self.LoadButton.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
        self.SaveButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        self.Title.Text = "💾 SPIELSTAND SPEICHERN"
    end
    
    self:RefreshSlots()
end

-- Slots aktualisieren
function SaveGameGUI:RefreshSlots()
    -- Bestehende Slots löschen
    for _, child in pairs(self.SlotsContainer:GetChildren()) do
        if not child:IsA("UIListLayout") and not child:IsA("UIPadding") then
            child:Destroy()
        end
    end
    
    -- Layout erstellen
    if not self.SlotsContainer:FindFirstChild("UIListLayout") then
        local listLayout = Instance.new("UIListLayout")
        listLayout.SortOrder = Enum.SortOrder.LayoutOrder
        listLayout.Padding = UDim.new(0, 10)
        listLayout.Parent = self.SlotsContainer
        
        local padding = Instance.new("UIPadding")
        padding.PaddingAll = UDim.new(0, 10)
        padding.Parent = self.SlotsContainer
    end
    
    -- Save-Slots vom Server abrufen
    local success, saveSlots = pcall(function()
        return GetSaveSlotsFunction:InvokeServer()
    end)
    
    if success and saveSlots then
        self.SaveSlots = saveSlots
        
        for i, slotData in ipairs(saveSlots) do
            self:CreateSlotCard(slotData, i)
        end
        
        -- Canvas-Größe setzen
        local totalHeight = (#saveSlots * 120) + ((#saveSlots - 1) * 10) + 20
        self.SlotsContainer.CanvasSize = UDim2.new(0, 0, 0, totalHeight)
    else
        warn("❌ Fehler beim Laden der Save-Slots:", saveSlots)
    end
end

-- Slot-Karte erstellen
function SaveGameGUI:CreateSlotCard(slotData, index)
    local card = Instance.new("Frame")
    card.Size = UDim2.new(1, -20, 0, 100)
    card.BackgroundColor3 = slotData.exists and Color3.fromRGB(30, 35, 40) or Color3.fromRGB(20, 25, 30)
    card.BorderSizePixel = 0
    card.LayoutOrder = index
    card.Parent = self.SlotsContainer
    
    local cardCorner = Instance.new("UICorner")
    cardCorner.CornerRadius = UDim.new(0, 12)
    cardCorner.Parent = card
    
    if slotData.exists then
        -- Existierender Slot
        self:CreateExistingSlotContent(card, slotData)
    else
        -- Leerer Slot
        self:CreateEmptySlotContent(card, slotData)
    end
    
    -- Hover-Effekt
    card.MouseEnter:Connect(function()
        local targetColor = slotData.exists and Color3.fromRGB(40, 45, 50) or Color3.fromRGB(30, 35, 40)
        TweenService:Create(card, TweenInfo.new(0.2), {
            BackgroundColor3 = targetColor
        }):Play()
    end)

    card.MouseLeave:Connect(function()
        local targetColor = slotData.exists and Color3.fromRGB(30, 35, 40) or Color3.fromRGB(20, 25, 30)
        TweenService:Create(card, TweenInfo.new(0.2), {
            BackgroundColor3 = targetColor
        }):Play()
    end)
end

-- Existierenden Slot-Inhalt erstellen
function SaveGameGUI:CreateExistingSlotContent(card, slotData)
    -- Slot-Nummer
    local slotNumber = Instance.new("TextLabel")
    slotNumber.Size = UDim2.new(0, 60, 0, 30)
    slotNumber.Position = UDim2.new(0, 10, 0, 10)
    slotNumber.BackgroundTransparency = 1
    slotNumber.Text = slotData.slotNumber == 0 and "AUTO" or ("SLOT " .. slotData.slotNumber)
    slotNumber.TextColor3 = Color3.fromRGB(100, 150, 255)
    slotNumber.TextScaled = true
    slotNumber.Font = Enum.Font.SourceSansBold
    slotNumber.Parent = card

    -- Slot-Name
    local slotName = Instance.new("TextLabel")
    slotName.Size = UDim2.new(1, -200, 0, 25)
    slotName.Position = UDim2.new(0, 80, 0, 10)
    slotName.BackgroundTransparency = 1
    slotName.Text = slotData.slotName or "Unbenannt"
    slotName.TextColor3 = Color3.fromRGB(255, 255, 255)
    slotName.TextScaled = true
    slotName.Font = Enum.Font.SourceSansBold
    slotName.TextXAlignment = Enum.TextXAlignment.Left
    slotName.Parent = card

    -- Spielzeit und Datum
    local timeInfo = Instance.new("TextLabel")
    timeInfo.Size = UDim2.new(1, -200, 0, 20)
    timeInfo.Position = UDim2.new(0, 80, 0, 35)
    timeInfo.BackgroundTransparency = 1
    timeInfo.Text = string.format("🕐 %s | 📅 %s",
        slotData.displayInfo.playTime,
        slotData.displayInfo.savedAt)
    timeInfo.TextColor3 = Color3.fromRGB(180, 180, 180)
    timeInfo.TextScaled = true
    timeInfo.Font = Enum.Font.SourceSans
    timeInfo.TextXAlignment = Enum.TextXAlignment.Left
    timeInfo.Parent = card

    -- Spiel-Informationen
    local gameInfo = Instance.new("TextLabel")
    gameInfo.Size = UDim2.new(1, -200, 0, 20)
    gameInfo.Position = UDim2.new(0, 80, 0, 55)
    gameInfo.BackgroundTransparency = 1
    gameInfo.Text = string.format("🏛️ Jahr %d | 💰 %s | 🏙️ %d Städte",
        slotData.displayInfo.gameYear,
        slotData.displayInfo.money,
        slotData.displayInfo.cities)
    gameInfo.TextColor3 = Color3.fromRGB(150, 200, 150)
    gameInfo.TextScaled = true
    gameInfo.Font = Enum.Font.SourceSans
    gameInfo.TextXAlignment = Enum.TextXAlignment.Left
    gameInfo.Parent = card

    -- Transport-Informationen
    local transportInfo = Instance.new("TextLabel")
    transportInfo.Size = UDim2.new(1, -200, 0, 20)
    transportInfo.Position = UDim2.new(0, 80, 0, 75)
    transportInfo.BackgroundTransparency = 1
    transportInfo.Text = string.format("🚂 %d Fahrzeuge | 📍 %d Linien | 👥 %s Einwohner",
        slotData.displayInfo.vehicles,
        slotData.displayInfo.lines,
        slotData.displayInfo.population)
    transportInfo.TextColor3 = Color3.fromRGB(150, 150, 200)
    transportInfo.TextScaled = true
    transportInfo.Font = Enum.Font.SourceSans
    transportInfo.TextXAlignment = Enum.TextXAlignment.Left
    transportInfo.Parent = card

    -- Aktions-Buttons
    local buttonWidth = 80
    local buttonHeight = 30
    local buttonY = 35

    if self.CurrentMode == "LOAD" then
        -- Laden-Button
        local loadButton = Instance.new("TextButton")
        loadButton.Size = UDim2.new(0, buttonWidth, 0, buttonHeight)
        loadButton.Position = UDim2.new(1, -buttonWidth - 10, 0, buttonY)
        loadButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
        loadButton.Text = "📂 LADEN"
        loadButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        loadButton.TextScaled = true
        loadButton.Font = Enum.Font.SourceSansBold
        loadButton.BorderSizePixel = 0
        loadButton.Parent = card

        local loadCorner = Instance.new("UICorner")
        loadCorner.CornerRadius = UDim.new(0, 6)
        loadCorner.Parent = loadButton

        loadButton.MouseButton1Click:Connect(function()
            self:LoadGame(slotData.slotNumber)
        end)
    else
        -- Überschreiben-Button
        local overwriteButton = Instance.new("TextButton")
        overwriteButton.Size = UDim2.new(0, buttonWidth, 0, buttonHeight)
        overwriteButton.Position = UDim2.new(1, -buttonWidth - 10, 0, buttonY)
        overwriteButton.BackgroundColor3 = Color3.fromRGB(255, 150, 100)
        overwriteButton.Text = "💾 ÜBERSCHREIBEN"
        overwriteButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        overwriteButton.TextScaled = true
        overwriteButton.Font = Enum.Font.SourceSansBold
        overwriteButton.BorderSizePixel = 0
        overwriteButton.Parent = card

        local overwriteCorner = Instance.new("UICorner")
        overwriteCorner.CornerRadius = UDim.new(0, 6)
        overwriteCorner.Parent = overwriteButton

        overwriteButton.MouseButton1Click:Connect(function()
            self:ShowSaveDialog(slotData.slotNumber, slotData.slotName)
        end)
    end

    -- Löschen-Button
    local deleteButton = Instance.new("TextButton")
    deleteButton.Size = UDim2.new(0, 30, 0, 30)
    deleteButton.Position = UDim2.new(1, -40, 0, 5)
    deleteButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    deleteButton.Text = "🗑️"
    deleteButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    deleteButton.TextScaled = true
    deleteButton.Font = Enum.Font.SourceSans
    deleteButton.BorderSizePixel = 0
    deleteButton.Parent = card

    local deleteCorner = Instance.new("UICorner")
    deleteCorner.CornerRadius = UDim.new(0, 15)
    deleteCorner.Parent = deleteButton

    deleteButton.MouseButton1Click:Connect(function()
        self:DeleteSlot(slotData.slotNumber, slotData.slotName)
    end)
end

-- Leeren Slot-Inhalt erstellen
function SaveGameGUI:CreateEmptySlotContent(card, slotData)
    -- Slot-Nummer
    local slotNumber = Instance.new("TextLabel")
    slotNumber.Size = UDim2.new(0, 100, 0, 30)
    slotNumber.Position = UDim2.new(0, 20, 0, 20)
    slotNumber.BackgroundTransparency = 1
    slotNumber.Text = slotData.slotNumber == 0 and "AUTO-SAVE" or ("SLOT " .. slotData.slotNumber)
    slotNumber.TextColor3 = Color3.fromRGB(120, 120, 120)
    slotNumber.TextScaled = true
    slotNumber.Font = Enum.Font.SourceSansBold
    slotNumber.TextXAlignment = Enum.TextXAlignment.Left
    slotNumber.Parent = card

    -- Leerer Slot Text
    local emptyText = Instance.new("TextLabel")
    emptyText.Size = UDim2.new(1, -250, 0, 25)
    emptyText.Position = UDim2.new(0, 130, 0, 25)
    emptyText.BackgroundTransparency = 1
    emptyText.Text = "--- Leerer Speicherplatz ---"
    emptyText.TextColor3 = Color3.fromRGB(100, 100, 100)
    emptyText.TextScaled = true
    emptyText.Font = Enum.Font.SourceSansItalic
    emptyText.TextXAlignment = Enum.TextXAlignment.Left
    emptyText.Parent = card

    if self.CurrentMode == "SAVE" then
        -- Speichern-Button
        local saveButton = Instance.new("TextButton")
        saveButton.Size = UDim2.new(0, 100, 0, 40)
        saveButton.Position = UDim2.new(1, -120, 0, 30)
        saveButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        saveButton.Text = "💾 SPEICHERN"
        saveButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        saveButton.TextScaled = true
        saveButton.Font = Enum.Font.SourceSansBold
        saveButton.BorderSizePixel = 0
        saveButton.Parent = card

        local saveCorner = Instance.new("UICorner")
        saveCorner.CornerRadius = UDim.new(0, 8)
        saveCorner.Parent = saveButton

        saveButton.MouseButton1Click:Connect(function()
            self:ShowSaveDialog(slotData.slotNumber)
        end)
    end
end

-- Spiel laden
function SaveGameGUI:LoadGame(slotNumber)
    print("📂 Lade Spielstand aus Slot:", slotNumber)
    LoadGameEvent:FireServer(slotNumber)
    self:CloseGUI()
end

-- Save-Dialog anzeigen
function SaveGameGUI:ShowSaveDialog(slotNumber, currentName)
    -- Hier würde ein Dialog zur Eingabe von Name und Beschreibung angezeigt
    local slotName = currentName or ("Spielstand " .. slotNumber)
    local description = "Gespeichert am " .. os.date("%d.%m.%Y %H:%M")

    print("💾 Speichere in Slot:", slotNumber, "Name:", slotName)
    SaveGameEvent:FireServer(slotNumber, slotName, description)

    -- GUI nach kurzer Verzögerung aktualisieren
    wait(1)
    self:RefreshSlots()
end

-- Slot löschen
function SaveGameGUI:DeleteSlot(slotNumber, slotName)
    -- Bestätigungsdialog (vereinfacht)
    print("🗑️ Lösche Slot:", slotNumber, slotName)
    DeleteSaveSlotEvent:FireServer(slotNumber)

    -- GUI nach kurzer Verzögerung aktualisieren
    wait(1)
    self:RefreshSlots()
end

-- Import-Dialog anzeigen
function SaveGameGUI:ShowImportDialog()
    print("📥 Import-Dialog (vereinfacht)")
    -- Hier würde ein Datei-Dialog oder Textfeld angezeigt
end

-- Export-Dialog anzeigen
function SaveGameGUI:ShowExportDialog()
    print("📤 Export-Dialog (vereinfacht)")
    -- Hier würde eine Slot-Auswahl für Export angezeigt
end

-- Backup erstellen
function SaveGameGUI:CreateBackup()
    print("🔄 Erstelle Backup")
    -- Hier würde ein Backup des aktuellen Spielstands erstellt
end

-- GUI öffnen
function SaveGameGUI:OpenGUI(mode)
    if not self.ScreenGui then
        self:CreateGUI()
    end

    self:SetMode(mode or "LOAD")
    self.MainFrame.Visible = true
    self.IsOpen = true

    -- Smooth fade-in
    self.MainFrame.Size = UDim2.new(0, 1000, 0, 640)
    self.MainFrame.BackgroundTransparency = 1

    local sizeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.4, Enum.EasingStyle.Back), {
        Size = UDim2.new(0, 1200, 0, 800)
    })

    local fadeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })

    sizeTween:Play()
    fadeTween:Play()

    self:RefreshSlots()
end

-- GUI schließen
function SaveGameGUI:CloseGUI()
    if self.MainFrame then
        local sizeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Size = UDim2.new(0, 1000, 0, 640)
        })

        local fadeTween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })

        sizeTween:Play()
        fadeTween:Play()

        fadeTween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.F5 then -- F5 für Schnell-Speichern
        SaveGameEvent:FireServer(1, "Schnell-Speicherung", "Gespeichert mit F5")
    elseif input.KeyCode == Enum.KeyCode.F9 then -- F9 für Schnell-Laden
        LoadGameEvent:FireServer(1)
    elseif input.KeyCode == Enum.KeyCode.Escape and SaveGameGUI.IsOpen then
        SaveGameGUI:CloseGUI()
    end
end)

-- Initialisierung
function SaveGameGUI:Initialize()
    print("💾 SaveGameGUI initialisiert - F5: Schnell-Speichern, F9: Schnell-Laden")
end

-- Auto-Start
SaveGameGUI:Initialize()

return SaveGameGUI
