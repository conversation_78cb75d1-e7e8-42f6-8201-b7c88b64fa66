-- ServerScriptService/Managers/TerrainManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Terrain-Management und Karten-Generierung

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local TerrainManager = {}
TerrainManager.__index = TerrainManager

function TerrainManager.new()
    local self = setmetatable({}, TerrainManager)
    
    -- Terrain-Daten
    self.terrainData = {
        size = {x = 2048, z = 2048},
        heightMap = {},
        biomes = {},
        resources = {}
    }
    
    -- Biom-Definitionen
    self.biomes = {
        plains = {
            name = "Ebenen",
            color = Color3.new(0.4, 0.8, 0.2),
            fertility = 0.8,
            buildCost = 1.0
        },
        
        hills = {
            name = "<PERSON><PERSON><PERSON>",
            color = Color3.new(0.6, 0.7, 0.3),
            fertility = 0.6,
            buildCost = 1.3
        },
        
        mountains = {
            name = "<PERSON><PERSON>",
            color = Color3.new(0.5, 0.5, 0.5),
            fertility = 0.2,
            buildCost = 2.0
        },
        
        water = {
            name = "Was<PERSON>",
            color = Color3.new(0.2, 0.4, 0.8),
            fertility = 0.0,
            buildCost = 5.0
        },
        
        forest = {
            name = "Wald",
            color = Color3.new(0.2, 0.6, 0.2),
            fertility = 0.7,
            buildCost = 1.5
        }
    }
    
    -- Ressourcen-Vorkommen
    self.resources = {
        coal = {name = "Kohle", rarity = 0.3, value = 10},
        iron = {name = "Eisenerz", rarity = 0.2, value = 15},
        oil = {name = "Öl", rarity = 0.1, value = 25},
        stone = {name = "Stein", rarity = 0.5, value = 5},
        wood = {name = "Holz", rarity = 0.4, value = 8}
    }
    
    return self
end

-- Terrain generieren
function TerrainManager:GenerateTerrain(seed)
    print("🗺️ Generiere Terrain mit Seed:", seed or "random")
    
    if seed then
        math.randomseed(seed)
    end
    
    -- Höhenkarte generieren
    self:GenerateHeightMap()
    
    -- Biome zuweisen
    self:AssignBiomes()
    
    -- Ressourcen platzieren
    self:PlaceResources()
    
    -- Städte-Positionen bestimmen
    self:DetermineCityLocations()
    
    print("✅ Terrain-Generierung abgeschlossen")
end

-- Höhenkarte generieren
function TerrainManager:GenerateHeightMap()
    local size = self.terrainData.size
    self.terrainData.heightMap = {}
    
    for x = 1, size.x do
        self.terrainData.heightMap[x] = {}
        for z = 1, size.z do
            -- Perlin-Noise-ähnliche Höhengenerierung
            local height = self:GenerateHeight(x, z)
            self.terrainData.heightMap[x][z] = height
        end
    end
end

-- Höhe für Position generieren
function TerrainManager:GenerateHeight(x, z)
    -- Vereinfachte Noise-Funktion
    local scale1 = 0.01
    local scale2 = 0.005
    local scale3 = 0.002
    
    local height1 = math.sin(x * scale1) * math.cos(z * scale1) * 20
    local height2 = math.sin(x * scale2) * math.cos(z * scale2) * 50
    local height3 = math.sin(x * scale3) * math.cos(z * scale3) * 100
    
    local totalHeight = height1 + height2 + height3
    
    -- Normalisieren (0-255)
    return math.max(0, math.min(255, totalHeight + 128))
end

-- Biome zuweisen
function TerrainManager:AssignBiomes()
    local size = self.terrainData.size
    self.terrainData.biomes = {}
    
    for x = 1, size.x do
        self.terrainData.biomes[x] = {}
        for z = 1, size.z do
            local height = self.terrainData.heightMap[x][z]
            local biome = self:DetermineBiome(height, x, z)
            self.terrainData.biomes[x][z] = biome
        end
    end
end

-- Biom basierend auf Höhe bestimmen
function TerrainManager:DetermineBiome(height, x, z)
    if height < 50 then
        return "water"
    elseif height < 100 then
        -- Ebenen mit gelegentlichen Wäldern
        if math.random() < 0.3 then
            return "forest"
        else
            return "plains"
        end
    elseif height < 180 then
        return "hills"
    else
        return "mountains"
    end
end

-- Ressourcen platzieren
function TerrainManager:PlaceResources()
    self.terrainData.resources = {}
    
    for resourceType, resourceData in pairs(self.resources) do
        local deposits = self:GenerateResourceDeposits(resourceType, resourceData)
        self.terrainData.resources[resourceType] = deposits
    end
end

-- Ressourcen-Vorkommen generieren
function TerrainManager:GenerateResourceDeposits(resourceType, resourceData)
    local deposits = {}
    local size = self.terrainData.size
    local numDeposits = math.floor(size.x * size.z * resourceData.rarity / 10000)
    
    for i = 1, numDeposits do
        local x = math.random(1, size.x)
        local z = math.random(1, size.z)
        local biome = self.terrainData.biomes[x] and self.terrainData.biomes[x][z]
        
        -- Ressourcen-spezifische Biom-Präferenzen
        local canPlace = self:CanPlaceResource(resourceType, biome)
        
        if canPlace then
            table.insert(deposits, {
                x = x,
                z = z,
                amount = math.random(1000, 10000),
                quality = math.random(50, 100) / 100
            })
        end
    end
    
    return deposits
end

-- Prüfen ob Ressource in Biom platziert werden kann
function TerrainManager:CanPlaceResource(resourceType, biome)
    if resourceType == "coal" then
        return biome == "hills" or biome == "mountains"
    elseif resourceType == "iron" then
        return biome == "mountains" or biome == "hills"
    elseif resourceType == "oil" then
        return biome == "plains" or biome == "water"
    elseif resourceType == "stone" then
        return biome == "mountains" or biome == "hills"
    elseif resourceType == "wood" then
        return biome == "forest"
    end
    
    return false
end

-- Stadt-Positionen bestimmen
function TerrainManager:DetermineCityLocations()
    self.cityLocations = {}
    local size = self.terrainData.size
    local numCities = 15 -- Anzahl der Städte
    
    for i = 1, numCities do
        local attempts = 0
        local placed = false
        
        while not placed and attempts < 100 do
            local x = math.random(100, size.x - 100)
            local z = math.random(100, size.z - 100)
            
            local biome = self.terrainData.biomes[x] and self.terrainData.biomes[x][z]
            local height = self.terrainData.heightMap[x] and self.terrainData.heightMap[x][z]
            
            -- Städte nur auf geeignetem Terrain
            if biome and (biome == "plains" or biome == "hills") and height and height > 50 and height < 150 then
                -- Mindestabstand zu anderen Städten prüfen
                local tooClose = false
                for _, existingCity in pairs(self.cityLocations) do
                    local distance = math.sqrt((x - existingCity.x)^2 + (z - existingCity.z)^2)
                    if distance < 200 then -- Mindestabstand 200 Studs
                        tooClose = true
                        break
                    end
                end
                
                if not tooClose then
                    table.insert(self.cityLocations, {
                        x = x,
                        z = z,
                        biome = biome,
                        height = height,
                        size = math.random(1, 3) -- 1=klein, 2=mittel, 3=groß
                    })
                    placed = true
                end
            end
            
            attempts = attempts + 1
        end
    end
    
    print("🏙️ Generiert", #self.cityLocations, "Stadt-Positionen")
end

-- Terrain-Daten für Position abrufen
function TerrainManager:GetTerrainData(x, z)
    if not self.terrainData.heightMap[x] or not self.terrainData.heightMap[x][z] then
        return nil
    end
    
    return {
        height = self.terrainData.heightMap[x][z],
        biome = self.terrainData.biomes[x][z],
        buildCost = self.biomes[self.terrainData.biomes[x][z]].buildCost
    }
end

-- Ressourcen in Bereich finden
function TerrainManager:FindResourcesInArea(centerX, centerZ, radius)
    local foundResources = {}
    
    for resourceType, deposits in pairs(self.terrainData.resources) do
        for _, deposit in pairs(deposits) do
            local distance = math.sqrt((deposit.x - centerX)^2 + (deposit.z - centerZ)^2)
            if distance <= radius then
                table.insert(foundResources, {
                    type = resourceType,
                    x = deposit.x,
                    z = deposit.z,
                    amount = deposit.amount,
                    quality = deposit.quality,
                    distance = distance
                })
            end
        end
    end
    
    return foundResources
end

-- Stadt-Positionen abrufen
function TerrainManager:GetCityLocations()
    return self.cityLocations or {}
end

-- Baukosten für Position berechnen
function TerrainManager:GetBuildCost(x, z, buildingType)
    local terrainData = self:GetTerrainData(x, z)
    if not terrainData then return 1000000 end -- Sehr hohe Kosten für ungültiges Terrain
    
    local baseCost = 10000 -- Basis-Baukosten
    local terrainMultiplier = terrainData.buildCost
    
    -- Gebäude-spezifische Modifikatoren
    local buildingMultiplier = 1.0
    if buildingType == "railway" then
        buildingMultiplier = 1.5
    elseif buildingType == "road" then
        buildingMultiplier = 1.0
    elseif buildingType == "bridge" then
        buildingMultiplier = 3.0
    elseif buildingType == "tunnel" then
        buildingMultiplier = 5.0
    end
    
    return math.floor(baseCost * terrainMultiplier * buildingMultiplier)
end

-- Terrain-Statistiken abrufen
function TerrainManager:GetTerrainStats()
    local stats = {
        totalArea = self.terrainData.size.x * self.terrainData.size.z,
        biomeDistribution = {},
        resourceDeposits = {},
        cityLocations = #(self.cityLocations or {})
    }
    
    -- Biom-Verteilung berechnen
    for biomeName in pairs(self.biomes) do
        stats.biomeDistribution[biomeName] = 0
    end
    
    for x = 1, self.terrainData.size.x, 10 do -- Sampling alle 10 Studs
        for z = 1, self.terrainData.size.z, 10 do
            local biome = self.terrainData.biomes[x] and self.terrainData.biomes[x][z]
            if biome then
                stats.biomeDistribution[biome] = stats.biomeDistribution[biome] + 1
            end
        end
    end
    
    -- Ressourcen-Statistiken
    for resourceType, deposits in pairs(self.terrainData.resources) do
        stats.resourceDeposits[resourceType] = #deposits
    end
    
    return stats
end

return TerrainManager
