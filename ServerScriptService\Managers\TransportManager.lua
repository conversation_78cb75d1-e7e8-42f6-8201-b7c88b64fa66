-- ServerScriptService/Managers/TransportManager.lua
-- Vollständiges Transportsystem mit Fahrzeugen, Routen und Infrastruktur

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local TransportManager = {}
TransportManager.PlayerTransport = {} -- Spieler-spezifische Transportdaten
TransportManager.GlobalInfrastructure = {} -- Globale Infrastruktur

-- Spieler-Transport initialisieren
function TransportManager:InitializePlayerTransport(playerId)
    self.PlayerTransport[playerId] = {
        vehicles = {},
        routes = {},
        infrastructure = {
            railways = {},
            roads = {},
            stations = {},
            bridges = {}
        },
        buildMode = {
            active = false,
            type = nil, -- "railway", "road", "station"
            startPos = nil,
            endPos = nil
        }
    }
    
    print("🚂 Transport-System für Spieler initialisiert:", playerId)
end

-- Route erstellen
function TransportManager:CreateRoute(playerId, routeData)
    local playerTransport = self.PlayerTransport[playerId]
    if not playerTransport then return false end
    
    local routeId = "route_" .. (#playerTransport.routes + 1)
    local route = {
        id = routeId,
        name = routeData.name or ("Route " .. #playerTransport.routes + 1),
        type = routeData.type, -- "passenger", "cargo"
        stations = routeData.stations or {},
        vehicles = {},
        schedule = routeData.schedule or {},
        active = false,
        totalDistance = 0,
        estimatedTime = 0
    }
    
    -- Distanz und Zeit berechnen
    route.totalDistance, route.estimatedTime = self:CalculateRouteMetrics(route.stations)
    
    table.insert(playerTransport.routes, route)
    
    print("🛤️ Route erstellt:", route.name, "ID:", routeId)
    return routeId
end

-- Fahrzeug zu Route zuweisen
function TransportManager:AssignVehicleToRoute(playerId, vehicleId, routeId)
    local playerTransport = self.PlayerTransport[playerId]
    if not playerTransport then return false end
    
    -- Fahrzeug finden
    local vehicle = nil
    for _, v in pairs(playerTransport.vehicles) do
        if v.id == vehicleId then
            vehicle = v
            break
        end
    end
    
    if not vehicle then return false end
    
    -- Route finden
    local route = nil
    for _, r in pairs(playerTransport.routes) do
        if r.id == routeId then
            route = r
            break
        end
    end
    
    if not route then return false end
    
    -- Fahrzeug zuweisen
    vehicle.routeId = routeId
    vehicle.currentStation = 1
    vehicle.status = "assigned"
    table.insert(route.vehicles, vehicleId)
    
    print("🚗 Fahrzeug", vehicleId, "zu Route", routeId, "zugewiesen")
    return true
end

-- Infrastruktur bauen
function TransportManager:BuildInfrastructure(playerId, buildData)
    local playerTransport = self.PlayerTransport[playerId]
    if not playerTransport then return false, "Spieler nicht gefunden" end
    
    local buildType = buildData.type
    local startPos = buildData.startPos
    local endPos = buildData.endPos
    
    -- Kosten berechnen
    local cost, distance = self:CalculateBuildCost(buildType, startPos, endPos)
    
    -- Validierung (vereinfacht)
    if not self:ValidateBuild(buildType, startPos, endPos) then
        return false, "Bau nicht möglich"
    end
    
    -- Infrastruktur erstellen
    local infrastructureId = buildType .. "_" .. (#playerTransport.infrastructure[buildType .. "s"] + 1)
    local infrastructure = {
        id = infrastructureId,
        type = buildType,
        startPos = startPos,
        endPos = endPos,
        distance = distance,
        cost = cost,
        maintenance = cost * 0.01, -- 1% der Baukosten pro Monat
        builtAt = os.time()
    }
    
    table.insert(playerTransport.infrastructure[buildType .. "s"], infrastructure)
    
    print("🔨 Infrastruktur gebaut:", buildType, "ID:", infrastructureId, "Kosten:", cost)
    return true, infrastructureId
end

-- Station bauen
function TransportManager:BuildStation(playerId, stationData)
    local playerTransport = self.PlayerTransport[playerId]
    if not playerTransport then return false end
    
    local stationId = "station_" .. (#playerTransport.infrastructure.stations + 1)
    local station = {
        id = stationId,
        name = stationData.name or ("Station " .. #playerTransport.infrastructure.stations + 1),
        type = stationData.type, -- "railway", "road", "harbor"
        position = stationData.position,
        capacity = stationData.capacity or 100,
        cost = stationData.cost,
        maintenance = stationData.maintenance,
        connections = {}, -- Verbundene Routen
        cargo = {}, -- Wartende Waren
        passengers = 0 -- Wartende Passagiere
    }
    
    table.insert(playerTransport.infrastructure.stations, station)
    
    print("🏢 Station gebaut:", station.name, "Typ:", station.type)
    return stationId
end

-- Fahrzeug-Bewegung aktualisieren
function TransportManager:Update(deltaTime)
    for playerId, playerTransport in pairs(self.PlayerTransport) do
        for _, vehicle in pairs(playerTransport.vehicles) do
            if vehicle.status == "moving" then
                self:UpdateVehicleMovement(vehicle, deltaTime)
            elseif vehicle.status == "assigned" and vehicle.routeId then
                self:StartVehicleRoute(playerId, vehicle)
            end
        end
    end
end

-- Fahrzeug-Bewegung aktualisieren
function TransportManager:UpdateVehicleMovement(vehicle, deltaTime)
    if not vehicle.targetPos or not vehicle.position then return end
    
    local direction = {
        x = vehicle.targetPos.x - vehicle.position.x,
        z = vehicle.targetPos.z - vehicle.position.z
    }
    
    local distance = math.sqrt(direction.x^2 + direction.z^2)
    
    if distance < 1 then
        -- Ziel erreicht
        vehicle.position = vehicle.targetPos
        vehicle.status = "arrived"
        self:HandleVehicleArrival(vehicle)
    else
        -- Bewegung fortsetzen
        local speed = vehicle.speed / 3.6 * deltaTime -- km/h zu Studs/s
        local moveDistance = math.min(speed, distance)
        
        vehicle.position.x = vehicle.position.x + (direction.x / distance) * moveDistance
        vehicle.position.z = vehicle.position.z + (direction.z / distance) * moveDistance
    end
end

-- Fahrzeug-Route starten
function TransportManager:StartVehicleRoute(playerId, vehicle)
    local playerTransport = self.PlayerTransport[playerId]
    local route = nil
    
    -- Route finden
    for _, r in pairs(playerTransport.routes) do
        if r.id == vehicle.routeId then
            route = r
            break
        end
    end
    
    if not route or #route.stations == 0 then return end
    
    -- Nächste Station als Ziel setzen
    local currentStationIndex = vehicle.currentStation or 1
    local targetStation = route.stations[currentStationIndex]
    
    if targetStation then
        vehicle.targetPos = targetStation.position
        vehicle.status = "moving"
        
        print("🚗 Fahrzeug", vehicle.id, "fährt zu Station", targetStation.name)
    end
end

-- Fahrzeug-Ankunft behandeln
function TransportManager:HandleVehicleArrival(vehicle)
    print("🏁 Fahrzeug", vehicle.id, "ist angekommen")
    
    -- Waren/Passagiere laden/entladen (vereinfacht)
    if vehicle.cargo then
        -- Entladen
        vehicle.cargo = {}
    end
    
    -- Nächste Station in Route
    if vehicle.routeId then
        vehicle.currentStation = (vehicle.currentStation or 1) + 1
        -- Route wiederholen wenn am Ende
        if vehicle.currentStation > #vehicle.route.stations then
            vehicle.currentStation = 1
        end
        vehicle.status = "assigned" -- Bereit für nächste Station
    else
        vehicle.status = "idle"
    end
end

-- Bau-Kosten berechnen
function TransportManager:CalculateBuildCost(buildType, startPos, endPos)
    local distance = math.sqrt((endPos.x - startPos.x)^2 + (endPos.z - startPos.z)^2)
    local costPerTile = 0
    
    if buildType == "railway" then
        costPerTile = GameConfig.Transport.Infrastructure.Railway.costPerTile
    elseif buildType == "road" then
        costPerTile = GameConfig.Transport.Infrastructure.Road.costPerTile
    end
    
    local totalCost = math.floor(distance * costPerTile)
    return totalCost, distance
end

-- Bau validieren
function TransportManager:ValidateBuild(buildType, startPos, endPos)
    -- Vereinfachte Validierung
    local distance = math.sqrt((endPos.x - startPos.x)^2 + (endPos.z - startPos.z)^2)
    
    -- Mindest- und Maximal-Distanz
    if distance < 2 or distance > 100 then
        return false
    end
    
    -- Weitere Validierungen könnten hier stehen:
    -- - Kollision mit bestehender Infrastruktur
    -- - Terrain-Kompatibilität
    -- - Wasser-Überquerung nur mit Brücken
    
    return true
end

-- Route-Metriken berechnen
function TransportManager:CalculateRouteMetrics(stations)
    if #stations < 2 then return 0, 0 end
    
    local totalDistance = 0
    local estimatedTime = 0
    
    for i = 1, #stations - 1 do
        local station1 = stations[i]
        local station2 = stations[i + 1]
        
        local distance = math.sqrt(
            (station2.position.x - station1.position.x)^2 + 
            (station2.position.z - station1.position.z)^2
        )
        
        totalDistance = totalDistance + distance
        estimatedTime = estimatedTime + (distance / 60) * 3.6 -- Annahme: 60 km/h Durchschnitt
    end
    
    return totalDistance, estimatedTime
end

-- Fahrzeug erstellen
function TransportManager:CreateVehicle(playerId, vehicleData)
    local playerTransport = self.PlayerTransport[playerId]
    if not playerTransport then return false end
    
    local vehicleId = "vehicle_" .. (#playerTransport.vehicles + 1)
    local vehicle = {
        id = vehicleId,
        type = vehicleData.type,
        model = vehicleData.model,
        name = vehicleData.name or (vehicleData.model .. " " .. #playerTransport.vehicles + 1),
        speed = vehicleData.speed,
        capacity = vehicleData.capacity,
        maintenance = vehicleData.maintenance,
        fuelType = vehicleData.fuelType,
        position = vehicleData.startPos or {x = 0, z = 0},
        targetPos = nil,
        routeId = nil,
        currentStation = 1,
        status = "idle", -- "idle", "assigned", "moving", "arrived", "loading"
        cargo = {},
        fuel = 100 -- Prozent
    }
    
    table.insert(playerTransport.vehicles, vehicle)
    
    print("🚗 Fahrzeug erstellt:", vehicle.name, "ID:", vehicleId)
    return vehicleId
end

-- Transport-Daten für Client abrufen
function TransportManager:GetTransportData(playerId)
    return self.PlayerTransport[playerId]
end

-- Alle verfügbaren Fahrzeuge abrufen (basierend auf Jahr)
function TransportManager:GetAvailableVehicles(currentYear)
    local availableVehicles = {}
    
    for vehicleType, config in pairs(GameConfig.Transport.Vehicles) do
        if currentYear >= config.availableFrom then
            availableVehicles[vehicleType] = config
        end
    end
    
    return availableVehicles
end

-- Initialisierung
function TransportManager:Initialize()
    print("🚂 TransportManager initialisiert")
end

return TransportManager
