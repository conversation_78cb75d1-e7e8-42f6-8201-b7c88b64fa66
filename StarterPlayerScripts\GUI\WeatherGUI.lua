-- StarterPlayerScripts/GUI/WeatherGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Wetter- und Zeit-Anzeige GUI

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetWeatherDataFunction = Events:WaitForChild("GetWeatherDataFunction")
local WeatherChangedEvent = Events:WaitForChild("WeatherChangedEvent")
local DisasterEvent = Events:WaitForChild("DisasterEvent")

local WeatherGUI = {}
WeatherGUI.IsInitialized = false

-- GUI erstellen
function WeatherGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "WeatherGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Wetter-Widget (oben rechts)
    local weatherWidget = Instance.new("Frame")
    weatherWidget.Size = UDim2.new(0, 300, 0, 120)
    weatherWidget.Position = UDim2.new(1, -320, 0, 20)
    weatherWidget.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    weatherWidget.BorderSizePixel = 0
    weatherWidget.Parent = screenGui
    
    local weatherCorner = Instance.new("UICorner")
    weatherCorner.CornerRadius = UDim.new(0, 15)
    weatherCorner.Parent = weatherWidget
    
    -- Wetter-Icon
    local weatherIcon = Instance.new("TextLabel")
    weatherIcon.Size = UDim2.new(0, 60, 0, 60)
    weatherIcon.Position = UDim2.new(0, 15, 0, 15)
    weatherIcon.BackgroundTransparency = 1
    weatherIcon.Text = "☀️"
    weatherIcon.TextColor3 = Color3.fromRGB(255, 255, 255)
    weatherIcon.TextScaled = true
    weatherIcon.Font = Enum.Font.SourceSans
    weatherIcon.Parent = weatherWidget
    
    -- Wetter-Name
    local weatherName = Instance.new("TextLabel")
    weatherName.Size = UDim2.new(1, -90, 0, 30)
    weatherName.Position = UDim2.new(0, 85, 0, 15)
    weatherName.BackgroundTransparency = 1
    weatherName.Text = "Sonnig"
    weatherName.TextColor3 = Color3.fromRGB(255, 255, 255)
    weatherName.TextScaled = true
    weatherName.Font = Enum.Font.SourceSansBold
    weatherName.TextXAlignment = Enum.TextXAlignment.Left
    weatherName.Parent = weatherWidget
    
    -- Jahreszeit
    local seasonLabel = Instance.new("TextLabel")
    seasonLabel.Size = UDim2.new(1, -90, 0, 25)
    seasonLabel.Position = UDim2.new(0, 85, 0, 45)
    seasonLabel.BackgroundTransparency = 1
    seasonLabel.Text = "🌸 Frühling"
    seasonLabel.TextColor3 = Color3.fromRGB(150, 255, 150)
    seasonLabel.TextScaled = true
    seasonLabel.Font = Enum.Font.SourceSans
    seasonLabel.TextXAlignment = Enum.TextXAlignment.Left
    seasonLabel.Parent = weatherWidget
    
    -- Zeit-Anzeige
    local timeLabel = Instance.new("TextLabel")
    timeLabel.Size = UDim2.new(1, -20, 0, 30)
    timeLabel.Position = UDim2.new(0, 10, 0, 80)
    timeLabel.BackgroundTransparency = 1
    timeLabel.Text = "🕐 08:00 - 01.01.1850"
    timeLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    timeLabel.TextScaled = true
    timeLabel.Font = Enum.Font.SourceSans
    timeLabel.Parent = weatherWidget
    
    -- Zeit-Skala Widget (unten rechts)
    local timeScaleWidget = Instance.new("Frame")
    timeScaleWidget.Size = UDim2.new(0, 200, 0, 80)
    timeScaleWidget.Position = UDim2.new(1, -220, 1, -100)
    timeScaleWidget.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    timeScaleWidget.BorderSizePixel = 0
    timeScaleWidget.Parent = screenGui
    
    local timeScaleCorner = Instance.new("UICorner")
    timeScaleCorner.CornerRadius = UDim.new(0, 10)
    timeScaleCorner.Parent = timeScaleWidget
    
    -- Zeit-Skala Titel
    local timeScaleTitle = Instance.new("TextLabel")
    timeScaleTitle.Size = UDim2.new(1, 0, 0, 25)
    timeScaleTitle.Position = UDim2.new(0, 0, 0, 5)
    timeScaleTitle.BackgroundTransparency = 1
    timeScaleTitle.Text = "⏰ Zeitgeschwindigkeit"
    timeScaleTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    timeScaleTitle.TextScaled = true
    timeScaleTitle.Font = Enum.Font.SourceSansBold
    timeScaleTitle.Parent = timeScaleWidget
    
    -- Zeit-Skala Anzeige
    local timeScaleDisplay = Instance.new("TextLabel")
    timeScaleDisplay.Size = UDim2.new(1, 0, 0, 30)
    timeScaleDisplay.Position = UDim2.new(0, 0, 0, 30)
    timeScaleDisplay.BackgroundTransparency = 1
    timeScaleDisplay.Text = "60x"
    timeScaleDisplay.TextColor3 = Color3.fromRGB(100, 255, 100)
    timeScaleDisplay.TextScaled = true
    timeScaleDisplay.Font = Enum.Font.SourceSansBold
    timeScaleDisplay.Parent = timeScaleWidget
    
    -- Pause/Play Button
    local pauseButton = Instance.new("TextButton")
    pauseButton.Size = UDim2.new(0, 40, 0, 20)
    pauseButton.Position = UDim2.new(0, 10, 1, -25)
    pauseButton.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    pauseButton.Text = "⏸️"
    pauseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    pauseButton.TextScaled = true
    pauseButton.Font = Enum.Font.SourceSansBold
    pauseButton.BorderSizePixel = 0
    pauseButton.Parent = timeScaleWidget
    
    local pauseCorner = Instance.new("UICorner")
    pauseCorner.CornerRadius = UDim.new(0, 5)
    pauseCorner.Parent = pauseButton
    
    -- Geschwindigkeit-Buttons
    local speedButtons = {}
    local speeds = {1, 10, 60, 300, 1800} -- 1x, 10x, 60x, 300x, 1800x
    local speedTexts = {"1x", "10x", "60x", "5m", "30m"}
    
    for i, speed in ipairs(speeds) do
        local speedButton = Instance.new("TextButton")
        speedButton.Size = UDim2.new(0, 25, 0, 20)
        speedButton.Position = UDim2.new(0, 55 + (i-1) * 28, 1, -25)
        speedButton.BackgroundColor3 = speed == 60 and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(60, 65, 70)
        speedButton.Text = speedTexts[i]
        speedButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        speedButton.TextScaled = true
        speedButton.Font = Enum.Font.SourceSans
        speedButton.BorderSizePixel = 0
        speedButton.Parent = timeScaleWidget
        
        local speedCorner = Instance.new("UICorner")
        speedCorner.CornerRadius = UDim.new(0, 3)
        speedCorner.Parent = speedButton
        
        speedButtons[speed] = speedButton
        
        speedButton.MouseButton1Click:Connect(function()
            self:SetTimeScale(speed)
        end)
    end
    
    -- Katastrophen-Warnung (zentriert oben)
    local disasterWarning = Instance.new("Frame")
    disasterWarning.Size = UDim2.new(0, 400, 0, 80)
    disasterWarning.Position = UDim2.new(0.5, -200, 0, 20)
    disasterWarning.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    disasterWarning.BorderSizePixel = 0
    disasterWarning.Visible = false
    disasterWarning.Parent = screenGui
    
    local disasterCorner = Instance.new("UICorner")
    disasterCorner.CornerRadius = UDim.new(0, 10)
    disasterCorner.Parent = disasterWarning
    
    local disasterIcon = Instance.new("TextLabel")
    disasterIcon.Size = UDim2.new(0, 50, 0, 50)
    disasterIcon.Position = UDim2.new(0, 15, 0, 15)
    disasterIcon.BackgroundTransparency = 1
    disasterIcon.Text = "⚠️"
    disasterIcon.TextColor3 = Color3.fromRGB(255, 255, 255)
    disasterIcon.TextScaled = true
    disasterIcon.Font = Enum.Font.SourceSans
    disasterIcon.Parent = disasterWarning
    
    local disasterText = Instance.new("TextLabel")
    disasterText.Size = UDim2.new(1, -80, 1, -10)
    disasterText.Position = UDim2.new(0, 75, 0, 5)
    disasterText.BackgroundTransparency = 1
    disasterText.Text = "NATURKATASTROPHE!\nErdbeben in der Region"
    disasterText.TextColor3 = Color3.fromRGB(255, 255, 255)
    disasterText.TextScaled = true
    disasterText.Font = Enum.Font.SourceSansBold
    disasterText.TextXAlignment = Enum.TextXAlignment.Left
    disasterText.Parent = disasterWarning
    
    -- Referenzen speichern
    self.ScreenGui = screenGui
    self.WeatherWidget = weatherWidget
    self.WeatherIcon = weatherIcon
    self.WeatherName = weatherName
    self.SeasonLabel = seasonLabel
    self.TimeLabel = timeLabel
    self.TimeScaleWidget = timeScaleWidget
    self.TimeScaleDisplay = timeScaleDisplay
    self.PauseButton = pauseButton
    self.SpeedButtons = speedButtons
    self.DisasterWarning = disasterWarning
    self.DisasterIcon = disasterIcon
    self.DisasterText = disasterText
    
    self.CurrentTimeScale = 60
    self.IsPaused = false
    
    return screenGui
end

-- Wetter-Daten aktualisieren
function WeatherGUI:UpdateWeatherData()
    local success, weatherData = pcall(function()
        return GetWeatherDataFunction:InvokeServer()
    end)
    
    if success and weatherData then
        -- Wetter anzeigen
        local weather = weatherData.weatherData
        if weather then
            self.WeatherIcon.Text = weather.icon
            self.WeatherName.Text = weather.name
        end
        
        -- Jahreszeit anzeigen
        local season = weatherData.seasonData
        if season then
            self.SeasonLabel.Text = season.icon .. " " .. season.name
        end
        
        -- Zeit anzeigen
        local gameTime = weatherData.gameTime
        if gameTime then
            local timeString = string.format("%02d:%02d - %02d.%02d.%d", 
                math.floor(gameTime.hour), 
                math.floor(gameTime.minute),
                gameTime.day,
                gameTime.month,
                gameTime.year
            )
            self.TimeLabel.Text = "🕐 " .. timeString
        end
        
        -- Zeit-Skala anzeigen
        if weatherData.timeScale then
            local scaleText = weatherData.timeScale .. "x"
            if weatherData.timeScale >= 60 then
                local minutes = weatherData.timeScale / 60
                if minutes >= 60 then
                    local hours = minutes / 60
                    scaleText = math.floor(hours) .. "h"
                else
                    scaleText = math.floor(minutes) .. "m"
                end
            end
            self.TimeScaleDisplay.Text = scaleText
        end
    end
end

-- Zeit-Skala setzen
function WeatherGUI:SetTimeScale(scale)
    self.CurrentTimeScale = scale
    
    -- Alle Speed-Buttons zurücksetzen
    for speed, button in pairs(self.SpeedButtons) do
        button.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
    end
    
    -- Aktiven Button hervorheben
    if self.SpeedButtons[scale] then
        self.SpeedButtons[scale].BackgroundColor3 = Color3.fromRGB(100, 255, 100)
    end
    
    -- Hier würde das Event an den Server gesendet werden
    print("⏰ Zeit-Skala geändert:", scale .. "x")
end

-- Pause/Play umschalten
function WeatherGUI:TogglePause()
    self.IsPaused = not self.IsPaused
    self.PauseButton.Text = self.IsPaused and "▶️" or "⏸️"
    self.PauseButton.BackgroundColor3 = self.IsPaused and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(100, 150, 255)
    
    -- Hier würde das Event an den Server gesendet werden
    print("⏰ Spiel", self.IsPaused and "pausiert" or "fortgesetzt")
end

-- Katastrophe anzeigen
function WeatherGUI:ShowDisaster(disasterId, disasterData)
    if disasterData then
        self.DisasterIcon.Text = disasterData.icon
        self.DisasterText.Text = "NATURKATASTROPHE!\n" .. disasterData.name
        
        self.DisasterWarning.Visible = true
        
        -- Blink-Effekt
        local blinkTween = TweenService:Create(self.DisasterWarning, TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
            BackgroundTransparency = 0.3
        })
        blinkTween:Play()
        
        self.DisasterBlinkTween = blinkTween
        
        print("⚠️ Katastrophe angezeigt:", disasterData.name)
    end
end

-- Katastrophe verstecken
function WeatherGUI:HideDisaster()
    self.DisasterWarning.Visible = false
    
    if self.DisasterBlinkTween then
        self.DisasterBlinkTween:Cancel()
        self.DisasterBlinkTween = nil
    end
    
    print("✅ Katastrophen-Warnung versteckt")
end

-- Event-Handler
function WeatherGUI:SetupEventHandlers()
    -- Wetter-Änderung
    WeatherChangedEvent.OnClientEvent:Connect(function(weatherType, season)
        print("🌤️ Wetter-Event erhalten:", weatherType, season)
        self:UpdateWeatherData()
    end)
    
    -- Katastrophen
    DisasterEvent.OnClientEvent:Connect(function(action, disasterId, disasterData)
        if action == "START" then
            self:ShowDisaster(disasterId, disasterData)
        elseif action == "END" then
            self:HideDisaster()
        end
    end)
    
    -- Pause-Button
    self.PauseButton.MouseButton1Click:Connect(function()
        self:TogglePause()
    end)
end

-- Update-Loop
function WeatherGUI:StartUpdateLoop()
    self.updateConnection = RunService.Heartbeat:Connect(function()
        -- Alle 5 Sekunden Wetter-Daten aktualisieren
        if not self.lastUpdate or tick() - self.lastUpdate >= 5 then
            self:UpdateWeatherData()
            self.lastUpdate = tick()
        end
    end)
end

-- Initialisierung
function WeatherGUI:Initialize()
    if self.IsInitialized then return end
    
    self:CreateGUI()
    self:SetupEventHandlers()
    self:StartUpdateLoop()
    self:UpdateWeatherData()
    
    self.IsInitialized = true
    print("🌤️ WeatherGUI initialisiert")
end

-- Cleanup
function WeatherGUI:Destroy()
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end
    
    if self.DisasterBlinkTween then
        self.DisasterBlinkTween:Cancel()
    end
    
    if self.ScreenGui then
        self.ScreenGui:Destroy()
    end
end

-- Auto-Start
WeatherGUI:Initialize()

return WeatherGUI
