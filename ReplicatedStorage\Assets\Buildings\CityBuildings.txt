# Stadt-Gebäude Assets für Transport Empire
# Nach Epochen strukturiert für historisch korrekte Stadtentwicklung

## ===== VIKTORIANISCHE ÄRA (1850-1900) =====

### WOHNGEBÄUDE - Viktorianisch
[HOUSE_VICTORIAN_SMALL]
Name = "Viktorianisches Cottage"
Era = 1850-1900
ModelId = "rbxassetid://HOUSE_VICTORIAN_SMALL"
Scale = Vector3(6, 5, 8)
Population = 4
BuildingType = "Residential"
ArchitecturalStyle = "Victorian"
Colors = {
    Primary = Color3(0.6, 0.4, 0.2),    # Dunkles Holz
    Secondary = Color3(0.8, 0.2, 0.2),  # Rotes Ziegeldach
    Accent = Color3(0.9, 0.9, 0.7)      # Weiße Verzierungen
}
Features = {"Bay_Windows", "Ornate_Trim", "Steep_Roof"}

[HOUSE_VICTORIAN_TOWNHOUSE]
Name = "Viktorianisches Reihenhaus"
Era = 1860-1900
ModelId = "rbxassetid://HOUSE_VICTORIAN_TOWNHOUSE"
Scale = Vector3(7, 8, 12)
Population = 8
BuildingType = "Residential"
ArchitecturalStyle = "Victorian"
Features = {"Multi_Story", "Shared_Walls", "Front_Steps"}

### GESCHÄFTE - Viktorianisch
[SHOP_VICTORIAN_GENERAL]
Name = "Viktorianischer Gemischtwarenladen"
Era = 1850-1900
ModelId = "rbxassetid://SHOP_VICTORIAN_GENERAL"
Scale = Vector3(8, 6, 12)
BuildingType = "Commercial"
ArchitecturalStyle = "Victorian"
Services = {"General_Goods"}
Workers = 3
Features = {"Large_Windows", "Wooden_Signage", "Living_Quarters_Above"}

[BANK_VICTORIAN]
Name = "Viktorianische Bank"
Era = 1860-1900
ModelId = "rbxassetid://BANK_VICTORIAN"
Scale = Vector3(15, 10, 20)
BuildingType = "Financial"
ArchitecturalStyle = "Victorian"
Services = {"Banking", "Loans"}
Workers = 8
Features = {"Stone_Facade", "Columns", "Vault"}

### ÖFFENTLICHE GEBÄUDE - Viktorianisch
[CITY_HALL_VICTORIAN]
Name = "Viktorianisches Rathaus"
Era = 1850-1900
ModelId = "rbxassetid://CITY_HALL_VICTORIAN"
Scale = Vector3(25, 12, 30)
BuildingType = "Government"
ArchitecturalStyle = "Victorian"
Workers = 15
Features = {"Clock_Tower", "Gothic_Revival", "Grand_Entrance"}

[SCHOOL_VICTORIAN]
Name = "Viktorianische Schule"
Era = 1850-1900
ModelId = "rbxassetid://SCHOOL_VICTORIAN"
Scale = Vector3(20, 8, 25)
BuildingType = "Education"
ArchitecturalStyle = "Victorian"
Capacity = 150
Workers = 8
Features = {"Bell_Tower", "Large_Windows", "Playground"}

## ===== JAHRHUNDERTWENDE (1900-1920) =====

### WOHNGEBÄUDE - Edwardianisch
[HOUSE_EDWARDIAN_VILLA]
Name = "Edwardianische Villa"
Era = 1900-1920
ModelId = "rbxassetid://HOUSE_EDWARDIAN_VILLA"
Scale = Vector3(12, 8, 15)
Population = 12
BuildingType = "Residential"
ArchitecturalStyle = "Edwardian"
Features = {"Symmetrical_Design", "Large_Gardens", "Servants_Quarters"}

[APARTMENT_EDWARDIAN]
Name = "Edwardianisches Mehrfamilienhaus"
Era = 1900-1920
ModelId = "rbxassetid://APARTMENT_EDWARDIAN"
Scale = Vector3(15, 12, 20)
Population = 24
BuildingType = "Residential"
ArchitecturalStyle = "Edwardian"
Floors = 3
Features = {"Brick_Construction", "Sash_Windows", "Shared_Courtyard"}

### GESCHÄFTE - Jahrhundertwende
[DEPARTMENT_STORE_EARLY]
Name = "Frühes Kaufhaus"
Era = 1900-1920
ModelId = "rbxassetid://DEPARTMENT_STORE_EARLY"
Scale = Vector3(25, 12, 35)
BuildingType = "Commercial"
ArchitecturalStyle = "Edwardian"
Services = {"Department_Store", "Fashion", "Home_Goods"}
Workers = 30
Features = {"Large_Display_Windows", "Multiple_Floors", "Electric_Lighting"}

[HOTEL_GRAND]
Name = "Grand Hotel"
Era = 1900-1920
ModelId = "rbxassetid://HOTEL_GRAND"
Scale = Vector3(30, 15, 40)
BuildingType = "Hospitality"
ArchitecturalStyle = "Edwardian"
Services = {"Accommodation", "Restaurant", "Ballroom"}
Workers = 50
Features = {"Ornate_Facade", "Grand_Entrance", "Multiple_Wings"}

## ===== ZWISCHENKRIEGSZEIT (1920-1940) =====

### WOHNGEBÄUDE - Art Deco
[APARTMENT_ART_DECO]
Name = "Art Deco Apartment"
Era = 1920-1940
ModelId = "rbxassetid://APARTMENT_ART_DECO"
Scale = Vector3(18, 15, 25)
Population = 40
BuildingType = "Residential"
ArchitecturalStyle = "Art_Deco"
Floors = 5
Features = {"Geometric_Patterns", "Stepped_Facade", "Modern_Amenities"}

[HOUSE_SUBURBAN]
Name = "Vorstadthaus"
Era = 1920-1940
ModelId = "rbxassetid://HOUSE_SUBURBAN"
Scale = Vector3(10, 6, 12)
Population = 6
BuildingType = "Residential"
ArchitecturalStyle = "Colonial_Revival"
Features = {"Front_Porch", "Garage", "Lawn"}

### GESCHÄFTE - Art Deco
[CINEMA_ART_DECO]
Name = "Art Deco Kino"
Era = 1920-1940
ModelId = "rbxassetid://CINEMA_ART_DECO"
Scale = Vector3(20, 12, 30)
BuildingType = "Entertainment"
ArchitecturalStyle = "Art_Deco"
Capacity = 300
Workers = 15
Features = {"Neon_Signs", "Streamlined_Design", "Marquee"}

[OFFICE_ART_DECO]
Name = "Art Deco Bürogebäude"
Era = 1920-1940
ModelId = "rbxassetid://OFFICE_ART_DECO"
Scale = Vector3(15, 20, 15)
BuildingType = "Office"
ArchitecturalStyle = "Art_Deco"
Workers = 80
Floors = 8
Features = {"Vertical_Lines", "Setbacks", "Decorative_Spire"}

## ===== NACHKRIEGSZEIT (1945-1960) =====

### WOHNGEBÄUDE - Modernistisch
[HOUSE_RANCH_STYLE]
Name = "Ranch-Style Haus"
Era = 1945-1960
ModelId = "rbxassetid://HOUSE_RANCH_STYLE"
Scale = Vector3(12, 4, 16)
Population = 6
BuildingType = "Residential"
ArchitecturalStyle = "Mid_Century_Modern"
Features = {"Single_Story", "Large_Windows", "Open_Floor_Plan", "Carport"}

### Mehrfamilienhäuser (1900-1960)
[APARTMENT_SMALL]
Name = "Kleines Mehrfamilienhaus"
Era = 1900-1960
ModelId = "rbxassetid://APARTMENT_SMALL"
Scale = Vector3(12, 8, 15)
Population = 20
BuildingType = "Residential"
Floors = 3

[APARTMENT_MEDIUM]
Name = "Mittleres Wohnhaus"
Era = 1920-1980
ModelId = "rbxassetid://APARTMENT_MEDIUM"
Scale = Vector3(15, 12, 20)
Population = 40
BuildingType = "Residential"
Floors = 4

### Moderne Wohnungen (1960+)
[APARTMENT_MODERN]
Name = "Moderner Wohnblock"
Era = 1960-2000
ModelId = "rbxassetid://APARTMENT_MODERN"
Scale = Vector3(20, 20, 25)
Population = 80
BuildingType = "Residential"
Floors = 8
Features = {"Elevator", "Balconies", "Parking"}

## GESCHÄFTSGEBÄUDE

### Läden und Märkte
[SHOP_GENERAL]
Name = "Gemischtwarenladen"
Era = 1850-1950
ModelId = "rbxassetid://SHOP_GENERAL"
Scale = Vector3(8, 4, 10)
BuildingType = "Commercial"
Services = {"General_Goods"}
Workers = 2

[MARKET_HALL]
Name = "Markthalle"
Era = 1880-2000
ModelId = "rbxassetid://MARKET_HALL"
Scale = Vector3(25, 8, 30)
BuildingType = "Commercial"
Services = {"Food", "Goods", "Market"}
Workers = 15

[SHOPPING_CENTER]
Name = "Einkaufszentrum"
Era = 1960-2000
ModelId = "rbxassetid://SHOPPING_CENTER"
Scale = Vector3(40, 6, 50)
BuildingType = "Commercial"
Services = {"Shopping", "Food", "Entertainment"}
Workers = 50
Features = {"Parking", "Multiple_Stores"}

### Bürogebäude
[OFFICE_SMALL]
Name = "Kleines Bürogebäude"
Era = 1920-2000
ModelId = "rbxassetid://OFFICE_SMALL"
Scale = Vector3(12, 15, 12)
BuildingType = "Office"
Workers = 30
Floors = 5

[OFFICE_SKYSCRAPER]
Name = "Wolkenkratzer"
Era = 1950-2000
ModelId = "rbxassetid://OFFICE_SKYSCRAPER"
Scale = Vector3(15, 40, 15)
BuildingType = "Office"
Workers = 200
Floors = 20
Features = {"Elevator", "Modern_Design"}

## ÖFFENTLICHE GEBÄUDE

### Verwaltung
[CITY_HALL_SMALL]
Name = "Kleines Rathaus"
Era = 1850-2000
ModelId = "rbxassetid://CITY_HALL_SMALL"
Scale = Vector3(20, 8, 25)
BuildingType = "Government"
Workers = 10
Services = {"Administration"}

[CITY_HALL_LARGE]
Name = "Großes Rathaus"
Era = 1900-2000
ModelId = "rbxassetid://CITY_HALL_LARGE"
Scale = Vector3(35, 12, 40)
BuildingType = "Government"
Workers = 50
Services = {"Administration", "Court", "Registry"}

### Bildung
[SCHOOL_ELEMENTARY]
Name = "Grundschule"
Era = 1850-2000
ModelId = "rbxassetid://SCHOOL_ELEMENTARY"
Scale = Vector3(25, 6, 30)
BuildingType = "Education"
Capacity = 200
Workers = 15

[UNIVERSITY]
Name = "Universität"
Era = 1900-2000
ModelId = "rbxassetid://UNIVERSITY"
Scale = Vector3(50, 15, 60)
BuildingType = "Education"
Capacity = 2000
Workers = 200
Features = {"Multiple_Buildings", "Campus"}

### Gesundheit
[HOSPITAL_SMALL]
Name = "Kleines Krankenhaus"
Era = 1880-2000
ModelId = "rbxassetid://HOSPITAL_SMALL"
Scale = Vector3(30, 8, 35)
BuildingType = "Healthcare"
Capacity = 50
Workers = 30

[HOSPITAL_LARGE]
Name = "Großes Krankenhaus"
Era = 1920-2000
ModelId = "rbxassetid://HOSPITAL_LARGE"
Scale = Vector3(50, 12, 60)
BuildingType = "Healthcare"
Capacity = 200
Workers = 150
Features = {"Emergency", "Surgery", "Parking"}

## FREIZEITGEBÄUDE

### Kultur
[THEATER]
Name = "Theater"
Era = 1850-2000
ModelId = "rbxassetid://THEATER"
Scale = Vector3(25, 12, 30)
BuildingType = "Entertainment"
Capacity = 300
Workers = 20

[CINEMA]
Name = "Kino"
Era = 1920-2000
ModelId = "rbxassetid://CINEMA"
Scale = Vector3(20, 8, 25)
BuildingType = "Entertainment"
Capacity = 150
Workers = 10

### Sport
[SPORTS_FIELD]
Name = "Sportplatz"
Era = 1850-2000
ModelId = "rbxassetid://SPORTS_FIELD"
Scale = Vector3(40, 2, 60)
BuildingType = "Recreation"
Capacity = 500
Workers = 5

[STADIUM]
Name = "Stadion"
Era = 1920-2000
ModelId = "rbxassetid://STADIUM"
Scale = Vector3(80, 20, 100)
BuildingType = "Recreation"
Capacity = 10000
Workers = 100
Features = {"Floodlights", "Parking", "VIP_Areas"}

## RELIGIÖSE GEBÄUDE

[CHURCH_SMALL]
Name = "Kleine Kirche"
Era = 1850-2000
ModelId = "rbxassetid://CHURCH_SMALL"
Scale = Vector3(15, 12, 25)
BuildingType = "Religious"
Capacity = 100
Workers = 2
Features = {"Bell_Tower", "Cemetery"}

[CATHEDRAL]
Name = "Kathedrale"
Era = 1850-2000
ModelId = "rbxassetid://CATHEDRAL"
Scale = Vector3(30, 25, 50)
BuildingType = "Religious"
Capacity = 500
Workers = 10
Features = {"Towers", "Stained_Glass", "Organ"}

## GEBÄUDE-KATEGORIEN

[BUILDING_CATEGORIES]
Residential = {
    "HOUSE_SMALL_WOODEN",
    "HOUSE_SMALL_BRICK",
    "APARTMENT_SMALL",
    "APARTMENT_MEDIUM",
    "APARTMENT_MODERN"
}

Commercial = {
    "SHOP_GENERAL",
    "MARKET_HALL",
    "SHOPPING_CENTER"
}

Office = {
    "OFFICE_SMALL",
    "OFFICE_SKYSCRAPER"
}

Public = {
    "CITY_HALL_SMALL",
    "CITY_HALL_LARGE",
    "SCHOOL_ELEMENTARY",
    "UNIVERSITY",
    "HOSPITAL_SMALL",
    "HOSPITAL_LARGE"
}

Entertainment = {
    "THEATER",
    "CINEMA",
    "SPORTS_FIELD",
    "STADIUM"
}

Religious = {
    "CHURCH_SMALL",
    "CATHEDRAL"
}

## STADT-ENTWICKLUNG

[CITY_GROWTH_STAGES]
Village = {
    Population = 0-500,
    Buildings = {"HOUSE_SMALL_WOODEN", "SHOP_GENERAL", "CHURCH_SMALL"}
}

Town = {
    Population = 500-2000,
    Buildings = {"HOUSE_SMALL_BRICK", "APARTMENT_SMALL", "MARKET_HALL", "SCHOOL_ELEMENTARY"}
}

City = {
    Population = 2000-10000,
    Buildings = {"APARTMENT_MEDIUM", "OFFICE_SMALL", "CITY_HALL_SMALL", "HOSPITAL_SMALL"}
}

Metropolis = {
    Population = 10000+,
    Buildings = {"APARTMENT_MODERN", "OFFICE_SKYSCRAPER", "SHOPPING_CENTER", "STADIUM"}
}
