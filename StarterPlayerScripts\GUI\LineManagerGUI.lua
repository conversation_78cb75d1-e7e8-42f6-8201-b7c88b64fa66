-- StarterPlayerScripts/GUI/LineManagerGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- <PERSON>ien-Management GUI wie in Transport Fever 2

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local BuildStationEvent = Events:WaitForChild("BuildStationEvent")
local CreateRouteEvent = Events:WaitForChild("CreateRouteEvent")
local CreateLineEvent = Events:WaitForChild("CreateLineEvent")
local GetStationDataFunction = Events:WaitForChild("GetStationDataFunction")

local LineManagerGUI = {}
LineManagerGUI.IsOpen = false
LineManagerGUI.CurrentMode = "STATIONS" -- STATIONS, ROUTES, LINES
LineManagerGUI.SelectedStations = {}
LineManagerGUI.StationData = {}

-- GUI erstellen
function LineManagerGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "LineManagerGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1000, 0, 700)
    mainFrame.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🚉 LINIEN-MANAGEMENT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "STATIONS", text = "🚉 Stationen", icon = "🚉"},
        {name = "ROUTES", text = "🛤️ Routen", icon = "🛤️"},
        {name = "LINES", text = "🚌 Linien", icon = "🚌"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentMode and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(50, 55, 60)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 570)
    contentFrame.Position = UDim2.new(0, 10, 0, 120)
    contentFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function LineManagerGUI:SwitchTab(tabName)
    self.CurrentMode = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function LineManagerGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentMode then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(50, 55, 60)
        end
    end
end

-- Content aktualisieren
function LineManagerGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentMode == "STATIONS" then
        self:CreateStationsContent()
    elseif self.CurrentMode == "ROUTES" then
        self:CreateRoutesContent()
    elseif self.CurrentMode == "LINES" then
        self:CreateLinesContent()
    end
end

-- Stationen-Content erstellen
function LineManagerGUI:CreateStationsContent()
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(0.7, 0, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    -- Stations-Liste
    local yPos = 10
    
    if self.StationData.stations then
        for stationId, station in pairs(self.StationData.stations) do
            local entry = self:CreateStationEntry(scrollFrame, station, yPos)
            yPos = yPos + 80
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
    
    -- Station-Bau Panel (rechts)
    local buildPanel = Instance.new("Frame")
    buildPanel.Size = UDim2.new(0.28, 0, 1, -10)
    buildPanel.Position = UDim2.new(0.72, 0, 0, 5)
    buildPanel.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    buildPanel.BorderSizePixel = 0
    buildPanel.Parent = self.ContentFrame
    
    local buildCorner = Instance.new("UICorner")
    buildCorner.CornerRadius = UDim.new(0, 8)
    buildCorner.Parent = buildPanel
    
    -- Titel für Bau-Panel
    local buildTitle = Instance.new("TextLabel")
    buildTitle.Size = UDim2.new(1, 0, 0, 40)
    buildTitle.BackgroundTransparency = 1
    buildTitle.Text = "🏗️ STATION BAUEN"
    buildTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    buildTitle.TextScaled = true
    buildTitle.Font = Enum.Font.SourceSansBold
    buildTitle.Parent = buildPanel
    
    -- Station-Typ Buttons
    local stationTypes = {
        {type = "TRAIN_STATION", name = "🚉 Bahnhof", cost = 10000},
        {type = "TRUCK_DEPOT", name = "🚛 LKW-Depot", cost = 5000},
        {type = "HARBOR", name = "⚓ Hafen", cost = 25000},
        {type = "BUS_STOP", name = "🚌 Bushaltestelle", cost = 2000}
    }
    
    local buttonY = 50
    for _, stationType in ipairs(stationTypes) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, -20, 0, 50)
        button.Position = UDim2.new(0, 10, 0, buttonY)
        button.BackgroundColor3 = Color3.fromRGB(60, 120, 180)
        button.Text = stationType.name .. "\n💰 " .. stationType.cost
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.BorderSizePixel = 0
        button.Parent = buildPanel
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:StartStationPlacement(stationType.type)
        end)
        
        buttonY = buttonY + 60
    end
end

-- Station-Eintrag erstellen
function LineManagerGUI:CreateStationEntry(parent, station, yPos)
    local entry = Instance.new("Frame")
    entry.Size = UDim2.new(1, -10, 0, 70)
    entry.Position = UDim2.new(0, 5, 0, yPos)
    entry.BackgroundColor3 = Color3.fromRGB(50, 55, 60)
    entry.BorderSizePixel = 0
    entry.Parent = parent
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 5)
    entryCorner.Parent = entry
    
    -- Station-Icon
    local icon = Instance.new("TextLabel")
    icon.Size = UDim2.new(0, 50, 0, 50)
    icon.Position = UDim2.new(0, 10, 0, 10)
    icon.BackgroundTransparency = 1
    icon.Text = station.type == "TRAIN_STATION" and "🚉" or 
                station.type == "TRUCK_DEPOT" and "🚛" or
                station.type == "HARBOR" and "⚓" or "🚌"
    icon.TextScaled = true
    icon.Font = Enum.Font.SourceSans
    icon.Parent = entry
    
    -- Station-Name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(0.4, 0, 0, 25)
    nameLabel.Position = UDim2.new(0, 70, 0, 10)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = station.name
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = entry
    
    -- Station-Info
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Size = UDim2.new(0.4, 0, 0, 20)
    infoLabel.Position = UDim2.new(0, 70, 0, 35)
    infoLabel.BackgroundTransparency = 1
    infoLabel.Text = string.format("👥 %d/%d | 🛤️ %d Routen", 
        station.waitingPassengers or 0, station.capacity, #station.connectedRoutes)
    infoLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.SourceSans
    infoLabel.TextXAlignment = Enum.TextXAlignment.Left
    infoLabel.Parent = entry
    
    -- Status-Indikator
    local statusIndicator = Instance.new("Frame")
    statusIndicator.Size = UDim2.new(0, 12, 0, 12)
    statusIndicator.Position = UDim2.new(1, -20, 0, 10)
    statusIndicator.BorderSizePixel = 0
    statusIndicator.BackgroundColor3 = station.isActive and Color3.fromRGB(0, 255, 0) or Color3.fromRGB(255, 0, 0)
    statusIndicator.Parent = entry
    
    local statusCorner = Instance.new("UICorner")
    statusCorner.CornerRadius = UDim.new(0, 6)
    statusCorner.Parent = statusIndicator
    
    return entry
end

-- Routen-Content erstellen
function LineManagerGUI:CreateRoutesContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🛤️ ROUTEN-MANAGEMENT\n\nWähle mindestens 2 Stationen aus,\num eine Route zu erstellen."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Linien-Content erstellen
function LineManagerGUI:CreateLinesContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🚌 LINIEN-MANAGEMENT\n\nErstelle Routen, um Linien\nmit Fahrzeugen zu betreiben."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Station-Platzierung starten
function LineManagerGUI:StartStationPlacement(stationType)
    print("🏗️ Station-Platzierung gestartet:", stationType)
    -- Hier würde die 3D-Platzierung implementiert werden
end

-- Daten laden
function LineManagerGUI:LoadStationData()
    local success, data = pcall(function()
        return GetStationDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.StationData = data
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Station-Daten")
    end
end

-- GUI öffnen
function LineManagerGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadStationData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function LineManagerGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.L then
        if LineManagerGUI.IsOpen then
            LineManagerGUI:CloseGUI()
        else
            LineManagerGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function LineManagerGUI:Initialize()
    print("🚉 LineManagerGUI initialisiert - Drücke 'L' zum Öffnen")
end

-- Auto-Start
LineManagerGUI:Initialize()

return LineManagerGUI
