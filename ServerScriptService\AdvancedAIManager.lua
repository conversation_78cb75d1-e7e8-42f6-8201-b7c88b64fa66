-- ServerScriptService/AdvancedAIManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Erweiterte KI-Systeme für intelligente Konkurrenten und Kooperation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local AdvancedAIManager = {}
AdvancedAIManager.__index = AdvancedAIManager

function AdvancedAIManager.new()
    local self = setmetatable({}, AdvancedAIManager)
    
    -- AI Configuration
    self.aiDifficulty = "medium" -- easy, medium, hard, expert
    self.maxAICompanies = 6
    self.aiLearningEnabled = true
    self.aiCooperationEnabled = true
    self.aiUpdateInterval = 3.0
    self.lastUpdate = 0
    
    -- AI Companies
    self.aiCompanies = {}
    self.aiAlliances = {}
    
    -- AI Personalities with detailed behavior patterns
    self.personalities = {
        aggressive = {
            name = "Aggressiv",
            description = "Expandiert schnell und konkurriert hart um Marktanteile",
            traits = {
                riskTolerance = 1.5,
                expansionSpeed = 1.4,
                priceAggression = 1.3,
                innovationFocus = 0.8,
                cooperationWillingness = 0.2,
                marketShareFocus = 1.6,
                profitMarginTolerance = 0.7
            },
            decisionWeights = {
                expansion = 0.4,
                competition = 0.3,
                efficiency = 0.1,
                innovation = 0.2
            }
        },
        
        conservative = {
            name = "Konservativ",
            description = "Fokussiert auf stabile Gewinne und nachhaltiges Wachstum",
            traits = {
                riskTolerance = 0.6,
                expansionSpeed = 0.7,
                priceAggression = 0.8,
                innovationFocus = 0.9,
                cooperationWillingness = 0.8,
                marketShareFocus = 0.8,
                profitMarginTolerance = 1.3
            },
            decisionWeights = {
                expansion = 0.2,
                competition = 0.2,
                efficiency = 0.4,
                innovation = 0.2
            }
        },
        
        innovative = {
            name = "Innovativ",
            description = "Investiert stark in neue Technologien und Effizienz",
            traits = {
                riskTolerance = 1.2,
                expansionSpeed = 0.9,
                priceAggression = 0.9,
                innovationFocus = 1.6,
                cooperationWillingness = 0.7,
                marketShareFocus = 1.0,
                profitMarginTolerance = 1.1
            },
            decisionWeights = {
                expansion = 0.2,
                competition = 0.2,
                efficiency = 0.2,
                innovation = 0.4
            }
        },
        
        opportunistic = {
            name = "Opportunistisch",
            description = "Nutzt Marktchancen und passt sich schnell an",
            traits = {
                riskTolerance = 1.3,
                expansionSpeed = 1.1,
                priceAggression = 1.2,
                innovationFocus = 1.1,
                cooperationWillingness = 0.5,
                marketShareFocus = 1.2,
                profitMarginTolerance = 0.9
            },
            decisionWeights = {
                expansion = 0.3,
                competition = 0.3,
                efficiency = 0.2,
                innovation = 0.2
            }
        },
        
        cooperative = {
            name = "Kooperativ",
            description = "Sucht Partnerschaften und gemeinsame Projekte",
            traits = {
                riskTolerance = 1.0,
                expansionSpeed = 0.8,
                priceAggression = 0.7,
                innovationFocus = 1.0,
                cooperationWillingness = 1.4,
                marketShareFocus = 0.9,
                profitMarginTolerance = 1.0
            },
            decisionWeights = {
                expansion = 0.2,
                competition = 0.1,
                efficiency = 0.3,
                innovation = 0.4
            }
        }
    }
    
    -- AI Learning system
    self.learningData = {
        playerBehaviorPatterns = {},
        marketTrends = {},
        successfulStrategies = {},
        failedStrategies = {}
    }
    
    -- AI Decision making system
    self.decisionSystem = {
        evaluationCriteria = {
            "profitability",
            "market_share",
            "growth_potential",
            "risk_assessment",
            "competition_level",
            "cooperation_opportunities"
        },
        
        strategicGoals = {
            "dominate_passenger_transport",
            "control_cargo_routes",
            "technological_leadership",
            "regional_expansion",
            "cost_efficiency",
            "market_diversification"
        }
    }
    
    return self
end

-- Initialize AI companies
function AdvancedAIManager:InitializeAICompanies(gameSettings)
    local numAI = math.min(gameSettings.aiCompetitors or 3, self.maxAICompanies)
    
    for i = 1, numAI do
        local company = self:CreateAICompany(i)
        self.aiCompanies[company.id] = company
        print("🤖 Created AI company:", company.name, "(" .. company.personality.name .. ")")
    end
end

-- Create AI company with personality
function AdvancedAIManager:CreateAICompany(index)
    local personalities = {"aggressive", "conservative", "innovative", "opportunistic", "cooperative"}
    local personalityType = personalities[((index - 1) % #personalities) + 1]
    local personality = self.personalities[personalityType]
    
    local companyNames = {
        "TransEuro Express", "Global Freight Solutions", "RapidRail Industries",
        "Continental Logistics", "MetroLink Transport", "SwiftCargo Systems",
        "EuroExpress Lines", "PrimeTrans Corporation", "InterCity Connect",
        "EliteFreight Services", "RegionalRail Network", "ExpressLink Transport"
    }
    
    local company = {
        id = HttpService:GenerateGUID(false),
        name = companyNames[index] or ("AI Transport " .. index),
        personality = personality,
        personalityType = personalityType,
        
        -- Financial data
        cash = 2000000 + math.random(-500000, 1000000),
        monthlyRevenue = 0,
        monthlyExpenses = 0,
        companyValue = 2000000,
        creditRating = math.random(60, 85),
        
        -- Operational data
        vehicles = {},
        routes = {},
        stations = {},
        reputation = math.random(40, 70),
        marketShare = 0,
        
        -- AI-specific data
        currentStrategy = nil,
        strategicGoals = {},
        decisionHistory = {},
        learningScore = 0,
        adaptationRate = 0.1,
        
        -- Relationships
        playerRelations = {}, -- Relations with human players
        aiRelations = {}, -- Relations with other AI
        currentAlliance = nil,
        
        -- Performance tracking
        performance = {
            profitability = 0,
            efficiency = 0,
            growth = 0,
            innovation = 0,
            cooperation = 0
        },
        
        -- Decision making state
        lastDecisionTime = 0,
        decisionCooldown = 10, -- seconds between major decisions
        currentFocus = "expansion", -- current strategic focus
        
        -- Market analysis
        marketKnowledge = {
            playerStrengths = {},
            marketOpportunities = {},
            threats = {},
            trends = {}
        }
    }
    
    -- Initialize strategic goals based on personality
    company.strategicGoals = self:GenerateStrategicGoals(company)
    
    return company
end

-- Generate strategic goals for AI company
function AdvancedAIManager:GenerateStrategicGoals(company)
    local goals = {}
    local personality = company.personality
    
    -- Primary goal based on personality
    if company.personalityType == "aggressive" then
        table.insert(goals, "dominate_passenger_transport")
        table.insert(goals, "control_cargo_routes")
    elseif company.personalityType == "conservative" then
        table.insert(goals, "cost_efficiency")
        table.insert(goals, "regional_expansion")
    elseif company.personalityType == "innovative" then
        table.insert(goals, "technological_leadership")
        table.insert(goals, "market_diversification")
    elseif company.personalityType == "opportunistic" then
        table.insert(goals, "market_diversification")
        table.insert(goals, "regional_expansion")
    elseif company.personalityType == "cooperative" then
        table.insert(goals, "cost_efficiency")
        table.insert(goals, "technological_leadership")
    end
    
    return goals
end

-- Advanced AI decision making
function AdvancedAIManager:MakeAIDecisions(company, gameData)
    local currentTime = tick()
    
    if currentTime - company.lastDecisionTime < company.decisionCooldown then
        return -- Still in cooldown
    end
    
    -- Analyze current situation
    local situation = self:AnalyzeMarketSituation(company, gameData)
    
    -- Make strategic decisions based on personality and situation
    local decisions = self:EvaluateStrategicOptions(company, situation)
    
    -- Execute top priority decision
    if #decisions > 0 then
        local topDecision = decisions[1]
        self:ExecuteAIDecision(company, topDecision, gameData)
        
        -- Record decision for learning
        table.insert(company.decisionHistory, {
            decision = topDecision,
            situation = situation,
            timestamp = currentTime
        })
        
        company.lastDecisionTime = currentTime
    end
end

-- Analyze market situation
function AdvancedAIManager:AnalyzeMarketSituation(company, gameData)
    return {
        marketGrowth = gameData.economicGrowth or 1.0,
        competitionLevel = self:CalculateCompetitionLevel(company, gameData),
        profitability = company.monthlyRevenue - company.monthlyExpenses,
        cashFlow = company.cash / math.max(company.monthlyExpenses, 1),
        marketShare = company.marketShare or 0,
        playerThreat = self:AssessPlayerThreat(company, gameData),
        cooperationOpportunities = self:FindCooperationOpportunities(company, gameData),
        expansionOpportunities = self:FindExpansionOpportunities(company, gameData)
    }
end

-- Calculate competition level
function AdvancedAIManager:CalculateCompetitionLevel(company, gameData)
    local totalCompetitors = 0
    local strongCompetitors = 0
    
    -- Count human players
    if gameData.activePlayers then
        totalCompetitors = totalCompetitors + gameData.activePlayers
        strongCompetitors = strongCompetitors + math.floor(gameData.activePlayers * 0.7)
    end
    
    -- Count AI competitors
    for _, aiCompany in pairs(self.aiCompanies) do
        if aiCompany.id ~= company.id then
            totalCompetitors = totalCompetitors + 1
            if aiCompany.marketShare > company.marketShare then
                strongCompetitors = strongCompetitors + 1
            end
        end
    end
    
    return {
        total = totalCompetitors,
        strong = strongCompetitors,
        intensity = strongCompetitors / math.max(totalCompetitors, 1)
    }
end

-- Assess player threat level
function AdvancedAIManager:AssessPlayerThreat(company, gameData)
    local threat = 0
    
    if gameData.playerRevenue and gameData.playerRevenue > company.monthlyRevenue then
        threat = threat + 0.3
    end
    
    if gameData.playerMarketShare and gameData.playerMarketShare > company.marketShare then
        threat = threat + 0.4
    end
    
    if gameData.playerGrowthRate and gameData.playerGrowthRate > 0.1 then
        threat = threat + 0.3
    end
    
    return math.min(threat, 1.0)
end

-- Find cooperation opportunities
function AdvancedAIManager:FindCooperationOpportunities(company, gameData)
    local opportunities = {}
    
    if not self.aiCooperationEnabled then
        return opportunities
    end
    
    -- Check for alliance opportunities with other AI
    for _, otherAI in pairs(self.aiCompanies) do
        if otherAI.id ~= company.id and not otherAI.currentAlliance then
            local compatibility = self:CalculateAllianceCompatibility(company, otherAI)
            if compatibility > 0.6 then
                table.insert(opportunities, {
                    type = "ai_alliance",
                    partner = otherAI.id,
                    compatibility = compatibility,
                    benefits = {"shared_costs", "market_expansion", "technology_sharing"}
                })
            end
        end
    end
    
    -- Check for player cooperation (if enabled)
    if gameData.playerCooperationEnabled then
        table.insert(opportunities, {
            type = "player_cooperation",
            benefits = {"infrastructure_sharing", "joint_ventures"},
            risk = 0.3
        })
    end
    
    return opportunities
end

-- Calculate alliance compatibility
function AdvancedAIManager:CalculateAllianceCompatibility(company1, company2)
    local compatibility = 0.5 -- Base compatibility
    
    -- Personality compatibility
    local p1 = company1.personality.traits
    local p2 = company2.personality.traits
    
    compatibility = compatibility + (p1.cooperationWillingness + p2.cooperationWillingness) * 0.2
    compatibility = compatibility - math.abs(p1.riskTolerance - p2.riskTolerance) * 0.1
    compatibility = compatibility - math.abs(p1.priceAggression - p2.priceAggression) * 0.1
    
    -- Market position compatibility (avoid direct competitors)
    if math.abs(company1.marketShare - company2.marketShare) < 0.1 then
        compatibility = compatibility - 0.2
    end
    
    return math.max(0, math.min(1, compatibility))
end

-- Find expansion opportunities
function AdvancedAIManager:FindExpansionOpportunities(company, gameData)
    local opportunities = {}
    
    -- Analyze underserved routes
    if gameData.underservedRoutes then
        for _, route in pairs(gameData.underservedRoutes) do
            local profitPotential = self:CalculateRouteProfitPotential(route, company)
            if profitPotential > 0.3 then
                table.insert(opportunities, {
                    type = "new_route",
                    route = route,
                    profitPotential = profitPotential,
                    investment = route.estimatedCost or 100000
                })
            end
        end
    end
    
    -- Analyze new markets
    if gameData.availableMarkets then
        for _, market in pairs(gameData.availableMarkets) do
            table.insert(opportunities, {
                type = "market_expansion",
                market = market,
                growthPotential = market.growthRate or 0.05,
                competition = market.competitionLevel or 0.5
            })
        end
    end
    
    return opportunities
end

-- Calculate route profit potential
function AdvancedAIManager:CalculateRouteProfitPotential(route, company)
    local basePotential = route.demand or 0.5
    local competitionFactor = 1.0 - (route.competition or 0.3)
    local personalityFactor = company.personality.traits.riskTolerance
    
    return basePotential * competitionFactor * personalityFactor
end

-- Update AI companies
function AdvancedAIManager:Update(deltaTime, gameData)
    self.lastUpdate = self.lastUpdate + deltaTime
    
    if self.lastUpdate >= self.aiUpdateInterval then
        for _, company in pairs(self.aiCompanies) do
            self:MakeAIDecisions(company, gameData)
            self:UpdateAIPerformance(company, gameData)
            
            if self.aiLearningEnabled then
                self:UpdateAILearning(company, gameData)
            end
        end
        
        self.lastUpdate = 0
    end
end

-- Update AI performance metrics
function AdvancedAIManager:UpdateAIPerformance(company, gameData)
    -- Calculate performance metrics
    local revenue = company.monthlyRevenue or 0
    local expenses = company.monthlyExpenses or 0
    local profit = revenue - expenses
    
    company.performance.profitability = profit / math.max(revenue, 1)
    company.performance.efficiency = revenue / math.max(expenses, 1)
    company.performance.growth = (revenue - (company.lastMonthRevenue or revenue)) / math.max(company.lastMonthRevenue or revenue, 1)
    
    company.lastMonthRevenue = revenue
end

-- Update AI learning
function AdvancedAIManager:UpdateAILearning(company, gameData)
    -- Learn from successful/failed decisions
    for i, decision in pairs(company.decisionHistory) do
        if tick() - decision.timestamp > 60 then -- Evaluate decisions after 1 minute
            local success = self:EvaluateDecisionSuccess(company, decision)
            
            if success then
                company.learningScore = company.learningScore + 0.1
                table.insert(self.learningData.successfulStrategies, {
                    decision = decision.decision,
                    situation = decision.situation,
                    personality = company.personalityType
                })
            else
                company.learningScore = company.learningScore - 0.05
                table.insert(self.learningData.failedStrategies, {
                    decision = decision.decision,
                    situation = decision.situation,
                    personality = company.personalityType
                })
            end
            
            -- Remove evaluated decision
            table.remove(company.decisionHistory, i)
        end
    end
    
    -- Adapt personality traits based on learning
    if company.learningScore > 1.0 then
        self:AdaptAIPersonality(company)
        company.learningScore = 0
    end
end

-- Evaluate decision success
function AdvancedAIManager:EvaluateDecisionSuccess(company, decision)
    -- Simple success evaluation based on performance improvement
    local currentPerformance = (company.performance.profitability + company.performance.efficiency + company.performance.growth) / 3
    local previousPerformance = decision.situation.previousPerformance or 0
    
    return currentPerformance > previousPerformance
end

-- Adapt AI personality based on learning
function AdvancedAIManager:AdaptAIPersonality(company)
    local adaptationRate = company.adaptationRate
    
    -- Slightly adjust personality traits based on successful strategies
    for _, strategy in pairs(self.learningData.successfulStrategies) do
        if strategy.personality == company.personalityType then
            -- Reinforce successful traits (simplified)
            if strategy.decision.type == "expansion" then
                company.personality.traits.expansionSpeed = company.personality.traits.expansionSpeed * (1 + adaptationRate)
            elseif strategy.decision.type == "cooperation" then
                company.personality.traits.cooperationWillingness = company.personality.traits.cooperationWillingness * (1 + adaptationRate)
            end
        end
    end
    
    print("🧠 AI", company.name, "adapted personality based on learning")
end

-- Get AI companies data
function AdvancedAIManager:GetAICompanies()
    return self.aiCompanies
end

-- Get AI company by ID
function AdvancedAIManager:GetAICompany(companyId)
    return self.aiCompanies[companyId]
end

return AdvancedAIManager
