-- ReplicatedStorage/Events/CreateRemoteEvents.lua
-- Erstellt alle RemoteEvents und RemoteFunctions für Client-Server-Kommunikation
-- ROBLOX SCRIPT TYPE: Script (Server-Side)

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Events-Ordner erstellen falls nicht vorhanden
local Events = ReplicatedStorage:FindFirstChild("Events")
if not Events then
    Events = Instance.new("Folder")
    Events.Name = "Events"
    Events.Parent = ReplicatedStorage
end

-- KARTEN-EVENTS
local GenerateMapEvent = Instance.new("RemoteEvent")
GenerateMapEvent.Name = "GenerateMapEvent"
GenerateMapEvent.Parent = Events

local MapGeneratedEvent = Instance.new("RemoteEvent")
MapGeneratedEvent.Name = "MapGeneratedEvent"
MapGeneratedEvent.Parent = Events

-- WIRTSCHAFTS-EVENTS
local EconomyUpdateEvent = Instance.new("RemoteEvent")
EconomyUpdateEvent.Name = "EconomyUpdateEvent"
EconomyUpdateEvent.Parent = Events

local CityDataEvent = Instance.new("RemoteEvent")
CityDataEvent.Name = "CityDataEvent"
CityDataEvent.Parent = Events

local IndustryDataEvent = Instance.new("RemoteEvent")
IndustryDataEvent.Name = "IndustryDataEvent"
IndustryDataEvent.Parent = Events

-- BUILDING-SYSTEM EVENTS
local BuildBuildingEvent = Instance.new("RemoteEvent")
BuildBuildingEvent.Name = "BuildBuildingEvent"
BuildBuildingEvent.Parent = Events

local BuildingCompleteEvent = Instance.new("RemoteEvent")
BuildingCompleteEvent.Name = "BuildingCompleteEvent"
BuildingCompleteEvent.Parent = Events

local BuildingDataEvent = Instance.new("RemoteEvent")
BuildingDataEvent.Name = "BuildingDataEvent"
BuildingDataEvent.Parent = Events

local UpgradeBuildingEvent = Instance.new("RemoteEvent")
UpgradeBuildingEvent.Name = "UpgradeBuildingEvent"
UpgradeBuildingEvent.Parent = Events

-- TECH-TREE EVENTS
local StartResearchEvent = Instance.new("RemoteEvent")
StartResearchEvent.Name = "StartResearchEvent"
StartResearchEvent.Parent = Events

local ResearchCompleteEvent = Instance.new("RemoteEvent")
ResearchCompleteEvent.Name = "ResearchCompleteEvent"
ResearchCompleteEvent.Parent = Events

local TechDataEvent = Instance.new("RemoteEvent")
TechDataEvent.Name = "TechDataEvent"
TechDataEvent.Parent = Events

-- TRANSPORT-EVENTS
local CreateVehicleEvent = Instance.new("RemoteEvent")
CreateVehicleEvent.Name = "CreateVehicleEvent"
CreateVehicleEvent.Parent = Events

local VehicleUpdateEvent = Instance.new("RemoteEvent")
VehicleUpdateEvent.Name = "VehicleUpdateEvent"
VehicleUpdateEvent.Parent = Events

local BuildInfrastructureEvent = Instance.new("RemoteEvent")
BuildInfrastructureEvent.Name = "BuildInfrastructureEvent"
BuildInfrastructureEvent.Parent = Events

-- SPIELZUSTAND-EVENTS
local GameStateUpdateEvent = Instance.new("RemoteEvent")
GameStateUpdateEvent.Name = "GameStateUpdateEvent"
GameStateUpdateEvent.Parent = Events

local TimeUpdateEvent = Instance.new("RemoteEvent")
TimeUpdateEvent.Name = "TimeUpdateEvent"
TimeUpdateEvent.Parent = Events

local SpeedChangeEvent = Instance.new("RemoteEvent")
SpeedChangeEvent.Name = "SpeedChangeEvent"
SpeedChangeEvent.Parent = Events

-- SPEICHER-EVENTS
local SaveGameEvent = Instance.new("RemoteEvent")
SaveGameEvent.Name = "SaveGameEvent"
SaveGameEvent.Parent = Events

local LoadGameEvent = Instance.new("RemoteEvent")
LoadGameEvent.Name = "LoadGameEvent"
LoadGameEvent.Parent = Events

local GameSavedEvent = Instance.new("RemoteEvent")
GameSavedEvent.Name = "GameSavedEvent"
GameSavedEvent.Parent = Events

local GameLoadedEvent = Instance.new("RemoteEvent")
GameLoadedEvent.Name = "GameLoadedEvent"
GameLoadedEvent.Parent = Events

-- GUI-EVENTS
local NotificationEvent = Instance.new("RemoteEvent")
NotificationEvent.Name = "NotificationEvent"
NotificationEvent.Parent = Events

local PlayerJoinedEvent = Instance.new("RemoteEvent")
PlayerJoinedEvent.Name = "PlayerJoinedEvent"
PlayerJoinedEvent.Parent = Events

local MenuStateEvent = Instance.new("RemoteEvent")
MenuStateEvent.Name = "MenuStateEvent"
MenuStateEvent.Parent = Events

-- REMOTE FUNCTIONS (für synchrone Datenabfragen)
local GetPlayerDataFunction = Instance.new("RemoteFunction")
GetPlayerDataFunction.Name = "GetPlayerDataFunction"
GetPlayerDataFunction.Parent = Events

local GetEconomyDataFunction = Instance.new("RemoteFunction")
GetEconomyDataFunction.Name = "GetEconomyDataFunction"
GetEconomyDataFunction.Parent = Events

local GetVehicleDataFunction = Instance.new("RemoteFunction")
GetVehicleDataFunction.Name = "GetVehicleDataFunction"
GetVehicleDataFunction.Parent = Events

local GetSaveDataFunction = Instance.new("RemoteFunction")
GetSaveDataFunction.Name = "GetSaveDataFunction"
GetSaveDataFunction.Parent = Events

local GetBuildingDataFunction = Instance.new("RemoteFunction")
GetBuildingDataFunction.Name = "GetBuildingDataFunction"
GetBuildingDataFunction.Parent = Events

local GetTechDataFunction = Instance.new("RemoteFunction")
GetTechDataFunction.Name = "GetTechDataFunction"
GetTechDataFunction.Parent = Events

print("✅ Alle RemoteEvents und RemoteFunctions erstellt!")

-- STADT-EVENTS
local GetCityStatsEvent = Instance.new("RemoteEvent")
GetCityStatsEvent.Name = "GetCityStatsEvent"
GetCityStatsEvent.Parent = Events

local CityStatsResponseEvent = Instance.new("RemoteEvent")
CityStatsResponseEvent.Name = "CityStatsResponseEvent"
CityStatsResponseEvent.Parent = Events

local RenameCityEvent = Instance.new("RemoteEvent")
RenameCityEvent.Name = "RenameCityEvent"
RenameCityEvent.Parent = Events

local CityRenamedEvent = Instance.new("RemoteEvent")
CityRenamedEvent.Name = "CityRenamedEvent"
CityRenamedEvent.Parent = Events

local GetAllCitiesEvent = Instance.new("RemoteEvent")
GetAllCitiesEvent.Name = "GetAllCitiesEvent"
GetAllCitiesEvent.Parent = Events

local AllCitiesResponseEvent = Instance.new("RemoteEvent")
AllCitiesResponseEvent.Name = "AllCitiesResponseEvent"
AllCitiesResponseEvent.Parent = Events

local CityGrowthEvent = Instance.new("RemoteEvent")
CityGrowthEvent.Name = "CityGrowthEvent"
CityGrowthEvent.Parent = Events

local IndustryUpgradeEvent = Instance.new("RemoteEvent")
IndustryUpgradeEvent.Name = "IndustryUpgradeEvent"
IndustryUpgradeEvent.Parent = Events

-- Event-Übersicht ausgeben
print("📡 Verfügbare Events:")
for _, child in pairs(Events:GetChildren()) do
    if child:IsA("RemoteEvent") then
        print("  🔄 RemoteEvent:", child.Name)
    elseif child:IsA("RemoteFunction") then
        print("  🔁 RemoteFunction:", child.Name)
    end
end
