-- ReplicatedStorage/Events/CreateRemoteEvents.lua
-- Erstellt alle RemoteEvents und RemoteFunctions für Client-Server-Kommunikation
-- ROBLOX SCRIPT TYPE: ModuleScript

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Events-Ordner erstellen falls nicht vorhanden
local Events = ReplicatedStorage:FindFirstChild("Events")
if not Events then
    Events = Instance.new("Folder")
    Events.Name = "Events"
    Events.Parent = ReplicatedStorage
end

-- KARTEN-EVENTS
local GenerateMapEvent = Instance.new("RemoteEvent")
GenerateMapEvent.Name = "GenerateMapEvent"
GenerateMapEvent.Parent = Events

local MapGeneratedEvent = Instance.new("RemoteEvent")
MapGeneratedEvent.Name = "MapGeneratedEvent"
MapGeneratedEvent.Parent = Events

-- WIRTSCHAFTS-EVENTS
local EconomyUpdateEvent = Instance.new("RemoteEvent")
EconomyUpdateEvent.Name = "EconomyUpdateEvent"
EconomyUpdateEvent.Parent = Events

local CityDataEvent = Instance.new("RemoteEvent")
CityDataEvent.Name = "CityDataEvent"
CityDataEvent.Parent = Events

local IndustryDataEvent = Instance.new("RemoteEvent")
IndustryDataEvent.Name = "IndustryDataEvent"
IndustryDataEvent.Parent = Events

-- BUILDING-SYSTEM EVENTS
local BuildBuildingEvent = Instance.new("RemoteEvent")
BuildBuildingEvent.Name = "BuildBuildingEvent"
BuildBuildingEvent.Parent = Events

local BuildingCompleteEvent = Instance.new("RemoteEvent")
BuildingCompleteEvent.Name = "BuildingCompleteEvent"
BuildingCompleteEvent.Parent = Events

local BuildingDataEvent = Instance.new("RemoteEvent")
BuildingDataEvent.Name = "BuildingDataEvent"
BuildingDataEvent.Parent = Events

local UpgradeBuildingEvent = Instance.new("RemoteEvent")
UpgradeBuildingEvent.Name = "UpgradeBuildingEvent"
UpgradeBuildingEvent.Parent = Events

-- TECH-TREE EVENTS
local StartResearchEvent = Instance.new("RemoteEvent")
StartResearchEvent.Name = "StartResearchEvent"
StartResearchEvent.Parent = Events

local ResearchCompleteEvent = Instance.new("RemoteEvent")
ResearchCompleteEvent.Name = "ResearchCompleteEvent"
ResearchCompleteEvent.Parent = Events

local TechDataEvent = Instance.new("RemoteEvent")
TechDataEvent.Name = "TechDataEvent"
TechDataEvent.Parent = Events

-- TRANSPORT-EVENTS
local CreateVehicleEvent = Instance.new("RemoteEvent")
CreateVehicleEvent.Name = "CreateVehicleEvent"
CreateVehicleEvent.Parent = Events

local VehicleUpdateEvent = Instance.new("RemoteEvent")
VehicleUpdateEvent.Name = "VehicleUpdateEvent"
VehicleUpdateEvent.Parent = Events

local BuildInfrastructureEvent = Instance.new("RemoteEvent")
BuildInfrastructureEvent.Name = "BuildInfrastructureEvent"
BuildInfrastructureEvent.Parent = Events

-- SPIELZUSTAND-EVENTS
local GameStateUpdateEvent = Instance.new("RemoteEvent")
GameStateUpdateEvent.Name = "GameStateUpdateEvent"
GameStateUpdateEvent.Parent = Events

local TimeUpdateEvent = Instance.new("RemoteEvent")
TimeUpdateEvent.Name = "TimeUpdateEvent"
TimeUpdateEvent.Parent = Events

local SpeedChangeEvent = Instance.new("RemoteEvent")
SpeedChangeEvent.Name = "SpeedChangeEvent"
SpeedChangeEvent.Parent = Events

-- SPEICHER-EVENTS
local SaveGameEvent = Instance.new("RemoteEvent")
SaveGameEvent.Name = "SaveGameEvent"
SaveGameEvent.Parent = Events

local LoadGameEvent = Instance.new("RemoteEvent")
LoadGameEvent.Name = "LoadGameEvent"
LoadGameEvent.Parent = Events

local GameSavedEvent = Instance.new("RemoteEvent")
GameSavedEvent.Name = "GameSavedEvent"
GameSavedEvent.Parent = Events

local GameLoadedEvent = Instance.new("RemoteEvent")
GameLoadedEvent.Name = "GameLoadedEvent"
GameLoadedEvent.Parent = Events

-- GUI-EVENTS
local NotificationEvent = Instance.new("RemoteEvent")
NotificationEvent.Name = "NotificationEvent"
NotificationEvent.Parent = Events

local PlayerJoinedEvent = Instance.new("RemoteEvent")
PlayerJoinedEvent.Name = "PlayerJoinedEvent"
PlayerJoinedEvent.Parent = Events

local MenuStateEvent = Instance.new("RemoteEvent")
MenuStateEvent.Name = "MenuStateEvent"
MenuStateEvent.Parent = Events

-- REMOTE FUNCTIONS (für synchrone Datenabfragen)
local GetPlayerDataFunction = Instance.new("RemoteFunction")
GetPlayerDataFunction.Name = "GetPlayerDataFunction"
GetPlayerDataFunction.Parent = Events

local GetEconomyDataFunction = Instance.new("RemoteFunction")
GetEconomyDataFunction.Name = "GetEconomyDataFunction"
GetEconomyDataFunction.Parent = Events

local GetVehicleDataFunction = Instance.new("RemoteFunction")
GetVehicleDataFunction.Name = "GetVehicleDataFunction"
GetVehicleDataFunction.Parent = Events

local GetSaveDataFunction = Instance.new("RemoteFunction")
GetSaveDataFunction.Name = "GetSaveDataFunction"
GetSaveDataFunction.Parent = Events

local GetBuildingDataFunction = Instance.new("RemoteFunction")
GetBuildingDataFunction.Name = "GetBuildingDataFunction"
GetBuildingDataFunction.Parent = Events

local GetTechDataFunction = Instance.new("RemoteFunction")
GetTechDataFunction.Name = "GetTechDataFunction"
GetTechDataFunction.Parent = Events

print("✅ Alle RemoteEvents und RemoteFunctions erstellt!")

-- STADT-EVENTS
local GetCityStatsEvent = Instance.new("RemoteEvent")
GetCityStatsEvent.Name = "GetCityStatsEvent"
GetCityStatsEvent.Parent = Events

local CityStatsResponseEvent = Instance.new("RemoteEvent")
CityStatsResponseEvent.Name = "CityStatsResponseEvent"
CityStatsResponseEvent.Parent = Events

local RenameCityEvent = Instance.new("RemoteEvent")
RenameCityEvent.Name = "RenameCityEvent"
RenameCityEvent.Parent = Events

local CityRenamedEvent = Instance.new("RemoteEvent")
CityRenamedEvent.Name = "CityRenamedEvent"
CityRenamedEvent.Parent = Events

local GetAllCitiesEvent = Instance.new("RemoteEvent")
GetAllCitiesEvent.Name = "GetAllCitiesEvent"
GetAllCitiesEvent.Parent = Events

local AllCitiesResponseEvent = Instance.new("RemoteEvent")
AllCitiesResponseEvent.Name = "AllCitiesResponseEvent"
AllCitiesResponseEvent.Parent = Events

local CityGrowthEvent = Instance.new("RemoteEvent")
CityGrowthEvent.Name = "CityGrowthEvent"
CityGrowthEvent.Parent = Events

local IndustryUpgradeEvent = Instance.new("RemoteEvent")
IndustryUpgradeEvent.Name = "IndustryUpgradeEvent"
IndustryUpgradeEvent.Parent = Events

-- Additional Industry Events
local GetIndustryStatsEvent = Instance.new("RemoteEvent")
GetIndustryStatsEvent.Name = "GetIndustryStatsEvent"
GetIndustryStatsEvent.Parent = Events

local IndustryStatsResponseEvent = Instance.new("RemoteEvent")
IndustryStatsResponseEvent.Name = "IndustryStatsResponseEvent"
IndustryStatsResponseEvent.Parent = Events

local IndustryUpgradeResponseEvent = Instance.new("RemoteEvent")
IndustryUpgradeResponseEvent.Name = "IndustryUpgradeResponseEvent"
IndustryUpgradeResponseEvent.Parent = Events

-- FINANZ-EVENTS
local GetFinancialSummaryEvent = Instance.new("RemoteEvent")
GetFinancialSummaryEvent.Name = "GetFinancialSummaryEvent"
GetFinancialSummaryEvent.Parent = Events

local FinancialSummaryResponseEvent = Instance.new("RemoteEvent")
FinancialSummaryResponseEvent.Name = "FinancialSummaryResponseEvent"
FinancialSummaryResponseEvent.Parent = Events

local ApplyForLoanEvent = Instance.new("RemoteEvent")
ApplyForLoanEvent.Name = "ApplyForLoanEvent"
ApplyForLoanEvent.Parent = Events

local LoanApplicationResponseEvent = Instance.new("RemoteEvent")
LoanApplicationResponseEvent.Name = "LoanApplicationResponseEvent"
LoanApplicationResponseEvent.Parent = Events

-- KI-KONKURRENTEN-EVENTS
local GetAICompetitorsEvent = Instance.new("RemoteEvent")
GetAICompetitorsEvent.Name = "GetAICompetitorsEvent"
GetAICompetitorsEvent.Parent = Events

local AICompetitorsResponseEvent = Instance.new("RemoteEvent")
AICompetitorsResponseEvent.Name = "AICompetitorsResponseEvent"
AICompetitorsResponseEvent.Parent = Events

local GetMarketOverviewEvent = Instance.new("RemoteEvent")
GetMarketOverviewEvent.Name = "GetMarketOverviewEvent"
GetMarketOverviewEvent.Parent = Events

local MarketOverviewResponseEvent = Instance.new("RemoteEvent")
MarketOverviewResponseEvent.Name = "MarketOverviewResponseEvent"
MarketOverviewResponseEvent.Parent = Events

-- KAMPAGNEN-EVENTS
local GetAvailableCampaignsEvent = Instance.new("RemoteEvent")
GetAvailableCampaignsEvent.Name = "GetAvailableCampaignsEvent"
GetAvailableCampaignsEvent.Parent = Events

local AvailableCampaignsResponseEvent = Instance.new("RemoteEvent")
AvailableCampaignsResponseEvent.Name = "AvailableCampaignsResponseEvent"
AvailableCampaignsResponseEvent.Parent = Events

local StartCampaignEvent = Instance.new("RemoteEvent")
StartCampaignEvent.Name = "StartCampaignEvent"
StartCampaignEvent.Parent = Events

local CampaignStartResponseEvent = Instance.new("RemoteEvent")
CampaignStartResponseEvent.Name = "CampaignStartResponseEvent"
CampaignStartResponseEvent.Parent = Events

local GetCampaignProgressEvent = Instance.new("RemoteEvent")
GetCampaignProgressEvent.Name = "GetCampaignProgressEvent"
GetCampaignProgressEvent.Parent = Events

local CampaignProgressResponseEvent = Instance.new("RemoteEvent")
CampaignProgressResponseEvent.Name = "CampaignProgressResponseEvent"
CampaignProgressResponseEvent.Parent = Events

local ResearchTechnologyEvent = Instance.new("RemoteEvent")
ResearchTechnologyEvent.Name = "ResearchTechnologyEvent"
ResearchTechnologyEvent.Parent = Events

local TechnologyResearchResponseEvent = Instance.new("RemoteEvent")
TechnologyResearchResponseEvent.Name = "TechnologyResearchResponseEvent"
TechnologyResearchResponseEvent.Parent = Events

-- ACHIEVEMENT-EVENTS
local GetPlayerAchievementsEvent = Instance.new("RemoteEvent")
GetPlayerAchievementsEvent.Name = "GetPlayerAchievementsEvent"
GetPlayerAchievementsEvent.Parent = Events

local PlayerAchievementsResponseEvent = Instance.new("RemoteEvent")
PlayerAchievementsResponseEvent.Name = "PlayerAchievementsResponseEvent"
PlayerAchievementsResponseEvent.Parent = Events

local GetAchievementStatsEvent = Instance.new("RemoteEvent")
GetAchievementStatsEvent.Name = "GetAchievementStatsEvent"
GetAchievementStatsEvent.Parent = Events

local AchievementStatsResponseEvent = Instance.new("RemoteEvent")
AchievementStatsResponseEvent.Name = "AchievementStatsResponseEvent"
AchievementStatsResponseEvent.Parent = Events

-- MULTIPLAYER-EVENTS
local GetConnectedPlayersEvent = Instance.new("RemoteEvent")
GetConnectedPlayersEvent.Name = "GetConnectedPlayersEvent"
GetConnectedPlayersEvent.Parent = Events

local ConnectedPlayersResponseEvent = Instance.new("RemoteEvent")
ConnectedPlayersResponseEvent.Name = "ConnectedPlayersResponseEvent"
ConnectedPlayersResponseEvent.Parent = Events

local SendChatMessageEvent = Instance.new("RemoteEvent")
SendChatMessageEvent.Name = "SendChatMessageEvent"
SendChatMessageEvent.Parent = Events

local ChatMessageBroadcastEvent = Instance.new("RemoteEvent")
ChatMessageBroadcastEvent.Name = "ChatMessageBroadcastEvent"
ChatMessageBroadcastEvent.Parent = Events

-- ALLIANCE-EVENTS
local CreateAllianceEvent = Instance.new("RemoteEvent")
CreateAllianceEvent.Name = "CreateAllianceEvent"
CreateAllianceEvent.Parent = Events

local AllianceCreationResponseEvent = Instance.new("RemoteEvent")
AllianceCreationResponseEvent.Name = "AllianceCreationResponseEvent"
AllianceCreationResponseEvent.Parent = Events

local InviteToAllianceEvent = Instance.new("RemoteEvent")
InviteToAllianceEvent.Name = "InviteToAllianceEvent"
InviteToAllianceEvent.Parent = Events

local AllianceInviteResponseEvent = Instance.new("RemoteEvent")
AllianceInviteResponseEvent.Name = "AllianceInviteResponseEvent"
AllianceInviteResponseEvent.Parent = Events

local AcceptAllianceInviteEvent = Instance.new("RemoteEvent")
AcceptAllianceInviteEvent.Name = "AcceptAllianceInviteEvent"
AcceptAllianceInviteEvent.Parent = Events

local AllianceJoinResponseEvent = Instance.new("RemoteEvent")
AllianceJoinResponseEvent.Name = "AllianceJoinResponseEvent"
AllianceJoinResponseEvent.Parent = Events

local LeaveAllianceEvent = Instance.new("RemoteEvent")
LeaveAllianceEvent.Name = "LeaveAllianceEvent"
LeaveAllianceEvent.Parent = Events

local AllianceLeaveResponseEvent = Instance.new("RemoteEvent")
AllianceLeaveResponseEvent.Name = "AllianceLeaveResponseEvent"
AllianceLeaveResponseEvent.Parent = Events

local GetPlayerAllianceEvent = Instance.new("RemoteEvent")
GetPlayerAllianceEvent.Name = "GetPlayerAllianceEvent"
GetPlayerAllianceEvent.Parent = Events

local PlayerAllianceResponseEvent = Instance.new("RemoteEvent")
PlayerAllianceResponseEvent.Name = "PlayerAllianceResponseEvent"
PlayerAllianceResponseEvent.Parent = Events

-- TRADE-EVENTS
local CreateTradeOfferEvent = Instance.new("RemoteEvent")
CreateTradeOfferEvent.Name = "CreateTradeOfferEvent"
CreateTradeOfferEvent.Parent = Events

local TradeOfferResponseEvent = Instance.new("RemoteEvent")
TradeOfferResponseEvent.Name = "TradeOfferResponseEvent"
TradeOfferResponseEvent.Parent = Events

local AcceptTradeOfferEvent = Instance.new("RemoteEvent")
AcceptTradeOfferEvent.Name = "AcceptTradeOfferEvent"
AcceptTradeOfferEvent.Parent = Events

local TradeAcceptResponseEvent = Instance.new("RemoteEvent")
TradeAcceptResponseEvent.Name = "TradeAcceptResponseEvent"
TradeAcceptResponseEvent.Parent = Events

local GetPlayerTradeOffersEvent = Instance.new("RemoteEvent")
GetPlayerTradeOffersEvent.Name = "GetPlayerTradeOffersEvent"
GetPlayerTradeOffersEvent.Parent = Events

local PlayerTradeOffersResponseEvent = Instance.new("RemoteEvent")
PlayerTradeOffersResponseEvent.Name = "PlayerTradeOffersResponseEvent"
PlayerTradeOffersResponseEvent.Parent = Events

local TradeNotificationEvent = Instance.new("RemoteEvent")
TradeNotificationEvent.Name = "TradeNotificationEvent"
TradeNotificationEvent.Parent = Events

-- COOPERATION PROJECT EVENTS
local CreateCooperationProjectEvent = Instance.new("RemoteEvent")
CreateCooperationProjectEvent.Name = "CreateCooperationProjectEvent"
CreateCooperationProjectEvent.Parent = Events

local CooperationProjectResponseEvent = Instance.new("RemoteEvent")
CooperationProjectResponseEvent.Name = "CooperationProjectResponseEvent"
CooperationProjectResponseEvent.Parent = Events

local InviteToProjectEvent = Instance.new("RemoteEvent")
InviteToProjectEvent.Name = "InviteToProjectEvent"
InviteToProjectEvent.Parent = Events

local ProjectInviteResponseEvent = Instance.new("RemoteEvent")
ProjectInviteResponseEvent.Name = "ProjectInviteResponseEvent"
ProjectInviteResponseEvent.Parent = Events

local GetCooperationProjectsEvent = Instance.new("RemoteEvent")
GetCooperationProjectsEvent.Name = "GetCooperationProjectsEvent"
GetCooperationProjectsEvent.Parent = Events

local CooperationProjectsResponseEvent = Instance.new("RemoteEvent")
CooperationProjectsResponseEvent.Name = "CooperationProjectsResponseEvent"
CooperationProjectsResponseEvent.Parent = Events

-- Event-Übersicht ausgeben
print("📡 Verfügbare Events:")
for _, child in pairs(Events:GetChildren()) do
    if child:IsA("RemoteEvent") then
        print("  🔄 RemoteEvent:", child.Name)
    elseif child:IsA("RemoteFunction") then
        print("  🔁 RemoteFunction:", child.Name)
    end
end

-- ModuleScript Return
local CreateRemoteEvents = {}

function CreateRemoteEvents.Initialize()
    print("✅ Alle RemoteEvents und RemoteFunctions erfolgreich erstellt!")
    return true
end

return CreateRemoteEvents
