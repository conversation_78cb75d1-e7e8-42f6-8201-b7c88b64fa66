-- StarterGui/AudioSettingsGUI.lua
-- RO<PERSON><PERSON> SCRIPT TYPE: LocalScript
-- Audio-Einstellungen GUI für Spieler

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local SoundService = game:GetService("SoundService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local AudioSettingsGUI = {}

-- Audio settings state
local audioSettings = {
    masterVolume = 0.8,
    musicVolume = 0.6,
    sfxVolume = 0.8,
    ambientVolume = 0.4,
    voiceVolume = 0.7,
    uiVolume = 0.5,
    muteAll = false,
    dynamicMusic = true,
    spatialAudio = true,
    audioQuality = "High"
}

local isAudioGUIOpen = false

-- Create audio settings interface
function AudioSettingsGUI.CreateAudioInterface()
    -- Main screen GUI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "AudioSettingsGUI"
    screenGui.Parent = playerGui
    screenGui.ResetOnSpawn = false
    
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 600, 0, 500)
    mainFrame.Position = UDim2.new(0.5, -300, 0.5, -250)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Add corner rounding
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleBar
    
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, -100, 1, 0)
    titleLabel.Position = UDim2.new(0, 20, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🔊 Audio-Einstellungen"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.TextXAlignment = Enum.TextXAlignment.Left
    titleLabel.Parent = titleBar
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 60, 60)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Content area
    local contentFrame = Instance.new("ScrollingFrame")
    contentFrame.Name = "ContentFrame"
    contentFrame.Size = UDim2.new(1, -20, 1, -70)
    contentFrame.Position = UDim2.new(0, 10, 0, 60)
    contentFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.ScrollBarThickness = 8
    contentFrame.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    local layout = Instance.new("UIListLayout")
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 10)
    layout.Parent = contentFrame
    
    -- Volume controls
    AudioSettingsGUI.CreateVolumeControls(contentFrame)
    
    -- Audio options
    AudioSettingsGUI.CreateAudioOptions(contentFrame)
    
    -- Audio quality settings
    AudioSettingsGUI.CreateQualitySettings(contentFrame)
    
    -- Apply/Reset buttons
    AudioSettingsGUI.CreateActionButtons(contentFrame)
    
    -- Close button functionality
    closeButton.MouseButton1Click:Connect(function()
        AudioSettingsGUI.ToggleAudioGUI()
    end)
    
    return screenGui
end

-- Create volume control sliders
function AudioSettingsGUI.CreateVolumeControls(parent)
    local volumeFrame = Instance.new("Frame")
    volumeFrame.Name = "VolumeFrame"
    volumeFrame.Size = UDim2.new(1, -20, 0, 300)
    volumeFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    volumeFrame.BorderSizePixel = 0
    volumeFrame.LayoutOrder = 1
    volumeFrame.Parent = parent
    
    local volumeCorner = Instance.new("UICorner")
    volumeCorner.CornerRadius = UDim.new(0, 8)
    volumeCorner.Parent = volumeFrame
    
    local volumeTitle = Instance.new("TextLabel")
    volumeTitle.Name = "VolumeTitle"
    volumeTitle.Size = UDim2.new(1, -20, 0, 30)
    volumeTitle.Position = UDim2.new(0, 10, 0, 10)
    volumeTitle.BackgroundTransparency = 1
    volumeTitle.Text = "🔊 Lautstärke-Einstellungen"
    volumeTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    volumeTitle.TextScaled = true
    volumeTitle.Font = Enum.Font.SourceSansBold
    volumeTitle.TextXAlignment = Enum.TextXAlignment.Left
    volumeTitle.Parent = volumeFrame
    
    local volumeLayout = Instance.new("UIListLayout")
    volumeLayout.SortOrder = Enum.SortOrder.LayoutOrder
    volumeLayout.Padding = UDim.new(0, 8)
    volumeLayout.Parent = volumeFrame
    
    -- Volume sliders
    local volumeTypes = {
        {name = "Master", key = "masterVolume", icon = "🔊"},
        {name = "Musik", key = "musicVolume", icon = "🎵"},
        {name = "Sound-Effekte", key = "sfxVolume", icon = "🔔"},
        {name = "Umgebung", key = "ambientVolume", icon = "🌊"},
        {name = "Stimmen", key = "voiceVolume", icon = "🗣️"},
        {name = "UI-Sounds", key = "uiVolume", icon = "🖱️"}
    }
    
    for i, volumeType in pairs(volumeTypes) do
        local sliderFrame = AudioSettingsGUI.CreateVolumeSlider(
            volumeType.name, 
            volumeType.key, 
            volumeType.icon, 
            audioSettings[volumeType.key]
        )
        sliderFrame.LayoutOrder = i + 1
        sliderFrame.Parent = volumeFrame
    end
    
    -- Update canvas size
    parent.CanvasSize = UDim2.new(0, 0, 0, volumeLayout.AbsoluteContentSize.Y + 20)
end

-- Create individual volume slider
function AudioSettingsGUI.CreateVolumeSlider(name, key, icon, initialValue)
    local sliderFrame = Instance.new("Frame")
    sliderFrame.Name = key .. "Slider"
    sliderFrame.Size = UDim2.new(1, -20, 0, 40)
    sliderFrame.BackgroundTransparency = 1
    
    local label = Instance.new("TextLabel")
    label.Name = "Label"
    label.Size = UDim2.new(0, 150, 1, 0)
    label.Position = UDim2.new(0, 10, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = icon .. " " .. name
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = sliderFrame
    
    -- Slider background
    local sliderBg = Instance.new("Frame")
    sliderBg.Name = "SliderBackground"
    sliderBg.Size = UDim2.new(0, 200, 0, 20)
    sliderBg.Position = UDim2.new(0, 170, 0, 10)
    sliderBg.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    sliderBg.BorderSizePixel = 0
    sliderBg.Parent = sliderFrame
    
    local sliderBgCorner = Instance.new("UICorner")
    sliderBgCorner.CornerRadius = UDim.new(0, 10)
    sliderBgCorner.Parent = sliderBg
    
    -- Slider fill
    local sliderFill = Instance.new("Frame")
    sliderFill.Name = "SliderFill"
    sliderFill.Size = UDim2.new(initialValue, 0, 1, 0)
    sliderFill.Position = UDim2.new(0, 0, 0, 0)
    sliderFill.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    sliderFill.BorderSizePixel = 0
    sliderFill.Parent = sliderBg
    
    local sliderFillCorner = Instance.new("UICorner")
    sliderFillCorner.CornerRadius = UDim.new(0, 10)
    sliderFillCorner.Parent = sliderFill
    
    -- Slider handle
    local sliderHandle = Instance.new("Frame")
    sliderHandle.Name = "SliderHandle"
    sliderHandle.Size = UDim2.new(0, 20, 0, 30)
    sliderHandle.Position = UDim2.new(initialValue, -10, 0, -5)
    sliderHandle.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    sliderHandle.BorderSizePixel = 0
    sliderHandle.Parent = sliderBg
    
    local handleCorner = Instance.new("UICorner")
    handleCorner.CornerRadius = UDim.new(0, 15)
    handleCorner.Parent = sliderHandle
    
    -- Value label
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Name = "ValueLabel"
    valueLabel.Size = UDim2.new(0, 50, 1, 0)
    valueLabel.Position = UDim2.new(0, 380, 0, 0)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = math.floor(initialValue * 100) .. "%"
    valueLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSans
    valueLabel.TextXAlignment = Enum.TextXAlignment.Center
    valueLabel.Parent = sliderFrame
    
    -- Slider interaction
    local dragging = false
    
    sliderBg.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = true
            AudioSettingsGUI.UpdateSlider(sliderBg, sliderFill, sliderHandle, valueLabel, key, input.Position.X)
        end
    end)
    
    sliderBg.InputChanged:Connect(function(input)
        if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
            AudioSettingsGUI.UpdateSlider(sliderBg, sliderFill, sliderHandle, valueLabel, key, input.Position.X)
        end
    end)
    
    UserInputService.InputEnded:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            dragging = false
        end
    end)
    
    return sliderFrame
end

-- Update slider value
function AudioSettingsGUI.UpdateSlider(sliderBg, sliderFill, sliderHandle, valueLabel, key, mouseX)
    local sliderPosition = sliderBg.AbsolutePosition.X
    local sliderWidth = sliderBg.AbsoluteSize.X
    local relativeX = mouseX - sliderPosition
    local percentage = math.max(0, math.min(1, relativeX / sliderWidth))
    
    -- Update visual elements
    sliderFill.Size = UDim2.new(percentage, 0, 1, 0)
    sliderHandle.Position = UDim2.new(percentage, -10, 0, -5)
    valueLabel.Text = math.floor(percentage * 100) .. "%"
    
    -- Update audio setting
    audioSettings[key] = percentage
    
    -- Apply audio change immediately
    AudioSettingsGUI.ApplyVolumeChange(key, percentage)
end

-- Apply volume change
function AudioSettingsGUI.ApplyVolumeChange(key, value)
    -- Apply to local sound groups
    local soundGroup = SoundService:FindFirstChild(key:gsub("Volume", "") .. "Group")
    if soundGroup then
        soundGroup.Volume = value * audioSettings.masterVolume
    end
    
    -- Play test sound for immediate feedback
    if key ~= "masterVolume" then
        AudioSettingsGUI.PlayTestSound(key)
    end
end

-- Play test sound for volume adjustment
function AudioSettingsGUI.PlayTestSound(volumeType)
    local testSounds = {
        musicVolume = "rbxassetid://1837879082",
        sfxVolume = "rbxassetid://131961136",
        ambientVolume = "rbxassetid://131961136",
        voiceVolume = "rbxassetid://131961136",
        uiVolume = "rbxassetid://131961136"
    }
    
    local soundId = testSounds[volumeType]
    if soundId then
        local testSound = Instance.new("Sound")
        testSound.SoundId = soundId
        testSound.Volume = audioSettings[volumeType] * audioSettings.masterVolume
        testSound.Parent = SoundService
        testSound:Play()
        
        testSound.Ended:Connect(function()
            testSound:Destroy()
        end)
        
        -- Auto-cleanup
        game:GetService("Debris"):AddItem(testSound, 5)
    end
end

-- Create audio options
function AudioSettingsGUI.CreateAudioOptions(parent)
    local optionsFrame = Instance.new("Frame")
    optionsFrame.Name = "OptionsFrame"
    optionsFrame.Size = UDim2.new(1, -20, 0, 150)
    optionsFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    optionsFrame.BorderSizePixel = 0
    optionsFrame.LayoutOrder = 2
    optionsFrame.Parent = parent
    
    local optionsCorner = Instance.new("UICorner")
    optionsCorner.CornerRadius = UDim.new(0, 8)
    optionsCorner.Parent = optionsFrame
    
    local optionsTitle = Instance.new("TextLabel")
    optionsTitle.Name = "OptionsTitle"
    optionsTitle.Size = UDim2.new(1, -20, 0, 30)
    optionsTitle.Position = UDim2.new(0, 10, 0, 10)
    optionsTitle.BackgroundTransparency = 1
    optionsTitle.Text = "⚙️ Audio-Optionen"
    optionsTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    optionsTitle.TextScaled = true
    optionsTitle.Font = Enum.Font.SourceSansBold
    optionsTitle.TextXAlignment = Enum.TextXAlignment.Left
    optionsTitle.Parent = optionsFrame
    
    -- Mute all checkbox
    local muteCheckbox = AudioSettingsGUI.CreateCheckbox("🔇 Alles stumm schalten", "muteAll", audioSettings.muteAll)
    muteCheckbox.Position = UDim2.new(0, 20, 0, 50)
    muteCheckbox.Parent = optionsFrame
    
    -- Dynamic music checkbox
    local dynamicCheckbox = AudioSettingsGUI.CreateCheckbox("🎵 Dynamische Musik", "dynamicMusic", audioSettings.dynamicMusic)
    dynamicCheckbox.Position = UDim2.new(0, 20, 0, 80)
    dynamicCheckbox.Parent = optionsFrame
    
    -- Spatial audio checkbox
    local spatialCheckbox = AudioSettingsGUI.CreateCheckbox("🌐 3D-Audio", "spatialAudio", audioSettings.spatialAudio)
    spatialCheckbox.Position = UDim2.new(0, 20, 0, 110)
    spatialCheckbox.Parent = optionsFrame
end

-- Create checkbox
function AudioSettingsGUI.CreateCheckbox(text, key, initialValue)
    local checkboxFrame = Instance.new("Frame")
    checkboxFrame.Name = key .. "Checkbox"
    checkboxFrame.Size = UDim2.new(0, 300, 0, 25)
    checkboxFrame.BackgroundTransparency = 1
    
    local checkbox = Instance.new("TextButton")
    checkbox.Name = "Checkbox"
    checkbox.Size = UDim2.new(0, 20, 0, 20)
    checkbox.Position = UDim2.new(0, 0, 0, 2)
    checkbox.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    checkbox.BorderSizePixel = 0
    checkbox.Text = initialValue and "✓" or ""
    checkbox.TextColor3 = Color3.fromRGB(100, 200, 100)
    checkbox.TextScaled = true
    checkbox.Font = Enum.Font.SourceSansBold
    checkbox.Parent = checkboxFrame
    
    local checkboxCorner = Instance.new("UICorner")
    checkboxCorner.CornerRadius = UDim.new(0, 4)
    checkboxCorner.Parent = checkbox
    
    local label = Instance.new("TextLabel")
    label.Name = "Label"
    label.Size = UDim2.new(1, -30, 1, 0)
    label.Position = UDim2.new(0, 30, 0, 0)
    label.BackgroundTransparency = 1
    label.Text = text
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = checkboxFrame
    
    checkbox.MouseButton1Click:Connect(function()
        audioSettings[key] = not audioSettings[key]
        checkbox.Text = audioSettings[key] and "✓" or ""
        AudioSettingsGUI.ApplyOptionChange(key, audioSettings[key])
    end)
    
    return checkboxFrame
end

-- Apply option change
function AudioSettingsGUI.ApplyOptionChange(key, value)
    if key == "muteAll" then
        SoundService.Volume = value and 0 or audioSettings.masterVolume
    elseif key == "dynamicMusic" then
        -- Would send to server to enable/disable dynamic music
        print("Dynamic music:", value and "enabled" or "disabled")
    elseif key == "spatialAudio" then
        -- Would configure 3D audio settings
        print("Spatial audio:", value and "enabled" or "disabled")
    end
end

-- Create quality settings
function AudioSettingsGUI.CreateQualitySettings(parent)
    local qualityFrame = Instance.new("Frame")
    qualityFrame.Name = "QualityFrame"
    qualityFrame.Size = UDim2.new(1, -20, 0, 100)
    qualityFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    qualityFrame.BorderSizePixel = 0
    qualityFrame.LayoutOrder = 3
    qualityFrame.Parent = parent
    
    local qualityCorner = Instance.new("UICorner")
    qualityCorner.CornerRadius = UDim.new(0, 8)
    qualityCorner.Parent = qualityFrame
    
    local qualityTitle = Instance.new("TextLabel")
    qualityTitle.Name = "QualityTitle"
    qualityTitle.Size = UDim2.new(1, -20, 0, 30)
    qualityTitle.Position = UDim2.new(0, 10, 0, 10)
    qualityTitle.BackgroundTransparency = 1
    qualityTitle.Text = "🎧 Audio-Qualität"
    qualityTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    qualityTitle.TextScaled = true
    qualityTitle.Font = Enum.Font.SourceSansBold
    qualityTitle.TextXAlignment = Enum.TextXAlignment.Left
    qualityTitle.Parent = qualityFrame
    
    -- Quality dropdown (simplified as buttons)
    local qualityOptions = {"Low", "Medium", "High", "Ultra"}
    for i, option in pairs(qualityOptions) do
        local qualityButton = Instance.new("TextButton")
        qualityButton.Name = option .. "Button"
        qualityButton.Size = UDim2.new(0, 80, 0, 30)
        qualityButton.Position = UDim2.new(0, 20 + (i-1) * 90, 0, 50)
        qualityButton.BackgroundColor3 = audioSettings.audioQuality == option and Color3.fromRGB(60, 120, 200) or Color3.fromRGB(60, 60, 60)
        qualityButton.BorderSizePixel = 0
        qualityButton.Text = option
        qualityButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        qualityButton.TextScaled = true
        qualityButton.Font = Enum.Font.SourceSans
        qualityButton.Parent = qualityFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 6)
        buttonCorner.Parent = qualityButton
        
        qualityButton.MouseButton1Click:Connect(function()
            audioSettings.audioQuality = option
            AudioSettingsGUI.UpdateQualityButtons(qualityFrame, option)
        end)
    end
end

-- Update quality buttons
function AudioSettingsGUI.UpdateQualityButtons(parent, selectedOption)
    for _, child in pairs(parent:GetChildren()) do
        if child:IsA("TextButton") and child.Name:find("Button") then
            local option = child.Name:gsub("Button", "")
            child.BackgroundColor3 = option == selectedOption and Color3.fromRGB(60, 120, 200) or Color3.fromRGB(60, 60, 60)
        end
    end
end

-- Create action buttons
function AudioSettingsGUI.CreateActionButtons(parent)
    local buttonFrame = Instance.new("Frame")
    buttonFrame.Name = "ButtonFrame"
    buttonFrame.Size = UDim2.new(1, -20, 0, 60)
    buttonFrame.BackgroundTransparency = 1
    buttonFrame.LayoutOrder = 4
    buttonFrame.Parent = parent
    
    local applyButton = Instance.new("TextButton")
    applyButton.Name = "ApplyButton"
    applyButton.Size = UDim2.new(0, 120, 0, 40)
    applyButton.Position = UDim2.new(0, 20, 0, 10)
    applyButton.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    applyButton.BorderSizePixel = 0
    applyButton.Text = "✓ Anwenden"
    applyButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    applyButton.TextScaled = true
    applyButton.Font = Enum.Font.SourceSansBold
    applyButton.Parent = buttonFrame
    
    local applyCorner = Instance.new("UICorner")
    applyCorner.CornerRadius = UDim.new(0, 8)
    applyCorner.Parent = applyButton
    
    local resetButton = Instance.new("TextButton")
    resetButton.Name = "ResetButton"
    resetButton.Size = UDim2.new(0, 120, 0, 40)
    resetButton.Position = UDim2.new(0, 160, 0, 10)
    resetButton.BackgroundColor3 = Color3.fromRGB(200, 60, 60)
    resetButton.BorderSizePixel = 0
    resetButton.Text = "↺ Zurücksetzen"
    resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    resetButton.TextScaled = true
    resetButton.Font = Enum.Font.SourceSansBold
    resetButton.Parent = buttonFrame
    
    local resetCorner = Instance.new("UICorner")
    resetCorner.CornerRadius = UDim.new(0, 8)
    resetCorner.Parent = resetButton
    
    applyButton.MouseButton1Click:Connect(function()
        AudioSettingsGUI.ApplyAllSettings()
        AudioSettingsGUI.ToggleAudioGUI()
    end)
    
    resetButton.MouseButton1Click:Connect(function()
        AudioSettingsGUI.ResetToDefaults()
    end)
end

-- Apply all settings
function AudioSettingsGUI.ApplyAllSettings()
    -- Apply all volume settings
    for key, value in pairs(audioSettings) do
        if key:find("Volume") then
            AudioSettingsGUI.ApplyVolumeChange(key, value)
        end
    end
    
    -- Apply options
    AudioSettingsGUI.ApplyOptionChange("muteAll", audioSettings.muteAll)
    AudioSettingsGUI.ApplyOptionChange("dynamicMusic", audioSettings.dynamicMusic)
    AudioSettingsGUI.ApplyOptionChange("spatialAudio", audioSettings.spatialAudio)
    
    print("🔊 Audio settings applied")
end

-- Reset to defaults
function AudioSettingsGUI.ResetToDefaults()
    audioSettings = {
        masterVolume = 0.8,
        musicVolume = 0.6,
        sfxVolume = 0.8,
        ambientVolume = 0.4,
        voiceVolume = 0.7,
        uiVolume = 0.5,
        muteAll = false,
        dynamicMusic = true,
        spatialAudio = true,
        audioQuality = "High"
    }
    
    -- Recreate interface with new values
    local gui = playerGui:FindFirstChild("AudioSettingsGUI")
    if gui then
        gui:Destroy()
    end
    AudioSettingsGUI.CreateAudioInterface()
    
    print("🔊 Audio settings reset to defaults")
end

-- Toggle audio GUI
function AudioSettingsGUI.ToggleAudioGUI()
    local gui = playerGui:FindFirstChild("AudioSettingsGUI")
    if gui then
        local mainFrame = gui:FindFirstChild("MainFrame")
        if mainFrame then
            isAudioGUIOpen = not isAudioGUIOpen
            mainFrame.Visible = isAudioGUIOpen
        end
    end
end

-- Initialize audio settings GUI
function AudioSettingsGUI.Initialize()
    AudioSettingsGUI.CreateAudioInterface()
    
    -- Hotkey to toggle (F10 key)
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F10 then
            AudioSettingsGUI.ToggleAudioGUI()
        end
    end)
    
    print("🔊 Audio Settings GUI initialized - Press F10 to open")
end

-- Initialize when script loads
AudioSettingsGUI.Initialize()

return AudioSettingsGUI
