-- ServerScriptService/GameInitializer.lua
-- ROBLOX SCRIPT TYPE: Script
-- Hauptinitialisierung für das komplette Transport Fever 2 Spiel

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

print("🚀 Transport Fever 2 Clone - Game Initializer gestartet")

-- Warten auf alle notwendigen Services
local function waitForServices()
    print("⏳ Warte auf Services...")
    
    -- Warten auf ReplicatedStorage Struktur
    local Events = ReplicatedStorage:WaitForChild("Events", 10)
    if not Events then
        error("Events folder nicht gefunden!")
    end
    
    local Assets = ReplicatedStorage:WaitForChild("Assets", 10)
    if not Assets then
        error("Assets folder nicht gefunden!")
    end
    
    print("✅ Alle Services verfügbar")
end

-- RemoteEvents erstellen
local function createRemoteEvents()
    print("📡 Erstelle RemoteEvents...")

    -- Events Script ausführen
    local createEventsScript = ReplicatedStorage.Events:FindFirstChild("CreateRemoteEvents")
    if createEventsScript then
        local CreateRemoteEvents = require(createEventsScript)
        CreateRemoteEvents.Initialize()
    else
        warn("CreateRemoteEvents Script nicht gefunden!")
    end
end

-- GameManager initialisieren
local function initializeGameManager()
    print("🎮 Initialisiere GameManager...")
    
    local GameManager = require(script.Parent.GameManager)
    local gameInstance = GameManager.GetInstance()
    
    if gameInstance then
        gameInstance:Initialize()
        print("✅ GameManager erfolgreich initialisiert")
        return gameInstance
    else
        error("GameManager konnte nicht initialisiert werden!")
    end
end

-- Performance Monitoring starten
local function startPerformanceMonitoring(gameManager)
    print("⚡ Starte Performance Monitoring...")
    
    spawn(function()
        while true do
            local stats = gameManager.performanceManager:GetPerformanceStats()
            
            -- Log Performance alle 30 Sekunden
            if tick() % 30 < 1 then
                print(string.format("📊 Performance: FPS=%d, Memory=%.1fMB, Objects=%d, Profile=%s", 
                    stats.fps, stats.memory, stats.activeObjects, stats.currentProfile))
            end
            
            wait(1)
        end
    end)
end

-- Automatische Tests starten
local function startAutomaticTesting(gameManager)
    print("🧪 Starte automatische Tests...")
    
    -- Ersten Test nach 10 Sekunden ausführen
    spawn(function()
        wait(10)
        gameManager.gameTester:RunAllTests()
    end)
end

-- Player Events einrichten
local function setupPlayerEvents(gameManager)
    print("👥 Richte Player Events ein...")
    
    Players.PlayerAdded:Connect(function(player)
        print("👋 Spieler beigetreten:", player.Name)
        
        -- Spielerdaten initialisieren
        gameManager:InitializePlayer(player)
        
        -- Willkommensnachricht senden
        wait(2) -- Kurz warten bis Client bereit ist
        local Events = ReplicatedStorage.Events
        if Events:FindFirstChild("PlayerJoinedEvent") then
            Events.PlayerJoinedEvent:FireClient(player, {
                message = "Willkommen bei Transport Fever 2 Clone, " .. player.Name .. "!",
                timestamp = tick()
            })
        end
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        print("👋 Spieler verlässt:", player.Name)
        gameManager:CleanupPlayer(player)
    end)
end

-- Hauptinitialisierung
local function main()
    print("🎯 Starte Hauptinitialisierung...")
    
    -- 1. Services warten
    waitForServices()
    
    -- 2. RemoteEvents erstellen
    createRemoteEvents()
    
    -- 3. GameManager initialisieren
    local gameManager = initializeGameManager()
    
    -- 4. Player Events einrichten
    setupPlayerEvents(gameManager)
    
    -- 5. Performance Monitoring starten
    startPerformanceMonitoring(gameManager)
    
    -- 6. Automatische Tests starten
    startAutomaticTesting(gameManager)
    
    -- 7. Game Loop starten
    print("🔄 Starte Game Loop...")
    local lastUpdate = tick()
    
    RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local deltaTime = currentTime - lastUpdate
        lastUpdate = currentTime
        
        -- GameManager Update
        gameManager:Update(deltaTime)
    end)
    
    print("🎉 Transport Fever 2 Clone erfolgreich gestartet!")
    print("📋 Verfügbare Features:")
    print("  🗺️  Prozedurale Kartengenerierung")
    print("  🏙️  Dynamische Städte und Industrien")
    print("  🚂  Vollständiges Transportsystem")
    print("  💰  Komplexe Wirtschaftssimulation")
    print("  🤖  KI-Konkurrenten")
    print("  👥  Multiplayer-Unterstützung")
    print("  🎵  Audio und Visual Effects")
    print("  ⚡  Performance-Optimierung")
    print("  🧪  Automatische Tests")
    print("  💾  Speicher-/Ladesystem")
    print("  🏆  Kampagnen und Achievements")
    print("  🌦️  Wetter und Tag/Nacht-Zyklen")
    print("")
    print("🎮 Das Spiel ist bereit! Spieler können jetzt beitreten.")
end

-- Fehlerbehandlung
local success, error = pcall(main)
if not success then
    warn("❌ Fehler beim Initialisieren des Spiels:", error)
    
    -- Fallback: Minimale Initialisierung
    print("🔧 Versuche Fallback-Initialisierung...")
    
    local GameManager = require(script.Parent.GameManager)
    local gameInstance = GameManager.GetInstance()
    if gameInstance then
        gameInstance:Initialize()
        print("✅ Fallback-Initialisierung erfolgreich")
    else
        error("❌ Auch Fallback-Initialisierung fehlgeschlagen!")
    end
end
