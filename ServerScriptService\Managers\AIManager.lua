-- ServerScriptService/Managers/AIManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Basis-KI-System für NPCs und automatisierte Systeme

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local AIManager = {}
AIManager.__index = AIManager

function AIManager.new()
    local self = setmetatable({}, AIManager)
    
    -- KI-Entitäten
    self.aiEntities = {}
    
    -- KI-Verhalten
    self.behaviors = {
        passive = {
            name = "Passiv",
            aggressiveness = 0.1,
            expansionRate = 0.5,
            riskTaking = 0.2
        },
        
        normal = {
            name = "Normal",
            aggressiveness = 0.5,
            expansionRate = 1.0,
            riskTaking = 0.5
        },
        
        aggressive = {
            name = "Aggressiv",
            aggressiveness = 0.9,
            expansionRate = 1.5,
            riskTaking = 0.8
        }
    }
    
    -- Entscheidungs-System
    self.decisionSystem = {
        lastDecision = os.time(),
        decisionInterval = 30 -- Sekunden zwischen Entscheidungen
    }
    
    return self
end

-- KI-Entität erstellen
function AIManager:CreateAIEntity(entityData)
    local entityId = HttpService:GenerateGUID(false)
    
    local entity = {
        id = entityId,
        name = entityData.name or "KI-Spieler",
        behavior = entityData.behavior or "normal",
        cash = entityData.startingCash or 1000000,
        vehicles = {},
        routes = {},
        buildings = {},
        reputation = 50, -- 0-100
        lastAction = os.time()
    }
    
    self.aiEntities[entityId] = entity
    
    print("🤖 KI-Entität erstellt:", entity.name)
    return entityId
end

-- KI-Entscheidungen verarbeiten
function AIManager:ProcessAIDecisions(gameData)
    local currentTime = os.time()
    
    if currentTime - self.decisionSystem.lastDecision < self.decisionSystem.decisionInterval then
        return
    end
    
    for entityId, entity in pairs(self.aiEntities) do
        self:MakeEntityDecision(entity, gameData)
    end
    
    self.decisionSystem.lastDecision = currentTime
end

-- Entscheidung für KI-Entität treffen
function AIManager:MakeEntityDecision(entity, gameData)
    local behavior = self.behaviors[entity.behavior]
    if not behavior then return end
    
    -- Verschiedene Entscheidungstypen
    local decisions = {
        "buyVehicle",
        "createRoute", 
        "buildInfrastructure",
        "expandBusiness"
    }
    
    -- Zufällige Entscheidung basierend auf Verhalten
    local decision = decisions[math.random(1, #decisions)]
    
    if decision == "buyVehicle" then
        self:AIBuyVehicle(entity, gameData, behavior)
    elseif decision == "createRoute" then
        self:AICreateRoute(entity, gameData, behavior)
    elseif decision == "buildInfrastructure" then
        self:AIBuildInfrastructure(entity, gameData, behavior)
    elseif decision == "expandBusiness" then
        self:AIExpandBusiness(entity, gameData, behavior)
    end
    
    entity.lastAction = os.time()
end

-- KI kauft Fahrzeug
function AIManager:AIBuyVehicle(entity, gameData, behavior)
    if entity.cash < 50000 then return end -- Nicht genug Geld
    
    -- Fahrzeugtyp basierend auf Verhalten wählen
    local vehicleTypes = {"bus", "truck", "train"}
    local vehicleType = vehicleTypes[math.random(1, #vehicleTypes)]
    
    -- Fahrzeugkosten
    local vehicleCosts = {
        bus = 30000,
        truck = 40000,
        train = 100000
    }
    
    local cost = vehicleCosts[vehicleType]
    if entity.cash >= cost then
        entity.cash = entity.cash - cost
        
        local vehicle = {
            id = HttpService:GenerateGUID(false),
            type = vehicleType,
            owner = entity.id,
            route = nil,
            condition = 100,
            purchaseDate = os.time()
        }
        
        table.insert(entity.vehicles, vehicle)
        
        print("🤖", entity.name, "kauft", vehicleType, "für", cost, "€")
        
        -- Event senden
        self:SendAIEvent("VehiclePurchased", {
            entityId = entity.id,
            vehicleType = vehicleType,
            cost = cost
        })
    end
end

-- KI erstellt Route
function AIManager:AICreateRoute(entity, gameData, behavior)
    if #entity.vehicles == 0 then return end -- Keine Fahrzeuge
    
    -- Verfügbare Städte
    local cities = gameData.cities or {}
    if #cities < 2 then return end
    
    -- Zufällige Städte wählen
    local city1 = cities[math.random(1, #cities)]
    local city2 = cities[math.random(1, #cities)]
    
    if city1.id == city2.id then return end
    
    -- Route erstellen
    local route = {
        id = HttpService:GenerateGUID(false),
        name = city1.name .. " - " .. city2.name,
        startCity = city1.id,
        endCity = city2.id,
        owner = entity.id,
        vehicles = {},
        created = os.time()
    }
    
    table.insert(entity.routes, route)
    
    -- Fahrzeug zur Route zuweisen
    for _, vehicle in pairs(entity.vehicles) do
        if not vehicle.route then
            vehicle.route = route.id
            table.insert(route.vehicles, vehicle.id)
            break
        end
    end
    
    print("🤖", entity.name, "erstellt Route:", route.name)
    
    -- Event senden
    self:SendAIEvent("RouteCreated", {
        entityId = entity.id,
        routeId = route.id,
        routeName = route.name
    })
end

-- KI baut Infrastruktur
function AIManager:AIBuildInfrastructure(entity, gameData, behavior)
    local infrastructureCost = 100000
    
    if entity.cash < infrastructureCost then return end
    
    -- Infrastruktur-Typen
    local infrastructureTypes = {"road", "railway", "station"}
    local infrastructureType = infrastructureTypes[math.random(1, #infrastructureTypes)]
    
    entity.cash = entity.cash - infrastructureCost
    
    local infrastructure = {
        id = HttpService:GenerateGUID(false),
        type = infrastructureType,
        owner = entity.id,
        built = os.time(),
        cost = infrastructureCost
    }
    
    table.insert(entity.buildings, infrastructure)
    
    print("🤖", entity.name, "baut", infrastructureType, "für", infrastructureCost, "€")
    
    -- Event senden
    self:SendAIEvent("InfrastructureBuilt", {
        entityId = entity.id,
        infrastructureType = infrastructureType,
        cost = infrastructureCost
    })
end

-- KI expandiert Geschäft
function AIManager:AIExpandBusiness(entity, gameData, behavior)
    -- Reputation verbessern
    if math.random() < behavior.aggressiveness then
        entity.reputation = math.min(100, entity.reputation + math.random(1, 5))
        
        print("🤖", entity.name, "verbessert Reputation auf", entity.reputation)
        
        -- Event senden
        self:SendAIEvent("ReputationImproved", {
            entityId = entity.id,
            newReputation = entity.reputation
        })
    end
end

-- KI-Event senden
function AIManager:SendAIEvent(eventType, eventData)
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if Events:FindFirstChild("AIActionEvent") then
        Events.AIActionEvent:FireAllClients(eventType, eventData)
    end
end

-- KI-Einnahmen verarbeiten
function AIManager:ProcessAIRevenue(deltaTime)
    for entityId, entity in pairs(self.aiEntities) do
        local revenue = 0
        
        -- Einnahmen von Routen
        for _, route in pairs(entity.routes) do
            if #route.vehicles > 0 then
                revenue = revenue + math.random(1000, 5000) -- Pro Route
            end
        end
        
        -- Einnahmen hinzufügen
        entity.cash = entity.cash + revenue
        
        if revenue > 0 then
            print("🤖", entity.name, "erhält", revenue, "€ Einnahmen")
        end
    end
end

-- KI-Statistiken abrufen
function AIManager:GetAIStats()
    local stats = {
        totalEntities = 0,
        totalCash = 0,
        totalVehicles = 0,
        totalRoutes = 0,
        averageReputation = 0
    }
    
    local reputationSum = 0
    
    for entityId, entity in pairs(self.aiEntities) do
        stats.totalEntities = stats.totalEntities + 1
        stats.totalCash = stats.totalCash + entity.cash
        stats.totalVehicles = stats.totalVehicles + #entity.vehicles
        stats.totalRoutes = stats.totalRoutes + #entity.routes
        reputationSum = reputationSum + entity.reputation
    end
    
    if stats.totalEntities > 0 then
        stats.averageReputation = reputationSum / stats.totalEntities
    end
    
    return stats
end

-- KI-Entität abrufen
function AIManager:GetAIEntity(entityId)
    return self.aiEntities[entityId]
end

-- Alle KI-Entitäten abrufen
function AIManager:GetAllAIEntities()
    return self.aiEntities
end

-- KI-Entität entfernen
function AIManager:RemoveAIEntity(entityId)
    if self.aiEntities[entityId] then
        print("🤖 KI-Entität entfernt:", self.aiEntities[entityId].name)
        self.aiEntities[entityId] = nil
        return true
    end
    return false
end

-- Update-Funktion
function AIManager:Update(deltaTime, gameData)
    -- KI-Entscheidungen verarbeiten
    self:ProcessAIDecisions(gameData)
    
    -- KI-Einnahmen verarbeiten
    self:ProcessAIRevenue(deltaTime)
end

return AIManager
