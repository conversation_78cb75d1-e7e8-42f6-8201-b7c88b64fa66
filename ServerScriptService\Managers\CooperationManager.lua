-- ServerScriptService/Managers/CooperationManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Kooperations-System für gemeinsame Projekte

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local CooperationManager = {}
CooperationManager.__index = CooperationManager

function CooperationManager.new()
    local self = setmetatable({}, CooperationManager)
    
    -- Kooperations-Projekte
    self.projects = {}
    
    -- Handels-Angebote
    self.tradeOffers = {}
    
    -- Gemeinsame Infrastruktur
    self.sharedInfrastructure = {}
    
    return self
end

-- Kooperations-Projekt erstellen
function CooperationManager:CreateProject(creatorId, projectData)
    local projectId = HttpService:GenerateGUID(false)
    
    local project = {
        id = projectId,
        name = projectData.name,
        description = projectData.description,
        type = projectData.type, -- "infrastructure", "research", "trade"
        creatorId = creatorId,
        participants = {creatorId},
        requiredInvestment = projectData.requiredInvestment,
        currentInvestment = 0,
        investments = {[creatorId] = 0},
        status = "recruiting", -- recruiting, active, completed, cancelled
        created = os.time(),
        deadline = projectData.deadline,
        rewards = projectData.rewards or {}
    }
    
    self.projects[projectId] = project
    return projectId
end

-- Projekt beitreten
function CooperationManager:JoinProject(playerId, projectId, investment)
    local project = self.projects[projectId]
    if not project or project.status ~= "recruiting" then
        return false, "Projekt nicht verfügbar"
    end
    
    -- Spieler bereits Teilnehmer?
    for _, participantId in pairs(project.participants) do
        if participantId == playerId then
            return false, "Bereits Teilnehmer"
        end
    end
    
    -- Zur Teilnehmerliste hinzufügen
    table.insert(project.participants, playerId)
    project.investments[playerId] = investment or 0
    project.currentInvestment = project.currentInvestment + (investment or 0)
    
    -- Projekt starten wenn genug Investition
    if project.currentInvestment >= project.requiredInvestment then
        project.status = "active"
        self:StartProject(projectId)
    end
    
    return true
end

-- Projekt starten
function CooperationManager:StartProject(projectId)
    local project = self.projects[projectId]
    if not project then return end
    
    project.startedAt = os.time()
    
    -- Je nach Projekttyp verschiedene Aktionen
    if project.type == "infrastructure" then
        self:StartInfrastructureProject(project)
    elseif project.type == "research" then
        self:StartResearchProject(project)
    elseif project.type == "trade" then
        self:StartTradeProject(project)
    end
    
    print("🤝 Kooperations-Projekt gestartet:", project.name)
end

-- Infrastruktur-Projekt starten
function CooperationManager:StartInfrastructureProject(project)
    -- Gemeinsame Infrastruktur erstellen
    local infrastructureId = HttpService:GenerateGUID(false)
    
    self.sharedInfrastructure[infrastructureId] = {
        id = infrastructureId,
        type = project.infrastructureType or "railway",
        owners = project.participants,
        maintenanceCost = project.maintenanceCost or 1000,
        revenue = 0,
        built = os.time()
    }
    
    project.infrastructureId = infrastructureId
end

-- Forschungs-Projekt starten
function CooperationManager:StartResearchProject(project)
    -- Forschungsfortschritt initialisieren
    project.researchProgress = 0
    project.researchTarget = project.researchTarget or 100
    
    -- Alle Teilnehmer erhalten Forschungsbonus
    for _, participantId in pairs(project.participants) do
        -- Forschungsbonus gewähren (über Events)
        local Events = ReplicatedStorage.Events
        if Events:FindFirstChild("GrantResearchBonusEvent") then
            Events.GrantResearchBonusEvent:Fire(participantId, project.researchType, project.bonus)
        end
    end
end

-- Handels-Projekt starten
function CooperationManager:StartTradeProject(project)
    -- Handelsroute zwischen Teilnehmern etablieren
    project.tradeRoutes = {}
    
    for i = 1, #project.participants - 1 do
        for j = i + 1, #project.participants do
            local route = {
                player1 = project.participants[i],
                player2 = project.participants[j],
                goods = project.tradeGoods or {},
                discount = project.tradeDiscount or 0.1
            }
            table.insert(project.tradeRoutes, route)
        end
    end
end

-- Handels-Angebot erstellen
function CooperationManager:CreateTradeOffer(playerId, offerData)
    local offerId = HttpService:GenerateGUID(false)
    
    local offer = {
        id = offerId,
        playerId = playerId,
        offering = offerData.offering, -- {good = "coal", amount = 100}
        requesting = offerData.requesting, -- {good = "steel", amount = 50}
        price = offerData.price,
        expires = os.time() + (offerData.duration or 3600), -- 1 Stunde Standard
        status = "active"
    }
    
    self.tradeOffers[offerId] = offer
    return offerId
end

-- Handels-Angebot annehmen
function CooperationManager:AcceptTradeOffer(playerId, offerId)
    local offer = self.tradeOffers[offerId]
    if not offer or offer.status ~= "active" or offer.expires < os.time() then
        return false, "Angebot nicht verfügbar"
    end
    
    if offer.playerId == playerId then
        return false, "Kann eigenes Angebot nicht annehmen"
    end
    
    -- Handel durchführen
    local success = self:ExecuteTrade(offer.playerId, playerId, offer)
    
    if success then
        offer.status = "completed"
        offer.acceptedBy = playerId
        offer.completedAt = os.time()
        return true
    else
        return false, "Handel fehlgeschlagen"
    end
end

-- Handel ausführen
function CooperationManager:ExecuteTrade(sellerId, buyerId, offer)
    -- Über Events an andere Manager weiterleiten
    local Events = ReplicatedStorage.Events
    
    if Events:FindFirstChild("ExecuteTradeEvent") then
        Events.ExecuteTradeEvent:Fire(sellerId, buyerId, offer.offering, offer.requesting, offer.price)
        return true
    end
    
    return false
end

-- Projekt-Fortschritt aktualisieren
function CooperationManager:UpdateProjectProgress(projectId, progress)
    local project = self.projects[projectId]
    if not project or project.status ~= "active" then return end
    
    if project.type == "research" then
        project.researchProgress = project.researchProgress + progress
        
        -- Projekt abgeschlossen?
        if project.researchProgress >= project.researchTarget then
            self:CompleteProject(projectId)
        end
    end
end

-- Projekt abschließen
function CooperationManager:CompleteProject(projectId)
    local project = self.projects[projectId]
    if not project then return end
    
    project.status = "completed"
    project.completedAt = os.time()
    
    -- Belohnungen verteilen
    self:DistributeRewards(project)
    
    print("✅ Kooperations-Projekt abgeschlossen:", project.name)
end

-- Belohnungen verteilen
function CooperationManager:DistributeRewards(project)
    local totalInvestment = project.currentInvestment
    
    for _, participantId in pairs(project.participants) do
        local investment = project.investments[participantId] or 0
        local share = investment / totalInvestment
        
        -- Belohnungen basierend auf Investitionsanteil
        for _, reward in pairs(project.rewards) do
            local playerReward = {
                type = reward.type,
                amount = math.floor(reward.amount * share),
                item = reward.item
            }
            
            self:GrantReward(participantId, playerReward)
        end
    end
end

-- Belohnung gewähren
function CooperationManager:GrantReward(playerId, reward)
    local Events = ReplicatedStorage.Events
    
    if reward.type == "money" then
        if Events:FindFirstChild("AddRevenueEvent") then
            Events.AddRevenueEvent:Fire(playerId, reward.amount, "Cooperation Reward")
        end
    elseif reward.type == "research" then
        if Events:FindFirstChild("GrantResearchBonusEvent") then
            Events.GrantResearchBonusEvent:Fire(playerId, reward.item, reward.amount)
        end
    end
end

-- Verfügbare Projekte abrufen
function CooperationManager:GetAvailableProjects()
    local availableProjects = {}
    
    for projectId, project in pairs(self.projects) do
        if project.status == "recruiting" and (not project.deadline or project.deadline > os.time()) then
            table.insert(availableProjects, {
                id = projectId,
                name = project.name,
                description = project.description,
                type = project.type,
                requiredInvestment = project.requiredInvestment,
                currentInvestment = project.currentInvestment,
                participantCount = #project.participants,
                timeLeft = project.deadline and (project.deadline - os.time()) or nil
            })
        end
    end
    
    return availableProjects
end

-- Aktive Handels-Angebote abrufen
function CooperationManager:GetActiveTradeOffers(excludePlayerId)
    local activeOffers = {}
    
    for offerId, offer in pairs(self.tradeOffers) do
        if offer.status == "active" and offer.expires > os.time() and offer.playerId ~= excludePlayerId then
            table.insert(activeOffers, {
                id = offerId,
                offering = offer.offering,
                requesting = offer.requesting,
                price = offer.price,
                timeLeft = offer.expires - os.time()
            })
        end
    end
    
    return activeOffers
end

-- Update-Funktion
function CooperationManager:Update(deltaTime)
    -- Abgelaufene Handels-Angebote entfernen
    for offerId, offer in pairs(self.tradeOffers) do
        if offer.expires < os.time() and offer.status == "active" then
            offer.status = "expired"
        end
    end
    
    -- Projekt-Deadlines prüfen
    for projectId, project in pairs(self.projects) do
        if project.deadline and project.deadline < os.time() and project.status == "recruiting" then
            project.status = "cancelled"
            print("❌ Kooperations-Projekt abgebrochen (Deadline):", project.name)
        end
    end
end

return CooperationManager
