-- ServerScriptService/Managers/AdvancedAIManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Erweiterte KI-Systeme für komplexe Strategien

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local AdvancedAIManager = {}
AdvancedAIManager.__index = AdvancedAIManager

function AdvancedAIManager.new()
    local self = setmetatable({}, AdvancedAIManager)
    
    -- KI-Strategien
    self.strategies = {
        aggressive = {
            name = "Aggressiv",
            description = "Schnelle Expansion, hohe Risiken",
            expansionRate = 1.5,
            riskTolerance = 0.8,
            competitiveness = 0.9
        },
        
        conservative = {
            name = "Konservativ", 
            description = "Langsame aber sichere Expansion",
            expansionRate = 0.7,
            riskTolerance = 0.3,
            competitiveness = 0.4
        },
        
        balanced = {
            name = "Ausgewogen",
            description = "Ausgewogene Strategie",
            expansionRate = 1.0,
            riskTolerance = 0.5,
            competitiveness = 0.6
        },
        
        opportunistic = {
            name = "Opportunistisch",
            description = "Nutzt Marktchancen optimal",
            expansionRate = 1.2,
            riskTolerance = 0.6,
            competitiveness = 0.8
        }
    }
    
    -- KI-Entscheidungsbaum
    self.decisionTree = {
        routeSelection = {},
        vehiclePurchase = {},
        infrastructureBuilding = {},
        marketAnalysis = {}
    }
    
    -- Lernende KI-Daten
    self.learningData = {
        successfulStrategies = {},
        failedStrategies = {},
        marketPatterns = {},
        playerBehaviorAnalysis = {}
    }
    
    return self
end

-- KI-Strategie für Konkurrenten auswählen
function AdvancedAIManager:SelectStrategy(competitorData, gameState)
    local strategy = "balanced" -- Standard
    
    -- Strategie basierend auf Spielsituation wählen
    if gameState.economicGrowth > 1.2 then
        -- Boom-Phase: Aggressiv expandieren
        strategy = "aggressive"
    elseif gameState.economicGrowth < 0.8 then
        -- Rezession: Konservativ spielen
        strategy = "conservative"
    elseif competitorData.cash > competitorData.averageRevenue * 12 then
        -- Viel Geld: Opportunistisch
        strategy = "opportunistic"
    end
    
    return self.strategies[strategy]
end

-- Erweiterte Routen-Analyse
function AdvancedAIManager:AnalyzeRouteOpportunities(gameData)
    local opportunities = {}
    
    -- Alle Stadt-Paare analysieren
    for cityId1, city1 in pairs(gameData.cities) do
        for cityId2, city2 in pairs(gameData.cities) do
            if cityId1 ~= cityId2 then
                local opportunity = self:EvaluateRoute(city1, city2, gameData)
                if opportunity.profitability > 0.1 then
                    table.insert(opportunities, opportunity)
                end
            end
        end
    end
    
    -- Nach Profitabilität sortieren
    table.sort(opportunities, function(a, b)
        return a.profitability > b.profitability
    end)
    
    return opportunities
end

-- Route bewerten
function AdvancedAIManager:EvaluateRoute(city1, city2, gameData)
    local distance = math.sqrt((city1.x - city2.x)^2 + (city1.z - city2.z)^2)
    local demand = self:CalculateRouteDemand(city1, city2)
    local competition = self:AnalyzeRouteCompetition(city1, city2, gameData)
    local infrastructure = self:EvaluateInfrastructure(city1, city2, gameData)
    
    local profitability = (demand * 0.4) - (distance * 0.2) - (competition * 0.3) + (infrastructure * 0.1)
    
    return {
        city1 = city1,
        city2 = city2,
        distance = distance,
        demand = demand,
        competition = competition,
        infrastructure = infrastructure,
        profitability = profitability
    }
end

-- Routen-Nachfrage berechnen
function AdvancedAIManager:CalculateRouteDemand(city1, city2)
    local totalDemand = 0
    
    -- Passagier-Nachfrage
    local passengerDemand = math.min(city1.population, city2.population) * 0.001
    totalDemand = totalDemand + passengerDemand
    
    -- Waren-Nachfrage
    for good, demand in pairs(city1.demands or {}) do
        if city2.supply and city2.supply[good] and city2.supply[good] > 0 then
            totalDemand = totalDemand + demand.amount * 0.1
        end
    end
    
    return totalDemand
end

-- Routen-Konkurrenz analysieren
function AdvancedAIManager:AnalyzeRouteCompetition(city1, city2, gameData)
    local competition = 0
    
    -- Spieler-Routen zählen
    if gameData.playerRoutes then
        for _, route in pairs(gameData.playerRoutes) do
            if (route.startCity == city1.id and route.endCity == city2.id) or
               (route.startCity == city2.id and route.endCity == city1.id) then
                competition = competition + 0.3
            end
        end
    end
    
    -- KI-Konkurrenten-Routen zählen
    if gameData.aiRoutes then
        for _, route in pairs(gameData.aiRoutes) do
            if (route.startCity == city1.id and route.endCity == city2.id) or
               (route.startCity == city2.id and route.endCity == city1.id) then
                competition = competition + 0.2
            end
        end
    end
    
    return math.min(competition, 1.0)
end

-- Infrastruktur bewerten
function AdvancedAIManager:EvaluateInfrastructure(city1, city2, gameData)
    local infrastructureScore = 0
    
    -- Bestehende Verbindungen prüfen
    if gameData.infrastructure then
        -- Straßen/Schienen zwischen Städten
        local hasRoad = self:HasConnection(city1, city2, gameData.infrastructure.roads)
        local hasRail = self:HasConnection(city1, city2, gameData.infrastructure.railways)
        
        if hasRoad then infrastructureScore = infrastructureScore + 0.3
        if hasRail then infrastructureScore = infrastructureScore + 0.5
    end
    
    return infrastructureScore
end

-- Verbindung prüfen
function AdvancedAIManager:HasConnection(city1, city2, connections)
    for _, connection in pairs(connections or {}) do
        if (connection.startCity == city1.id and connection.endCity == city2.id) or
           (connection.startCity == city2.id and connection.endCity == city1.id) then
            return true
        end
    end
    return false
end

-- Markt-Trends analysieren
function AdvancedAIManager:AnalyzeMarketTrends(gameData)
    local trends = {
        growingCities = {},
        decliningCities = {},
        profitableGoods = {},
        oversuppliedGoods = {}
    }
    
    -- Stadt-Trends
    for cityId, city in pairs(gameData.cities) do
        if city.growthRate and city.growthRate > 0.05 then
            table.insert(trends.growingCities, city)
        elseif city.growthRate and city.growthRate < -0.02 then
            table.insert(trends.decliningCities, city)
        end
    end
    
    -- Waren-Trends
    if gameData.economy and gameData.economy.goods then
        for good, data in pairs(gameData.economy.goods) do
            if data.priceChange and data.priceChange > 0.1 then
                table.insert(trends.profitableGoods, good)
            elseif data.priceChange and data.priceChange < -0.1 then
                table.insert(trends.oversuppliedGoods, good)
            end
        end
    end
    
    return trends
end

-- KI-Entscheidung für Fahrzeugkauf
function AdvancedAIManager:DecideVehiclePurchase(competitorData, gameData, strategy)
    local decision = {
        shouldBuy = false,
        vehicleType = nil,
        reason = ""
    }
    
    -- Budget prüfen
    if competitorData.cash < 100000 then
        decision.reason = "Nicht genug Geld"
        return decision
    end
    
    -- Strategie-basierte Entscheidung
    local expansionFactor = strategy.expansionRate
    local currentVehicles = #(competitorData.vehicles or {})
    local targetVehicles = math.floor(competitorData.monthlyRevenue / 50000 * expansionFactor)
    
    if currentVehicles < targetVehicles then
        decision.shouldBuy = true
        
        -- Fahrzeugtyp basierend auf Marktanalyse wählen
        local trends = self:AnalyzeMarketTrends(gameData)
        
        if #trends.growingCities > 0 then
            decision.vehicleType = "bus" -- Für wachsende Städte
        elseif #trends.profitableGoods > 0 then
            decision.vehicleType = "truck" -- Für profitable Waren
        else
            decision.vehicleType = "train" -- Standard für lange Strecken
        end
        
        decision.reason = "Expansion basierend auf " .. strategy.name .. " Strategie"
    else
        decision.reason = "Genug Fahrzeuge für aktuelle Strategie"
    end
    
    return decision
end

-- Lernende KI: Erfolg/Misserfolg speichern
function AdvancedAIManager:RecordStrategyOutcome(strategy, outcome, context)
    local record = {
        strategy = strategy,
        outcome = outcome, -- "success" oder "failure"
        context = context,
        timestamp = os.time()
    }
    
    if outcome == "success" then
        table.insert(self.learningData.successfulStrategies, record)
    else
        table.insert(self.learningData.failedStrategies, record)
    end
    
    -- Nur letzte 100 Einträge behalten
    if #self.learningData.successfulStrategies > 100 then
        table.remove(self.learningData.successfulStrategies, 1)
    end
    if #self.learningData.failedStrategies > 100 then
        table.remove(self.learningData.failedStrategies, 1)
    end
end

-- Beste Strategie basierend auf Lerndaten empfehlen
function AdvancedAIManager:RecommendStrategy(context)
    local strategyScores = {}
    
    -- Erfolgreiche Strategien bewerten
    for _, record in pairs(self.learningData.successfulStrategies) do
        if self:ContextMatches(record.context, context) then
            strategyScores[record.strategy] = (strategyScores[record.strategy] or 0) + 1
        end
    end
    
    -- Fehlgeschlagene Strategien abziehen
    for _, record in pairs(self.learningData.failedStrategies) do
        if self:ContextMatches(record.context, context) then
            strategyScores[record.strategy] = (strategyScores[record.strategy] or 0) - 0.5
        end
    end
    
    -- Beste Strategie finden
    local bestStrategy = "balanced"
    local bestScore = -999
    
    for strategy, score in pairs(strategyScores) do
        if score > bestScore then
            bestScore = score
            bestStrategy = strategy
        end
    end
    
    return bestStrategy
end

-- Kontext-Übereinstimmung prüfen
function AdvancedAIManager:ContextMatches(context1, context2)
    -- Vereinfachte Kontext-Übereinstimmung
    local economicMatch = math.abs((context1.economicGrowth or 1) - (context2.economicGrowth or 1)) < 0.2
    local competitionMatch = math.abs((context1.competition or 0.5) - (context2.competition or 0.5)) < 0.3
    
    return economicMatch and competitionMatch
end

return AdvancedAIManager
