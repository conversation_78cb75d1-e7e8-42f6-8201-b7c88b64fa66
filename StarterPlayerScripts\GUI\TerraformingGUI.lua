-- StarterPlayerScripts/GUI/TerraformingGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Terraforming GUI wie in Transport Fever 2

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local Mouse = game.Players.LocalPlayer:GetMouse()

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local TerraformEvent = Events:WaitForChild("TerraformEvent")
local GetTerraformDataFunction = Events:WaitForChild("GetTerraformDataFunction")

local TerraformingGUI = {}
TerraformingGUI.IsOpen = false
TerraformingGUI.CurrentTool = "RAISE"
TerraformingGUI.BrushSize = 10
TerraformingGUI.Strength = 0.5
TerraformingGUI.SelectedMaterial = Enum.Material.Grass
TerraformingGUI.IsActive = false

-- Tool-Icons
local ToolIcons = {
    RAISE = "⬆️",
    LOWER = "⬇️",
    FLATTEN = "📏",
    SMOOTH = "🌊",
    ADD_WATER = "💧",
    REMOVE_WATER = "🏜️",
    PAINT = "🎨"
}

-- GUI erstellen
function TerraformingGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "TerraformingGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 400, 0, 600)
    mainFrame.Position = UDim2.new(0, 20, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🏔️ TERRAFORMING"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 0.9, 0)
    scrollFrame.Position = UDim2.new(0, 5, 0, 55)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = mainFrame
    
    local yPos = 10
    
    -- Tool-Auswahl
    self:CreateSection(scrollFrame, "🛠️ WERKZEUGE", yPos)
    yPos = yPos + 40
    
    local toolButtons = {}
    local toolsPerRow = 2
    local buttonSize = 80
    local spacing = 10
    
    local tools = {"RAISE", "LOWER", "FLATTEN", "SMOOTH", "ADD_WATER", "REMOVE_WATER", "PAINT"}
    
    for i, tool in ipairs(tools) do
        local row = math.floor((i - 1) / toolsPerRow)
        local col = (i - 1) % toolsPerRow
        
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(0, buttonSize, 0, buttonSize)
        button.Position = UDim2.new(0, 20 + col * (buttonSize + spacing), 0, yPos + row * (buttonSize + spacing))
        button.BackgroundColor3 = tool == self.CurrentTool and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 70, 80)
        button.Text = ToolIcons[tool] .. "\n" .. tool
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.BorderSizePixel = 0
        button.Parent = scrollFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 8)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SelectTool(tool)
            self:UpdateToolButtons(toolButtons)
        end)
        
        toolButtons[tool] = button
    end
    
    yPos = yPos + math.ceil(#tools / toolsPerRow) * (buttonSize + spacing) + 20
    
    -- Pinselgröße
    self:CreateSection(scrollFrame, "📏 PINSELGRÖSSE", yPos)
    yPos = yPos + 40
    
    self:CreateSlider(scrollFrame, "Größe:", yPos, 5, 100, self.BrushSize, function(value)
        self.BrushSize = value
    end)
    yPos = yPos + 60
    
    -- Stärke
    self:CreateSection(scrollFrame, "💪 STÄRKE", yPos)
    yPos = yPos + 40
    
    self:CreateSlider(scrollFrame, "Stärke:", yPos, 0.1, 2.0, self.Strength, function(value)
        self.Strength = value
    end)
    yPos = yPos + 60
    
    -- Material-Auswahl
    self:CreateSection(scrollFrame, "🎨 MATERIAL", yPos)
    yPos = yPos + 40
    
    local materials = {
        {Enum.Material.Grass, "🌱 Gras"},
        {Enum.Material.Rock, "🪨 Fels"},
        {Enum.Material.Sand, "🏖️ Sand"},
        {Enum.Material.Snow, "❄️ Schnee"},
        {Enum.Material.Mud, "🟤 Schlamm"},
        {Enum.Material.Asphalt, "🛣️ Asphalt"}
    }
    
    for i, materialData in ipairs(materials) do
        local material, name = materialData[1], materialData[2]
        
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1, -40, 0, 35)
        button.Position = UDim2.new(0, 20, 0, yPos)
        button.BackgroundColor3 = material == self.SelectedMaterial and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 70, 80)
        button.Text = name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.BorderSizePixel = 0
        button.Parent = scrollFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self.SelectedMaterial = material
            self:UpdateMaterialButtons(scrollFrame)
        end)
        
        yPos = yPos + 40
    end
    
    -- Aktions-Buttons
    self:CreateSection(scrollFrame, "⚡ AKTIONEN", yPos)
    yPos = yPos + 40
    
    -- Undo/Redo Buttons
    local undoButton = Instance.new("TextButton")
    undoButton.Size = UDim2.new(0.45, 0, 0, 40)
    undoButton.Position = UDim2.new(0, 20, 0, yPos)
    undoButton.BackgroundColor3 = Color3.fromRGB(80, 120, 80)
    undoButton.Text = "↶ UNDO"
    undoButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    undoButton.TextScaled = true
    undoButton.Font = Enum.Font.SourceSansBold
    undoButton.BorderSizePixel = 0
    undoButton.Parent = scrollFrame
    
    local undoCorner = Instance.new("UICorner")
    undoCorner.CornerRadius = UDim.new(0, 5)
    undoCorner.Parent = undoButton
    
    local redoButton = Instance.new("TextButton")
    redoButton.Size = UDim2.new(0.45, 0, 0, 40)
    redoButton.Position = UDim2.new(0.55, 0, 0, yPos)
    redoButton.BackgroundColor3 = Color3.fromRGB(80, 120, 80)
    redoButton.Text = "↷ REDO"
    redoButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    redoButton.TextScaled = true
    redoButton.Font = Enum.Font.SourceSansBold
    redoButton.BorderSizePixel = 0
    redoButton.Parent = scrollFrame
    
    local redoCorner = Instance.new("UICorner")
    redoCorner.CornerRadius = UDim.new(0, 5)
    redoCorner.Parent = redoButton
    
    undoButton.MouseButton1Click:Connect(function()
        TerraformEvent:FireServer("UNDO", {})
    end)
    
    redoButton.MouseButton1Click:Connect(function()
        TerraformEvent:FireServer("REDO", {})
    end)
    
    yPos = yPos + 50
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ToolButtons = toolButtons
    
    return screenGui
end

-- Sektion erstellen
function TerraformingGUI:CreateSection(parent, title, yPos)
    local section = Instance.new("TextLabel")
    section.Size = UDim2.new(1, -20, 0, 30)
    section.Position = UDim2.new(0, 10, 0, yPos)
    section.BackgroundColor3 = Color3.fromRGB(80, 120, 160)
    section.Text = title
    section.TextColor3 = Color3.fromRGB(255, 255, 255)
    section.TextScaled = true
    section.Font = Enum.Font.SourceSansBold
    section.BorderSizePixel = 0
    section.Parent = parent

    local sectionCorner = Instance.new("UICorner")
    sectionCorner.CornerRadius = UDim.new(0, 5)
    sectionCorner.Parent = section
end

-- Slider erstellen
function TerraformingGUI:CreateSlider(parent, label, yPos, minValue, maxValue, defaultValue, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0, 20, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    -- Slider-Hintergrund
    local sliderBg = Instance.new("Frame")
    sliderBg.Size = UDim2.new(0.4, 0, 0, 8)
    sliderBg.Position = UDim2.new(0.45, 0, 0, yPos + 11)
    sliderBg.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    sliderBg.BorderSizePixel = 0
    sliderBg.Parent = parent

    local sliderCorner = Instance.new("UICorner")
    sliderCorner.CornerRadius = UDim.new(0, 4)
    sliderCorner.Parent = sliderBg

    -- Slider-Knopf
    local sliderKnob = Instance.new("Frame")
    sliderKnob.Size = UDim2.new(0, 16, 0, 16)
    sliderKnob.Position = UDim2.new((defaultValue - minValue) / (maxValue - minValue), -8, 0, -4)
    sliderKnob.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    sliderKnob.BorderSizePixel = 0
    sliderKnob.Parent = sliderBg

    local knobCorner = Instance.new("UICorner")
    knobCorner.CornerRadius = UDim.new(0, 8)
    knobCorner.Parent = sliderKnob

    -- Wert-Anzeige
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Size = UDim2.new(0.1, 0, 0, 30)
    valueLabel.Position = UDim2.new(0.87, 0, 0, yPos)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = string.format("%.1f", defaultValue)
    valueLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSans
    valueLabel.Parent = parent

    -- Slider-Interaktion
    local isDragging = false

    sliderBg.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            isDragging = true
            local function updateSlider()
                local mousePos = input.Position.X
                local sliderPos = sliderBg.AbsolutePosition.X
                local sliderWidth = sliderBg.AbsoluteSize.X
                local relativePos = math.max(0, math.min(1, (mousePos - sliderPos) / sliderWidth))

                local value = minValue + (maxValue - minValue) * relativePos
                sliderKnob.Position = UDim2.new(relativePos, -8, 0, -4)
                valueLabel.Text = string.format("%.1f", value)

                if callback then callback(value) end
            end
            updateSlider()
        end
    end)
end

-- Tool auswählen
function TerraformingGUI:SelectTool(tool)
    self.CurrentTool = tool
    print("🛠️ Tool gewählt:", tool)
end

-- Tool-Buttons aktualisieren
function TerraformingGUI:UpdateToolButtons(toolButtons)
    for tool, button in pairs(toolButtons) do
        if tool == self.CurrentTool then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
        end
    end
end

-- Material-Buttons aktualisieren
function TerraformingGUI:UpdateMaterialButtons(parent)
    for _, child in pairs(parent:GetChildren()) do
        if child:IsA("TextButton") and child.Text:find("🌱") then
            -- Material-Button gefunden, Farbe aktualisieren
            -- (Vereinfachte Implementierung)
        end
    end
end

-- Terraforming durchführen
function TerraformingGUI:PerformTerraforming(position)
    if not self.IsActive then return end

    local data = {
        position = position,
        brushSize = self.BrushSize,
        strength = self.Strength,
        material = self.SelectedMaterial
    }

    if self.CurrentTool == "RAISE" then
        TerraformEvent:FireServer("RAISE_TERRAIN", data)
    elseif self.CurrentTool == "LOWER" then
        TerraformEvent:FireServer("LOWER_TERRAIN", data)
    elseif self.CurrentTool == "FLATTEN" then
        data.targetHeight = position.Y
        TerraformEvent:FireServer("FLATTEN_TERRAIN", data)
    elseif self.CurrentTool == "SMOOTH" then
        TerraformEvent:FireServer("SMOOTH_TERRAIN", data)
    elseif self.CurrentTool == "ADD_WATER" then
        data.depth = 10
        TerraformEvent:FireServer("ADD_WATER", data)
    elseif self.CurrentTool == "REMOVE_WATER" then
        TerraformEvent:FireServer("REMOVE_WATER", data)
    elseif self.CurrentTool == "PAINT" then
        TerraformEvent:FireServer("PAINT_TERRAIN", data)
    end
end

-- GUI öffnen
function TerraformingGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end

    self.MainFrame.Visible = true
    self.IsOpen = true
    self.IsActive = true

    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()

    print("🏔️ Terraforming-Modus aktiviert - Klicke auf das Terrain zum Bearbeiten")
end

-- GUI schließen
function TerraformingGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()

        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
            self.IsActive = false
        end)
    end
end

-- Maus-Klick Handler
Mouse.Button1Down:Connect(function()
    if TerraformingGUI.IsActive and TerraformingGUI.IsOpen then
        local hit = Mouse.Hit
        if hit then
            TerraformingGUI:PerformTerraforming(hit.Position)
        end
    end
end)

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.F then
        if TerraformingGUI.IsOpen then
            TerraformingGUI:CloseGUI()
        else
            TerraformingGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function TerraformingGUI:Initialize()
    print("🏔️ TerraformingGUI initialisiert - Drücke 'F' zum Öffnen")
end

-- Auto-Start
TerraformingGUI:Initialize()

return TerraformingGUI
