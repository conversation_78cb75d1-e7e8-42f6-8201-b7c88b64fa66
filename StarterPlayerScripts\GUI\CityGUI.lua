-- StarterPlayerScripts/GUI/CityGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- <PERSON>rweiterte Stadt-Verwaltung und Statistiken

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetCityDataFunction = Events:WaitForChild("GetCityDataFunction")
local RenameCityEvent = Events:WaitForChild("RenameCityEvent")

local CityGUI = {}
CityGUI.IsOpen = false
CityGUI.CurrentCity = nil
CityGUI.CityData = {}

-- GUI erstellen
function CityGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CityGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1000, 0, 700)
    mainFrame.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 60)
    title.BackgroundTransparency = 1
    title.Text = "🏙️ STADT-VERWALTUNG"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Stadt-Auswahl
    local citySelectFrame = Instance.new("Frame")
    citySelectFrame.Size = UDim2.new(1, -20, 0, 50)
    citySelectFrame.Position = UDim2.new(0, 10, 0, 70)
    citySelectFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    citySelectFrame.BorderSizePixel = 0
    citySelectFrame.Parent = mainFrame
    
    local selectCorner = Instance.new("UICorner")
    selectCorner.CornerRadius = UDim.new(0, 8)
    selectCorner.Parent = citySelectFrame
    
    local selectLabel = Instance.new("TextLabel")
    selectLabel.Size = UDim2.new(0, 150, 1, 0)
    selectLabel.Position = UDim2.new(0, 15, 0, 0)
    selectLabel.BackgroundTransparency = 1
    selectLabel.Text = "Stadt auswählen:"
    selectLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    selectLabel.TextScaled = true
    selectLabel.Font = Enum.Font.SourceSansBold
    selectLabel.TextXAlignment = Enum.TextXAlignment.Left
    selectLabel.Parent = citySelectFrame
    
    -- Stadt-Dropdown (vereinfacht als Buttons)
    local cityButtonsFrame = Instance.new("Frame")
    cityButtonsFrame.Size = UDim2.new(1, -180, 1, -10)
    cityButtonsFrame.Position = UDim2.new(0, 170, 0, 5)
    cityButtonsFrame.BackgroundTransparency = 1
    cityButtonsFrame.Parent = citySelectFrame
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 560)
    contentFrame.Position = UDim2.new(0, 10, 0, 130)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.CityButtonsFrame = cityButtonsFrame
    
    return screenGui
end

-- Stadt-Auswahl-Buttons erstellen
function CityGUI:CreateCityButtons()
    -- Alte Buttons löschen
    for _, child in pairs(self.CityButtonsFrame:GetChildren()) do
        child:Destroy()
    end
    
    local buttonWidth = 1 / math.max(1, #self.CityData)
    local i = 0
    
    for cityId, cityData in pairs(self.CityData) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(buttonWidth, -5, 1, -10)
        button.Position = UDim2.new(i * buttonWidth, 5, 0, 5)
        button.BackgroundColor3 = cityId == self.CurrentCity and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = cityData.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = self.CityButtonsFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SelectCity(cityId)
        end)
        
        i = i + 1
    end
end

-- Stadt auswählen
function CityGUI:SelectCity(cityId)
    self.CurrentCity = cityId
    self:CreateCityButtons() -- Buttons aktualisieren
    
    local cityData = self.CityData[cityId]
    if cityData then
        self:ShowCityDetails(cityData)
    end
end

-- Detaillierte Stadt-Informationen anzeigen
function CityGUI:ShowCityDetails(cityData)
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    local yPos = 10
    
    -- Stadt-Header
    local headerFrame = Instance.new("Frame")
    headerFrame.Size = UDim2.new(1, -20, 0, 80)
    headerFrame.Position = UDim2.new(0, 10, 0, yPos)
    headerFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    headerFrame.BorderSizePixel = 0
    headerFrame.Parent = scrollFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 10)
    headerCorner.Parent = headerFrame
    
    -- Stadt-Name (editierbar)
    local nameFrame = Instance.new("Frame")
    nameFrame.Size = UDim2.new(0.6, 0, 0, 40)
    nameFrame.Position = UDim2.new(0, 15, 0, 10)
    nameFrame.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
    nameFrame.BorderSizePixel = 0
    nameFrame.Parent = headerFrame
    
    local nameCorner = Instance.new("UICorner")
    nameCorner.CornerRadius = UDim.new(0, 5)
    nameCorner.Parent = nameFrame
    
    local nameBox = Instance.new("TextBox")
    nameBox.Size = UDim2.new(1, -20, 1, 0)
    nameBox.Position = UDim2.new(0, 10, 0, 0)
    nameBox.BackgroundTransparency = 1
    nameBox.Text = cityData.name
    nameBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameBox.TextScaled = true
    nameBox.Font = Enum.Font.SourceSansBold
    nameBox.TextXAlignment = Enum.TextXAlignment.Left
    nameBox.Parent = nameFrame
    
    -- Umbenennen-Button
    local renameButton = Instance.new("TextButton")
    renameButton.Size = UDim2.new(0.15, 0, 0, 40)
    renameButton.Position = UDim2.new(0.62, 0, 0, 10)
    renameButton.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
    renameButton.Text = "✏️ Umbenennen"
    renameButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    renameButton.TextScaled = true
    renameButton.Font = Enum.Font.SourceSansBold
    renameButton.BorderSizePixel = 0
    renameButton.Parent = headerFrame
    
    local renameCorner = Instance.new("UICorner")
    renameCorner.CornerRadius = UDim.new(0, 5)
    renameCorner.Parent = renameButton
    
    renameButton.MouseButton1Click:Connect(function()
        self:RenameCity(cityData.id, nameBox.Text)
    end)
    
    -- Einwohnerzahl
    local populationLabel = Instance.new("TextLabel")
    populationLabel.Size = UDim2.new(0.2, 0, 0, 25)
    populationLabel.Position = UDim2.new(0.78, 0, 0, 15)
    populationLabel.BackgroundTransparency = 1
    populationLabel.Text = "👥 " .. string.format("%,d", cityData.population) .. " Einwohner"
    populationLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    populationLabel.TextScaled = true
    populationLabel.Font = Enum.Font.SourceSans
    populationLabel.Parent = headerFrame
    
    -- Stadt-Typ
    local typeLabel = Instance.new("TextLabel")
    typeLabel.Size = UDim2.new(0.3, 0, 0, 20)
    typeLabel.Position = UDim2.new(0, 15, 0, 55)
    typeLabel.BackgroundTransparency = 1
    typeLabel.Text = "🏙️ " .. (cityData.cityType or "Unbekannt")
    typeLabel.TextColor3 = Color3.fromRGB(150, 200, 255)
    typeLabel.TextScaled = true
    typeLabel.Font = Enum.Font.SourceSans
    typeLabel.TextXAlignment = Enum.TextXAlignment.Left
    typeLabel.Parent = headerFrame
    
    -- Wachstumsrate
    local growthLabel = Instance.new("TextLabel")
    growthLabel.Size = UDim2.new(0.3, 0, 0, 20)
    growthLabel.Position = UDim2.new(0.35, 0, 0, 55)
    growthLabel.BackgroundTransparency = 1
    growthLabel.Text = "📈 +" .. string.format("%.1f", (cityData.growthRate or 0)) .. "‰ Wachstum"
    growthLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
    growthLabel.TextScaled = true
    growthLabel.Font = Enum.Font.SourceSans
    growthLabel.TextXAlignment = Enum.TextXAlignment.Left
    growthLabel.Parent = headerFrame
    
    yPos = yPos + 100
    
    -- Lebensqualität-Karte
    local qualityFrame = Instance.new("Frame")
    qualityFrame.Size = UDim2.new(1, -20, 0, 150)
    qualityFrame.Position = UDim2.new(0, 10, 0, yPos)
    qualityFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    qualityFrame.BorderSizePixel = 0
    qualityFrame.Parent = scrollFrame
    
    local qualityCorner = Instance.new("UICorner")
    qualityCorner.CornerRadius = UDim.new(0, 10)
    qualityCorner.Parent = qualityFrame
    
    local qualityTitle = Instance.new("TextLabel")
    qualityTitle.Size = UDim2.new(1, 0, 0, 30)
    qualityTitle.Position = UDim2.new(0, 15, 0, 10)
    qualityTitle.BackgroundTransparency = 1
    qualityTitle.Text = "😊 LEBENSQUALITÄT"
    qualityTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    qualityTitle.TextScaled = true
    qualityTitle.Font = Enum.Font.SourceSansBold
    qualityTitle.TextXAlignment = Enum.TextXAlignment.Left
    qualityTitle.Parent = qualityFrame
    
    -- Qualitäts-Indikatoren
    local qualityStats = {
        {label = "😊 Zufriedenheit", value = cityData.happiness or 75, color = Color3.fromRGB(100, 255, 100)},
        {label = "🏥 Gesundheit", value = cityData.healthLevel or 80, color = Color3.fromRGB(255, 150, 150)},
        {label = "🌱 Umwelt", value = 100 - (cityData.pollution or 20), color = Color3.fromRGB(150, 255, 150)},
        {label = "💼 Beschäftigung", value = 100 - (cityData.unemployment or 8), color = Color3.fromRGB(150, 200, 255)}
    }
    
    for i, stat in ipairs(qualityStats) do
        local statFrame = Instance.new("Frame")
        statFrame.Size = UDim2.new(0.23, 0, 0, 80)
        statFrame.Position = UDim2.new((i-1) * 0.25, 10, 0, 50)
        statFrame.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
        statFrame.BorderSizePixel = 0
        statFrame.Parent = qualityFrame
        
        local statCorner = Instance.new("UICorner")
        statCorner.CornerRadius = UDim.new(0, 8)
        statCorner.Parent = statFrame
        
        local statLabel = Instance.new("TextLabel")
        statLabel.Size = UDim2.new(1, -10, 0, 25)
        statLabel.Position = UDim2.new(0, 5, 0, 5)
        statLabel.BackgroundTransparency = 1
        statLabel.Text = stat.label
        statLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        statLabel.TextScaled = true
        statLabel.Font = Enum.Font.SourceSans
        statLabel.Parent = statFrame
        
        -- Fortschrittsbalken
        local progressBg = Instance.new("Frame")
        progressBg.Size = UDim2.new(1, -20, 0, 20)
        progressBg.Position = UDim2.new(0, 10, 0, 35)
        progressBg.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
        progressBg.BorderSizePixel = 0
        progressBg.Parent = statFrame
        
        local progressCorner = Instance.new("UICorner")
        progressCorner.CornerRadius = UDim.new(0, 5)
        progressCorner.Parent = progressBg
        
        local progressBar = Instance.new("Frame")
        progressBar.Size = UDim2.new(stat.value / 100, 0, 1, 0)
        progressBar.BackgroundColor3 = stat.color
        progressBar.BorderSizePixel = 0
        progressBar.Parent = progressBg
        
        local progressBarCorner = Instance.new("UICorner")
        progressBarCorner.CornerRadius = UDim.new(0, 5)
        progressBarCorner.Parent = progressBar
        
        local valueLabel = Instance.new("TextLabel")
        valueLabel.Size = UDim2.new(1, -10, 0, 15)
        valueLabel.Position = UDim2.new(0, 5, 0, 60)
        valueLabel.BackgroundTransparency = 1
        valueLabel.Text = string.format("%.0f%%", stat.value)
        valueLabel.TextColor3 = stat.color
        valueLabel.TextScaled = true
        valueLabel.Font = Enum.Font.SourceSansBold
        valueLabel.Parent = statFrame
    end
    
    yPos = yPos + 170
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Stadt umbenennen
function CityGUI:RenameCity(cityId, newName)
    if newName and newName ~= "" then
        RenameCityEvent:FireServer(cityId, newName)
        print("🏙️ Stadt-Umbenennung angefordert:", cityId, "→", newName)
        
        -- Lokale Daten aktualisieren
        if self.CityData[cityId] then
            self.CityData[cityId].name = newName
            self:CreateCityButtons() -- Buttons aktualisieren
        end
    end
end

-- GUI öffnen
function CityGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadCityData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function CityGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Stadt-Daten laden
function CityGUI:LoadCityData()
    local success, data = pcall(function()
        return GetCityDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.CityData = data
        self:CreateCityButtons()
        
        -- Erste Stadt automatisch auswählen
        if not self.CurrentCity then
            for cityId, _ in pairs(data) do
                self:SelectCity(cityId)
                break
            end
        elseif self.CityData[self.CurrentCity] then
            self:ShowCityDetails(self.CityData[self.CurrentCity])
        end
    else
        warn("Fehler beim Laden der Stadt-Daten")
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.Y then
        if CityGUI.IsOpen then
            CityGUI:CloseGUI()
        else
            CityGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function CityGUI:Initialize()
    print("🏙️ CityGUI initialisiert - Drücke 'Y' zum Öffnen")
end

-- Auto-Start
CityGUI:Initialize()

return CityGUI
