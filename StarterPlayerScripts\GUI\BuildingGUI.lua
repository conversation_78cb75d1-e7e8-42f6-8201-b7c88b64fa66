-- StarterPlayerScripts/GUI/BuildingGUI.lua
-- Client-seitige <PERSON>-GUI
-- ROBLOX SCRIPT TYPE: LocalScript

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local BuildingGUI = {}
BuildingGUI.__index = BuildingGUI

-- Initialisierung
function BuildingGUI.new()
    local self = setmetatable({}, BuildingGUI)
    
    self.IsOpen = false
    self.SelectedCategory = "Residential"
    self.AvailableBuildings = {}
    self.CurrentYear = 1850
    
    self:CreateGUI()
    self:ConnectEvents()
    
    return self
end

-- GUI erstellen
function BuildingGUI:CreateGUI()
    -- Haupt-ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "BuildingGUI"
    screenGui.Parent = playerGui
    screenGui.ResetOnSpawn = false
    
    -- Haupt-Frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0.8, 0, 0.7, 0)
    mainFrame.Position = UDim2.new(0.1, 0, 0.15, 0)
    mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Titel
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, 0, 0.1, 0)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundColor3 = Color3.new(0.2, 0.4, 0.6)
    titleLabel.BorderSizePixel = 0
    titleLabel.Text = "🏗️ Gebäude-Menü - Jahr: " .. self.CurrentYear
    titleLabel.TextColor3 = Color3.new(1, 1, 1)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.Parent = mainFrame
    
    -- Kategorie-Buttons
    local categoryFrame = Instance.new("Frame")
    categoryFrame.Name = "CategoryFrame"
    categoryFrame.Size = UDim2.new(0.2, 0, 0.8, 0)
    categoryFrame.Position = UDim2.new(0, 0, 0.1, 0)
    categoryFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
    categoryFrame.BorderSizePixel = 0
    categoryFrame.Parent = mainFrame
    
    local categories = {"Residential", "Commercial", "Industry", "Public"}
    for i, category in pairs(categories) do
        local categoryButton = Instance.new("TextButton")
        categoryButton.Name = category .. "Button"
        categoryButton.Size = UDim2.new(1, -10, 0.2, -5)
        categoryButton.Position = UDim2.new(0, 5, (i-1) * 0.2, 5)
        categoryButton.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
        categoryButton.BorderSizePixel = 0
        categoryButton.Text = category
        categoryButton.TextColor3 = Color3.new(1, 1, 1)
        categoryButton.TextScaled = true
        categoryButton.Font = Enum.Font.SourceSans
        categoryButton.Parent = categoryFrame
        
        -- Button-Event
        categoryButton.MouseButton1Click:Connect(function()
            self:SelectCategory(category)
        end)
    end
    
    -- Gebäude-Liste
    local buildingFrame = Instance.new("ScrollingFrame")
    buildingFrame.Name = "BuildingFrame"
    buildingFrame.Size = UDim2.new(0.8, -10, 0.8, 0)
    buildingFrame.Position = UDim2.new(0.2, 5, 0.1, 0)
    buildingFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
    buildingFrame.BorderSizePixel = 0
    buildingFrame.ScrollBarThickness = 10
    buildingFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    buildingFrame.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0.1, 0, 0.1, 0)
    closeButton.Position = UDim2.new(0.9, 0, 0, 0)
    closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = mainFrame
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.BuildingFrame = buildingFrame
    self.TitleLabel = titleLabel
end

-- Events verbinden
function BuildingGUI:ConnectEvents()
    -- Keyboard-Input für GUI öffnen/schließen
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.B then
            if self.IsOpen then
                self:CloseGUI()
            else
                self:OpenGUI()
            end
        end
    end)
    
    -- Server-Events
    local buildingDataEvent = ReplicatedStorage.Events:WaitForChild("BuildingDataEvent")
    buildingDataEvent.OnClientEvent:Connect(function(buildings, currentYear)
        self.AvailableBuildings = buildings
        self.CurrentYear = currentYear
        self:UpdateGUI()
    end)
    
    local buildingCompleteEvent = ReplicatedStorage.Events:WaitForChild("BuildingCompleteEvent")
    buildingCompleteEvent.OnClientEvent:Connect(function(building)
        self:OnBuildingComplete(building)
    end)
end

-- GUI öffnen
function BuildingGUI:OpenGUI()
    if self.IsOpen then return end
    
    self.IsOpen = true
    self.MainFrame.Visible = true
    
    -- Aktuelle Gebäude-Daten vom Server anfordern
    local getBuildingDataFunction = ReplicatedStorage.Events:WaitForChild("GetBuildingDataFunction")
    local success, buildings, currentYear = pcall(function()
        return getBuildingDataFunction:InvokeServer(self.SelectedCategory)
    end)
    
    if success then
        self.AvailableBuildings = buildings
        self.CurrentYear = currentYear
        self:UpdateGUI()
    end
    
    -- Smooth Fade-In
    self.MainFrame.BackgroundTransparency = 1
    local fadeIn = TweenService:Create(
        self.MainFrame,
        TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundTransparency = 0}
    )
    fadeIn:Play()
end

-- GUI schließen
function BuildingGUI:CloseGUI()
    if not self.IsOpen then return end
    
    self.IsOpen = false
    
    -- Smooth Fade-Out
    local fadeOut = TweenService:Create(
        self.MainFrame,
        TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
        {BackgroundTransparency = 1}
    )
    fadeOut:Play()
    
    fadeOut.Completed:Connect(function()
        self.MainFrame.Visible = false
    end)
end

-- Kategorie auswählen
function BuildingGUI:SelectCategory(category)
    self.SelectedCategory = category
    
    -- Button-Farben aktualisieren
    for _, button in pairs(self.MainFrame.CategoryFrame:GetChildren()) do
        if button:IsA("TextButton") then
            if button.Name == category .. "Button" then
                button.BackgroundColor3 = Color3.new(0.2, 0.4, 0.6)
            else
                button.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
            end
        end
    end
    
    -- Gebäude-Liste aktualisieren
    local getBuildingDataFunction = ReplicatedStorage.Events:WaitForChild("GetBuildingDataFunction")
    local success, buildings, currentYear = pcall(function()
        return getBuildingDataFunction:InvokeServer(category)
    end)
    
    if success then
        self.AvailableBuildings = buildings
        self:UpdateBuildingList()
    end
end

-- GUI aktualisieren
function BuildingGUI:UpdateGUI()
    self.TitleLabel.Text = "🏗️ Gebäude-Menü - Jahr: " .. self.CurrentYear
    self:UpdateBuildingList()
end

-- Gebäude-Liste aktualisieren
function BuildingGUI:UpdateBuildingList()
    -- Alte Einträge löschen
    for _, child in pairs(self.BuildingFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    local yPosition = 0
    local entryHeight = 100
    
    for buildingId, buildingData in pairs(self.AvailableBuildings) do
        local buildingEntry = self:CreateBuildingEntry(buildingData, yPosition)
        buildingEntry.Parent = self.BuildingFrame
        yPosition = yPosition + entryHeight + 10
    end
    
    -- Canvas-Größe anpassen
    self.BuildingFrame.CanvasSize = UDim2.new(0, 0, 0, yPosition)
end

-- Gebäude-Eintrag erstellen
function BuildingGUI:CreateBuildingEntry(buildingData, yPosition)
    local entryFrame = Instance.new("Frame")
    entryFrame.Name = buildingData.ID .. "Entry"
    entryFrame.Size = UDim2.new(1, -20, 0, 100)
    entryFrame.Position = UDim2.new(0, 10, 0, yPosition)
    entryFrame.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
    entryFrame.BorderSizePixel = 1
    entryFrame.BorderColor3 = Color3.new(0.4, 0.4, 0.4)
    
    -- Name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(0.4, 0, 0.4, 0)
    nameLabel.Position = UDim2.new(0.05, 0, 0.05, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = buildingData.Name or buildingData.ID
    nameLabel.TextColor3 = Color3.new(1, 1, 1)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = entryFrame
    
    -- Kosten
    local costLabel = Instance.new("TextLabel")
    costLabel.Name = "CostLabel"
    costLabel.Size = UDim2.new(0.3, 0, 0.3, 0)
    costLabel.Position = UDim2.new(0.05, 0, 0.45, 0)
    costLabel.BackgroundTransparency = 1
    costLabel.Text = "💰 " .. (buildingData.BuildCost or 0) .. " $"
    costLabel.TextColor3 = Color3.new(0.9, 0.9, 0.5)
    costLabel.TextScaled = true
    costLabel.Font = Enum.Font.SourceSans
    costLabel.TextXAlignment = Enum.TextXAlignment.Left
    costLabel.Parent = entryFrame
    
    -- Info
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Name = "InfoLabel"
    infoLabel.Size = UDim2.new(0.3, 0, 0.3, 0)
    infoLabel.Position = UDim2.new(0.05, 0, 0.7, 0)
    infoLabel.BackgroundTransparency = 1
    
    local infoText = ""
    if buildingData.Population then
        infoText = "👥 " .. buildingData.Population .. " Einwohner"
    elseif buildingData.Workers then
        infoText = "👷 " .. buildingData.Workers .. " Arbeiter"
    end
    
    infoLabel.Text = infoText
    infoLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.SourceSans
    infoLabel.TextXAlignment = Enum.TextXAlignment.Left
    infoLabel.Parent = entryFrame
    
    -- Bauen-Button
    local buildButton = Instance.new("TextButton")
    buildButton.Name = "BuildButton"
    buildButton.Size = UDim2.new(0.2, 0, 0.6, 0)
    buildButton.Position = UDim2.new(0.75, 0, 0.2, 0)
    buildButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
    buildButton.BorderSizePixel = 0
    buildButton.Text = "🏗️ Bauen"
    buildButton.TextColor3 = Color3.new(1, 1, 1)
    buildButton.TextScaled = true
    buildButton.Font = Enum.Font.SourceSansBold
    buildButton.Parent = entryFrame
    
    -- Build-Event
    buildButton.MouseButton1Click:Connect(function()
        self:BuildBuilding(buildingData.ID)
    end)
    
    return entryFrame
end

-- Gebäude bauen
function BuildingGUI:BuildBuilding(buildingId)
    local buildBuildingEvent = ReplicatedStorage.Events:WaitForChild("BuildBuildingEvent")
    buildBuildingEvent:FireServer(buildingId, Vector3.new(0, 0, 0))  -- Position später implementieren
    
    print("🏗️ Gebäude-Bau angefordert:", buildingId)
end

-- Gebäude-Bau abgeschlossen
function BuildingGUI:OnBuildingComplete(building)
    print("🏗️ Gebäude fertiggestellt:", building.Data.Name)
    -- Hier könnte eine Benachrichtigung angezeigt werden
end

-- GUI initialisieren
local buildingGUI = BuildingGUI.new()

return buildingGUI
