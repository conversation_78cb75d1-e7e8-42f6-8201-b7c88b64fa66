# Viktorianischer Laden
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "SHOP_VICTORIAN"
Name = "Viktorianischer Laden"
Category = "Commercial"
Size = "Small"
Era = "Victorian"
Era_Years = {1850, 1900}

[MODEL_DATA]
ModelId = "rbxassetid://SHOP_VICTORIAN"
Scale = Vector3(15, 8, 12)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.7, 0.5, 0.3)     # Braunes Holz
Secondary = Color3(0.8, 0.2, 0.2)   # Rotes Backstein
Accent = Color3(0.9, 0.9, 0.7)      # Cremefarbene Fenster
Trim = Color3(0.3, 0.2, 0.1)        # Dunkles Holz

[GAMEPLAY_STATS]
Workers = 3
BuildCost = 3500
MaintenanceCost = 35
BuildTime = 30
PowerConsumption = 5
LandSize = Vector2(3, 4)

[COMMERCIAL_DATA]
ShopType = "General_Store"
CustomerCapacity = 20
ServiceRadius = 150
GoodsTypes = {"Food", "Clothing", "Tools"}
Revenue = 180  # Pro Monat

[REQUIREMENTS]
MinPopulation = 200
MinYear = 1850
RequiredTech = {"Basic_Commerce"}
RequiredResources = {"Wood", "Brick"}
UnlockCost = 500

[FEATURES]
ArchitecturalStyle = "Victorian_Commercial"
HasStorefront = true
HasLivingQuarters = true
HasStorage = true
WindowStyle = "Large_Display_Windows"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "DEPARTMENT_STORE_VICTORIAN"
UpgradeCost = 5000
UpgradeTime = 45

[ECONOMIC_DATA]
TaxRevenue = 25
PropertyValue = 3500
AttractsCustomers = true
BoostsLocalEconomy = true

[DESCRIPTION]
ShortDesc = "Traditioneller Laden der viktorianischen Zeit"
LongDesc = "Ein gemütlicher Laden mit Wohnbereich darüber. Verkauft Grundbedarf an die lokale Bevölkerung und trägt zur wirtschaftlichen Entwicklung bei."
