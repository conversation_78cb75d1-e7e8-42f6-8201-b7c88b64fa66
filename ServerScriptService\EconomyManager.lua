-- ServerScriptService/EconomyManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vollständiges Wirtschaftssystem für Transport Fever 2

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")

local EconomyManager = {}
EconomyManager.__index = EconomyManager

function EconomyManager.new()
    local self = setmetatable({}, EconomyManager)
    
    -- Economic Data
    self.playerFinances = {}
    self.marketPrices = {
        passengers = {base = 2.5, current = 2.5, trend = 0},
        mail = {base = 1.8, current = 1.8, trend = 0},
        coal = {base = 15, current = 15, trend = 0},
        iron = {base = 25, current = 25, trend = 0},
        steel = {base = 45, current = 45, trend = 0},
        wood = {base = 12, current = 12, trend = 0},
        grain = {base = 8, current = 8, trend = 0},
        livestock = {base = 20, current = 20, trend = 0},
        goods = {base = 35, current = 35, trend = 0},
        oil = {base = 30, current = 30, trend = 0}
    }
    
    -- Interest rates
    self.interestRates = {
        easy = 0.02,    -- 2% per month
        normal = 0.035, -- 3.5% per month
        hard = 0.05,    -- 5% per month
        expert = 0.075  -- 7.5% per month
    }
    
    -- Economic cycles
    self.economicCycle = {
        phase = "growth", -- growth, peak, recession, trough
        duration = 0,
        maxDuration = 120 -- 120 game days per phase
    }
    
    return self
end

-- Initialize player finances
function EconomyManager:InitializePlayerFinances(player)
    if self.playerFinances[player.UserId] then return end
    
    self.playerFinances[player.UserId] = {
        balance = 2000000, -- Starting balance: 2M
        monthlyIncome = 0,
        monthlyExpenses = 0,
        loans = {},
        transactions = {},
        statistics = {
            totalRevenue = 0,
            totalExpenses = 0,
            totalProfit = 0,
            bestMonth = 0,
            worstMonth = 0
        },
        lastUpdate = os.time()
    }
end

-- Get player finances
function EconomyManager:GetPlayerFinances(player)
    self:InitializePlayerFinances(player)
    return self.playerFinances[player.UserId]
end

-- Add transaction
function EconomyManager:AddTransaction(player, amount, description, category)
    local finances = self:GetPlayerFinances(player)
    
    local transaction = {
        id = HttpService:GenerateGUID(false),
        amount = amount,
        description = description,
        category = category, -- income, expense, loan, repayment
        timestamp = os.time(),
        date = self:GetGameDate()
    }
    
    table.insert(finances.transactions, transaction)
    
    -- Update balance
    finances.balance = finances.balance + amount
    
    -- Update monthly totals
    if amount > 0 then
        finances.monthlyIncome = finances.monthlyIncome + amount
        finances.statistics.totalRevenue = finances.statistics.totalRevenue + amount
    else
        finances.monthlyExpenses = finances.monthlyExpenses + math.abs(amount)
        finances.statistics.totalExpenses = finances.statistics.totalExpenses + math.abs(amount)
    end
    
    -- Keep only last 1000 transactions
    if #finances.transactions > 1000 then
        table.remove(finances.transactions, 1)
    end
    
    -- Notify client
    ReplicatedStorage.Events.TransactionAddedEvent:FireClient(player, transaction)
    
    print("💰 Transaction:", player.Name, amount, description)
end

-- Loan System
function EconomyManager:RequestLoan(player, amount)
    local finances = self:GetPlayerFinances(player)
    local playerData = self:GetPlayerData(player)
    
    -- Validate loan amount
    if amount < 50000 or amount > 10000000 then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Ungültiger Kreditbetrag!", "error")
        return
    end
    
    -- Check credit rating
    local creditRating = self:CalculateCreditRating(player)
    local maxLoan = creditRating * 1000000 -- Credit rating * 1M
    
    if amount > maxLoan then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Kreditlimit überschritten!", "error")
        return
    end
    
    -- Check existing loans
    local totalDebt = 0
    for _, loan in pairs(finances.loans) do
        totalDebt = totalDebt + loan.remaining
    end
    
    if totalDebt + amount > maxLoan * 2 then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Zu viele Schulden!", "error")
        return
    end
    
    -- Create loan
    local difficulty = playerData.settings.gameplay.difficulty or "Normal"
    local interestRate = self.interestRates[difficulty:lower()] or self.interestRates.normal
    
    local loan = {
        id = HttpService:GenerateGUID(false),
        amount = amount,
        remaining = amount,
        interestRate = interestRate,
        monthlyPayment = math.ceil(amount * (interestRate + 0.01)), -- Principal + interest
        startDate = self:GetGameDate(),
        nextPayment = self:GetNextMonthDate(),
        status = "active"
    }
    
    finances.loans[loan.id] = loan
    
    -- Add money to balance
    self:AddTransaction(player, amount, "Kredit aufgenommen: " .. amount .. " $", "loan")
    
    -- Notify client
    ReplicatedStorage.Events.LoanApprovedEvent:FireClient(player, loan)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Kredit genehmigt: " .. amount .. " $", "success")
    
    print("🏦 Loan approved:", player.Name, amount, "Interest:", interestRate * 100 .. "%")
end

-- Repay loan
function EconomyManager:RepayLoan(player, loanId, amount)
    local finances = self:GetPlayerFinances(player)
    local loan = finances.loans[loanId]
    
    if not loan or loan.status ~= "active" then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Kredit nicht gefunden!", "error")
        return
    end
    
    if amount > finances.balance then
        ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Nicht genügend Geld!", "error")
        return
    end
    
    if amount > loan.remaining then
        amount = loan.remaining
    end
    
    -- Update loan
    loan.remaining = loan.remaining - amount
    
    if loan.remaining <= 0 then
        loan.status = "paid"
        loan.remaining = 0
    end
    
    -- Deduct money
    self:AddTransaction(player, -amount, "Kreditrückzahlung: " .. loanId, "repayment")
    
    -- Notify client
    ReplicatedStorage.Events.LoanRepaidEvent:FireClient(player, loan)
    ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Kredit zurückgezahlt: " .. amount .. " $", "success")
    
    print("💳 Loan repayment:", player.Name, amount, "Remaining:", loan.remaining)
end

-- Calculate credit rating (1-10 scale)
function EconomyManager:CalculateCreditRating(player)
    local finances = self:GetPlayerFinances(player)
    local playerData = self:GetPlayerData(player)
    
    local rating = 5 -- Base rating
    
    -- Balance factor
    if finances.balance > 5000000 then rating = rating + 2
    elseif finances.balance > 1000000 then rating = rating + 1
    elseif finances.balance < 100000 then rating = rating - 2
    
    -- Profit factor
    local monthlyProfit = finances.monthlyIncome - finances.monthlyExpenses
    if monthlyProfit > 500000 then rating = rating + 2
    elseif monthlyProfit > 100000 then rating = rating + 1
    elseif monthlyProfit < -100000 then rating = rating - 2
    
    -- Loan history
    local totalLoans = 0
    local paidLoans = 0
    for _, loan in pairs(finances.loans) do
        totalLoans = totalLoans + 1
        if loan.status == "paid" then paidLoans = paidLoans + 1 end
    end
    
    if totalLoans > 0 then
        local paymentRatio = paidLoans / totalLoans
        if paymentRatio > 0.8 then rating = rating + 1
        elseif paymentRatio < 0.5 then rating = rating - 1
    end
    
    -- Level factor
    rating = rating + math.floor(playerData.level / 10)
    
    return math.max(1, math.min(10, rating))
end

-- Market price updates
function EconomyManager:UpdateMarketPrices()
    for commodity, data in pairs(self.marketPrices) do
        -- Random price fluctuation
        local change = (math.random() - 0.5) * 0.1 -- ±5% max change
        
        -- Economic cycle influence
        local cycleMultiplier = 1
        if self.economicCycle.phase == "growth" then
            cycleMultiplier = 1.02
        elseif self.economicCycle.phase == "recession" then
            cycleMultiplier = 0.98
        end
        
        data.current = data.current * (1 + change) * cycleMultiplier
        data.current = math.max(data.base * 0.5, math.min(data.base * 2, data.current))
        data.trend = change
    end
end

-- Economic cycle management
function EconomyManager:UpdateEconomicCycle()
    self.economicCycle.duration = self.economicCycle.duration + 1
    
    if self.economicCycle.duration >= self.economicCycle.maxDuration then
        self.economicCycle.duration = 0
        
        -- Transition to next phase
        local phases = {"growth", "peak", "recession", "trough"}
        local currentIndex = 1
        for i, phase in ipairs(phases) do
            if phase == self.economicCycle.phase then
                currentIndex = i
                break
            end
        end
        
        local nextIndex = (currentIndex % #phases) + 1
        self.economicCycle.phase = phases[nextIndex]
        
        -- Notify all players
        ReplicatedStorage.Events.EconomicCycleChangedEvent:FireAllClients(self.economicCycle.phase)
        print("📈 Economic cycle changed to:", self.economicCycle.phase)
    end
end

-- Calculate transport revenue
function EconomyManager:CalculateTransportRevenue(player, transportType, distance, cargoType, amount)
    local basePrice = self.marketPrices[cargoType] and self.marketPrices[cargoType].current or 1
    local distanceMultiplier = math.min(2, 1 + (distance / 1000)) -- Max 2x for long distances
    
    -- Transport type efficiency
    local efficiency = {
        train = 1.2,
        truck = 1.0,
        ship = 1.5,
        plane = 0.8
    }
    
    local revenue = amount * basePrice * distanceMultiplier * (efficiency[transportType] or 1)
    
    -- Add some randomness (±10%)
    revenue = revenue * (0.9 + math.random() * 0.2)
    
    return math.floor(revenue)
end

-- Monthly financial update
function EconomyManager:ProcessMonthlyFinances(player)
    local finances = self:GetPlayerFinances(player)
    
    -- Process loan payments
    for loanId, loan in pairs(finances.loans) do
        if loan.status == "active" then
            if finances.balance >= loan.monthlyPayment then
                local interestPayment = math.ceil(loan.remaining * loan.interestRate)
                local principalPayment = loan.monthlyPayment - interestPayment
                
                loan.remaining = loan.remaining - principalPayment
                
                if loan.remaining <= 0 then
                    loan.status = "paid"
                    loan.remaining = 0
                end
                
                self:AddTransaction(player, -loan.monthlyPayment, "Monatliche Kreditrate: " .. loanId, "expense")
            else
                -- Missed payment - penalty
                self:AddTransaction(player, -5000, "Säumnisgebühr: " .. loanId, "expense")
                ReplicatedStorage.Events.NotificationEvent:FireClient(player, "Kreditrate nicht bezahlt! Säumnisgebühr: 5000$", "warning")
            end
        end
    end
    
    -- Calculate monthly profit
    local monthlyProfit = finances.monthlyIncome - finances.monthlyExpenses
    finances.statistics.totalProfit = finances.statistics.totalProfit + monthlyProfit
    
    -- Update best/worst month
    if monthlyProfit > finances.statistics.bestMonth then
        finances.statistics.bestMonth = monthlyProfit
    end
    if monthlyProfit < finances.statistics.worstMonth then
        finances.statistics.worstMonth = monthlyProfit
    end
    
    -- Reset monthly counters
    finances.monthlyIncome = 0
    finances.monthlyExpenses = 0
    
    -- Notify client
    ReplicatedStorage.Events.MonthlyFinancesEvent:FireClient(player, {
        profit = monthlyProfit,
        balance = finances.balance,
        loans = finances.loans
    })
    
    print("📊 Monthly finances processed for:", player.Name, "Profit:", monthlyProfit)
end

-- Get economy statistics
function EconomyManager:GetEconomyStats(player)
    local finances = self:GetPlayerFinances(player)
    
    return {
        balance = finances.balance,
        monthlyIncome = finances.monthlyIncome,
        monthlyExpenses = finances.monthlyExpenses,
        totalLoans = self:GetTotalDebt(player),
        creditRating = self:CalculateCreditRating(player),
        marketPrices = self.marketPrices,
        economicCycle = self.economicCycle,
        statistics = finances.statistics
    }
end

-- Get total debt
function EconomyManager:GetTotalDebt(player)
    local finances = self:GetPlayerFinances(player)
    local totalDebt = 0
    
    for _, loan in pairs(finances.loans) do
        if loan.status == "active" then
            totalDebt = totalDebt + loan.remaining
        end
    end
    
    return totalDebt
end

-- Get monthly profit
function EconomyManager:GetMonthlyProfit(player)
    local finances = self:GetPlayerFinances(player)
    return finances.monthlyIncome - finances.monthlyExpenses
end

-- Update function (called by GameManager)
function EconomyManager:Update(deltaTime)
    -- Update market prices every game day
    if math.random() < 0.1 then -- 10% chance per update
        self:UpdateMarketPrices()
    end
    
    -- Update economic cycle
    self:UpdateEconomicCycle()
    
    -- Process monthly finances for all players
    for _, player in pairs(Players:GetPlayers()) do
        if math.random() < 0.01 then -- 1% chance per update (roughly monthly)
            self:ProcessMonthlyFinances(player)
        end
    end
end

-- Helper functions
function EconomyManager:GetGameDate()
    -- This would get the current game date from GameManager
    return {year = 1850, month = 1, day = 1}
end

function EconomyManager:GetNextMonthDate()
    local date = self:GetGameDate()
    date.month = date.month + 1
    if date.month > 12 then
        date.month = 1
        date.year = date.year + 1
    end
    return date
end

function EconomyManager:GetPlayerData(player)
    -- Get player data from GameManager
    local GameManager = require(script.Parent.GameManager)
    return GameManager:GetPlayerData(player)
end

return EconomyManager
