-- StarterPlayerScripts/GUI/MarketGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Markt-GUI für Handel und Preisübersicht

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetMarketDataFunction = Events:WaitForChild("GetMarketDataFunction")
local TradeResourceEvent = Events:WaitForChild("TradeResourceEvent")

local MarketGUI = {}
MarketGUI.IsOpen = false
MarketGUI.CurrentTab = "PRICES" -- PRICES, TRADE, ANALYSIS
MarketGUI.MarketData = {}
MarketGUI.SelectedResource = nil

-- Ressourcen-Icons
local ResourceIcons = {
    Coal = "⚫", Iron = "🔩", Wood = "🪵", Oil = "🛢️",
    Stone = "🪨", Sand = "🏖️", Grain = "🌾", Livestock = "🐄",
    Steel = "🔧", Tools = "🔨", Machinery = "⚙️", Food = "🍞",
    Goods = "📦", Fuel = "⛽", Chemicals = "🧪", Passengers = "👥",
    Mail = "📧", Luxury_Goods = "💎", Electronics = "📱"
}

-- GUI erstellen
function MarketGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MarketGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1200, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "📈 MARKT & HANDEL"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "PRICES", text = "📊 Preise", icon = "📊"},
        {name = "TRADE", text = "💰 Handel", icon = "💰"},
        {name = "ANALYSIS", text = "📈 Analyse", icon = "📈"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 670)
    contentFrame.Position = UDim2.new(0, 10, 0, 120)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function MarketGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function MarketGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        end
    end
end

-- Content aktualisieren
function MarketGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "PRICES" then
        self:CreatePricesContent()
    elseif self.CurrentTab == "TRADE" then
        self:CreateTradeContent()
    elseif self.CurrentTab == "ANALYSIS" then
        self:CreateAnalysisContent()
    end
end

-- Preise-Content erstellen
function MarketGUI:CreatePricesContent()
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    -- Header
    local header = Instance.new("Frame")
    header.Size = UDim2.new(1, -10, 0, 40)
    header.Position = UDim2.new(0, 5, 0, 10)
    header.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    header.BorderSizePixel = 0
    header.Parent = scrollFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 5)
    headerCorner.Parent = header
    
    -- Header-Labels
    local headerLabels = {"Ressource", "Preis", "Trend", "Angebot", "Nachfrage"}
    for i, label in ipairs(headerLabels) do
        local headerLabel = Instance.new("TextLabel")
        headerLabel.Size = UDim2.new(0.2, 0, 1, 0)
        headerLabel.Position = UDim2.new((i-1) * 0.2, 0, 0, 0)
        headerLabel.BackgroundTransparency = 1
        headerLabel.Text = label
        headerLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        headerLabel.TextScaled = true
        headerLabel.Font = Enum.Font.SourceSansBold
        headerLabel.Parent = header
    end
    
    -- Ressourcen-Liste
    local yPos = 60
    
    if self.MarketData.prices then
        for resource, price in pairs(self.MarketData.prices) do
            local entry = self:CreatePriceEntry(scrollFrame, resource, yPos)
            yPos = yPos + 50
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
end

-- Preis-Eintrag erstellen
function MarketGUI:CreatePriceEntry(parent, resource, yPos)
    local entry = Instance.new("Frame")
    entry.Size = UDim2.new(1, -10, 0, 40)
    entry.Position = UDim2.new(0, 5, 0, yPos)
    entry.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    entry.BorderSizePixel = 0
    entry.Parent = parent
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 5)
    entryCorner.Parent = entry
    
    -- Ressourcen-Name mit Icon
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(0.2, 0, 1, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = (ResourceIcons[resource] or "📦") .. " " .. resource
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSans
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = entry
    
    -- Preis
    local priceLabel = Instance.new("TextLabel")
    priceLabel.Size = UDim2.new(0.2, 0, 1, 0)
    priceLabel.Position = UDim2.new(0.2, 0, 0, 0)
    priceLabel.BackgroundTransparency = 1
    priceLabel.Text = "💰 " .. string.format("%.2f", self.MarketData.prices[resource] or 0)
    priceLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    priceLabel.TextScaled = true
    priceLabel.Font = Enum.Font.SourceSans
    priceLabel.Parent = entry
    
    -- Trend
    local trend = self.MarketData.trends[resource] or 0
    local trendLabel = Instance.new("TextLabel")
    trendLabel.Size = UDim2.new(0.2, 0, 1, 0)
    trendLabel.Position = UDim2.new(0.4, 0, 0, 0)
    trendLabel.BackgroundTransparency = 1
    trendLabel.Text = (trend > 0 and "📈 +" or trend < 0 and "📉 " or "➡️ ") .. string.format("%.1f%%", trend)
    trendLabel.TextColor3 = trend > 0 and Color3.fromRGB(0, 255, 0) or trend < 0 and Color3.fromRGB(255, 0, 0) or Color3.fromRGB(200, 200, 200)
    trendLabel.TextScaled = true
    trendLabel.Font = Enum.Font.SourceSans
    trendLabel.Parent = entry
    
    -- Angebot
    local supplyLabel = Instance.new("TextLabel")
    supplyLabel.Size = UDim2.new(0.2, 0, 1, 0)
    supplyLabel.Position = UDim2.new(0.6, 0, 0, 0)
    supplyLabel.BackgroundTransparency = 1
    supplyLabel.Text = "📦 " .. (self.MarketData.supply[resource] or 0)
    supplyLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    supplyLabel.TextScaled = true
    supplyLabel.Font = Enum.Font.SourceSans
    supplyLabel.Parent = entry
    
    -- Nachfrage
    local demandLabel = Instance.new("TextLabel")
    demandLabel.Size = UDim2.new(0.2, 0, 1, 0)
    demandLabel.Position = UDim2.new(0.8, 0, 0, 0)
    demandLabel.BackgroundTransparency = 1
    demandLabel.Text = "🎯 " .. (self.MarketData.demand[resource] or 0)
    demandLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    demandLabel.TextScaled = true
    demandLabel.Font = Enum.Font.SourceSans
    demandLabel.Parent = entry
    
    -- Klick-Handler für Details
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(1, 0, 1, 0)
    button.BackgroundTransparency = 1
    button.Text = ""
    button.Parent = entry
    
    button.MouseButton1Click:Connect(function()
        self.SelectedResource = resource
        self:SwitchTab("TRADE")
        self:UpdateTabButtons(self.TabButtons)
    end)
    
    return entry
end

-- Handel-Content erstellen
function MarketGUI:CreateTradeContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 0.3, 0)
    label.BackgroundTransparency = 1
    label.Text = "💰 HANDEL\n\nWähle eine Ressource aus der Preisliste\num zu handeln."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
    
    if self.SelectedResource then
        -- Handel-Interface für ausgewählte Ressource
        self:CreateTradeInterface()
    end
end

-- Handel-Interface erstellen
function MarketGUI:CreateTradeInterface()
    -- Implementierung des Handel-Interfaces
    -- (Vereinfacht für Platzgründe)
end

-- Analyse-Content erstellen
function MarketGUI:CreateAnalysisContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "📈 MARKT-ANALYSE\n\nDetaillierte Marktanalyse\nund Trends werden hier angezeigt."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Markt-Daten laden
function MarketGUI:LoadMarketData()
    local success, data = pcall(function()
        return GetMarketDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.MarketData = data
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Markt-Daten")
    end
end

-- GUI öffnen
function MarketGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadMarketData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function MarketGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.M then
        if MarketGUI.IsOpen then
            MarketGUI:CloseGUI()
        else
            MarketGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function MarketGUI:Initialize()
    print("📈 MarketGUI initialisiert - Drücke 'M' zum Öffnen")
end

-- Auto-Start
MarketGUI:Initialize()

return MarketGUI
