-- ServerScriptService/Managers/LoanManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Vollständiges Kredite- und Finanz-Management-System

local LoanManager = {}
LoanManager.__index = LoanManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Kredit-Typen
local LOAN_TYPES = {
    SMALL_BUSINESS = {
        name = "Kleinkredit",
        minAmount = 10000,
        maxAmount = 100000,
        baseInterestRate = 0.08, -- 8% jährlich
        termMonths = 24,
        collateralRequired = false,
        description = "Schneller Kleinkredit für den Einstieg"
    },
    MEDIUM_BUSINESS = {
        name = "Geschäftskredit",
        minAmount = 50000,
        maxAmount = 500000,
        baseInterestRate = 0.06,
        termMonths = 60,
        collateralRequired = true,
        description = "Mittlerer Kredit für Expansion"
    },
    LARGE_BUSINESS = {
        name = "Großkredit",
        minAmount = 200000,
        maxAmount = 2000000,
        baseInterestRate = 0.05,
        termMonths = 120,
        collateralRequired = true,
        description = "Großer Kredit für Großprojekte"
    },
    INFRASTRUCTURE = {
        name = "Infrastruktur-Kredit",
        minAmount = 500000,
        maxAmount = 10000000,
        baseInterestRate = 0.04,
        termMonths = 240,
        collateralRequired = true,
        description = "Spezieller Kredit für Infrastruktur-Projekte"
    },
    EMERGENCY = {
        name = "Notfall-Kredit",
        minAmount = 5000,
        maxAmount = 50000,
        baseInterestRate = 0.15, -- Hoher Zinssatz
        termMonths = 12,
        collateralRequired = false,
        description = "Schnelle Hilfe in Notfällen"
    }
}

-- Kredit-Rating System
local CREDIT_RATINGS = {
    AAA = {name = "AAA", multiplier = 0.8, description = "Exzellente Bonität"},
    AA = {name = "AA", multiplier = 0.9, description = "Sehr gute Bonität"},
    A = {name = "A", multiplier = 1.0, description = "Gute Bonität"},
    BBB = {name = "BBB", multiplier = 1.1, description = "Befriedigende Bonität"},
    BB = {name = "BB", multiplier = 1.3, description = "Ausreichende Bonität"},
    B = {name = "B", multiplier = 1.5, description = "Schwache Bonität"},
    CCC = {name = "CCC", multiplier = 2.0, description = "Sehr schwache Bonität"},
    DEFAULT = {name = "DEFAULT", multiplier = 999, description = "Zahlungsunfähig"}
}

-- Konstruktor
function LoanManager.new()
    local self = setmetatable({}, LoanManager)
    
    self.playerLoans = {} -- [playerId] = {loans}
    self.playerCreditRatings = {} -- [playerId] = rating
    self.playerFinancialHistory = {} -- [playerId] = {history}
    self.nextLoanId = 1
    
    self.bankSettings = {
        baseInterestRate = 0.05, -- 5% Basis-Zinssatz
        maxTotalDebt = 5000000, -- Maximale Gesamtverschuldung pro Spieler
        minCreditScore = 300,
        maxCreditScore = 850
    }
    
    self:InitializeEvents()
    
    -- Monatliche Zins-Updates
    self.monthlyUpdateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdateLoans(deltaTime)
    end)
    
    return self
end

-- Events initialisieren
function LoanManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("RequestLoanEvent") then
        local requestLoanEvent = Instance.new("RemoteEvent")
        requestLoanEvent.Name = "RequestLoanEvent"
        requestLoanEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("RepayLoanEvent") then
        local repayLoanEvent = Instance.new("RemoteEvent")
        repayLoanEvent.Name = "RepayLoanEvent"
        repayLoanEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("GetLoanDataFunction") then
        local getLoanDataFunction = Instance.new("RemoteFunction")
        getLoanDataFunction.Name = "GetLoanDataFunction"
        getLoanDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("GetCreditRatingFunction") then
        local getCreditRatingFunction = Instance.new("RemoteFunction")
        getCreditRatingFunction.Name = "GetCreditRatingFunction"
        getCreditRatingFunction.Parent = Events
    end
    
    -- Event-Handler
    Events.RequestLoanEvent.OnServerEvent:Connect(function(player, loanType, amount, termMonths)
        self:ProcessLoanRequest(player, loanType, amount, termMonths)
    end)
    
    Events.RepayLoanEvent.OnServerEvent:Connect(function(player, loanId, amount)
        self:RepayLoan(player, loanId, amount)
    end)
    
    Events.GetLoanDataFunction.OnServerInvoke = function(player)
        return self:GetPlayerLoanData(player)
    end
    
    Events.GetCreditRatingFunction.OnServerInvoke = function(player)
        return self:GetPlayerCreditRating(player)
    end
end

-- Kredit-Antrag bearbeiten
function LoanManager:ProcessLoanRequest(player, loanType, amount, termMonths)
    local playerId = player.UserId
    local loanConfig = LOAN_TYPES[loanType]
    
    if not loanConfig then
        warn("Unbekannter Kredit-Typ:", loanType)
        return false
    end
    
    -- Betrag prüfen
    if amount < loanConfig.minAmount or amount > loanConfig.maxAmount then
        print("❌ Kredit abgelehnt: Betrag außerhalb der Grenzen")
        return false
    end
    
    -- Bonität prüfen
    local creditRating = self:GetPlayerCreditRating(player)
    local creditScore = self:CalculateCreditScore(player)
    
    if creditScore < self.bankSettings.minCreditScore then
        print("❌ Kredit abgelehnt: Unzureichende Bonität")
        return false
    end
    
    -- Gesamtverschuldung prüfen
    local totalDebt = self:GetPlayerTotalDebt(playerId)
    if totalDebt + amount > self.bankSettings.maxTotalDebt then
        print("❌ Kredit abgelehnt: Maximale Verschuldung erreicht")
        return false
    end
    
    -- Zinssatz berechnen
    local interestRate = self:CalculateInterestRate(loanConfig, creditRating, creditScore)
    
    -- Kredit erstellen
    local loanId = "loan_" .. self.nextLoanId
    self.nextLoanId = self.nextLoanId + 1
    
    local loan = {
        id = loanId,
        playerId = playerId,
        type = loanType,
        originalAmount = amount,
        remainingAmount = amount,
        interestRate = interestRate,
        termMonths = termMonths or loanConfig.termMonths,
        monthlyPayment = self:CalculateMonthlyPayment(amount, interestRate, termMonths or loanConfig.termMonths),
        startDate = tick(),
        nextPaymentDate = tick() + (30 * 24 * 60 * 60), -- 30 Tage
        missedPayments = 0,
        status = "ACTIVE", -- ACTIVE, PAID_OFF, DEFAULTED
        collateral = loanConfig.collateralRequired and self:GetPlayerCollateral(playerId) or nil
    }
    
    -- Kredit zu Spieler-Liste hinzufügen
    if not self.playerLoans[playerId] then
        self.playerLoans[playerId] = {}
    end
    self.playerLoans[playerId][loanId] = loan
    
    -- Geld an Spieler überweisen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager then
        economyManager:AddPlayerMoney(playerId, amount)
    end
    
    -- Finanz-Historie aktualisieren
    self:AddToFinancialHistory(playerId, "LOAN_TAKEN", amount, "Kredit aufgenommen: " .. loanConfig.name)
    
    print("✅ Kredit genehmigt:", loanConfig.name, "Betrag:", amount, "Zinssatz:", interestRate * 100 .. "%")
    return true
end

-- Zinssatz berechnen
function LoanManager:CalculateInterestRate(loanConfig, creditRating, creditScore)
    local baseRate = loanConfig.baseInterestRate
    local ratingMultiplier = CREDIT_RATINGS[creditRating].multiplier
    
    -- Credit Score Anpassung
    local scoreAdjustment = (850 - creditScore) / 1000 -- 0 bis 0.55
    
    -- Markt-Bedingungen (vereinfacht)
    local marketAdjustment = math.random(-10, 10) / 1000 -- -1% bis +1%
    
    local finalRate = baseRate * ratingMultiplier + scoreAdjustment + marketAdjustment
    
    -- Minimum 1%, Maximum 25%
    return math.max(0.01, math.min(0.25, finalRate))
end

-- Monatliche Zahlung berechnen
function LoanManager:CalculateMonthlyPayment(principal, annualRate, termMonths)
    local monthlyRate = annualRate / 12
    if monthlyRate == 0 then
        return principal / termMonths
    end
    
    local payment = principal * (monthlyRate * (1 + monthlyRate)^termMonths) / ((1 + monthlyRate)^termMonths - 1)
    return math.ceil(payment)
end

-- Kredit zurückzahlen
function LoanManager:RepayLoan(player, loanId, amount)
    local playerId = player.UserId
    local playerLoans = self.playerLoans[playerId]
    
    if not playerLoans or not playerLoans[loanId] then
        warn("Kredit nicht gefunden:", loanId)
        return false
    end
    
    local loan = playerLoans[loanId]
    
    -- Geld prüfen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager and not economyManager:CanPlayerAfford(playerId, amount) then
        print("❌ Nicht genug Geld für Rückzahlung")
        return false
    end
    
    -- Zahlung verarbeiten
    local paymentAmount = math.min(amount, loan.remainingAmount)
    loan.remainingAmount = loan.remainingAmount - paymentAmount
    
    -- Geld abziehen
    if economyManager then
        economyManager:DeductPlayerMoney(playerId, paymentAmount)
    end
    
    -- Kredit-Status aktualisieren
    if loan.remainingAmount <= 0 then
        loan.status = "PAID_OFF"
        loan.paidOffDate = tick()
        print("✅ Kredit vollständig zurückgezahlt:", loanId)
    end
    
    -- Finanz-Historie aktualisieren
    self:AddToFinancialHistory(playerId, "LOAN_PAYMENT", paymentAmount, "Kredit-Rückzahlung: " .. loanId)
    
    -- Bonität verbessern
    self:UpdateCreditRating(playerId, "PAYMENT_MADE")
    
    return true
end

-- Bonität berechnen
function LoanManager:CalculateCreditScore(player)
    local playerId = player.UserId
    local baseScore = 650 -- Basis-Score
    
    -- Zahlungshistorie (35% des Scores)
    local paymentHistory = self:GetPaymentHistoryScore(playerId)
    
    -- Verschuldungsgrad (30% des Scores)
    local debtRatio = self:GetDebtRatioScore(playerId)
    
    -- Kredit-Historie Länge (15% des Scores)
    local creditHistory = self:GetCreditHistoryScore(playerId)
    
    -- Kredit-Mix (10% des Scores)
    local creditMix = self:GetCreditMixScore(playerId)
    
    -- Neue Kredite (10% des Scores)
    local newCredit = self:GetNewCreditScore(playerId)
    
    local totalScore = baseScore + 
        (paymentHistory * 0.35) + 
        (debtRatio * 0.30) + 
        (creditHistory * 0.15) + 
        (creditMix * 0.10) + 
        (newCredit * 0.10)
    
    return math.max(300, math.min(850, math.floor(totalScore)))
end

-- Spieler-Bonität abrufen
function LoanManager:GetPlayerCreditRating(player)
    local playerId = player.UserId
    local creditScore = self:CalculateCreditScore(player)
    
    if creditScore >= 800 then
        return "AAA"
    elseif creditScore >= 750 then
        return "AA"
    elseif creditScore >= 700 then
        return "A"
    elseif creditScore >= 650 then
        return "BBB"
    elseif creditScore >= 600 then
        return "BB"
    elseif creditScore >= 550 then
        return "B"
    else
        return "CCC"
    end
end

-- Gesamtverschuldung abrufen
function LoanManager:GetPlayerTotalDebt(playerId)
    local playerLoans = self.playerLoans[playerId]
    if not playerLoans then
        return 0
    end
    
    local totalDebt = 0
    for _, loan in pairs(playerLoans) do
        if loan.status == "ACTIVE" then
            totalDebt = totalDebt + loan.remainingAmount
        end
    end
    
    return totalDebt
end

-- Kredite aktualisieren (monatlich)
function LoanManager:UpdateLoans(deltaTime)
    -- Vereinfachte monatliche Updates (in echtem Spiel würde das zeitbasiert sein)
    for playerId, playerLoans in pairs(self.playerLoans) do
        for loanId, loan in pairs(playerLoans) do
            if loan.status == "ACTIVE" and tick() >= loan.nextPaymentDate then
                self:ProcessMonthlyPayment(playerId, loan)
            end
        end
    end
end

-- Monatliche Zahlung verarbeiten
function LoanManager:ProcessMonthlyPayment(playerId, loan)
    local economyManager = require(script.Parent.EconomyManager)
    
    if economyManager and economyManager:CanPlayerAfford(playerId, loan.monthlyPayment) then
        -- Automatische Zahlung
        economyManager:DeductPlayerMoney(playerId, loan.monthlyPayment)
        
        -- Zinsen und Tilgung berechnen
        local interestPayment = loan.remainingAmount * (loan.interestRate / 12)
        local principalPayment = loan.monthlyPayment - interestPayment
        
        loan.remainingAmount = loan.remainingAmount - principalPayment
        loan.nextPaymentDate = loan.nextPaymentDate + (30 * 24 * 60 * 60)
        
        if loan.remainingAmount <= 0 then
            loan.status = "PAID_OFF"
            loan.paidOffDate = tick()
        end
        
        self:AddToFinancialHistory(playerId, "LOAN_PAYMENT", loan.monthlyPayment, "Monatliche Kredit-Zahlung")
    else
        -- Zahlungsausfall
        loan.missedPayments = loan.missedPayments + 1
        loan.nextPaymentDate = loan.nextPaymentDate + (30 * 24 * 60 * 60)
        
        if loan.missedPayments >= 3 then
            loan.status = "DEFAULTED"
            self:HandleLoanDefault(playerId, loan)
        end
        
        self:UpdateCreditRating(playerId, "PAYMENT_MISSED")
        self:AddToFinancialHistory(playerId, "PAYMENT_MISSED", loan.monthlyPayment, "Verpasste Kredit-Zahlung")
    end
end

-- Zahlungsausfall behandeln
function LoanManager:HandleLoanDefault(playerId, loan)
    print("⚠️ Kredit-Ausfall:", loan.id, "Spieler:", playerId)
    
    -- Bonität drastisch verschlechtern
    self.playerCreditRatings[playerId] = "DEFAULT"
    
    -- Kollateral einziehen (falls vorhanden)
    if loan.collateral then
        self:SeizeCollateral(playerId, loan.collateral)
    end
    
    -- Insolvenz-Verfahren einleiten (falls Gesamtverschuldung zu hoch)
    local totalDebt = self:GetPlayerTotalDebt(playerId)
    if totalDebt > 1000000 then -- 1 Million Schulden
        self:InitiateBankruptcy(playerId)
    end
end

-- Insolvenz einleiten
function LoanManager:InitiateBankruptcy(playerId)
    print("💸 INSOLVENZ eingeleitet für Spieler:", playerId)
    
    -- Alle Vermögenswerte beschlagnahmen
    local economyManager = require(script.Parent.EconomyManager)
    if economyManager then
        economyManager:SetPlayerMoney(playerId, 10000) -- Minimales Startkapital
    end
    
    -- Alle aktiven Kredite auf "DEFAULTED" setzen
    local playerLoans = self.playerLoans[playerId]
    if playerLoans then
        for _, loan in pairs(playerLoans) do
            if loan.status == "ACTIVE" then
                loan.status = "DEFAULTED"
            end
        end
    end
    
    -- Bonität auf Minimum setzen
    self.playerCreditRatings[playerId] = "DEFAULT"
    
    self:AddToFinancialHistory(playerId, "BANKRUPTCY", 0, "Insolvenz-Verfahren eingeleitet")
end

-- Finanz-Historie hinzufügen
function LoanManager:AddToFinancialHistory(playerId, eventType, amount, description)
    if not self.playerFinancialHistory[playerId] then
        self.playerFinancialHistory[playerId] = {}
    end
    
    table.insert(self.playerFinancialHistory[playerId], {
        timestamp = tick(),
        type = eventType,
        amount = amount,
        description = description
    })
    
    -- Nur die letzten 100 Einträge behalten
    local history = self.playerFinancialHistory[playerId]
    if #history > 100 then
        table.remove(history, 1)
    end
end

-- Spieler-Kredit-Daten abrufen
function LoanManager:GetPlayerLoanData(player)
    local playerId = player.UserId
    
    return {
        loans = self.playerLoans[playerId] or {},
        creditRating = self:GetPlayerCreditRating(player),
        creditScore = self:CalculateCreditScore(player),
        totalDebt = self:GetPlayerTotalDebt(playerId),
        financialHistory = self.playerFinancialHistory[playerId] or {},
        availableLoanTypes = LOAN_TYPES
    }
end

return LoanManager
