# Kohlemine - Rohstoff-Industrie
# Era: 1850-1980 | Type: Mining

[ASSET_INFO]
ID = "COAL_MINE"
Name = "Kohlemine"
Category = "Industry"
Era_Start = 1850
Era_End = 1980
IndustryType = "Mining"
IndustryEra = "Early_Industrial"

[MODEL_DATA]
ModelId = "rbxassetid://COAL_MINE"
Scale = Vector3(30, 15, 40)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.3, 0.3, 0.3)      # Dunkler Stein/Kohle
Secondary = Color3(0.6, 0.4, 0.2)    # Holzstrukturen
Accent = Color3(0.8, 0.6, 0.4)       # Metallteile
Trim = Color3(0.2, 0.2, 0.2)         # Schwarze <PERSON>n

[GAMEPLAY_DATA]
Workers = 50
BuildingType = "Industry"
MaintenanceCost = 200
BuildCost = 15000
BuildTime = 90
PowerConsumption = 0
WaterConsumption = 10

[PRODUCTION_DATA]
Production = {"Coal"}
ProductionRate = 100  # Tonnen/Monat
InputResources = {}
OutputResources = {"Coal"}
Storage_Capacity = 500
Production_Cycle = 24  # Stunden

[FEATURES]
Features = {
    "Mine_Shaft",
    "Conveyor_Belt",
    "Storage_Silos",
    "Headframe",
    "Coal_Carts",
    "Ventilation_System"
}

[REQUIREMENTS]
RequiredTech = "Mining_Technology"
RequiredPopulation = 500
RequiredYear = 1850
UnlockCost = 1000
RequiredResources = {"Coal_Deposit"}

[VISUAL_EFFECTS]
HasSmoke = true
SmokeColor = Color3(0.4, 0.4, 0.4)
HasLights = true
NightLighting = true
LightColor = Color3(1, 0.8, 0.6)
HasDust = true
DustColor = Color3(0.3, 0.3, 0.3)

[SOUND_EFFECTS]
AmbientSound = "rbxassetid://MINE_MACHINERY"
BuildSound = "rbxassetid://HEAVY_CONSTRUCTION"
DestroySound = "rbxassetid://INDUSTRIAL_COLLAPSE"
ProductionSound = "rbxassetid://CONVEYOR_BELT"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "COAL_MINE_MODERN"
UpgradeCost = 8000
UpgradeTime = 60

[ECONOMIC_DATA]
Employment_Provided = 50
Tax_Revenue = 100
Resource_Value = 5  # Per Tonne
Export_Potential = true
Maintenance_Jobs = 5

[ENVIRONMENTAL_DATA]
Pollution_Level = 8
Noise_Level = 7
Water_Pollution = 3
Air_Pollution = 6
Land_Usage = "Heavy_Industrial"

[SAFETY_DATA]
Accident_Risk = 7
Safety_Measures = {
    "Ventilation",
    "Safety_Lamps",
    "Emergency_Exits"
}
Insurance_Cost = 150

[TRANSPORT_DATA]
Requires_Rail_Access = true
Truck_Access = true
Ship_Access = false
Daily_Shipments = 20
Loading_Time = 2  # Stunden

[DESCRIPTION]
ShortDesc = "Traditionelle Kohlemine mit Förderturm"
LongDesc = "Eine vollständige Kohlemine mit Schacht, Förderturm und Verarbeitungsanlagen. Produziert Kohle für Industrie und Haushalte."
HistoricalNote = "Kohleminen waren das Rückgrat der industriellen Revolution und prägten ganze Regionen."
