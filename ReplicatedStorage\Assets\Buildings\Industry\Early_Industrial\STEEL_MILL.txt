# Stahlwerk - Schwerindustrie
# Era: 1870-2000 | Type: Heavy Industry

[ASSET_INFO]
ID = "STEEL_MILL"
Name = "Stahlwerk"
Category = "Industry"
Era_Start = 1870
Era_End = 2000
IndustryType = "Heavy_Industry"
IndustryEra = "Early_Industrial"

[MODEL_DATA]
ModelId = "rbxassetid://STEEL_MILL"
Scale = Vector3(80, 25, 100)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.4, 0.4, 0.4)      # Stahl/Beton
Secondary = Color3(0.6, 0.3, 0.1)    # Rost/Backstein
Accent = Color3(1, 0.5, 0)           # Glühende Öfen
Trim = Color3(0.2, 0.2, 0.2)         # Schwarzer <PERSON>ß

[GAMEPLAY_DATA]
Workers = 200
BuildingType = "Industry"
MaintenanceCost = 500
BuildCost = 50000
BuildTime = 180
PowerConsumption = 100
WaterConsumption = 50

[PRODUCTION_DATA]
Production = {"Steel"}
ProductionRate = 200  # Tonnen/Monat
InputResources = {"Iron_Ore", "Coal"}
OutputResources = {"Steel"}
Input_Ratios = {
    Iron_Ore = 150,  # Tonnen/Monat
    Coal = 100       # Tonnen/Monat
}
Storage_Capacity = 1000
Production_Cycle = 12  # Stunden

[FEATURES]
Features = {
    "Blast_Furnaces",
    "Smokestacks",
    "Rail_Access",
    "Molten_Steel_Vats",
    "Cooling_Towers",
    "Crane_System",
    "Loading_Docks"
}

[REQUIREMENTS]
RequiredTech = "Steel_Production"
RequiredPopulation = 2000
RequiredYear = 1870
UnlockCost = 5000
RequiredResources = {"Iron_Ore", "Coal"}

[VISUAL_EFFECTS]
HasSmoke = true
SmokeColor = Color3(0.3, 0.3, 0.3)
HasLights = true
NightLighting = true
LightColor = Color3(1, 0.6, 0.2)
HasFire = true
FireColor = Color3(1, 0.4, 0)
HasSparks = true

[SOUND_EFFECTS]
AmbientSound = "rbxassetid://STEEL_MILL_AMBIENT"
BuildSound = "rbxassetid://HEAVY_CONSTRUCTION"
DestroySound = "rbxassetid://INDUSTRIAL_COLLAPSE"
ProductionSound = "rbxassetid://FURNACE_ROAR"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "STEEL_MILL_ELECTRIC"
UpgradeCost = 25000
UpgradeTime = 120

[ECONOMIC_DATA]
Employment_Provided = 200
Tax_Revenue = 400
Resource_Value = 15  # Per Tonne Steel
Export_Potential = true
Maintenance_Jobs = 20

[ENVIRONMENTAL_DATA]
Pollution_Level = 10
Noise_Level = 9
Water_Pollution = 8
Air_Pollution = 10
Land_Usage = "Heavy_Industrial"

[SAFETY_DATA]
Accident_Risk = 9
Safety_Measures = {
    "Fire_Suppression",
    "Safety_Equipment",
    "Emergency_Protocols",
    "Medical_Station"
}
Insurance_Cost = 400

[TRANSPORT_DATA]
Requires_Rail_Access = true
Truck_Access = true
Ship_Access = true
Daily_Shipments = 50
Loading_Time = 4  # Stunden

[SUPPLY_CHAIN]
Suppliers = {
    "COAL_MINE",
    "IRON_MINE"
}
Customers = {
    "MACHINE_FACTORY",
    "CONSTRUCTION_INDUSTRY",
    "SHIPYARD"
}

[DESCRIPTION]
ShortDesc = "Großes Stahlwerk mit Hochöfen und Schmelzanlagen"
LongDesc = "Ein komplettes Stahlwerk mit Hochöfen, Schmelzanlagen und Walzwerken. Verarbeitet Eisenerz und Kohle zu hochwertigem Stahl."
HistoricalNote = "Stahlwerke waren die Grundlage der industriellen Entwicklung und prägten das Bild ganzer Industrieregionen."
