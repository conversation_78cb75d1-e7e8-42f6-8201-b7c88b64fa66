-- ServerScriptService/Managers/AchievementManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Achievement-System mit Belohnungen und Fortschritt

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local HttpService = game:GetService("HttpService")

local AchievementManager = {}
AchievementManager.__index = AchievementManager

function AchievementManager.new()
    local self = setmetatable({}, AchievementManager)
    
    -- Spieler-Achievements
    self.playerAchievements = {}
    
    -- Achievement-Definitionen
    self.achievements = {
        -- Transport Achievements
        first_vehicle = {
            id = "first_vehicle",
            name = "<PERSON>rst<PERSON> Fahrzeug",
            description = "Kaufe dein erstes Fahrzeug",
            category = "transport",
            points = 10,
            reward = {type = "money", amount = 50000}
        },
        
        transport_tycoon = {
            id = "transport_tycoon",
            name = "Transport-Tycoon",
            description = "Besitze 100 Fahrzeuge gleichzeitig",
            category = "transport",
            points = 100,
            reward = {type = "money", amount = 1000000}
        },
        
        -- Wirtschafts-Achievements
        first_million = {
            id = "first_million",
            name = "Erste Million",
            description = "Erreiche 1 Million € Bargeld",
            category = "economy",
            points = 50,
            reward = {type = "money", amount = 100000}
        },
        
        billionaire = {
            id = "billionaire",
            name = "Milliardär",
            description = "Erreiche 1 Milliarde € Nettovermögen",
            category = "economy",
            points = 500,
            reward = {type = "special", name = "Golden Vehicle"}
        },
        
        -- Stadt-Achievements
        city_builder = {
            id = "city_builder",
            name = "Stadtbauer",
            description = "Verbinde 10 Städte mit deinem Netzwerk",
            category = "cities",
            points = 75,
            reward = {type = "money", amount = 500000}
        },
        
        -- Multiplayer-Achievements
        team_player = {
            id = "team_player",
            name = "Teamplayer",
            description = "Tritt einer Allianz bei",
            category = "multiplayer",
            points = 25,
            reward = {type = "money", amount = 200000}
        },
        
        alliance_leader = {
            id = "alliance_leader",
            name = "Allianz-Anführer",
            description = "Gründe eine Allianz mit 5 Mitgliedern",
            category = "multiplayer",
            points = 100,
            reward = {type = "special", name = "Alliance Badge"}
        }
    }
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function AchievementManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetAchievementsFunction") then
        local getAchievementsFunction = Instance.new("RemoteFunction")
        getAchievementsFunction.Name = "GetAchievementsFunction"
        getAchievementsFunction.Parent = Events
        
        getAchievementsFunction.OnServerInvoke = function(player)
            return self:GetPlayerAchievements(player.UserId)
        end
    end
    
    if not Events:FindFirstChild("AchievementUnlockedEvent") then
        local achievementUnlockedEvent = Instance.new("RemoteEvent")
        achievementUnlockedEvent.Name = "AchievementUnlockedEvent"
        achievementUnlockedEvent.Parent = Events
    end
end

-- Spieler-Achievements initialisieren
function AchievementManager:InitializePlayerAchievements(playerId)
    self.playerAchievements[playerId] = {
        unlockedAchievements = {},
        progress = {},
        totalPoints = 0,
        lastUnlocked = nil
    }
    
    print("🏆 Achievements für Spieler initialisiert:", playerId)
end

-- Achievement freischalten
function AchievementManager:UnlockAchievement(playerId, achievementId)
    local playerAchievements = self.playerAchievements[playerId]
    if not playerAchievements then return false end
    
    -- Prüfen ob bereits freigeschaltet
    if playerAchievements.unlockedAchievements[achievementId] then
        return false
    end
    
    local achievement = self.achievements[achievementId]
    if not achievement then return false end
    
    -- Achievement freischalten
    playerAchievements.unlockedAchievements[achievementId] = {
        unlockedAt = os.time(),
        achievement = achievement
    }
    
    playerAchievements.totalPoints = playerAchievements.totalPoints + achievement.points
    playerAchievements.lastUnlocked = achievementId
    
    -- Belohnung gewähren
    self:GrantReward(playerId, achievement.reward)
    
    -- Event an Client senden
    local player = Players:GetPlayerByUserId(playerId)
    if player then
        local Events = ReplicatedStorage.Events
        if Events:FindFirstChild("AchievementUnlockedEvent") then
            Events.AchievementUnlockedEvent:FireClient(player, achievement)
        end
    end
    
    print("🏆 Achievement freigeschaltet:", achievement.name, "für Spieler:", playerId)
    return true
end

-- Belohnung gewähren
function AchievementManager:GrantReward(playerId, reward)
    if not reward then return end
    
    if reward.type == "money" then
        -- Geld-Belohnung über FinanceManager
        local Events = ReplicatedStorage.Events
        if Events:FindFirstChild("AddRevenueEvent") then
            Events.AddRevenueEvent:Fire(playerId, reward.amount, "Achievement Reward")
        end
        
    elseif reward.type == "special" then
        -- Spezielle Belohnungen (Badges, Items, etc.)
        print("🎁 Spezielle Belohnung gewährt:", reward.name, "für Spieler:", playerId)
    end
end

-- Achievement-Fortschritt aktualisieren
function AchievementManager:UpdateProgress(playerId, achievementId, progress)
    local playerAchievements = self.playerAchievements[playerId]
    if not playerAchievements then return end
    
    playerAchievements.progress[achievementId] = progress
    
    -- Prüfen ob Achievement freigeschaltet werden kann
    local achievement = self.achievements[achievementId]
    if achievement and achievement.requirement and progress >= achievement.requirement then
        self:UnlockAchievement(playerId, achievementId)
    end
end

-- Fahrzeug-bezogene Achievement-Checks
function AchievementManager:CheckVehicleAchievements(playerId, vehicleCount)
    if vehicleCount == 1 then
        self:UnlockAchievement(playerId, "first_vehicle")
    elseif vehicleCount >= 100 then
        self:UnlockAchievement(playerId, "transport_tycoon")
    end
end

-- Wirtschafts-bezogene Achievement-Checks
function AchievementManager:CheckEconomyAchievements(playerId, cash, netWorth)
    if cash >= 1000000 then
        self:UnlockAchievement(playerId, "first_million")
    end
    
    if netWorth >= 1000000000 then
        self:UnlockAchievement(playerId, "billionaire")
    end
end

-- Stadt-bezogene Achievement-Checks
function AchievementManager:CheckCityAchievements(playerId, connectedCities)
    if connectedCities >= 10 then
        self:UnlockAchievement(playerId, "city_builder")
    end
end

-- Multiplayer-bezogene Achievement-Checks
function AchievementManager:CheckMultiplayerAchievements(playerId, allianceData)
    if allianceData.joined then
        self:UnlockAchievement(playerId, "team_player")
    end
    
    if allianceData.isLeader and allianceData.memberCount >= 5 then
        self:UnlockAchievement(playerId, "alliance_leader")
    end
end

-- Spieler-Achievements abrufen
function AchievementManager:GetPlayerAchievements(playerId)
    local playerAchievements = self.playerAchievements[playerId]
    if not playerAchievements then
        self:InitializePlayerAchievements(playerId)
        return self.playerAchievements[playerId]
    end
    
    return playerAchievements
end

-- Alle verfügbaren Achievements abrufen
function AchievementManager:GetAllAchievements()
    return self.achievements
end

-- Achievement-Statistiken abrufen
function AchievementManager:GetAchievementStats(playerId)
    local playerAchievements = self.playerAchievements[playerId]
    if not playerAchievements then return nil end
    
    local totalAchievements = 0
    for _ in pairs(self.achievements) do
        totalAchievements = totalAchievements + 1
    end
    
    local unlockedCount = 0
    for _ in pairs(playerAchievements.unlockedAchievements) do
        unlockedCount = unlockedCount + 1
    end
    
    return {
        totalAchievements = totalAchievements,
        unlockedAchievements = unlockedCount,
        completionPercentage = math.floor((unlockedCount / totalAchievements) * 100),
        totalPoints = playerAchievements.totalPoints,
        lastUnlocked = playerAchievements.lastUnlocked
    }
end

return AchievementManager
