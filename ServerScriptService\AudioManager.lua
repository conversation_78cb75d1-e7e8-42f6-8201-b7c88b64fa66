-- ServerScriptService/AudioManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Audio-System für Musik, Sound-Effekte und dynamische Soundtracks

local SoundService = game:GetService("SoundService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local HttpService = game:GetService("HttpService")

local AudioManager = {}
AudioManager.__index = AudioManager

function AudioManager.new()
    local self = setmetatable({}, AudioManager)
    
    -- Audio configuration
    self.masterVolume = 0.8
    self.musicVolume = 0.6
    self.sfxVolume = 0.8
    self.ambientVolume = 0.4
    self.voiceVolume = 0.7
    
    -- Audio categories
    self.audioCategories = {
        music = {volume = self.musicVolume, sounds = {}},
        sfx = {volume = self.sfxVolume, sounds = {}},
        ambient = {volume = self.ambientVolume, sounds = {}},
        voice = {volume = self.voiceVolume, sounds = {}},
        ui = {volume = self.sfxVolume, sounds = {}}
    }
    
    -- Music system
    self.currentTrack = nil
    self.musicQueue = {}
    self.musicMode = "dynamic" -- dynamic, playlist, ambient
    self.lastMusicChange = 0
    self.musicChangeInterval = 180 -- 3 minutes
    
    -- Dynamic music system
    self.musicMoods = {
        peaceful = {
            tracks = {"peaceful_countryside", "gentle_morning", "calm_waters"},
            conditions = {speed = "slow", activity = "low", time = "day"}
        },
        energetic = {
            tracks = {"busy_city", "industrial_power", "rush_hour"},
            conditions = {speed = "fast", activity = "high", time = "day"}
        },
        atmospheric = {
            tracks = {"night_lights", "distant_thunder", "moonlit_rails"},
            conditions = {speed = "any", activity = "any", time = "night"}
        },
        dramatic = {
            tracks = {"storm_approaching", "crisis_moment", "final_push"},
            conditions = {speed = "any", activity = "crisis", time = "any"}
        },
        victory = {
            tracks = {"achievement_fanfare", "success_theme", "celebration"},
            conditions = {speed = "any", activity = "achievement", time = "any"}
        }
    }
    
    -- Sound effects library
    self.soundEffects = {
        -- Transport sounds
        train_horn = {id = "rbxassetid://131961136", volume = 0.7, pitch = 1.0},
        train_chug = {id = "rbxassetid://213761112", volume = 0.5, pitch = 1.0},
        train_brake = {id = "rbxassetid://131961136", volume = 0.6, pitch = 0.8},
        truck_engine = {id = "rbxassetid://131961136", volume = 0.4, pitch = 1.2},
        ship_horn = {id = "rbxassetid://131961136", volume = 0.8, pitch = 0.6},
        
        -- Construction sounds
        construction_hammer = {id = "rbxassetid://131961136", volume = 0.6, pitch = 1.0},
        construction_drill = {id = "rbxassetid://131961136", volume = 0.5, pitch = 1.1},
        building_complete = {id = "rbxassetid://131961136", volume = 0.7, pitch = 1.0},
        demolition = {id = "rbxassetid://131961136", volume = 0.8, pitch = 0.9},
        
        -- UI sounds
        button_click = {id = "rbxassetid://131961136", volume = 0.3, pitch = 1.0},
        button_hover = {id = "rbxassetid://131961136", volume = 0.2, pitch = 1.2},
        notification = {id = "rbxassetid://131961136", volume = 0.5, pitch = 1.0},
        error_sound = {id = "rbxassetid://131961136", volume = 0.4, pitch = 0.8},
        success_sound = {id = "rbxassetid://131961136", volume = 0.6, pitch = 1.2},
        
        -- Economic sounds
        money_gain = {id = "rbxassetid://131961136", volume = 0.5, pitch = 1.1},
        money_loss = {id = "rbxassetid://131961136", volume = 0.4, pitch = 0.9},
        trade_complete = {id = "rbxassetid://131961136", volume = 0.6, pitch = 1.0},
        
        -- Environmental sounds
        city_ambient = {id = "rbxassetid://131961136", volume = 0.3, pitch = 1.0},
        industrial_ambient = {id = "rbxassetid://131961136", volume = 0.4, pitch = 0.9},
        nature_ambient = {id = "rbxassetid://131961136", volume = 0.3, pitch = 1.0},
        
        -- Weather sounds
        rain_light = {id = "rbxassetid://131961136", volume = 0.4, pitch = 1.0},
        rain_heavy = {id = "rbxassetid://131961136", volume = 0.6, pitch = 0.9},
        thunder = {id = "rbxassetid://131961136", volume = 0.8, pitch = 0.8},
        wind = {id = "rbxassetid://131961136", volume = 0.3, pitch = 1.0}
    }
    
    -- Music tracks (placeholder IDs - would be replaced with actual music)
    self.musicTracks = {
        -- Peaceful tracks
        peaceful_countryside = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        gentle_morning = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        calm_waters = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        
        -- Energetic tracks
        busy_city = {id = "rbxassetid://1837879082", volume = 0.5, loop = true},
        industrial_power = {id = "rbxassetid://1837879082", volume = 0.5, loop = true},
        rush_hour = {id = "rbxassetid://1837879082", volume = 0.5, loop = true},
        
        -- Atmospheric tracks
        night_lights = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        distant_thunder = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        moonlit_rails = {id = "rbxassetid://1837879082", volume = 0.4, loop = true},
        
        -- Dramatic tracks
        storm_approaching = {id = "rbxassetid://1837879082", volume = 0.6, loop = false},
        crisis_moment = {id = "rbxassetid://1837879082", volume = 0.6, loop = false},
        final_push = {id = "rbxassetid://1837879082", volume = 0.6, loop = false},
        
        -- Victory tracks
        achievement_fanfare = {id = "rbxassetid://1837879082", volume = 0.7, loop = false},
        success_theme = {id = "rbxassetid://1837879082", volume = 0.6, loop = false},
        celebration = {id = "rbxassetid://1837879082", volume = 0.6, loop = false}
    }
    
    -- Player audio preferences
    self.playerPreferences = {}
    
    -- Active sounds tracking
    self.activeSounds = {}
    self.soundInstances = {}
    
    -- Audio zones (3D positional audio)
    self.audioZones = {}
    
    return self
end

-- Initialize audio system
function AudioManager:Initialize()
    -- Create sound groups for better organization
    self:CreateSoundGroups()
    
    -- Start background music
    self:StartBackgroundMusic()
    
    -- Initialize ambient sounds
    self:InitializeAmbientSounds()
    
    print("🎵 Audio system initialized")
end

-- Create sound groups
function AudioManager:CreateSoundGroups()
    for categoryName, category in pairs(self.audioCategories) do
        local soundGroup = Instance.new("SoundGroup")
        soundGroup.Name = categoryName .. "Group"
        soundGroup.Volume = category.volume * self.masterVolume
        soundGroup.Parent = SoundService
        
        category.soundGroup = soundGroup
    end
end

-- Play sound effect
function AudioManager:PlaySoundEffect(soundName, position, player)
    local soundData = self.soundEffects[soundName]
    if not soundData then
        warn("Sound effect not found:", soundName)
        return nil
    end
    
    local sound = Instance.new("Sound")
    sound.SoundId = soundData.id
    sound.Volume = soundData.volume * self.sfxVolume * self.masterVolume
    sound.Pitch = soundData.pitch or 1.0
    sound.SoundGroup = self.audioCategories.sfx.soundGroup
    
    -- 3D positional audio
    if position then
        local part = Instance.new("Part")
        part.Name = "SoundSource"
        part.Size = Vector3.new(1, 1, 1)
        part.Position = position
        part.Anchored = true
        part.CanCollide = false
        part.Transparency = 1
        part.Parent = workspace
        
        sound.Parent = part
        
        -- Clean up after sound finishes
        sound.Ended:Connect(function()
            part:Destroy()
        end)
        
        -- Auto-cleanup after 10 seconds if sound doesn't end
        game:GetService("Debris"):AddItem(part, 10)
    else
        sound.Parent = SoundService
    end
    
    -- Player-specific audio
    if player then
        sound:Play()
        -- Would use RemoteEvent to play sound only for specific player
    else
        sound:Play()
    end
    
    -- Track active sound
    local soundId = HttpService:GenerateGUID(false)
    self.activeSounds[soundId] = {
        sound = sound,
        category = "sfx",
        startTime = tick()
    }
    
    return sound
end

-- Play music track
function AudioManager:PlayMusicTrack(trackName, fadeIn)
    local trackData = self.musicTracks[trackName]
    if not trackData then
        warn("Music track not found:", trackName)
        return nil
    end
    
    -- Fade out current track
    if self.currentTrack then
        self:FadeOutTrack(self.currentTrack, fadeIn and 2 or 0.5)
    end
    
    -- Create new track
    local sound = Instance.new("Sound")
    sound.SoundId = trackData.id
    sound.Volume = 0 -- Start at 0 for fade in
    sound.Looped = trackData.loop or false
    sound.SoundGroup = self.audioCategories.music.soundGroup
    sound.Parent = SoundService
    
    sound:Play()
    
    -- Fade in new track
    if fadeIn then
        self:FadeInTrack(sound, trackData.volume * self.musicVolume * self.masterVolume, 2)
    else
        sound.Volume = trackData.volume * self.musicVolume * self.masterVolume
    end
    
    self.currentTrack = {
        sound = sound,
        name = trackName,
        data = trackData
    }
    
    print("🎵 Playing music track:", trackName)
    return sound
end

-- Fade in track
function AudioManager:FadeInTrack(sound, targetVolume, duration)
    local startTime = tick()
    local startVolume = sound.Volume
    
    local connection
    connection = game:GetService("RunService").Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local progress = math.min(elapsed / duration, 1)
        
        sound.Volume = startVolume + (targetVolume - startVolume) * progress
        
        if progress >= 1 then
            connection:Disconnect()
        end
    end)
end

-- Fade out track
function AudioManager:FadeOutTrack(track, duration)
    if not track or not track.sound then return end
    
    local sound = track.sound
    local startTime = tick()
    local startVolume = sound.Volume
    
    local connection
    connection = game:GetService("RunService").Heartbeat:Connect(function()
        local elapsed = tick() - startTime
        local progress = math.min(elapsed / duration, 1)
        
        sound.Volume = startVolume * (1 - progress)
        
        if progress >= 1 then
            sound:Stop()
            sound:Destroy()
            connection:Disconnect()
        end
    end)
end

-- Dynamic music system
function AudioManager:UpdateDynamicMusic(gameState)
    if self.musicMode ~= "dynamic" then return end
    
    local currentTime = tick()
    if currentTime - self.lastMusicChange < self.musicChangeInterval then return end
    
    -- Analyze game state to determine mood
    local mood = self:AnalyzeGameMood(gameState)
    local moodData = self.musicMoods[mood]
    
    if moodData and #moodData.tracks > 0 then
        -- Select random track from mood
        local trackName = moodData.tracks[math.random(1, #moodData.tracks)]
        
        -- Don't repeat the same track
        if not self.currentTrack or self.currentTrack.name ~= trackName then
            self:PlayMusicTrack(trackName, true)
            self.lastMusicChange = currentTime
        end
    end
end

-- Analyze game mood for dynamic music
function AudioManager:AnalyzeGameMood(gameState)
    local mood = "peaceful" -- default
    
    if gameState.timeOfDay and gameState.timeOfDay >= 18 or gameState.timeOfDay <= 6 then
        mood = "atmospheric"
    elseif gameState.gameSpeed and gameState.gameSpeed > 2 then
        mood = "energetic"
    elseif gameState.crisis or gameState.emergency then
        mood = "dramatic"
    elseif gameState.recentAchievement then
        mood = "victory"
    elseif gameState.economicActivity and gameState.economicActivity > 0.7 then
        mood = "energetic"
    end
    
    return mood
end

-- Start background music
function AudioManager:StartBackgroundMusic()
    if self.musicMode == "dynamic" then
        self:PlayMusicTrack("peaceful_countryside", false)
    end
end

-- Initialize ambient sounds
function AudioManager:InitializeAmbientSounds()
    -- City ambient sounds
    self:CreateAmbientZone("city", Vector3.new(0, 0, 0), 100, "city_ambient")
    
    -- Industrial ambient sounds
    self:CreateAmbientZone("industrial", Vector3.new(200, 0, 200), 80, "industrial_ambient")
    
    -- Nature ambient sounds
    self:CreateAmbientZone("nature", Vector3.new(-200, 0, -200), 120, "nature_ambient")
end

-- Create ambient audio zone
function AudioManager:CreateAmbientZone(zoneName, position, radius, soundName)
    local soundData = self.soundEffects[soundName]
    if not soundData then return end
    
    local part = Instance.new("Part")
    part.Name = zoneName .. "AmbientZone"
    part.Size = Vector3.new(1, 1, 1)
    part.Position = position
    part.Anchored = true
    part.CanCollide = false
    part.Transparency = 1
    part.Parent = workspace
    
    local sound = Instance.new("Sound")
    sound.SoundId = soundData.id
    sound.Volume = soundData.volume * self.ambientVolume * self.masterVolume
    sound.Looped = true
    sound.SoundGroup = self.audioCategories.ambient.soundGroup
    sound.Parent = part
    
    sound:Play()
    
    self.audioZones[zoneName] = {
        part = part,
        sound = sound,
        position = position,
        radius = radius,
        soundName = soundName
    }
end

-- Update ambient audio based on player position
function AudioManager:UpdateAmbientAudio(playerPosition)
    for zoneName, zone in pairs(self.audioZones) do
        local distance = (playerPosition - zone.position).Magnitude
        local volume = 0
        
        if distance <= zone.radius then
            -- Calculate volume based on distance (closer = louder)
            local normalizedDistance = distance / zone.radius
            volume = (1 - normalizedDistance) * self.ambientVolume * self.masterVolume
        end
        
        zone.sound.Volume = volume
    end
end

-- Set player audio preferences
function AudioManager:SetPlayerPreferences(playerId, preferences)
    self.playerPreferences[playerId] = preferences
    
    -- Apply preferences immediately
    if preferences.masterVolume then
        -- Would send to client to adjust local audio
    end
end

-- Get player audio preferences
function AudioManager:GetPlayerPreferences(playerId)
    return self.playerPreferences[playerId] or {
        masterVolume = 0.8,
        musicVolume = 0.6,
        sfxVolume = 0.8,
        ambientVolume = 0.4,
        voiceVolume = 0.7
    }
end

-- Play UI sound
function AudioManager:PlayUISound(soundName, player)
    local soundData = self.soundEffects[soundName]
    if not soundData then return end
    
    -- UI sounds are typically played locally on client
    -- This would trigger a RemoteEvent to play the sound on client
    if player then
        -- Events.PlayUISoundEvent:FireClient(player, soundName)
        print("🔊 Playing UI sound for", player.Name, ":", soundName)
    else
        -- Play for all players
        print("🔊 Playing UI sound for all players:", soundName)
    end
end

-- Play transport sound
function AudioManager:PlayTransportSound(vehicleType, soundType, position, volume)
    local soundName = vehicleType .. "_" .. soundType
    local sound = self:PlaySoundEffect(soundName, position)
    
    if sound and volume then
        sound.Volume = sound.Volume * volume
    end
    
    return sound
end

-- Play construction sound
function AudioManager:PlayConstructionSound(constructionType, position)
    local soundName = "construction_" .. constructionType
    return self:PlaySoundEffect(soundName, position)
end

-- Play economic sound
function AudioManager:PlayEconomicSound(eventType, player, amount)
    local soundName = eventType
    
    -- Adjust pitch based on amount for money sounds
    if eventType == "money_gain" or eventType == "money_loss" then
        local sound = self:PlaySoundEffect(soundName, nil, player)
        if sound and amount then
            -- Higher amounts = higher pitch (within reason)
            local pitchMultiplier = math.min(1.5, 1 + (amount / 1000000) * 0.3)
            sound.Pitch = sound.Pitch * pitchMultiplier
        end
        return sound
    else
        return self:PlaySoundEffect(soundName, nil, player)
    end
end

-- Stop all sounds in category
function AudioManager:StopCategorySound(category)
    if self.audioCategories[category] and self.audioCategories[category].soundGroup then
        for _, sound in pairs(self.audioCategories[category].soundGroup:GetChildren()) do
            if sound:IsA("Sound") then
                sound:Stop()
            end
        end
    end
end

-- Set category volume
function AudioManager:SetCategoryVolume(category, volume)
    if self.audioCategories[category] then
        self.audioCategories[category].volume = volume
        if self.audioCategories[category].soundGroup then
            self.audioCategories[category].soundGroup.Volume = volume * self.masterVolume
        end
    end
end

-- Set master volume
function AudioManager:SetMasterVolume(volume)
    self.masterVolume = volume
    
    -- Update all category volumes
    for categoryName, category in pairs(self.audioCategories) do
        if category.soundGroup then
            category.soundGroup.Volume = category.volume * self.masterVolume
        end
    end
end

-- Clean up finished sounds
function AudioManager:CleanupSounds()
    local currentTime = tick()
    
    for soundId, soundInfo in pairs(self.activeSounds) do
        if not soundInfo.sound.Parent or not soundInfo.sound.IsPlaying then
            self.activeSounds[soundId] = nil
        elseif currentTime - soundInfo.startTime > 300 then -- 5 minutes max
            soundInfo.sound:Stop()
            if soundInfo.sound.Parent then
                soundInfo.sound:Destroy()
            end
            self.activeSounds[soundId] = nil
        end
    end
end

-- Update audio system
function AudioManager:Update(deltaTime, gameState, playerPosition)
    -- Update dynamic music
    self:UpdateDynamicMusic(gameState)
    
    -- Update ambient audio
    if playerPosition then
        self:UpdateAmbientAudio(playerPosition)
    end
    
    -- Cleanup old sounds
    self:CleanupSounds()
end

-- Get audio statistics
function AudioManager:GetAudioStats()
    return {
        activeSounds = #self.activeSounds,
        currentTrack = self.currentTrack and self.currentTrack.name or "None",
        musicMode = self.musicMode,
        masterVolume = self.masterVolume,
        audioZones = #self.audioZones
    }
end

return AudioManager
