-- StarterGui/MainMenu/MainMenuComplete.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Vollständiges Hauptmenü mit Kartenvorschau und Spielstart

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events warten
local Events = ReplicatedStorage:WaitForChild("Events")
local GenerateMapEvent = Events:WaitForChild("GenerateMapEvent")

local MainMenu = {}
MainMenu.IsOpen = true
MainMenu.MapConfig = {
    seed = tostring(math.random(1000, 9999)),
    mapSize = "Medium",
    mapType = "European",
    hilliness = 0.6,
    waterAmount = 0.3,
    startYear = 1850
}

-- Hauptmenü erstellen
function MainMenu:CreateMainMenu()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainMenuGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hintergrund
    local background = Instance.new("Frame")
    background.Size = UDim2.new(1, 0, 1, 0)
    background.BackgroundColor3 = Color3.fromRGB(20, 30, 40)
    background.BorderSizePixel = 0
    background.Parent = screenGui
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(0, 600, 0, 100)
    title.Position = UDim2.new(0.5, -300, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🚂 TRANSPORT EMPIRE"
    title.TextColor3 = Color3.fromRGB(255, 215, 0)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = background
    
    -- Hauptmenü-Panel
    local menuPanel = Instance.new("Frame")
    menuPanel.Size = UDim2.new(0, 300, 0, 400)
    menuPanel.Position = UDim2.new(0, 50, 0.5, -200)
    menuPanel.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    menuPanel.BorderSizePixel = 0
    menuPanel.Parent = background
    
    local menuCorner = Instance.new("UICorner")
    menuCorner.CornerRadius = UDim.new(0, 15)
    menuCorner.Parent = menuPanel
    
    -- Buttons
    self:CreateButton(menuPanel, "🎮 NEUES SPIEL", UDim2.new(0, 0, 0, 50), function()
        self:ShowNewGameMenu()
    end)
    
    self:CreateButton(menuPanel, "📁 SPIEL LADEN", UDim2.new(0, 0, 0, 120), function()
        print("Laden noch nicht implementiert")
    end)
    
    self:CreateButton(menuPanel, "⚙️ EINSTELLUNGEN", UDim2.new(0, 0, 0, 190), function()
        print("Einstellungen noch nicht implementiert")
    end)
    
    self:CreateButton(menuPanel, "❌ BEENDEN", UDim2.new(0, 0, 0, 260), function()
        screenGui:Destroy()
    end)
    
    -- Kartenvorschau-Panel
    local previewPanel = Instance.new("Frame")
    previewPanel.Size = UDim2.new(0, 500, 0, 400)
    previewPanel.Position = UDim2.new(1, -550, 0.5, -200)
    previewPanel.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    previewPanel.BorderSizePixel = 0
    previewPanel.Visible = false
    previewPanel.Parent = background
    
    local previewCorner = Instance.new("UICorner")
    previewCorner.CornerRadius = UDim.new(0, 15)
    previewCorner.Parent = previewPanel
    
    self.ScreenGui = screenGui
    self.MenuPanel = menuPanel
    self.PreviewPanel = previewPanel
    self.Background = background
    
    return screenGui
end

-- Button erstellen
function MainMenu:CreateButton(parent, text, position, callback)
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(0.9, 0, 0, 50)
    button.Position = UDim2.new(0.05, 0, 0, position.Y.Offset)
    button.BackgroundColor3 = Color3.fromRGB(60, 120, 180)
    button.Text = text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.SourceSansBold
    button.BorderSizePixel = 0
    button.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = button
    
    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(80, 140, 200)
        })
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(60, 120, 180)
        })
        tween:Play()
    end)
    
    button.MouseButton1Click:Connect(callback)
    
    return button
end

-- Neues Spiel Menü anzeigen
function MainMenu:ShowNewGameMenu()
    self.MenuPanel.Visible = false
    self.PreviewPanel.Visible = true
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "🗺️ NEUE KARTE ERSTELLEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.PreviewPanel
    
    -- Konfiguration
    local configFrame = Instance.new("Frame")
    configFrame.Size = UDim2.new(0.45, 0, 0.8, 0)
    configFrame.Position = UDim2.new(0.05, 0, 0.15, 0)
    configFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    configFrame.BorderSizePixel = 0
    configFrame.Parent = self.PreviewPanel
    
    local configCorner = Instance.new("UICorner")
    configCorner.CornerRadius = UDim.new(0, 8)
    configCorner.Parent = configFrame
    
    -- Kartenvorschau
    local previewFrame = Instance.new("Frame")
    previewFrame.Size = UDim2.new(0.45, 0, 0.6, 0)
    previewFrame.Position = UDim2.new(0.52, 0, 0.15, 0)
    previewFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    previewFrame.BorderSizePixel = 0
    previewFrame.Parent = self.PreviewPanel
    
    local previewCorner = Instance.new("UICorner")
    previewCorner.CornerRadius = UDim.new(0, 8)
    previewCorner.Parent = previewFrame
    
    -- Konfigurationsoptionen
    self:CreateConfigOption(configFrame, "Seed:", 20, function(value)
        self.MapConfig.seed = value
        self:UpdateMapPreview(previewFrame)
    end, self.MapConfig.seed)
    
    self:CreateConfigDropdown(configFrame, "Kartengröße:", 80, {"Small", "Medium", "Large"}, function(value)
        self.MapConfig.mapSize = value
        self:UpdateMapPreview(previewFrame)
    end)
    
    self:CreateConfigDropdown(configFrame, "Kartentyp:", 140, {"European", "American", "Asian"}, function(value)
        self.MapConfig.mapType = value
        self:UpdateMapPreview(previewFrame)
    end)
    
    -- Buttons
    self:CreateButton(self.PreviewPanel, "🚀 SPIEL STARTEN", UDim2.new(0.52, 0, 0.8, 0), function()
        self:StartGame()
    end)
    
    self:CreateButton(self.PreviewPanel, "⬅️ ZURÜCK", UDim2.new(0.05, 0, 0.8, 0), function()
        self:ShowMainMenu()
    end)
    
    -- Initiale Kartenvorschau
    self:UpdateMapPreview(previewFrame)
end

-- Konfigurationsoption erstellen
function MainMenu:CreateConfigOption(parent, label, yPos, callback, defaultValue)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0.05, 0, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent
    
    local textBox = Instance.new("TextBox")
    textBox.Size = UDim2.new(0.5, 0, 0, 30)
    textBox.Position = UDim2.new(0.45, 0, 0, yPos)
    textBox.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    textBox.Text = defaultValue or ""
    textBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    textBox.TextScaled = true
    textBox.Font = Enum.Font.SourceSans
    textBox.BorderSizePixel = 0
    textBox.Parent = parent
    
    local boxCorner = Instance.new("UICorner")
    boxCorner.CornerRadius = UDim.new(0, 4)
    boxCorner.Parent = textBox
    
    textBox.FocusLost:Connect(function()
        if callback then callback(textBox.Text) end
    end)
end

-- Dropdown erstellen
function MainMenu:CreateConfigDropdown(parent, label, yPos, options, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0.05, 0, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent
    
    local dropdown = Instance.new("TextButton")
    dropdown.Size = UDim2.new(0.5, 0, 0, 30)
    dropdown.Position = UDim2.new(0.45, 0, 0, yPos)
    dropdown.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    dropdown.Text = options[1] .. " ▼"
    dropdown.TextColor3 = Color3.fromRGB(255, 255, 255)
    dropdown.TextScaled = true
    dropdown.Font = Enum.Font.SourceSans
    dropdown.BorderSizePixel = 0
    dropdown.Parent = parent
    
    local dropCorner = Instance.new("UICorner")
    dropCorner.CornerRadius = UDim.new(0, 4)
    dropCorner.Parent = dropdown
    
    local currentIndex = 1
    dropdown.MouseButton1Click:Connect(function()
        currentIndex = (currentIndex % #options) + 1
        dropdown.Text = options[currentIndex] .. " ▼"
        if callback then callback(options[currentIndex]) end
    end)
end

-- Kartenvorschau aktualisieren
function MainMenu:UpdateMapPreview(previewFrame)
    -- Alte Vorschau löschen
    for _, child in pairs(previewFrame:GetChildren()) do
        if child.Name == "PreviewPixel" then
            child:Destroy()
        end
    end

    local gridSize = 25
    local pixelSize = 1 / gridSize

    -- Seed für konsistente Generierung
    math.randomseed(tonumber(self.MapConfig.seed) or 12345)

    for x = 1, gridSize do
        for y = 1, gridSize do
            local pixel = Instance.new("Frame")
            pixel.Name = "PreviewPixel"
            pixel.Size = UDim2.new(pixelSize, -1, pixelSize, -1)
            pixel.Position = UDim2.new((x-1) * pixelSize, 0, (y-1) * pixelSize, 0)
            pixel.BorderSizePixel = 0
            pixel.Parent = previewFrame

            -- Terrain basierend auf Kartentyp
            local noise = math.noise(x * 0.2, y * 0.2, 0)
            local waterNoise = math.noise(x * 0.15, y * 0.15, 100)

            if waterNoise > 0.4 then
                -- Wasser
                pixel.BackgroundColor3 = Color3.fromRGB(50, 100, 200)
            elseif noise > 0.3 then
                -- Berge
                if self.MapConfig.mapType == "European" then
                    pixel.BackgroundColor3 = Color3.fromRGB(120, 80, 60)
                elseif self.MapConfig.mapType == "American" then
                    pixel.BackgroundColor3 = Color3.fromRGB(140, 100, 80)
                else -- Asian
                    pixel.BackgroundColor3 = Color3.fromRGB(100, 120, 80)
                end
            elseif noise > 0.1 then
                -- Hügel
                pixel.BackgroundColor3 = Color3.fromRGB(80, 140, 80)
            else
                -- Ebenen
                pixel.BackgroundColor3 = Color3.fromRGB(100, 180, 100)
            end

            -- Städte
            if math.random() < 0.03 then
                pixel.BackgroundColor3 = Color3.fromRGB(200, 200, 50)
            end
        end
    end

    math.randomseed(tick())
end

-- Hauptmenü anzeigen
function MainMenu:ShowMainMenu()
    self.MenuPanel.Visible = true
    self.PreviewPanel.Visible = false

    -- Alte Inhalte löschen
    for _, child in pairs(self.PreviewPanel:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
end

-- Spiel starten
function MainMenu:StartGame()
    print("🚀 Starte Spiel mit Konfiguration:")
    for key, value in pairs(self.MapConfig) do
        print("  " .. key .. ":", value)
    end

    -- Event an Server senden
    GenerateMapEvent:FireServer(self.MapConfig)

    -- GUI ausblenden
    self.ScreenGui.Enabled = false
    self.IsOpen = false

    -- Game Interface aktivieren (vereinfacht)
    print("🎮 Spiel gestartet! Verwende folgende Steuerung:")
    print("  📋 B = Gebäude-Menü")
    print("  🚂 V = Fahrzeug-Menü")
    print("  🔬 T = Tech-Tree")
    print("  💰 M = Geld-Status")
end

-- Initialisierung
function MainMenu:Initialize()
    self:CreateMainMenu()
    print("🎨 Vollständiges Hauptmenü erstellt")
end

-- Auto-Start
MainMenu:Initialize()

return MainMenu
