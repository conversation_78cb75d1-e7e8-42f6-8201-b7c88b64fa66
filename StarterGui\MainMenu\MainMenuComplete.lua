-- StarterGui/MainMenu/MainMenuComplete.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Vollständiges Hauptmenü mit Kartenvorschau und Spielstart

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events warten
local Events = ReplicatedStorage:WaitForChild("Events")
local GenerateMapEvent = Events:WaitForChild("GenerateMapEvent")

local MainMenu = {}
MainMenu.IsOpen = true
MainMenu.MapConfig = {
    seed = tostring(math.random(1000, 9999)),
    mapSize = "Medium",
    mapType = "European",
    hilliness = 0.6,
    waterAmount = 0.3,
    startYear = 1850,
    climate = "Temperate",
    difficulty = "Normal",
    cityGrowth = 1.0,
    industryDensity = 1.0
}

-- Hauptmenü erstellen
function MainMenu:CreateMainMenu()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainMenuGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hintergrund
    local background = Instance.new("Frame")
    background.Size = UDim2.new(1, 0, 1, 0)
    background.BackgroundColor3 = Color3.fromRGB(20, 30, 40)
    background.BorderSizePixel = 0
    background.Parent = screenGui
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(0, 600, 0, 100)
    title.Position = UDim2.new(0.5, -300, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🚂 TRANSPORT EMPIRE"
    title.TextColor3 = Color3.fromRGB(255, 215, 0)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = background
    
    -- Hauptmenü-Panel
    local menuPanel = Instance.new("Frame")
    menuPanel.Size = UDim2.new(0, 300, 0, 400)
    menuPanel.Position = UDim2.new(0, 50, 0.5, -200)
    menuPanel.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    menuPanel.BorderSizePixel = 0
    menuPanel.Parent = background
    
    local menuCorner = Instance.new("UICorner")
    menuCorner.CornerRadius = UDim.new(0, 15)
    menuCorner.Parent = menuPanel
    
    -- Buttons
    self:CreateButton(menuPanel, "🎮 NEUES SPIEL", UDim2.new(0, 0, 0, 50), function()
        self:ShowNewGameMenu()
    end)
    
    self:CreateButton(menuPanel, "📁 SPIEL LADEN", UDim2.new(0, 0, 0, 120), function()
        print("Laden noch nicht implementiert")
    end)
    
    self:CreateButton(menuPanel, "⚙️ EINSTELLUNGEN", UDim2.new(0, 0, 0, 190), function()
        print("Einstellungen noch nicht implementiert")
    end)
    
    self:CreateButton(menuPanel, "❌ BEENDEN", UDim2.new(0, 0, 0, 260), function()
        screenGui:Destroy()
    end)
    
    -- Kartenvorschau-Panel
    local previewPanel = Instance.new("Frame")
    previewPanel.Size = UDim2.new(0, 500, 0, 400)
    previewPanel.Position = UDim2.new(1, -550, 0.5, -200)
    previewPanel.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    previewPanel.BorderSizePixel = 0
    previewPanel.Visible = false
    previewPanel.Parent = background
    
    local previewCorner = Instance.new("UICorner")
    previewCorner.CornerRadius = UDim.new(0, 15)
    previewCorner.Parent = previewPanel
    
    self.ScreenGui = screenGui
    self.MenuPanel = menuPanel
    self.PreviewPanel = previewPanel
    self.Background = background
    
    return screenGui
end

-- Button erstellen
function MainMenu:CreateButton(parent, text, position, callback)
    local button = Instance.new("TextButton")
    button.Size = UDim2.new(0.9, 0, 0, 50)
    button.Position = UDim2.new(0.05, 0, 0, position.Y.Offset)
    button.BackgroundColor3 = Color3.fromRGB(60, 120, 180)
    button.Text = text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.SourceSansBold
    button.BorderSizePixel = 0
    button.Parent = parent
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = button
    
    -- Hover-Effekt
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(80, 140, 200)
        })
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {
            BackgroundColor3 = Color3.fromRGB(60, 120, 180)
        })
        tween:Play()
    end)
    
    button.MouseButton1Click:Connect(callback)
    
    return button
end

-- Neues Spiel Menü anzeigen
function MainMenu:ShowNewGameMenu()
    self.MenuPanel.Visible = false
    self.PreviewPanel.Visible = true
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "🗺️ NEUE KARTE ERSTELLEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.PreviewPanel
    
    -- Konfiguration
    local configFrame = Instance.new("Frame")
    configFrame.Size = UDim2.new(0.45, 0, 0.8, 0)
    configFrame.Position = UDim2.new(0.05, 0, 0.15, 0)
    configFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    configFrame.BorderSizePixel = 0
    configFrame.Parent = self.PreviewPanel
    
    local configCorner = Instance.new("UICorner")
    configCorner.CornerRadius = UDim.new(0, 8)
    configCorner.Parent = configFrame
    
    -- Kartenvorschau
    local previewFrame = Instance.new("Frame")
    previewFrame.Size = UDim2.new(0.45, 0, 0.6, 0)
    previewFrame.Position = UDim2.new(0.52, 0, 0.15, 0)
    previewFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    previewFrame.BorderSizePixel = 0
    previewFrame.Parent = self.PreviewPanel
    
    local previewCorner = Instance.new("UICorner")
    previewCorner.CornerRadius = UDim.new(0, 8)
    previewCorner.Parent = previewFrame
    
    -- Konfigurationsoptionen mit Scroll
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 6
    scrollFrame.Parent = configFrame

    local yPos = 10

    -- Seed
    self:CreateConfigOption(scrollFrame, "🎲 Seed:", yPos, function(value)
        self.MapConfig.seed = value
        self:UpdateMapPreview(previewFrame)
    end, self.MapConfig.seed)
    yPos = yPos + 50

    -- Kartengröße
    self:CreateConfigDropdown(scrollFrame, "📏 Kartengröße:", yPos, {"Small", "Medium", "Large", "Huge"}, function(value)
        self.MapConfig.mapSize = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Kartentyp
    self:CreateConfigDropdown(scrollFrame, "🌍 Kartentyp:", yPos, {"European", "American", "Asian", "African"}, function(value)
        self.MapConfig.mapType = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Startjahr
    self:CreateConfigDropdown(scrollFrame, "📅 Startjahr:", yPos, {"1850", "1900", "1950", "2000"}, function(value)
        self.MapConfig.startYear = tonumber(value)
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Klima
    self:CreateConfigDropdown(scrollFrame, "🌡️ Klima:", yPos, {"Arctic", "Temperate", "Tropical", "Desert"}, function(value)
        self.MapConfig.climate = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Schwierigkeit
    self:CreateConfigDropdown(scrollFrame, "⚡ Schwierigkeit:", yPos, {"Easy", "Normal", "Hard", "Expert"}, function(value)
        self.MapConfig.difficulty = value
    end)
    yPos = yPos + 50

    -- Hügeligkeit Slider
    self:CreateConfigSlider(scrollFrame, "⛰️ Hügeligkeit:", yPos, 0.0, 1.0, self.MapConfig.hilliness, function(value)
        self.MapConfig.hilliness = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Wassermenge Slider
    self:CreateConfigSlider(scrollFrame, "🌊 Wassermenge:", yPos, 0.0, 1.0, self.MapConfig.waterAmount, function(value)
        self.MapConfig.waterAmount = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    -- Stadtwachstum Slider
    self:CreateConfigSlider(scrollFrame, "🏙️ Stadtwachstum:", yPos, 0.5, 2.0, self.MapConfig.cityGrowth, function(value)
        self.MapConfig.cityGrowth = value
    end)
    yPos = yPos + 50

    -- Industrie-Dichte Slider
    self:CreateConfigSlider(scrollFrame, "🏭 Industrie-Dichte:", yPos, 0.5, 2.0, self.MapConfig.industryDensity, function(value)
        self.MapConfig.industryDensity = value
        self:UpdateMapPreview(previewFrame)
    end)
    yPos = yPos + 50

    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
    
    -- Buttons
    self:CreateButton(self.PreviewPanel, "🚀 SPIEL STARTEN", UDim2.new(0.52, 0, 0.8, 0), function()
        self:StartGame()
    end)
    
    self:CreateButton(self.PreviewPanel, "⬅️ ZURÜCK", UDim2.new(0.05, 0, 0.8, 0), function()
        self:ShowMainMenu()
    end)
    
    -- Initiale Kartenvorschau
    self:UpdateMapPreview(previewFrame)
end

-- Konfigurationsoption erstellen
function MainMenu:CreateConfigOption(parent, label, yPos, callback, defaultValue)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0.05, 0, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent
    
    local textBox = Instance.new("TextBox")
    textBox.Size = UDim2.new(0.5, 0, 0, 30)
    textBox.Position = UDim2.new(0.45, 0, 0, yPos)
    textBox.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    textBox.Text = defaultValue or ""
    textBox.TextColor3 = Color3.fromRGB(255, 255, 255)
    textBox.TextScaled = true
    textBox.Font = Enum.Font.SourceSans
    textBox.BorderSizePixel = 0
    textBox.Parent = parent
    
    local boxCorner = Instance.new("UICorner")
    boxCorner.CornerRadius = UDim.new(0, 4)
    boxCorner.Parent = textBox
    
    textBox.FocusLost:Connect(function()
        if callback then callback(textBox.Text) end
    end)
end

-- Dropdown erstellen
function MainMenu:CreateConfigDropdown(parent, label, yPos, options, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0.05, 0, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent
    
    local dropdown = Instance.new("TextButton")
    dropdown.Size = UDim2.new(0.5, 0, 0, 30)
    dropdown.Position = UDim2.new(0.45, 0, 0, yPos)
    dropdown.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    dropdown.Text = options[1] .. " ▼"
    dropdown.TextColor3 = Color3.fromRGB(255, 255, 255)
    dropdown.TextScaled = true
    dropdown.Font = Enum.Font.SourceSans
    dropdown.BorderSizePixel = 0
    dropdown.Parent = parent
    
    local dropCorner = Instance.new("UICorner")
    dropCorner.CornerRadius = UDim.new(0, 4)
    dropCorner.Parent = dropdown
    
    local currentIndex = 1
    dropdown.MouseButton1Click:Connect(function()
        currentIndex = (currentIndex % #options) + 1
        dropdown.Text = options[currentIndex] .. " ▼"
        if callback then callback(options[currentIndex]) end
    end)
end

-- Slider erstellen
function MainMenu:CreateConfigSlider(parent, label, yPos, minValue, maxValue, defaultValue, callback)
    local labelText = Instance.new("TextLabel")
    labelText.Size = UDim2.new(0.4, 0, 0, 30)
    labelText.Position = UDim2.new(0.05, 0, 0, yPos)
    labelText.BackgroundTransparency = 1
    labelText.Text = label
    labelText.TextColor3 = Color3.fromRGB(255, 255, 255)
    labelText.TextScaled = true
    labelText.Font = Enum.Font.SourceSans
    labelText.TextXAlignment = Enum.TextXAlignment.Left
    labelText.Parent = parent

    -- Slider-Hintergrund
    local sliderBg = Instance.new("Frame")
    sliderBg.Size = UDim2.new(0.4, 0, 0, 8)
    sliderBg.Position = UDim2.new(0.45, 0, 0, yPos + 11)
    sliderBg.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    sliderBg.BorderSizePixel = 0
    sliderBg.Parent = parent

    local sliderCorner = Instance.new("UICorner")
    sliderCorner.CornerRadius = UDim.new(0, 4)
    sliderCorner.Parent = sliderBg

    -- Slider-Knopf
    local sliderKnob = Instance.new("Frame")
    sliderKnob.Size = UDim2.new(0, 16, 0, 16)
    sliderKnob.Position = UDim2.new((defaultValue - minValue) / (maxValue - minValue), -8, 0, -4)
    sliderKnob.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
    sliderKnob.BorderSizePixel = 0
    sliderKnob.Parent = sliderBg

    local knobCorner = Instance.new("UICorner")
    knobCorner.CornerRadius = UDim.new(0, 8)
    knobCorner.Parent = sliderKnob

    -- Wert-Anzeige
    local valueLabel = Instance.new("TextLabel")
    valueLabel.Size = UDim2.new(0.1, 0, 0, 30)
    valueLabel.Position = UDim2.new(0.87, 0, 0, yPos)
    valueLabel.BackgroundTransparency = 1
    valueLabel.Text = string.format("%.2f", defaultValue)
    valueLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    valueLabel.TextScaled = true
    valueLabel.Font = Enum.Font.SourceSans
    valueLabel.Parent = parent

    -- Vereinfachte Slider-Interaktion
    local isDragging = false

    sliderBg.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            isDragging = true
            local function updateSlider()
                local mousePos = input.Position.X
                local sliderPos = sliderBg.AbsolutePosition.X
                local sliderWidth = sliderBg.AbsoluteSize.X
                local relativePos = math.max(0, math.min(1, (mousePos - sliderPos) / sliderWidth))

                local value = minValue + (maxValue - minValue) * relativePos
                sliderKnob.Position = UDim2.new(relativePos, -8, 0, -4)
                valueLabel.Text = string.format("%.2f", value)

                if callback then callback(value) end
            end
            updateSlider()
        end
    end)
end

-- Kartenvorschau aktualisieren
function MainMenu:UpdateMapPreview(previewFrame)
    -- Alte Vorschau löschen
    for _, child in pairs(previewFrame:GetChildren()) do
        if child.Name == "PreviewPixel" or child.Name == "PreviewInfo" then
            child:Destroy()
        end
    end

    local gridSize = 30
    local pixelSize = 1 / gridSize

    -- Seed für konsistente Generierung
    local seedNum = tonumber(self.MapConfig.seed) or 12345
    math.randomseed(seedNum)

    local cityCount = 0
    local industryCount = 0
    local waterCount = 0

    for x = 1, gridSize do
        for y = 1, gridSize do
            local pixel = Instance.new("Frame")
            pixel.Name = "PreviewPixel"
            pixel.Size = UDim2.new(pixelSize, -1, pixelSize, -1)
            pixel.Position = UDim2.new((x-1) * pixelSize, 0, (y-1) * pixelSize, 0)
            pixel.BorderSizePixel = 0
            pixel.Parent = previewFrame

            -- Terrain basierend auf Konfiguration
            local noise = math.noise(x * 0.15, y * 0.15, seedNum) * self.MapConfig.hilliness
            local waterNoise = math.noise(x * 0.1, y * 0.1, seedNum + 100) * self.MapConfig.waterAmount
            local industryNoise = math.noise(x * 0.3, y * 0.3, seedNum + 200) * self.MapConfig.industryDensity

            -- Klima-basierte Farben
            local climateColors = {
                Arctic = {water = Color3.fromRGB(150, 200, 255), land = Color3.fromRGB(240, 240, 255), mountain = Color3.fromRGB(200, 200, 220)},
                Temperate = {water = Color3.fromRGB(50, 100, 200), land = Color3.fromRGB(100, 180, 100), mountain = Color3.fromRGB(120, 80, 60)},
                Tropical = {water = Color3.fromRGB(0, 150, 200), land = Color3.fromRGB(50, 200, 50), mountain = Color3.fromRGB(100, 150, 50)},
                Desert = {water = Color3.fromRGB(100, 150, 200), land = Color3.fromRGB(200, 180, 120), mountain = Color3.fromRGB(180, 140, 100)}
            }

            local colors = climateColors[self.MapConfig.climate] or climateColors.Temperate

            if waterNoise > 0.3 then
                -- Wasser
                pixel.BackgroundColor3 = colors.water
                waterCount = waterCount + 1
            elseif noise > 0.4 then
                -- Berge
                pixel.BackgroundColor3 = colors.mountain
            elseif noise > 0.1 then
                -- Hügel
                pixel.BackgroundColor3 = Color3.new(
                    colors.land.R * 0.8,
                    colors.land.G * 0.8,
                    colors.land.B * 0.8
                )
            else
                -- Ebenen
                pixel.BackgroundColor3 = colors.land
            end

            -- Städte
            if math.random() < 0.02 * self.MapConfig.cityGrowth and waterNoise <= 0.3 then
                pixel.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
                cityCount = cityCount + 1
            end

            -- Industrien
            if industryNoise > 0.5 and waterNoise <= 0.3 and math.random() < 0.015 then
                pixel.BackgroundColor3 = Color3.fromRGB(150, 75, 0)
                industryCount = industryCount + 1
            end
        end
    end

    -- Statistik-Info hinzufügen
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Name = "PreviewInfo"
    infoLabel.Size = UDim2.new(1, 0, 0, 60)
    infoLabel.Position = UDim2.new(0, 0, 1, -60)
    infoLabel.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    infoLabel.BackgroundTransparency = 0.3
    infoLabel.Text = string.format("🏙️ Städte: %d | 🏭 Industrien: %d | 🌊 Wasser: %.1f%%",
        cityCount, industryCount, (waterCount / (gridSize * gridSize)) * 100)
    infoLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.SourceSans
    infoLabel.Parent = previewFrame

    math.randomseed(tick())
end

-- Hauptmenü anzeigen
function MainMenu:ShowMainMenu()
    self.MenuPanel.Visible = true
    self.PreviewPanel.Visible = false

    -- Alte Inhalte löschen
    for _, child in pairs(self.PreviewPanel:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
end

-- Spiel starten
function MainMenu:StartGame()
    print("🚀 Starte Spiel mit Konfiguration:")
    for key, value in pairs(self.MapConfig) do
        print("  " .. key .. ":", value)
    end

    -- Event an Server senden
    GenerateMapEvent:FireServer(self.MapConfig)

    -- GUI ausblenden
    self.ScreenGui.Enabled = false
    self.IsOpen = false

    -- Game Interface aktivieren (vereinfacht)
    print("🎮 Spiel gestartet! Verwende folgende Steuerung:")
    print("  📋 B = Gebäude-Menü")
    print("  🚂 V = Fahrzeug-Menü")
    print("  🔬 T = Tech-Tree")
    print("  💰 M = Geld-Status")
end

-- Initialisierung
function MainMenu:Initialize()
    self:CreateMainMenu()
    print("🎨 Vollständiges Hauptmenü erstellt")
end

-- Auto-Start
MainMenu:Initialize()

return MainMenu
