-- StarterPlayerScripts/GUI/StatisticsGUI.lua
-- RO<PERSON>OX SCRIPT TYPE: LocalScript
-- Statistiken-Übersicht GUI wie in Transport Fever 2

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetStatisticsDataFunction = Events:WaitForChild("GetStatisticsDataFunction")

local StatisticsGUI = {}
StatisticsGUI.IsOpen = false
StatisticsGUI.CurrentTab = "TRANSPORT" -- TRANSPORT, ECONOMY, CITIES, ENVIRONMENT
StatisticsGUI.StatisticsData = {}

-- GUI erstellen
function StatisticsGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "StatisticsGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1200, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "📊 STATISTIKEN & ANALYSEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "TRANSPORT", text = "🚂 Transport", icon = "🚂"},
        {name = "ECONOMY", text = "💰 Wirtschaft", icon = "💰"},
        {name = "CITIES", text = "🏘️ Städte", icon = "🏘️"},
        {name = "ENVIRONMENT", text = "🌍 Umwelt", icon = "🌍"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 670)
    contentFrame.Position = UDim2.new(0, 10, 0, 120)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function StatisticsGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function StatisticsGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        end
    end
end

-- Content aktualisieren
function StatisticsGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "TRANSPORT" then
        self:CreateTransportContent()
    elseif self.CurrentTab == "ECONOMY" then
        self:CreateEconomyContent()
    elseif self.CurrentTab == "CITIES" then
        self:CreateCitiesContent()
    elseif self.CurrentTab == "ENVIRONMENT" then
        self:CreateEnvironmentContent()
    end
end

-- Transport-Statistiken erstellen
function StatisticsGUI:CreateTransportContent()
    -- Statistik-Karten
    local transportStats = {
        {title = "🚂 Aktive Züge", value = "24", color = Color3.fromRGB(100, 150, 255)},
        {title = "🚛 Aktive LKWs", value = "18", color = Color3.fromRGB(255, 150, 100)},
        {title = "🚢 Aktive Schiffe", value = "6", color = Color3.fromRGB(100, 255, 150)},
        {title = "👥 Passagiere/Monat", value = "45,678", color = Color3.fromRGB(255, 100, 150)},
        {title = "📦 Cargo/Monat", value = "123,456t", color = Color3.fromRGB(150, 100, 255)},
        {title = "🛤️ Schienen-km", value = "234 km", color = Color3.fromRGB(200, 200, 100)}
    }
    
    for i, stat in ipairs(transportStats) do
        local cardFrame = Instance.new("Frame")
        cardFrame.Size = UDim2.new(0.32, 0, 0.25, 0)
        cardFrame.Position = UDim2.new(((i-1) % 3) * 0.34, 0, math.floor((i-1) / 3) * 0.3, 10)
        cardFrame.BackgroundColor3 = stat.color
        cardFrame.BorderSizePixel = 0
        cardFrame.Parent = self.ContentFrame
        
        local cardCorner = Instance.new("UICorner")
        cardCorner.CornerRadius = UDim.new(0, 10)
        cardCorner.Parent = cardFrame
        
        local titleLabel = Instance.new("TextLabel")
        titleLabel.Size = UDim2.new(1, -20, 0.5, 0)
        titleLabel.Position = UDim2.new(0, 10, 0, 10)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Text = stat.title
        titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        titleLabel.TextScaled = true
        titleLabel.Font = Enum.Font.SourceSans
        titleLabel.TextXAlignment = Enum.TextXAlignment.Left
        titleLabel.Parent = cardFrame
        
        local valueLabel = Instance.new("TextLabel")
        valueLabel.Size = UDim2.new(1, -20, 0.5, 0)
        valueLabel.Position = UDim2.new(0, 10, 0.5, 0)
        valueLabel.BackgroundTransparency = 1
        valueLabel.Text = stat.value
        valueLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        valueLabel.TextScaled = true
        valueLabel.Font = Enum.Font.SourceSansBold
        valueLabel.TextXAlignment = Enum.TextXAlignment.Left
        valueLabel.Parent = cardFrame
    end
    
    -- Transport-Effizienz Diagramm
    local chartFrame = Instance.new("Frame")
    chartFrame.Size = UDim2.new(1, -20, 0.35, 0)
    chartFrame.Position = UDim2.new(0, 10, 0.65, 0)
    chartFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    chartFrame.BorderSizePixel = 0
    chartFrame.Parent = self.ContentFrame
    
    local chartCorner = Instance.new("UICorner")
    chartCorner.CornerRadius = UDim.new(0, 8)
    chartCorner.Parent = chartFrame
    
    local chartTitle = Instance.new("TextLabel")
    chartTitle.Size = UDim2.new(1, 0, 0, 40)
    chartTitle.BackgroundTransparency = 1
    chartTitle.Text = "📈 TRANSPORT-EFFIZIENZ (LETZTE 12 MONATE)"
    chartTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    chartTitle.TextScaled = true
    chartTitle.Font = Enum.Font.SourceSansBold
    chartTitle.Parent = chartFrame
    
    -- Vereinfachtes Liniendiagramm
    local lineFrame = Instance.new("Frame")
    lineFrame.Size = UDim2.new(1, -20, 1, -50)
    lineFrame.Position = UDim2.new(0, 10, 0, 45)
    lineFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    lineFrame.BorderSizePixel = 0
    lineFrame.Parent = chartFrame
    
    local lineCorner = Instance.new("UICorner")
    lineCorner.CornerRadius = UDim.new(0, 5)
    lineCorner.Parent = lineFrame
    
    -- Datenpunkte
    for i = 1, 12 do
        local point = Instance.new("Frame")
        point.Size = UDim2.new(0, 8, 0, 8)
        point.Position = UDim2.new(i/12, -4, math.random(20, 80)/100, -4)
        point.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        point.BorderSizePixel = 0
        point.Parent = lineFrame
        
        local pointCorner = Instance.new("UICorner")
        pointCorner.CornerRadius = UDim.new(0.5, 0)
        pointCorner.Parent = point
    end
end

-- Wirtschafts-Statistiken erstellen
function StatisticsGUI:CreateEconomyContent()
    -- Wirtschafts-Karten
    local economyStats = {
        {title = "💰 Gesamtvermögen", value = "$2,345,678", color = Color3.fromRGB(0, 150, 0)},
        {title = "📈 Monatlicher Gewinn", value = "$45,678", color = Color3.fromRGB(0, 100, 200)},
        {title = "🏭 Aktive Industrien", value = "28", color = Color3.fromRGB(200, 100, 0)},
        {title = "📦 Waren produziert", value = "567,890t", color = Color3.fromRGB(150, 0, 150)},
        {title = "🔄 Handelsvolumen", value = "$234,567", color = Color3.fromRGB(100, 200, 100)},
        {title = "📊 Marktanteil", value = "34%", color = Color3.fromRGB(200, 150, 100)}
    }
    
    for i, stat in ipairs(economyStats) do
        local cardFrame = Instance.new("Frame")
        cardFrame.Size = UDim2.new(0.32, 0, 0.25, 0)
        cardFrame.Position = UDim2.new(((i-1) % 3) * 0.34, 0, math.floor((i-1) / 3) * 0.3, 10)
        cardFrame.BackgroundColor3 = stat.color
        cardFrame.BorderSizePixel = 0
        cardFrame.Parent = self.ContentFrame
        
        local cardCorner = Instance.new("UICorner")
        cardCorner.CornerRadius = UDim.new(0, 10)
        cardCorner.Parent = cardFrame
        
        local titleLabel = Instance.new("TextLabel")
        titleLabel.Size = UDim2.new(1, -20, 0.5, 0)
        titleLabel.Position = UDim2.new(0, 10, 0, 10)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Text = stat.title
        titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        titleLabel.TextScaled = true
        titleLabel.Font = Enum.Font.SourceSans
        titleLabel.TextXAlignment = Enum.TextXAlignment.Left
        titleLabel.Parent = cardFrame
        
        local valueLabel = Instance.new("TextLabel")
        valueLabel.Size = UDim2.new(1, -20, 0.5, 0)
        valueLabel.Position = UDim2.new(0, 10, 0.5, 0)
        valueLabel.BackgroundTransparency = 1
        valueLabel.Text = stat.value
        valueLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        valueLabel.TextScaled = true
        valueLabel.Font = Enum.Font.SourceSansBold
        valueLabel.TextXAlignment = Enum.TextXAlignment.Left
        valueLabel.Parent = cardFrame
    end
    
    -- Wirtschafts-Wachstum Diagramm
    local chartFrame = Instance.new("Frame")
    chartFrame.Size = UDim2.new(1, -20, 0.35, 0)
    chartFrame.Position = UDim2.new(0, 10, 0.65, 0)
    chartFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    chartFrame.BorderSizePixel = 0
    chartFrame.Parent = self.ContentFrame
    
    local chartCorner = Instance.new("UICorner")
    chartCorner.CornerRadius = UDim.new(0, 8)
    chartCorner.Parent = chartFrame
    
    local chartTitle = Instance.new("TextLabel")
    chartTitle.Size = UDim2.new(1, 0, 0, 40)
    chartTitle.BackgroundTransparency = 1
    chartTitle.Text = "💹 WIRTSCHAFTS-WACHSTUM (LETZTE 12 MONATE)"
    chartTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    chartTitle.TextScaled = true
    chartTitle.Font = Enum.Font.SourceSansBold
    chartTitle.Parent = chartFrame
end

-- Städte-Statistiken erstellen
function StatisticsGUI:CreateCitiesContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🏘️ STÄDTE-STATISTIKEN\n\nBevölkerungswachstum,\nZufriedenheit und Entwicklung."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Umwelt-Statistiken erstellen
function StatisticsGUI:CreateEnvironmentContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🌍 UMWELT-STATISTIKEN\n\nUmweltauswirkungen,\nNachhaltigkeit und Emissionen."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Statistik-Daten laden
function StatisticsGUI:LoadStatisticsData()
    local success, data = pcall(function()
        return GetStatisticsDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.StatisticsData = data
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Statistik-Daten")
    end
end

-- GUI öffnen
function StatisticsGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadStatisticsData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function StatisticsGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.S then
        if StatisticsGUI.IsOpen then
            StatisticsGUI:CloseGUI()
        else
            StatisticsGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function StatisticsGUI:Initialize()
    print("📊 StatisticsGUI initialisiert - Drücke 'S' zum Öffnen")
end

-- Auto-Start
StatisticsGUI:Initialize()

return StatisticsGUI
