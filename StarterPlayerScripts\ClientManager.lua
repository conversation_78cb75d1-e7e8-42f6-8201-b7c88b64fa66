-- StarterPlayerScripts/ClientManager.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Client-seitiger Manager für Transport Empire

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Warten auf Module und GUIs
wait(2)

-- Module laden
local GameConfig = require(ReplicatedStorage:WaitForChild("Modules"):WaitForChild("GameConfig"))

-- GUI-Module laden
local MainMenuGui = require(player.PlayerGui:WaitForChild("MainMenuGui"))
local GameInterface = require(player.PlayerGui:WaitForChild("GameInterface"))

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")

local ClientManager = {}
ClientManager.CurrentState = "menu" -- "menu", "loading", "playing"
ClientManager.GameData = nil

-- Events verbinden
function ClientManager:ConnectEvents()
    -- Ka<PERSON> wurde generiert
    local MapGeneratedEvent = Events:FindFirstChild("MapGeneratedEvent")
    if MapGeneratedEvent then
        MapGeneratedEvent.OnClientEvent:Connect(function(success)
            if success then
                print("✅ Karte erfolgreich generiert!")
                self:StartGame()
            else
                print("❌ Kartengenerierung fehlgeschlagen!")
                self:ShowMessage("Kartengenerierung fehlgeschlagen!")
            end
        end)
    end
    
    -- Spieler beigetreten
    local PlayerJoinedEvent = Events:FindFirstChild("PlayerJoinedEvent")
    if PlayerJoinedEvent then
        PlayerJoinedEvent.OnClientEvent:Connect(function(data)
            print("👋 " .. data.message)
            self:ShowMessage(data.message)
        end)
    end
    
    -- Wirtschaftsdaten aktualisiert
    local UpdateEconomyEvent = Events:FindFirstChild("UpdateEconomyEvent")
    if UpdateEconomyEvent then
        UpdateEconomyEvent.OnClientEvent:Connect(function(economyData)
            self:UpdateEconomyData(economyData)
        end)
    end
    
    -- Benachrichtigungen
    local ShowNotificationEvent = Events:FindFirstChild("ShowNotificationEvent")
    if ShowNotificationEvent then
        ShowNotificationEvent.OnClientEvent:Connect(function(message)
            self:ShowMessage(message)
        end)
    end
end

-- Events erstellen falls nicht vorhanden
function ClientManager:CreateMissingEvents()
    local eventNames = {
        "MapGeneratedEvent",
        "PlayerJoinedEvent", 
        "UpdateEconomyEvent",
        "ShowNotificationEvent",
        "GameSavedEvent",
        "GameLoadedEvent"
    }
    
    for _, eventName in pairs(eventNames) do
        if not Events:FindFirstChild(eventName) then
            local event = Instance.new("RemoteEvent")
            event.Name = eventName
            event.Parent = Events
            print("📡 Event erstellt:", eventName)
        end
    end
end

-- Spiel starten
function ClientManager:StartGame()
    self.CurrentState = "playing"
    
    -- Hauptmenü ausblenden
    if MainMenuGui and MainMenuGui.ScreenGui then
        MainMenuGui.ScreenGui.Enabled = false
    end
    
    -- Spiel-Interface anzeigen
    if GameInterface then
        GameInterface:Show()
    end
    
    -- Wirtschaftsdaten regelmäßig abrufen
    self:StartDataUpdates()
    
    print("🎮 Spiel gestartet!")
end

-- Regelmäßige Daten-Updates starten
function ClientManager:StartDataUpdates()
    -- Alle 5 Sekunden Wirtschaftsdaten abrufen
    spawn(function()
        while self.CurrentState == "playing" do
            wait(5)
            self:RequestEconomyData()
        end
    end)
end

-- Wirtschaftsdaten anfordern
function ClientManager:RequestEconomyData()
    local GetEconomyDataFunction = Events:FindFirstChild("GetEconomyDataFunction")
    if GetEconomyDataFunction then
        local success, economyData = pcall(function()
            return GetEconomyDataFunction:InvokeServer()
        end)
        
        if success and economyData then
            self:UpdateEconomyData(economyData)
        end
    end
end

-- Wirtschaftsdaten aktualisieren
function ClientManager:UpdateEconomyData(economyData)
    self.GameData = economyData
    
    -- Interface aktualisieren
    if GameInterface and GameInterface.IsVisible then
        GameInterface:UpdateData(economyData)
    end
end

-- Nachricht anzeigen
function ClientManager:ShowMessage(message)
    print("📢 " .. message)
    
    -- In Interface anzeigen
    if GameInterface and GameInterface.IsVisible then
        GameInterface:ShowMessage(message)
    end
end

-- Zurück zum Hauptmenü
function ClientManager:ReturnToMenu()
    self.CurrentState = "menu"
    
    -- Spiel-Interface ausblenden
    if GameInterface then
        GameInterface:Hide()
    end
    
    -- Hauptmenü anzeigen
    if MainMenuGui and MainMenuGui.ScreenGui then
        MainMenuGui.ScreenGui.Enabled = true
    end
    
    print("🏠 Zurück zum Hauptmenü")
end

-- Kamera-Steuerung
function ClientManager:SetupCamera()
    local camera = workspace.CurrentCamera
    camera.CameraType = Enum.CameraType.Scriptable
    
    -- Standard-Kamera-Position (Vogelperspektive)
    camera.CFrame = CFrame.new(0, 50, 0, 0, -1, 0, 0, 0, 1, 1, 0, 0)
    
    print("📷 Kamera eingerichtet")
end

-- Input-Handling
function ClientManager:SetupInput()
    local UserInputService = game:GetService("UserInputService")
    
    -- Tastatur-Shortcuts
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.Escape then
            -- ESC = Zurück zum Hauptmenü
            if self.CurrentState == "playing" then
                self:ReturnToMenu()
            end
        elseif input.KeyCode == Enum.KeyCode.Space then
            -- Leertaste = Pause/Unpause
            if self.CurrentState == "playing" then
                print("⏸️ Pause/Unpause")
            end
        end
    end)
    
    print("⌨️ Input-Handling eingerichtet")
end

-- Initialisierung
function ClientManager:Initialize()
    print("🖥️ ClientManager wird initialisiert...")
    
    -- Events erstellen
    self:CreateMissingEvents()
    
    -- Events verbinden
    self:ConnectEvents()
    
    -- GUIs initialisieren
    if MainMenuGui then
        MainMenuGui:Initialize()
    end
    
    if GameInterface then
        GameInterface:Initialize()
    end
    
    -- Kamera und Input einrichten
    self:SetupCamera()
    self:SetupInput()
    
    print("✅ ClientManager erfolgreich initialisiert!")
end

-- Auto-Start
ClientManager:Initialize()

return ClientManager
