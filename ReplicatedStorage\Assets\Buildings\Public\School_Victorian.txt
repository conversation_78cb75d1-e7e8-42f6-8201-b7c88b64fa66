# Viktorianische Schule
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "SCHOOL_VICTORIAN"
Name = "Viktorianische Schule"
Category = "Public"
Size = "Medium"
Era = "Victorian"
Era_Years = {1850, 1900}

[MODEL_DATA]
ModelId = "rbxassetid://SCHOOL_VICTORIAN"
Scale = Vector3(25, 12, 20)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.8, 0.2, 0.2)     # Rotes Backstein
Secondary = Color3(0.9, 0.9, 0.7)   # Cremefarbene Fenster
Accent = Color3(0.3, 0.2, 0.1)      # Dunkles Holz
Trim = Color3(0.1, 0.1, 0.1)        # Schwarze Akzente

[GAMEPLAY_STATS]
Workers = 8
BuildCost = 8000
MaintenanceCost = 80
BuildTime = 60
PowerConsumption = 10
LandSize = Vector2(5, 4)

[PUBLIC_DATA]
ServiceType = "Education"
Capacity = 120  # Schüler
ServiceRadius = 300
EducationLevel = "Primary"
BoostsPopulationGrowth = true

[REQUIREMENTS]
MinPopulation = 800
MinYear = 1850
RequiredTech = {"Public_Education"}
RequiredResources = {"Brick", "Wood"}
UnlockCost = 2000

[FEATURES]
ArchitecturalStyle = "Victorian_Institutional"
HasBellTower = true
HasPlayground = true
HasLibrary = true
Floors = 2

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "GYMNASIUM_VICTORIAN"
UpgradeCost = 12000
UpgradeTime = 90

[ECONOMIC_DATA]
TaxRevenue = 0  # Öffentliches Gebäude
PropertyValue = 8000
BoostsEducation = true
AttractsYoungFamilies = true

[DESCRIPTION]
ShortDesc = "Bildungseinrichtung für die lokale Bevölkerung"
LongDesc = "Eine solide Backsteinschule mit Glockenturm. Bietet Grundbildung für Kinder und fördert das Bevölkerungswachstum in der Umgebung."
