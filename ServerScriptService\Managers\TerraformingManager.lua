-- ServerScriptService/Managers/TerraformingManager.lua
-- R<PERSON><PERSON><PERSON> SCRIPT TYPE: ModuleScript
-- Vollständiges Terraforming-System wie in Transport Fever 2

local TerraformingManager = {}
TerraformingManager.__index = TerraformingManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local RunService = game:GetService("RunService")

-- Terraforming-Konfiguration
local TERRAFORMING_CONFIG = {
    -- Terrain-Einstellungen
    TERRAIN_RESOLUTION = 4, -- Voxel pro Stud
    MIN_HEIGHT = -50,
    MAX_HEIGHT = 200,
    BRUSH_SIZES = {5, 10, 20, 50, 100}, -- Pinselgrößen
    STRENGTH_LEVELS = {0.1, 0.25, 0.5, 1.0, 2.0}, -- Stärke-Level
    
    -- <PERSON>sten pro Operation
    COSTS = {
        RAISE_TERRAIN = 10, -- Pro Kubikmeter
        LOWER_TERRAIN = 5,
        ADD_WATER = 15,
        REMOVE_WATER = 20,
        FLATTEN = 8,
        SMOOTH = 3
    },
    
    -- <PERSON><PERSON>_MATERIALS = {
        Enum.Material.Grass,
        Enum.Material.Rock,
        Enum.Material.Sand,
        Enum.Material.Snow,
        Enum.Material.Mud,
        Enum.Material.Ground,
        Enum.Material.Asphalt
    },
    
    WATER_MATERIALS = {
        Enum.Material.Water
    }
}

-- Konstruktor
function TerraformingManager.new()
    local self = setmetatable({}, TerraformingManager)
    
    self.terrain = Workspace.Terrain
    self.activeOperations = {}
    self.undoHistory = {}
    self.redoHistory = {}
    self.maxHistorySize = 50
    
    -- Terraforming-Tools
    self.tools = {
        RAISE = "raise",
        LOWER = "lower",
        FLATTEN = "flatten",
        SMOOTH = "smooth",
        ADD_WATER = "add_water",
        REMOVE_WATER = "remove_water",
        PAINT = "paint"
    }
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function TerraformingManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Terraforming-Events erstellen falls nicht vorhanden
    if not Events:FindFirstChild("TerraformEvent") then
        local terraformEvent = Instance.new("RemoteEvent")
        terraformEvent.Name = "TerraformEvent"
        terraformEvent.Parent = Events
    end
    
    if not Events:FindFirstChild("GetTerraformDataFunction") then
        local getDataFunction = Instance.new("RemoteFunction")
        getDataFunction.Name = "GetTerraformDataFunction"
        getDataFunction.Parent = Events
    end
    
    -- Event-Handler
    Events.TerraformEvent.OnServerEvent:Connect(function(player, action, data)
        self:HandleTerraformRequest(player, action, data)
    end)
    
    Events.GetTerraformDataFunction.OnServerInvoke = function(player)
        return self:GetTerraformData(player)
    end
end

-- Terraforming-Anfrage verarbeiten
function TerraformingManager:HandleTerraformRequest(player, action, data)
    if not self:CanPlayerTerraform(player, action, data) then
        return false
    end
    
    local success = false
    local cost = 0
    
    if action == "RAISE_TERRAIN" then
        success, cost = self:RaiseTerrain(data.position, data.brushSize, data.strength, data.material)
    elseif action == "LOWER_TERRAIN" then
        success, cost = self:LowerTerrain(data.position, data.brushSize, data.strength)
    elseif action == "FLATTEN_TERRAIN" then
        success, cost = self:FlattenTerrain(data.position, data.brushSize, data.targetHeight)
    elseif action == "SMOOTH_TERRAIN" then
        success, cost = self:SmoothTerrain(data.position, data.brushSize, data.strength)
    elseif action == "ADD_WATER" then
        success, cost = self:AddWater(data.position, data.brushSize, data.depth)
    elseif action == "REMOVE_WATER" then
        success, cost = self:RemoveWater(data.position, data.brushSize)
    elseif action == "PAINT_TERRAIN" then
        success, cost = self:PaintTerrain(data.position, data.brushSize, data.material)
    elseif action == "UNDO" then
        success = self:UndoLastOperation(player)
    elseif action == "REDO" then
        success = self:RedoLastOperation(player)
    end
    
    if success and cost > 0 then
        -- Kosten vom Spieler abziehen (Integration mit EconomyManager)
        local economyManager = require(script.Parent.EconomyManager)
        if economyManager and economyManager.DeductPlayerMoney then
            economyManager:DeductPlayerMoney(player.UserId, cost)
        end
    end
    
    return success
end

-- Terrain erhöhen
function TerraformingManager:RaiseTerrain(position, brushSize, strength, material)
    local region = self:CreateRegion(position, brushSize)
    local material = material or Enum.Material.Grass
    
    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)
    
    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)
    
    -- Neue Höhen berechnen
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 then
                    local falloff = self:CalculateFalloff(distance)
                    local currentOccupancy = readOccupancies[x][y][z]
                    local newOccupancy = math.min(1.0, currentOccupancy + (strength * falloff * 0.1))
                    
                    readOccupancies[x][y][z] = newOccupancy
                    if newOccupancy > 0.5 then
                        readMaterials[x][y][z] = material
                    end
                end
            end
        end
    end
    
    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)
    
    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end
    
    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.RAISE_TERRAIN * strength
    
    return true, cost
end

-- Terrain senken
function TerraformingManager:LowerTerrain(position, brushSize, strength)
    local region = self:CreateRegion(position, brushSize)
    
    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)
    
    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)
    
    -- Neue Höhen berechnen
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 then
                    local falloff = self:CalculateFalloff(distance)
                    local currentOccupancy = readOccupancies[x][y][z]
                    local newOccupancy = math.max(0.0, currentOccupancy - (strength * falloff * 0.1))
                    
                    readOccupancies[x][y][z] = newOccupancy
                end
            end
        end
    end
    
    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)
    
    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end
    
    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.LOWER_TERRAIN * strength
    
    return true, cost
end

-- Terrain glätten
function TerraformingManager:FlattenTerrain(position, brushSize, targetHeight)
    local region = self:CreateRegion(position, brushSize)
    
    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)
    
    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)
    
    -- Ziel-Höhe setzen
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 then
                    local falloff = self:CalculateFalloff(distance)
                    local worldY = region.CFrame.Position.Y + (y - size.Y/2) * (region.Size.Y / size.Y)
                    
                    if worldY <= targetHeight then
                        readOccupancies[x][y][z] = 1.0 * falloff
                        readMaterials[x][y][z] = Enum.Material.Grass
                    else
                        readOccupancies[x][y][z] = 0.0
                    end
                end
            end
        end
    end
    
    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)
    
    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end
    
    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.FLATTEN
    
    return true, cost
end

-- Terrain weichzeichnen
function TerraformingManager:SmoothTerrain(position, brushSize, strength)
    local region = self:CreateRegion(position, brushSize)
    
    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)
    
    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)
    
    -- Glättungs-Algorithmus
    local size = readMaterials.Size
    local smoothedOccupancies = {}
    
    -- Initialisiere smoothedOccupancies
    for x = 1, size.X do
        smoothedOccupancies[x] = {}
        for y = 1, size.Y do
            smoothedOccupancies[x][y] = {}
            for z = 1, size.Z do
                smoothedOccupancies[x][y][z] = readOccupancies[x][y][z]
            end
        end
    end
    
    -- Glättung anwenden
    for x = 2, size.X - 1 do
        for y = 2, size.Y - 1 do
            for z = 2, size.Z - 1 do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 then
                    local falloff = self:CalculateFalloff(distance)
                    local sum = 0
                    local count = 0
                    
                    -- Nachbarn mitteln
                    for dx = -1, 1 do
                        for dy = -1, 1 do
                            for dz = -1, 1 do
                                sum = sum + readOccupancies[x + dx][y + dy][z + dz]
                                count = count + 1
                            end
                        end
                    end
                    
                    local average = sum / count
                    local currentValue = readOccupancies[x][y][z]
                    smoothedOccupancies[x][y][z] = currentValue + (average - currentValue) * strength * falloff
                end
            end
        end
    end
    
    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, smoothedOccupancies)
    
    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end
    
    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.SMOOTH * strength
    
    return true, cost
end

-- Wasser hinzufügen
function TerraformingManager:AddWater(position, brushSize, depth)
    local region = self:CreateRegion(position, brushSize)

    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)

    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)

    -- Wasser hinzufügen
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 then
                    local falloff = self:CalculateFalloff(distance)
                    local worldY = region.CFrame.Position.Y + (y - size.Y/2) * (region.Size.Y / size.Y)

                    if worldY <= position.Y + depth * falloff then
                        readOccupancies[x][y][z] = 1.0
                        readMaterials[x][y][z] = Enum.Material.Water
                    end
                end
            end
        end
    end

    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)

    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end

    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.ADD_WATER

    return true, cost
end

-- Wasser entfernen
function TerraformingManager:RemoveWater(position, brushSize)
    local region = self:CreateRegion(position, brushSize)

    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)

    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)

    -- Wasser entfernen
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 and readMaterials[x][y][z] == Enum.Material.Water then
                    readOccupancies[x][y][z] = 0.0
                    readMaterials[x][y][z] = Enum.Material.Air
                end
            end
        end
    end

    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)

    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end

    -- Kosten berechnen
    local volume = (brushSize * 2) ^ 3
    local cost = volume * TERRAFORMING_CONFIG.COSTS.REMOVE_WATER

    return true, cost
end

-- Terrain bemalen
function TerraformingManager:PaintTerrain(position, brushSize, material)
    local region = self:CreateRegion(position, brushSize)

    -- Backup für Undo erstellen
    local backup = self:CreateBackup(region)

    -- Terrain-Daten lesen
    local readMaterials, readOccupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)

    -- Material ändern
    local size = readMaterials.Size
    for x = 1, size.X do
        for y = 1, size.Y do
            for z = 1, size.Z do
                local distance = self:CalculateDistance(x, y, z, size, brushSize)
                if distance <= 1.0 and readOccupancies[x][y][z] > 0.1 then
                    local falloff = self:CalculateFalloff(distance)
                    if math.random() < falloff then
                        readMaterials[x][y][z] = material
                    end
                end
            end
        end
    end

    -- Terrain schreiben
    self.terrain:WriteVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, readMaterials, readOccupancies)

    -- Backup speichern
    table.insert(self.undoHistory, backup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end

    return true, 0 -- Bemalen kostet nichts
end

-- Hilfsfunktionen
function TerraformingManager:CreateRegion(position, brushSize)
    local size = Vector3.new(brushSize * 2, brushSize * 2, brushSize * 2)
    return Region3.new(position - size/2, position + size/2)
end

function TerraformingManager:CalculateDistance(x, y, z, size, brushSize)
    local centerX = size.X / 2
    local centerY = size.Y / 2
    local centerZ = size.Z / 2

    local dx = (x - centerX) / centerX
    local dy = (y - centerY) / centerY
    local dz = (z - centerZ) / centerZ

    return math.sqrt(dx*dx + dy*dy + dz*dz)
end

function TerraformingManager:CalculateFalloff(distance)
    -- Smooth falloff curve
    if distance >= 1.0 then return 0.0 end
    return (1.0 - distance) ^ 2
end

function TerraformingManager:CreateBackup(region)
    local materials, occupancies = self.terrain:ReadVoxels(region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION)
    return {
        region = region,
        materials = materials,
        occupancies = occupancies,
        timestamp = tick()
    }
end

function TerraformingManager:UndoLastOperation(player)
    if #self.undoHistory == 0 then return false end

    local backup = table.remove(self.undoHistory)

    -- Aktuellen Zustand für Redo speichern
    local currentBackup = self:CreateBackup(backup.region)
    table.insert(self.redoHistory, currentBackup)
    if #self.redoHistory > self.maxHistorySize then
        table.remove(self.redoHistory, 1)
    end

    -- Backup wiederherstellen
    self.terrain:WriteVoxels(backup.region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, backup.materials, backup.occupancies)

    return true
end

function TerraformingManager:RedoLastOperation(player)
    if #self.redoHistory == 0 then return false end

    local backup = table.remove(self.redoHistory)

    -- Aktuellen Zustand für Undo speichern
    local currentBackup = self:CreateBackup(backup.region)
    table.insert(self.undoHistory, currentBackup)
    if #self.undoHistory > self.maxHistorySize then
        table.remove(self.undoHistory, 1)
    end

    -- Backup wiederherstellen
    self.terrain:WriteVoxels(backup.region, TERRAFORMING_CONFIG.TERRAIN_RESOLUTION, backup.materials, backup.occupancies)

    return true
end

function TerraformingManager:CanPlayerTerraform(player, action, data)
    -- Hier können Berechtigungen, Geld-Checks etc. implementiert werden
    return true
end

function TerraformingManager:GetTerraformData(player)
    return {
        brushSizes = TERRAFORMING_CONFIG.BRUSH_SIZES,
        strengthLevels = TERRAFORMING_CONFIG.STRENGTH_LEVELS,
        materials = TERRAFORMING_CONFIG.TERRAIN_MATERIALS,
        costs = TERRAFORMING_CONFIG.COSTS,
        canUndo = #self.undoHistory > 0,
        canRedo = #self.redoHistory > 0
    }
end

return TerraformingManager
