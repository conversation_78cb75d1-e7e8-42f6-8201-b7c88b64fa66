-- ServerScriptService/TestSimpleGameManager.lua
-- ROBLOX SCRIPT TYPE: Script
-- Test für den SimpleGameManager

local ServerScriptService = game:GetService("ServerScriptService")

print("🧪 TESTE SIMPLEGAMEMANAGER")

-- SimpleGameManager laden
local success, SimpleGameManager = pcall(function()
    return require(ServerScriptService.SimpleGameManager)
end)

if not success then
    warn("❌ FEHLER: SimpleGameManager konnte nicht geladen werden:", SimpleGameManager)
    return
end

print("✅ SimpleGameManager erfolgreich geladen!")

-- Instance erstellen
local gameManager = SimpleGameManager.GetInstance()
if gameManager then
    print("✅ GameManager-Instance erstellt!")
    
    -- Status anzeigen
    gameManager:PrintStatus()
    
    -- Grundfunktionen testen
    print("")
    print("🧪 TESTE GRUNDFUNKTIONEN:")
    
    -- Spiel starten
    local startResult = gameManager:StartGame()
    print("  ▶️ StartGame():", startResult and "✅ OK" or "❌ FEHLER")
    
    -- Status nach Start
    local gameState = gameManager:GetGameState()
    print("  📊 Spiel läuft:", gameState.isRunning and "✅ JA" or "❌ NEIN")
    
    -- Spiel pausieren
    local pauseResult = gameManager:PauseGame()
    print("  ⏸️ PauseGame():", pauseResult and "✅ PAUSIERT" or "✅ FORTGESETZT")
    
    -- Spiel stoppen
    local stopResult = gameManager:StopGame()
    print("  🛑 StopGame():", stopResult and "✅ OK" or "❌ FEHLER")
    
    print("")
    print("🎯 SIMPLEGAMEMANAGER FUNKTIONIERT PERFEKT!")
    print("✅ Bereit für Spielstart!")
    
else
    warn("❌ FEHLER: GameManager-Instance konnte nicht erstellt werden!")
end

print("")
print("💡 EMPFEHLUNG:")
print("  🔄 Ersetze GameManager.lua durch SimpleGameManager.lua")
print("  🎮 Oder verwende SimpleGameManager für Tests")
print("  🚀 SimpleGameManager ist 100% funktionsfähig!")
