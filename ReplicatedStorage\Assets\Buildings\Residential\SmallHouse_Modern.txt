# Kleines Modernes Haus
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "SMALL_HOUSE_MODERN"
Name = "Kleines Modernes Haus"
Category = "Residential"
Size = "Small"
Era = "Modern"
Era_Years = {1945, 1970}

[MODEL_DATA]
ModelId = "rbxassetid://SMALL_HOUSE_MODERN"
Scale = Vector3(10, 4, 12)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.9, 0.9, 0.8)      # Helle Außenwände
Secondary = Color3(0.4, 0.3, 0.2)    # Dunkles Flachdach
Accent = Color3(0.2, 0.6, 0.8)       # Blaue Akzente
Trim = Color3(0.8, 0.6, 0.4)         # Holzverkleidung

[GAMEPLAY_STATS]
Population = 5
BuildCost = 6500
MaintenanceCost = 65
BuildTime = 25
PowerConsumption = 8
WaterConsumption = 6
LandSize = Vector2(3, 2)  # 3x2 Felder

[REQUIREMENTS]
MinPopulation = 2000
MinYear = 1945
RequiredTech = {"Modern_Construction", "Electricity"}
RequiredResources = {"Concrete", "Steel", "Glass"}
UnlockCost = 3000

[FEATURES]
ArchitecturalStyle = "Mid_Century_Modern"
HasGarden = true
HasChimney = false
HasBasement = false
Floors = 1
WindowStyle = "Large_Picture_Windows"
RoofStyle = "Flat_Modern"
HasCarport = true
HasModernKitchen = true

[UTILITIES]
RequiresElectricity = true
RequiresWater = true
RequiresSewer = true
RequiresTelephone = true

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "MEDIUM_HOUSE_MODERN"
UpgradeCost = 4000
UpgradeTime = 30
UpgradeRequirements = {"Suburban_Development"}

[ECONOMIC_DATA]
TaxRevenue = 45
PropertyValue = 6500
MaintenanceJobs = 1
ConstructionJobs = 6

[DESCRIPTION]
ShortDesc = "Einstöckiges Haus im modernen Stil der 1950er"
LongDesc = "Ein typisches Nachkriegshaus mit offenem Grundriss, großen Fenstern und integrierter Garage. Repräsentiert den amerikanischen Traum der 1950er Jahre mit modernen Annehmlichkeiten wie Elektrizität und Telefon."
