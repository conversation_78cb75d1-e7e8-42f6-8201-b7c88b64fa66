# Kohlemine - Grundstufe
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "COAL_MINE_BASIC"
Name = "Kohlemine"
Category = "Industry"
IndustryType = "Mining"
TechLevel = 1
Era_Years = {1850, 2000}

[MODEL_DATA]
ModelId = "rbxassetid://COAL_MINE_BASIC"
Scale = Vector3(25, 12, 35)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.3, 0.3, 0.3)      # Dunkler Stein
Secondary = Color3(0.6, 0.4, 0.2)    # Holzstrukturen
Accent = Color3(0.8, 0.6, 0.4)       # Metallteile
Trim = Color3(0.2, 0.2, 0.2)         # Ko<PERSON><PERSON>lecken

[GAMEPLAY_STATS]
Workers = 25
BuildCost = 8000
MaintenanceCost = 80
BuildTime = 60
PowerConsumption = 0
WaterConsumption = 5
LandSize = Vector2(5, 7)  # 5x7 Felder

[PRODUCTION_DATA]
OutputResource = "Coal"
ProductionRate = 50  # Tonnen/Monat
InputResources = {}
StorageCapacity = 200
ProductionCycle = 24  # Stunden

[REQUIREMENTS]
MinPopulation = 500
MinYear = 1850
RequiredTech = {"Basic_Mining"}
RequiredResources = {"Coal_Deposit"}
UnlockCost = 1000

[TECH_UPGRADES]
Level_1 = {
    Name = "COAL_MINE_BASIC",
    ProductionRate = 50,
    Workers = 25,
    Texture = "Basic_Wooden_Structures"
}
Level_2 = {
    Name = "COAL_MINE_IMPROVED",
    ProductionRate = 75,
    Workers = 30,
    Texture = "Steel_Reinforced_Structures",
    RequiredTech = {"Improved_Mining"},
    UpgradeCost = 4000,
    UnlockYear = 1880
}
Level_3 = {
    Name = "COAL_MINE_MECHANIZED",
    ProductionRate = 120,
    Workers = 35,
    Texture = "Mechanized_Equipment",
    RequiredTech = {"Mechanized_Mining"},
    UpgradeCost = 8000,
    UnlockYear = 1920
}
Level_4 = {
    Name = "COAL_MINE_MODERN",
    ProductionRate = 200,
    Workers = 20,
    Texture = "Modern_Automated_Systems",
    RequiredTech = {"Automated_Mining"},
    UpgradeCost = 15000,
    UnlockYear = 1960
}

[FEATURES]
HasMineShaft = true
HasConveyorBelt = false  # Wird bei Level 2 hinzugefügt
HasStorageSilos = true
HasHeadframe = true
HasSafetyEquipment = false  # Wird bei Level 3 hinzugefügt

[ENVIRONMENTAL_DATA]
PollutionLevel = 6
NoiseLevel = 5
WaterPollution = 2
AirPollution = 4

[ECONOMIC_DATA]
Employment = 25
TaxRevenue = 60
ResourceValue = 3  # Pro Tonne
ExportPotential = true

[DESCRIPTION]
ShortDesc = "Traditionelle Kohlemine mit Holzstrukturen"
LongDesc = "Eine grundlegende Kohlemine mit einfachen Holzstrukturen und manueller Förderung. Kann durch Technologie-Fortschritte modernisiert und effizienter gemacht werden."
