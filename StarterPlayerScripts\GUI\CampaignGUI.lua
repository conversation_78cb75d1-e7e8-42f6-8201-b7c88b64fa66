-- StarterPlayerScripts/GUI/CampaignGUI.lua
-- RO<PERSON>OX SCRIPT TYPE: LocalScript
-- Kampagnen-Auswahl und Fortschritts-Anzeige

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local StartCampaignEvent = Events:WaitForChild("StartCampaignEvent")
local GetCampaignDataFunction = Events:WaitForChild("GetCampaignDataFunction")

local CampaignGUI = {}
CampaignGUI.IsOpen = false
CampaignGUI.CampaignData = {}

-- Schwierigkeits-Farben
local DIFFICULTY_COLORS = {
    EASY = Color3.fromRGB(100, 255, 100),
    NORMAL = Color3.fromRGB(255, 200, 100),
    HARD = Color3.fromRGB(255, 100, 100),
    EXPERT = Color3.fromRGB(200, 100, 255)
}

-- GUI erstellen
function CampaignGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CampaignGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1200, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -600, 0.5, -400)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 60)
    title.BackgroundTransparency = 1
    title.Text = "🎯 KAMPAGNEN & SZENARIEN"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Tab-System
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 70)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tabs
    local campaignTab = self:CreateTab(tabFrame, "🎯 Kampagnen", 0, true)
    local achievementTab = self:CreateTab(tabFrame, "🏆 Achievements", 0.33, false)
    local progressTab = self:CreateTab(tabFrame, "📊 Fortschritt", 0.66, false)
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 650)
    contentFrame.Position = UDim2.new(0, 10, 0, 130)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.CurrentTab = "campaigns"
    
    -- Tab-Events
    campaignTab.MouseButton1Click:Connect(function()
        self:SwitchTab("campaigns")
    end)
    
    achievementTab.MouseButton1Click:Connect(function()
        self:SwitchTab("achievements")
    end)
    
    progressTab.MouseButton1Click:Connect(function()
        self:SwitchTab("progress")
    end)
    
    return screenGui
end

-- Tab erstellen
function CampaignGUI:CreateTab(parent, text, xPos, active)
    local tab = Instance.new("TextButton")
    tab.Size = UDim2.new(0.33, -5, 1, -10)
    tab.Position = UDim2.new(xPos, 5, 0, 5)
    tab.BackgroundColor3 = active and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
    tab.Text = text
    tab.TextColor3 = Color3.fromRGB(255, 255, 255)
    tab.TextScaled = true
    tab.Font = Enum.Font.SourceSansBold
    tab.BorderSizePixel = 0
    tab.Parent = parent
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 5)
    tabCorner.Parent = tab
    
    return tab
end

-- Tab wechseln
function CampaignGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    
    if tabName == "campaigns" then
        self:ShowCampaigns()
    elseif tabName == "achievements" then
        self:ShowAchievements()
    elseif tabName == "progress" then
        self:ShowProgress()
    end
end

-- Kampagnen anzeigen
function CampaignGUI:ShowCampaigns()
    -- Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    local yPos = 10
    
    if self.CampaignData.campaigns then
        for campaignId, campaign in pairs(self.CampaignData.campaigns) do
            local campaignFrame = Instance.new("Frame")
            campaignFrame.Size = UDim2.new(1, -20, 0, 200)
            campaignFrame.Position = UDim2.new(0, 10, 0, yPos)
            campaignFrame.BackgroundColor3 = campaign.unlocked and Color3.fromRGB(40, 45, 50) or Color3.fromRGB(30, 30, 30)
            campaignFrame.BorderSizePixel = 0
            campaignFrame.Parent = scrollFrame
            
            local campaignCorner = Instance.new("UICorner")
            campaignCorner.CornerRadius = UDim.new(0, 10)
            campaignCorner.Parent = campaignFrame
            
            -- Kampagnen-Info
            local nameLabel = Instance.new("TextLabel")
            nameLabel.Size = UDim2.new(0.6, 0, 0, 40)
            nameLabel.Position = UDim2.new(0, 15, 0, 10)
            nameLabel.BackgroundTransparency = 1
            nameLabel.Text = campaign.name
            nameLabel.TextColor3 = campaign.unlocked and Color3.fromRGB(255, 255, 255) or Color3.fromRGB(150, 150, 150)
            nameLabel.TextScaled = true
            nameLabel.Font = Enum.Font.SourceSansBold
            nameLabel.TextXAlignment = Enum.TextXAlignment.Left
            nameLabel.Parent = campaignFrame
            
            local descLabel = Instance.new("TextLabel")
            descLabel.Size = UDim2.new(0.6, 0, 0, 60)
            descLabel.Position = UDim2.new(0, 15, 0, 50)
            descLabel.BackgroundTransparency = 1
            descLabel.Text = campaign.description
            descLabel.TextColor3 = campaign.unlocked and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(120, 120, 120)
            descLabel.TextScaled = true
            descLabel.Font = Enum.Font.SourceSans
            descLabel.TextXAlignment = Enum.TextXAlignment.Left
            descLabel.TextWrapped = true
            descLabel.Parent = campaignFrame
            
            -- Schwierigkeit
            local difficultyFrame = Instance.new("Frame")
            difficultyFrame.Size = UDim2.new(0, 120, 0, 30)
            difficultyFrame.Position = UDim2.new(0, 15, 0, 120)
            difficultyFrame.BackgroundColor3 = DIFFICULTY_COLORS[campaign.difficulty] or Color3.fromRGB(100, 100, 100)
            difficultyFrame.BorderSizePixel = 0
            difficultyFrame.Parent = campaignFrame
            
            local difficultyCorner = Instance.new("UICorner")
            difficultyCorner.CornerRadius = UDim.new(0, 5)
            difficultyCorner.Parent = difficultyFrame
            
            local difficultyLabel = Instance.new("TextLabel")
            difficultyLabel.Size = UDim2.new(1, 0, 1, 0)
            difficultyLabel.BackgroundTransparency = 1
            difficultyLabel.Text = campaign.difficulty
            difficultyLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
            difficultyLabel.TextScaled = true
            difficultyLabel.Font = Enum.Font.SourceSansBold
            difficultyLabel.Parent = difficultyFrame
            
            -- Geschätzte Zeit
            local timeLabel = Instance.new("TextLabel")
            timeLabel.Size = UDim2.new(0, 200, 0, 25)
            timeLabel.Position = UDim2.new(0, 15, 0, 160)
            timeLabel.BackgroundTransparency = 1
            timeLabel.Text = "⏱️ " .. (campaign.estimatedTime or "Unbekannt")
            timeLabel.TextColor3 = campaign.unlocked and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(120, 120, 120)
            timeLabel.TextScaled = true
            timeLabel.Font = Enum.Font.SourceSans
            timeLabel.TextXAlignment = Enum.TextXAlignment.Left
            timeLabel.Parent = campaignFrame
            
            -- Start-Button
            if campaign.unlocked then
                local startButton = Instance.new("TextButton")
                startButton.Size = UDim2.new(0, 150, 0, 50)
                startButton.Position = UDim2.new(1, -170, 0, 75)
                startButton.BackgroundColor3 = Color3.fromRGB(100, 200, 100)
                startButton.Text = "🚀 Starten"
                startButton.TextColor3 = Color3.fromRGB(255, 255, 255)
                startButton.TextScaled = true
                startButton.Font = Enum.Font.SourceSansBold
                startButton.BorderSizePixel = 0
                startButton.Parent = campaignFrame
                
                local startCorner = Instance.new("UICorner")
                startCorner.CornerRadius = UDim.new(0, 8)
                startCorner.Parent = startButton
                
                startButton.MouseButton1Click:Connect(function()
                    self:StartCampaign(campaignId)
                end)
            else
                local lockedLabel = Instance.new("TextLabel")
                lockedLabel.Size = UDim2.new(0, 150, 0, 50)
                lockedLabel.Position = UDim2.new(1, -170, 0, 75)
                lockedLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
                lockedLabel.Text = "🔒 Gesperrt"
                lockedLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
                lockedLabel.TextScaled = true
                lockedLabel.Font = Enum.Font.SourceSansBold
                lockedLabel.BorderSizePixel = 0
                lockedLabel.Parent = campaignFrame
                
                local lockedCorner = Instance.new("UICorner")
                lockedCorner.CornerRadius = UDim.new(0, 8)
                lockedCorner.Parent = lockedLabel
            end
            
            -- Fortschritt anzeigen (falls aktiv)
            if self.CampaignData.currentCampaign and self.CampaignData.currentCampaign.campaignId == campaignId then
                local progressFrame = Instance.new("Frame")
                progressFrame.Size = UDim2.new(0.35, 0, 0, 80)
                progressFrame.Position = UDim2.new(0.63, 0, 0, 10)
                progressFrame.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
                progressFrame.BorderSizePixel = 0
                progressFrame.Parent = campaignFrame
                
                local progressCorner = Instance.new("UICorner")
                progressCorner.CornerRadius = UDim.new(0, 8)
                progressCorner.Parent = progressFrame
                
                local progressTitle = Instance.new("TextLabel")
                progressTitle.Size = UDim2.new(1, 0, 0, 25)
                progressTitle.Position = UDim2.new(0, 5, 0, 5)
                progressTitle.BackgroundTransparency = 1
                progressTitle.Text = "📊 Aktueller Fortschritt"
                progressTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
                progressTitle.TextScaled = true
                progressTitle.Font = Enum.Font.SourceSansBold
                progressTitle.TextXAlignment = Enum.TextXAlignment.Left
                progressTitle.Parent = progressFrame
                
                local completedObjectives = #self.CampaignData.currentCampaign.completedObjectives
                local totalObjectives = 0
                
                -- Zähle Pflicht-Ziele
                for _, objective in ipairs(campaign.objectives or {}) do
                    if objective.mandatory then
                        totalObjectives = totalObjectives + 1
                    end
                end
                
                local progressText = string.format("%d/%d Ziele", completedObjectives, totalObjectives)
                local progressLabel = Instance.new("TextLabel")
                progressLabel.Size = UDim2.new(1, 0, 0, 20)
                progressLabel.Position = UDim2.new(0, 5, 0, 30)
                progressLabel.BackgroundTransparency = 1
                progressLabel.Text = progressText
                progressLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
                progressLabel.TextScaled = true
                progressLabel.Font = Enum.Font.SourceSans
                progressLabel.TextXAlignment = Enum.TextXAlignment.Left
                progressLabel.Parent = progressFrame
                
                -- Fortschrittsbalken
                local progressBg = Instance.new("Frame")
                progressBg.Size = UDim2.new(1, -10, 0, 15)
                progressBg.Position = UDim2.new(0, 5, 0, 55)
                progressBg.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
                progressBg.BorderSizePixel = 0
                progressBg.Parent = progressFrame
                
                local progressBgCorner = Instance.new("UICorner")
                progressBgCorner.CornerRadius = UDim.new(0, 3)
                progressBgCorner.Parent = progressBg
                
                local progressBar = Instance.new("Frame")
                local progressPercent = totalObjectives > 0 and (completedObjectives / totalObjectives) or 0
                progressBar.Size = UDim2.new(progressPercent, 0, 1, 0)
                progressBar.BackgroundColor3 = Color3.fromRGB(100, 200, 100)
                progressBar.BorderSizePixel = 0
                progressBar.Parent = progressBg
                
                local progressBarCorner = Instance.new("UICorner")
                progressBarCorner.CornerRadius = UDim.new(0, 3)
                progressBarCorner.Parent = progressBar
            end
            
            yPos = yPos + 220
        end
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Achievements anzeigen
function CampaignGUI:ShowAchievements()
    -- Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    local achievementLabel = Instance.new("TextLabel")
    achievementLabel.Size = UDim2.new(1, 0, 1, 0)
    achievementLabel.BackgroundTransparency = 1
    achievementLabel.Text = "🏆 Achievements werden hier angezeigt..."
    achievementLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    achievementLabel.TextScaled = true
    achievementLabel.Font = Enum.Font.SourceSans
    achievementLabel.Parent = self.ContentFrame
end

-- Fortschritt anzeigen
function CampaignGUI:ShowProgress()
    -- Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    local progressLabel = Instance.new("TextLabel")
    progressLabel.Size = UDim2.new(1, 0, 1, 0)
    progressLabel.BackgroundTransparency = 1
    progressLabel.Text = "📊 Fortschritt wird hier angezeigt..."
    progressLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    progressLabel.TextScaled = true
    progressLabel.Font = Enum.Font.SourceSans
    progressLabel.Parent = self.ContentFrame
end

-- Kampagne starten
function CampaignGUI:StartCampaign(campaignId)
    StartCampaignEvent:FireServer(campaignId)
    print("🎯 Kampagne-Start angefordert:", campaignId)
    self:CloseGUI()
end

-- GUI öffnen
function CampaignGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadCampaignData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
    
    self:ShowCampaigns()
end

-- GUI schließen
function CampaignGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Kampagnen-Daten laden
function CampaignGUI:LoadCampaignData()
    local success, data = pcall(function()
        return GetCampaignDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.CampaignData = data
    else
        warn("Fehler beim Laden der Kampagnen-Daten")
        self.CampaignData = {}
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.F1 then
        if CampaignGUI.IsOpen then
            CampaignGUI:CloseGUI()
        else
            CampaignGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function CampaignGUI:Initialize()
    print("🎯 CampaignGUI initialisiert - Drücke 'F1' zum Öffnen")
end

-- Auto-Start
CampaignGUI:Initialize()

return CampaignGUI
