-- StarterGui/MultiplayerGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Multiplayer-Interface für Chat, Allianzen und Handel

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local Events = ReplicatedStorage:WaitForChild("Events")

local MultiplayerGUI = {}

-- GUI State
local isMultiplayerGUIOpen = false
local currentTab = "players"
local connectedPlayers = {}
local chatHistory = {}
local tradeOffers = {}
local playerAlliance = nil

-- Create main multiplayer interface
function MultiplayerGUI.CreateMultiplayerInterface()
    -- Main frame
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MultiplayerGUI"
    screenGui.Parent = playerGui
    screenGui.ResetOnSpawn = false
    
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 800, 0, 600)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Add corner rounding
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleBar
    
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(1, -100, 1, 0)
    titleLabel.Position = UDim2.new(0, 20, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🌐 Multiplayer"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.TextXAlignment = Enum.TextXAlignment.Left
    titleLabel.Parent = titleBar
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 60, 60)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Tab system
    local tabFrame = Instance.new("Frame")
    tabFrame.Name = "TabFrame"
    tabFrame.Size = UDim2.new(1, 0, 0, 50)
    tabFrame.Position = UDim2.new(0, 0, 0, 50)
    tabFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabs = {"players", "chat", "alliance", "trade"}
    local tabLabels = {"👥 Spieler", "💬 Chat", "🤝 Allianz", "💰 Handel"}
    local tabButtons = {}
    local tabContents = {}
    
    -- Create tab buttons
    for i, tab in pairs(tabs) do
        local tabButton = Instance.new("TextButton")
        tabButton.Name = tab .. "Tab"
        tabButton.Size = UDim2.new(0.25, -2, 1, -4)
        tabButton.Position = UDim2.new((i-1) * 0.25, 2, 0, 2)
        tabButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
        tabButton.BorderSizePixel = 0
        tabButton.Text = tabLabels[i]
        tabButton.TextColor3 = Color3.fromRGB(200, 200, 200)
        tabButton.TextScaled = true
        tabButton.Font = Enum.Font.SourceSans
        tabButton.Parent = tabFrame
        
        local tabCorner = Instance.new("UICorner")
        tabCorner.CornerRadius = UDim.new(0, 6)
        tabCorner.Parent = tabButton
        
        tabButtons[i] = tabButton
        
        -- Create tab content
        local tabContent = Instance.new("ScrollingFrame")
        tabContent.Name = tab .. "Content"
        tabContent.Size = UDim2.new(1, -20, 1, -120)
        tabContent.Position = UDim2.new(0, 10, 0, 110)
        tabContent.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
        tabContent.BorderSizePixel = 0
        tabContent.ScrollBarThickness = 8
        tabContent.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
        tabContent.Visible = (i == 1)
        tabContent.Parent = mainFrame
        
        local contentCorner = Instance.new("UICorner")
        contentCorner.CornerRadius = UDim.new(0, 8)
        contentCorner.Parent = tabContent
        
        tabContents[i] = tabContent
        
        -- Tab button click handler
        tabButton.MouseButton1Click:Connect(function()
            MultiplayerGUI.SwitchTab(i, tabButtons, tabContents)
            currentTab = tab
        end)
    end
    
    -- Initialize tab contents
    MultiplayerGUI.CreatePlayersTab(tabContents[1])
    MultiplayerGUI.CreateChatTab(tabContents[2])
    MultiplayerGUI.CreateAllianceTab(tabContents[3])
    MultiplayerGUI.CreateTradeTab(tabContents[4])
    
    -- Set initial active tab
    MultiplayerGUI.SwitchTab(1, tabButtons, tabContents)
    
    -- Close button functionality
    closeButton.MouseButton1Click:Connect(function()
        MultiplayerGUI.ToggleMultiplayerGUI()
    end)
    
    return screenGui
end

-- Switch between tabs
function MultiplayerGUI.SwitchTab(activeIndex, tabButtons, tabContents)
    for i, button in pairs(tabButtons) do
        if i == activeIndex then
            button.BackgroundColor3 = Color3.fromRGB(80, 120, 200)
            button.TextColor3 = Color3.fromRGB(255, 255, 255)
            tabContents[i].Visible = true
        else
            button.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
            button.TextColor3 = Color3.fromRGB(200, 200, 200)
            tabContents[i].Visible = false
        end
    end
end

-- Create players tab
function MultiplayerGUI.CreatePlayersTab(parent)
    local layout = Instance.new("UIListLayout")
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 5)
    layout.Parent = parent
    
    -- Refresh button
    local refreshButton = Instance.new("TextButton")
    refreshButton.Name = "RefreshButton"
    refreshButton.Size = UDim2.new(1, -10, 0, 40)
    refreshButton.Position = UDim2.new(0, 5, 0, 5)
    refreshButton.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    refreshButton.BorderSizePixel = 0
    refreshButton.Text = "🔄 Spieler aktualisieren"
    refreshButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    refreshButton.TextScaled = true
    refreshButton.Font = Enum.Font.SourceSans
    refreshButton.LayoutOrder = 1
    refreshButton.Parent = parent
    
    local refreshCorner = Instance.new("UICorner")
    refreshCorner.CornerRadius = UDim.new(0, 8)
    refreshCorner.Parent = refreshButton
    
    refreshButton.MouseButton1Click:Connect(function()
        MultiplayerGUI.RefreshPlayerList()
    end)
    
    -- Player list container
    local playerListFrame = Instance.new("Frame")
    playerListFrame.Name = "PlayerListFrame"
    playerListFrame.Size = UDim2.new(1, -10, 1, -50)
    playerListFrame.BackgroundTransparency = 1
    playerListFrame.LayoutOrder = 2
    playerListFrame.Parent = parent
    
    local playerLayout = Instance.new("UIListLayout")
    playerLayout.SortOrder = Enum.SortOrder.LayoutOrder
    playerLayout.Padding = UDim.new(0, 5)
    playerLayout.Parent = playerListFrame
    
    -- Load initial player list
    MultiplayerGUI.RefreshPlayerList()
end

-- Create chat tab
function MultiplayerGUI.CreateChatTab(parent)
    -- Chat history
    local chatHistory = Instance.new("ScrollingFrame")
    chatHistory.Name = "ChatHistory"
    chatHistory.Size = UDim2.new(1, -10, 1, -60)
    chatHistory.Position = UDim2.new(0, 5, 0, 5)
    chatHistory.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    chatHistory.BorderSizePixel = 0
    chatHistory.ScrollBarThickness = 6
    chatHistory.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
    chatHistory.Parent = parent
    
    local chatCorner = Instance.new("UICorner")
    chatCorner.CornerRadius = UDim.new(0, 8)
    chatCorner.Parent = chatHistory
    
    local chatLayout = Instance.new("UIListLayout")
    chatLayout.SortOrder = Enum.SortOrder.LayoutOrder
    chatLayout.Padding = UDim.new(0, 2)
    chatLayout.Parent = chatHistory
    
    -- Chat input
    local inputFrame = Instance.new("Frame")
    inputFrame.Name = "InputFrame"
    inputFrame.Size = UDim2.new(1, -10, 0, 50)
    inputFrame.Position = UDim2.new(0, 5, 1, -55)
    inputFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    inputFrame.BorderSizePixel = 0
    inputFrame.Parent = parent
    
    local inputCorner = Instance.new("UICorner")
    inputCorner.CornerRadius = UDim.new(0, 8)
    inputCorner.Parent = inputFrame
    
    local chatInput = Instance.new("TextBox")
    chatInput.Name = "ChatInput"
    chatInput.Size = UDim2.new(1, -60, 1, -10)
    chatInput.Position = UDim2.new(0, 10, 0, 5)
    chatInput.BackgroundTransparency = 1
    chatInput.Text = ""
    chatInput.PlaceholderText = "Nachricht eingeben..."
    chatInput.TextColor3 = Color3.fromRGB(255, 255, 255)
    chatInput.PlaceholderColor3 = Color3.fromRGB(150, 150, 150)
    chatInput.TextScaled = true
    chatInput.Font = Enum.Font.SourceSans
    chatInput.TextXAlignment = Enum.TextXAlignment.Left
    chatInput.Parent = inputFrame
    
    local sendButton = Instance.new("TextButton")
    sendButton.Name = "SendButton"
    sendButton.Size = UDim2.new(0, 40, 1, -10)
    sendButton.Position = UDim2.new(1, -50, 0, 5)
    sendButton.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    sendButton.BorderSizePixel = 0
    sendButton.Text = "📤"
    sendButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    sendButton.TextScaled = true
    sendButton.Font = Enum.Font.SourceSans
    sendButton.Parent = inputFrame
    
    local sendCorner = Instance.new("UICorner")
    sendCorner.CornerRadius = UDim.new(0, 6)
    sendCorner.Parent = sendButton
    
    -- Send message functionality
    local function sendMessage()
        local message = chatInput.Text
        if message and message ~= "" then
            Events.SendChatMessageEvent:FireServer(message, "global")
            chatInput.Text = ""
        end
    end
    
    sendButton.MouseButton1Click:Connect(sendMessage)
    chatInput.FocusLost:Connect(function(enterPressed)
        if enterPressed then
            sendMessage()
        end
    end)
end

-- Create alliance tab
function MultiplayerGUI.CreateAllianceTab(parent)
    local layout = Instance.new("UIListLayout")
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 10)
    layout.Parent = parent
    
    -- Alliance status
    local statusFrame = Instance.new("Frame")
    statusFrame.Name = "StatusFrame"
    statusFrame.Size = UDim2.new(1, -10, 0, 100)
    statusFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    statusFrame.BorderSizePixel = 0
    statusFrame.LayoutOrder = 1
    statusFrame.Parent = parent
    
    local statusCorner = Instance.new("UICorner")
    statusCorner.CornerRadius = UDim.new(0, 8)
    statusCorner.Parent = statusFrame
    
    local statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "StatusLabel"
    statusLabel.Size = UDim2.new(1, -20, 1, -20)
    statusLabel.Position = UDim2.new(0, 10, 0, 10)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = "🤝 Keine Allianz"
    statusLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.SourceSans
    statusLabel.TextXAlignment = Enum.TextXAlignment.Left
    statusLabel.TextYAlignment = Enum.TextYAlignment.Top
    statusLabel.Parent = statusFrame
    
    -- Create alliance button
    local createButton = Instance.new("TextButton")
    createButton.Name = "CreateButton"
    createButton.Size = UDim2.new(1, -10, 0, 50)
    createButton.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    createButton.BorderSizePixel = 0
    createButton.Text = "➕ Allianz erstellen"
    createButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    createButton.TextScaled = true
    createButton.Font = Enum.Font.SourceSans
    createButton.LayoutOrder = 2
    createButton.Parent = parent
    
    local createCorner = Instance.new("UICorner")
    createCorner.CornerRadius = UDim.new(0, 8)
    createCorner.Parent = createButton
    
    createButton.MouseButton1Click:Connect(function()
        MultiplayerGUI.ShowCreateAllianceDialog()
    end)
end

-- Create trade tab
function MultiplayerGUI.CreateTradeTab(parent)
    local layout = Instance.new("UIListLayout")
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 10)
    layout.Parent = parent
    
    -- Active trades header
    local tradesHeader = Instance.new("TextLabel")
    tradesHeader.Name = "TradesHeader"
    tradesHeader.Size = UDim2.new(1, -10, 0, 40)
    tradesHeader.BackgroundTransparency = 1
    tradesHeader.Text = "💰 Aktive Handelsangebote"
    tradesHeader.TextColor3 = Color3.fromRGB(255, 255, 255)
    tradesHeader.TextScaled = true
    tradesHeader.Font = Enum.Font.SourceSansBold
    tradesHeader.TextXAlignment = Enum.TextXAlignment.Left
    tradesHeader.LayoutOrder = 1
    tradesHeader.Parent = parent
    
    -- Trades list
    local tradesFrame = Instance.new("Frame")
    tradesFrame.Name = "TradesFrame"
    tradesFrame.Size = UDim2.new(1, -10, 1, -100)
    tradesFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 45)
    tradesFrame.BorderSizePixel = 0
    tradesFrame.LayoutOrder = 2
    tradesFrame.Parent = parent
    
    local tradesCorner = Instance.new("UICorner")
    tradesCorner.CornerRadius = UDim.new(0, 8)
    tradesCorner.Parent = tradesFrame
    
    local tradesLayout = Instance.new("UIListLayout")
    tradesLayout.SortOrder = Enum.SortOrder.LayoutOrder
    tradesLayout.Padding = UDim.new(0, 5)
    tradesLayout.Parent = tradesFrame
    
    -- Refresh trades button
    local refreshTradesButton = Instance.new("TextButton")
    refreshTradesButton.Name = "RefreshTradesButton"
    refreshTradesButton.Size = UDim2.new(1, -10, 0, 40)
    refreshTradesButton.BackgroundColor3 = Color3.fromRGB(60, 120, 200)
    refreshTradesButton.BorderSizePixel = 0
    refreshTradesButton.Text = "🔄 Handelsangebote aktualisieren"
    refreshTradesButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    refreshTradesButton.TextScaled = true
    refreshTradesButton.Font = Enum.Font.SourceSans
    refreshTradesButton.LayoutOrder = 3
    refreshTradesButton.Parent = parent
    
    local refreshTradesCorner = Instance.new("UICorner")
    refreshTradesCorner.CornerRadius = UDim.new(0, 8)
    refreshTradesCorner.Parent = refreshTradesButton
    
    refreshTradesButton.MouseButton1Click:Connect(function()
        MultiplayerGUI.RefreshTradeOffers()
    end)
end

-- Refresh player list
function MultiplayerGUI.RefreshPlayerList()
    Events.GetConnectedPlayersEvent:FireServer()
end

-- Refresh trade offers
function MultiplayerGUI.RefreshTradeOffers()
    Events.GetPlayerTradeOffersEvent:FireServer()
end

-- Show create alliance dialog
function MultiplayerGUI.ShowCreateAllianceDialog()
    -- Simple input dialog (would be more sophisticated in real implementation)
    local allianceName = "Meine Allianz" -- Placeholder
    local settings = {
        maxMembers = 4,
        shareRevenue = false,
        shareInfrastructure = true
    }
    
    Events.CreateAllianceEvent:FireServer(allianceName, settings)
end

-- Toggle multiplayer GUI
function MultiplayerGUI.ToggleMultiplayerGUI()
    local gui = playerGui:FindFirstChild("MultiplayerGUI")
    if gui then
        local mainFrame = gui:FindFirstChild("MainFrame")
        if mainFrame then
            isMultiplayerGUIOpen = not isMultiplayerGUIOpen
            mainFrame.Visible = isMultiplayerGUIOpen
            
            if isMultiplayerGUIOpen then
                -- Refresh data when opening
                MultiplayerGUI.RefreshPlayerList()
                MultiplayerGUI.RefreshTradeOffers()
            end
        end
    end
end

-- Initialize multiplayer GUI
function MultiplayerGUI.Initialize()
    MultiplayerGUI.CreateMultiplayerInterface()
    
    -- Hotkey to toggle (M key)
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.M then
            MultiplayerGUI.ToggleMultiplayerGUI()
        end
    end)
    
    print("🌐 Multiplayer GUI initialized - Press M to open")
end

-- Event handlers
Events.ConnectedPlayersResponseEvent.OnClientEvent:Connect(function(players)
    connectedPlayers = players
    -- Update player list UI
end)

Events.ChatMessageBroadcastEvent.OnClientEvent:Connect(function(message)
    table.insert(chatHistory, message)
    -- Update chat UI
end)

Events.AllianceCreationResponseEvent.OnClientEvent:Connect(function(success, result)
    if success then
        print("✅ Alliance created:", result)
    else
        print("❌ Alliance creation failed:", result)
    end
end)

Events.PlayerTradeOffersResponseEvent.OnClientEvent:Connect(function(offers)
    tradeOffers = offers
    -- Update trade offers UI
end)

-- Initialize when script loads
MultiplayerGUI.Initialize()

return MultiplayerGUI
