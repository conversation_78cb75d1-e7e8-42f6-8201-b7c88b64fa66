-- ServerScriptService/Managers/AICompetitorManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Intelligente KI-Konkurrenten mit strategischen Entscheidungen

local AICompetitorManager = {}
AICompetitorManager.__index = AICompetitorManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- KI-Persönlichkeiten
local AI_PERSONALITIES = {
    AGGRESSIVE = {
        name = "Aggressiv",
        priceCompetition = 0.9, -- Unterbietet um 10%
        expansionRate = 1.5, -- 50% schnellere Expansion
        riskTolerance = 0.8, -- Hohe Risikobereitschaft
        marketShareTarget = 0.4, -- Zielt auf 40% Marktanteil
        description = "Aggressive Expansion und Preiskampf"
    },
    CONSERVATIVE = {
        name = "Konservativ",
        priceCompetition = 1.05, -- 5% höhere Preise
        expansionRate = 0.7, -- Langsamere Expansion
        riskTolerance = 0.3, -- <PERSON><PERSON><PERSON><PERSON> Risikobereitschaft
        marketShareTarget = 0.2, -- Zielt auf 20% Marktanteil
        description = "Stabile, nachhaltige Entwicklung"
    },
    INNOVATIVE = {
        name = "Innovativ",
        priceCompetition = 1.1, -- Premium-Preise
        expansionRate = 1.2, -- Fokus auf neue Technologien
        riskTolerance = 0.6, -- Mittlere Risikobereitschaft
        marketShareTarget = 0.25, -- Nischenmärkte
        description = "Technologie-Fokus und Innovation"
    },
    OPPORTUNISTIC = {
        name = "Opportunistisch",
        priceCompetition = 0.95, -- Flexible Preise
        expansionRate = 1.0, -- Reagiert auf Marktchancen
        riskTolerance = 0.7, -- Hohe Flexibilität
        marketShareTarget = 0.3, -- Nutzt Marktlücken
        description = "Nutzt Marktchancen optimal aus"
    }
}

-- KI-Schwierigkeitsgrade
local AI_DIFFICULTIES = {
    EASY = {
        efficiency = 0.7, -- 70% Effizienz
        decisionSpeed = 0.5, -- Langsame Entscheidungen
        marketAnalysis = 0.6, -- Begrenzte Marktanalyse
        resourceManagement = 0.7
    },
    NORMAL = {
        efficiency = 0.85,
        decisionSpeed = 0.8,
        marketAnalysis = 0.8,
        resourceManagement = 0.85
    },
    HARD = {
        efficiency = 0.95,
        decisionSpeed = 1.0,
        marketAnalysis = 0.95,
        resourceManagement = 0.95
    },
    EXPERT = {
        efficiency = 1.1, -- Übermenschliche Effizienz
        decisionSpeed = 1.2,
        marketAnalysis = 1.0,
        resourceManagement = 1.1
    }
}

-- Konstruktor
function AICompetitorManager.new(gameConfig)
    local self = setmetatable({}, AICompetitorManager)
    
    self.competitors = {}
    self.gameConfig = gameConfig or {}
    self.nextCompetitorId = 1
    
    -- Markt-Daten
    self.marketData = {
        totalRevenue = 0,
        marketShares = {},
        priceIndex = 1.0,
        competitionLevel = 0.5
    }
    
    self:InitializeEvents()
    
    -- KI-Update-Loop
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdateAI(deltaTime)
    end)
    
    return self
end

-- Events initialisieren
function AICompetitorManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetCompetitorDataFunction") then
        local getCompetitorDataFunction = Instance.new("RemoteFunction")
        getCompetitorDataFunction.Name = "GetCompetitorDataFunction"
        getCompetitorDataFunction.Parent = Events
    end
    
    Events.GetCompetitorDataFunction.OnServerInvoke = function(player)
        return self:GetCompetitorData()
    end
end

-- KI-Konkurrenten erstellen
function AICompetitorManager:CreateCompetitors(count, difficulty)
    local companyNames = {
        "TransGlobal Corp", "RailMaster Inc", "CargoExpress Ltd", "LogiTech Solutions",
        "FastTrack Industries", "MegaTransport Co", "SwiftCargo Systems", "UltraLogistics",
        "PrimeMover Corp", "EliteTransport Ltd", "PowerHaul Industries", "SpeedLine Corp"
    }
    
    local personalities = {"AGGRESSIVE", "CONSERVATIVE", "INNOVATIVE", "OPPORTUNISTIC"}
    
    for i = 1, count do
        local competitorId = "ai_" .. self.nextCompetitorId
        self.nextCompetitorId = self.nextCompetitorId + 1
        
        local personality = personalities[math.random(1, #personalities)]
        local companyName = companyNames[math.random(1, #companyNames)]
        
        local competitor = {
            id = competitorId,
            name = companyName,
            personality = personality,
            difficulty = difficulty or "NORMAL",
            
            -- Finanzen
            money = math.random(500000, 2000000),
            revenue = 0,
            expenses = 0,
            profit = 0,
            
            -- Unternehmen
            founded = tick(),
            marketShare = 0,
            reputation = math.random(50, 80),
            
            -- Assets
            vehicles = {},
            routes = {},
            stations = {},
            
            -- Strategie
            currentStrategy = "EXPANSION",
            strategicGoals = {},
            lastDecision = tick(),
            
            -- Performance
            efficiency = AI_DIFFICULTIES[difficulty].efficiency,
            customerSatisfaction = math.random(60, 85),
            
            -- KI-Zustand
            thinkingCooldown = 0,
            lastAnalysis = 0,
            decisionQueue = {}
        }
        
        self.competitors[competitorId] = competitor
        self:InitializeCompetitorStrategy(competitor)
        
        print("🤖 KI-Konkurrent erstellt:", companyName, "(" .. personality .. ")")
    end
end

-- Konkurrent-Strategie initialisieren
function AICompetitorManager:InitializeCompetitorStrategy(competitor)
    local personality = AI_PERSONALITIES[competitor.personality]
    
    -- Strategische Ziele setzen
    competitor.strategicGoals = {
        targetMarketShare = personality.marketShareTarget,
        targetRevenue = competitor.money * 2, -- Verdopplung als Ziel
        expansionPriority = math.random(1, 3), -- 1=Züge, 2=LKWs, 3=Schiffe
        priceStrategy = personality.priceCompetition
    }
    
    -- Erste Fahrzeuge kaufen
    self:ExecuteInitialPurchases(competitor)
end

-- Erste Käufe durchführen
function AICompetitorManager:ExecuteInitialPurchases(competitor)
    local vehicleManager = require(script.Parent.VehicleManager)
    if not vehicleManager then return end
    
    -- Depot erstellen (vereinfacht)
    local depot = {
        id = "depot_" .. competitor.id,
        owner = competitor.id,
        position = Vector3.new(math.random(-500, 500), 0, math.random(-500, 500)),
        type = "MIXED"
    }
    
    -- Erste Fahrzeuge kaufen basierend auf Strategie
    local initialBudget = competitor.money * 0.3 -- 30% für erste Fahrzeuge
    local vehicleTypes = {"SMALL_TRUCK", "STEAM_LOCOMOTIVE", "PASSENGER_CAR_BASIC"}
    
    for _, vehicleType in ipairs(vehicleTypes) do
        if initialBudget > 50000 then
            local vehicle = {
                id = "vehicle_" .. competitor.id .. "_" .. #competitor.vehicles + 1,
                type = vehicleType,
                owner = competitor.id,
                purchasePrice = 50000, -- Vereinfacht
                condition = 1.0,
                isActive = true
            }
            
            table.insert(competitor.vehicles, vehicle)
            competitor.money = competitor.money - 50000
            initialBudget = initialBudget - 50000
        end
    end
end

-- KI aktualisieren
function AICompetitorManager:UpdateAI(deltaTime)
    for competitorId, competitor in pairs(self.competitors) do
        -- Thinking Cooldown
        competitor.thinkingCooldown = competitor.thinkingCooldown - deltaTime
        
        if competitor.thinkingCooldown <= 0 then
            self:ProcessAIDecisions(competitor)
            competitor.thinkingCooldown = math.random(5, 15) -- 5-15 Sekunden zwischen Entscheidungen
        end
        
        -- Kontinuierliche Updates
        self:UpdateCompetitorOperations(competitor)
    end
    
    -- Markt-Analyse alle 30 Sekunden
    if tick() - (self.lastMarketAnalysis or 0) > 30 then
        self:AnalyzeMarket()
        self.lastMarketAnalysis = tick()
    end
end

-- KI-Entscheidungen verarbeiten
function AICompetitorManager:ProcessAIDecisions(competitor)
    local personality = AI_PERSONALITIES[competitor.personality]
    local difficulty = AI_DIFFICULTIES[competitor.difficulty]
    
    -- Markt analysieren
    local marketAnalysis = self:AnalyzeMarketForCompetitor(competitor)
    
    -- Entscheidungsbaum
    local decisions = {}
    
    -- 1. Finanzielle Situation bewerten
    if competitor.money < 100000 then
        table.insert(decisions, {type = "EMERGENCY_LOAN", priority = 10})
    end
    
    -- 2. Expansion prüfen
    if competitor.money > 200000 and #competitor.vehicles < 10 then
        table.insert(decisions, {type = "BUY_VEHICLE", priority = 8})
    end
    
    -- 3. Preisanpassung prüfen
    if marketAnalysis.competitionLevel > 0.7 then
        table.insert(decisions, {type = "ADJUST_PRICES", priority = 6})
    end
    
    -- 4. Route-Optimierung
    if #competitor.routes > 0 then
        table.insert(decisions, {type = "OPTIMIZE_ROUTES", priority = 5})
    end
    
    -- 5. Neue Routen erstellen
    if #competitor.vehicles > #competitor.routes then
        table.insert(decisions, {type = "CREATE_ROUTE", priority = 7})
    end
    
    -- Entscheidung nach Priorität sortieren
    table.sort(decisions, function(a, b) return a.priority > b.priority end)
    
    -- Beste Entscheidung ausführen
    if #decisions > 0 then
        self:ExecuteAIDecision(competitor, decisions[1])
    end
end

-- KI-Entscheidung ausführen
function AICompetitorManager:ExecuteAIDecision(competitor, decision)
    if decision.type == "BUY_VEHICLE" then
        self:AIBuyVehicle(competitor)
    elseif decision.type == "ADJUST_PRICES" then
        self:AIAdjustPrices(competitor)
    elseif decision.type == "CREATE_ROUTE" then
        self:AICreateRoute(competitor)
    elseif decision.type == "OPTIMIZE_ROUTES" then
        self:AIOptimizeRoutes(competitor)
    elseif decision.type == "EMERGENCY_LOAN" then
        self:AITakeLoan(competitor)
    end
    
    competitor.lastDecision = tick()
end

-- KI kauft Fahrzeug
function AICompetitorManager:AIBuyVehicle(competitor)
    local personality = AI_PERSONALITIES[competitor.personality]
    
    -- Fahrzeugtyp basierend auf Strategie wählen
    local vehicleTypes = {"SMALL_TRUCK", "MEDIUM_TRUCK", "DIESEL_LOCOMOTIVE", "PASSENGER_CAR_BASIC"}
    local selectedType = vehicleTypes[math.random(1, #vehicleTypes)]
    
    -- Kosten schätzen (vereinfacht)
    local estimatedCost = math.random(30000, 100000)
    
    if competitor.money >= estimatedCost * 1.2 then -- 20% Sicherheitspuffer
        local vehicle = {
            id = "vehicle_" .. competitor.id .. "_" .. #competitor.vehicles + 1,
            type = selectedType,
            owner = competitor.id,
            purchasePrice = estimatedCost,
            condition = 1.0,
            isActive = false -- Wird später einer Route zugewiesen
        }
        
        table.insert(competitor.vehicles, vehicle)
        competitor.money = competitor.money - estimatedCost
        
        print("🤖", competitor.name, "kaufte", selectedType, "für $" .. estimatedCost)
    end
end

-- KI passt Preise an
function AICompetitorManager:AIAdjustPrices(competitor)
    local personality = AI_PERSONALITIES[competitor.personality]
    local priceMultiplier = personality.priceCompetition
    
    -- Preise für alle Routen anpassen
    for _, route in ipairs(competitor.routes) do
        route.priceMultiplier = priceMultiplier + math.random(-10, 10) / 100 -- ±10% Variation
    end
    
    print("🤖", competitor.name, "passte Preise an (Faktor:", priceMultiplier, ")")
end

-- KI erstellt neue Route
function AICompetitorManager:AICreateRoute(competitor)
    -- Verfügbare Fahrzeuge finden
    local availableVehicles = {}
    for _, vehicle in ipairs(competitor.vehicles) do
        if not vehicle.isActive then
            table.insert(availableVehicles, vehicle)
        end
    end
    
    if #availableVehicles > 0 then
        local vehicle = availableVehicles[1]
        
        local route = {
            id = "route_" .. competitor.id .. "_" .. #competitor.routes + 1,
            vehicleId = vehicle.id,
            startStation = "station_" .. math.random(1, 10),
            endStation = "station_" .. math.random(1, 10),
            cargoType = "GENERAL",
            priceMultiplier = 1.0,
            isActive = true,
            revenue = 0
        }
        
        table.insert(competitor.routes, route)
        vehicle.isActive = true
        
        print("🤖", competitor.name, "erstellte neue Route:", route.id)
    end
end

-- Markt für Konkurrent analysieren
function AICompetitorManager:AnalyzeMarketForCompetitor(competitor)
    return {
        competitionLevel = math.random(30, 90) / 100,
        priceLevel = math.random(80, 120) / 100,
        demand = math.random(50, 150) / 100,
        profitability = math.random(60, 140) / 100
    }
end

-- Konkurrent-Operationen aktualisieren
function AICompetitorManager:UpdateCompetitorOperations(competitor)
    -- Einnahmen generieren (vereinfacht)
    local hourlyRevenue = 0
    for _, route in ipairs(competitor.routes) do
        if route.isActive then
            hourlyRevenue = hourlyRevenue + math.random(100, 500)
        end
    end
    
    competitor.revenue = competitor.revenue + hourlyRevenue
    competitor.money = competitor.money + hourlyRevenue
    
    -- Betriebskosten abziehen
    local hourlyCosts = #competitor.vehicles * 10 -- $10 pro Fahrzeug pro Stunde
    competitor.expenses = competitor.expenses + hourlyCosts
    competitor.money = competitor.money - hourlyCosts
    
    -- Gewinn berechnen
    competitor.profit = competitor.revenue - competitor.expenses
end

-- Markt analysieren
function AICompetitorManager:AnalyzeMarket()
    local totalRevenue = 0
    local competitorCount = 0
    
    for _, competitor in pairs(self.competitors) do
        totalRevenue = totalRevenue + competitor.revenue
        competitorCount = competitorCount + 1
    end
    
    self.marketData.totalRevenue = totalRevenue
    self.marketData.competitionLevel = math.min(1.0, competitorCount / 5) -- Max bei 5 Konkurrenten
    
    -- Marktanteile berechnen
    for competitorId, competitor in pairs(self.competitors) do
        if totalRevenue > 0 then
            competitor.marketShare = competitor.revenue / totalRevenue
        else
            competitor.marketShare = 1 / competitorCount
        end
        self.marketData.marketShares[competitorId] = competitor.marketShare
    end
end

-- Konkurrent-Daten abrufen
function AICompetitorManager:GetCompetitorData()
    local competitorData = {}
    
    for competitorId, competitor in pairs(self.competitors) do
        competitorData[competitorId] = {
            name = competitor.name,
            personality = competitor.personality,
            money = competitor.money,
            revenue = competitor.revenue,
            profit = competitor.profit,
            marketShare = competitor.marketShare,
            reputation = competitor.reputation,
            vehicleCount = #competitor.vehicles,
            routeCount = #competitor.routes,
            efficiency = competitor.efficiency,
            customerSatisfaction = competitor.customerSatisfaction
        }
    end
    
    return {
        competitors = competitorData,
        marketData = self.marketData
    }
end

-- Konkurrenten entfernen
function AICompetitorManager:RemoveCompetitor(competitorId)
    if self.competitors[competitorId] then
        print("🤖 KI-Konkurrent entfernt:", self.competitors[competitorId].name)
        self.competitors[competitorId] = nil
    end
end

-- Cleanup
function AICompetitorManager:Destroy()
    if self.updateConnection then
        self.updateConnection:Disconnect()
    end
end

return AICompetitorManager
