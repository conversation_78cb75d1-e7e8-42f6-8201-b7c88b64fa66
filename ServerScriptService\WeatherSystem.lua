-- ServerScriptService/WeatherSystem.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Dynamisches Wetter-System mit Tag/Nacht-Zyklen und Jahreszeiten

local TweenService = game:GetService("TweenService")
local Lighting = game:GetService("Lighting")
local RunService = game:GetService("RunService")
local HttpService = game:GetService("HttpService")

local WeatherSystem = {}
WeatherSystem.__index = WeatherSystem

function WeatherSystem.new()
    local self = setmetatable({}, WeatherSystem)
    
    -- Time system
    self.gameTime = 0 -- In game hours (0-24)
    self.gameDate = {year = 1850, month = 1, day = 1}
    self.timeScale = 1 -- 1 real second = 1 game minute
    self.dayLength = 1440 -- 24 hours in minutes
    
    -- Weather system
    self.currentWeather = {
        type = "clear",
        intensity = 0,
        temperature = 20, -- Celsius
        humidity = 50, -- Percentage
        windSpeed = 5, -- km/h
        windDirection = 0, -- Degrees
        pressure = 1013 -- hPa
    }
    
    -- Weather types and their properties
    self.weatherTypes = {
        clear = {
            name = "Klar",
            icon = "☀️",
            visibility = 1.0,
            lightingModifier = 1.0,
            temperatureModifier = 0,
            humidityRange = {30, 60},
            windSpeedRange = {0, 15},
            duration = {60, 180}, -- minutes
            transitionTime = 10
        },
        
        cloudy = {
            name = "Bewölkt",
            icon = "☁️",
            visibility = 0.8,
            lightingModifier = 0.7,
            temperatureModifier = -2,
            humidityRange = {50, 80},
            windSpeedRange = {5, 20},
            duration = {30, 120},
            transitionTime = 15
        },
        
        overcast = {
            name = "Bedeckt",
            icon = "☁️",
            visibility = 0.6,
            lightingModifier = 0.5,
            temperatureModifier = -3,
            humidityRange = {70, 90},
            windSpeedRange = {10, 25},
            duration = {45, 150},
            transitionTime = 20
        },
        
        light_rain = {
            name = "Leichter Regen",
            icon = "🌦️",
            visibility = 0.7,
            lightingModifier = 0.6,
            temperatureModifier = -4,
            humidityRange = {80, 95},
            windSpeedRange = {5, 20},
            duration = {20, 90},
            transitionTime = 10,
            hasParticles = true,
            particleIntensity = 0.3
        },
        
        rain = {
            name = "Regen",
            icon = "🌧️",
            visibility = 0.5,
            lightingModifier = 0.4,
            temperatureModifier = -5,
            humidityRange = {85, 100},
            windSpeedRange = {10, 30},
            duration = {30, 120},
            transitionTime = 15,
            hasParticles = true,
            particleIntensity = 0.7
        },
        
        heavy_rain = {
            name = "Starker Regen",
            icon = "⛈️",
            visibility = 0.3,
            lightingModifier = 0.3,
            temperatureModifier = -6,
            humidityRange = {90, 100},
            windSpeedRange = {20, 50},
            duration = {15, 60},
            transitionTime = 10,
            hasParticles = true,
            particleIntensity = 1.0,
            hasThunder = true
        },
        
        snow = {
            name = "Schnee",
            icon = "❄️",
            visibility = 0.6,
            lightingModifier = 0.8,
            temperatureModifier = -10,
            humidityRange = {70, 90},
            windSpeedRange = {0, 25},
            duration = {60, 240},
            transitionTime = 20,
            hasParticles = true,
            particleIntensity = 0.5,
            seasonalOnly = {"winter"}
        },
        
        blizzard = {
            name = "Schneesturm",
            icon = "🌨️",
            visibility = 0.2,
            lightingModifier = 0.4,
            temperatureModifier = -15,
            humidityRange = {80, 95},
            windSpeedRange = {40, 80},
            duration = {20, 90},
            transitionTime = 15,
            hasParticles = true,
            particleIntensity = 1.2,
            seasonalOnly = {"winter"}
        },
        
        fog = {
            name = "Nebel",
            icon = "🌫️",
            visibility = 0.3,
            lightingModifier = 0.6,
            temperatureModifier = -2,
            humidityRange = {90, 100},
            windSpeedRange = {0, 5},
            duration = {30, 180},
            transitionTime = 25
        },
        
        storm = {
            name = "Gewitter",
            icon = "⛈️",
            visibility = 0.4,
            lightingModifier = 0.2,
            temperatureModifier = -7,
            humidityRange = {85, 100},
            windSpeedRange = {30, 70},
            duration = {10, 45},
            transitionTime = 5,
            hasParticles = true,
            particleIntensity = 0.9,
            hasThunder = true,
            hasLightning = true
        }
    }
    
    -- Seasons and their weather probabilities
    self.seasons = {
        spring = {
            name = "Frühling",
            months = {3, 4, 5},
            baseTemperature = 15,
            weatherProbabilities = {
                clear = 0.3,
                cloudy = 0.25,
                overcast = 0.15,
                light_rain = 0.2,
                rain = 0.08,
                heavy_rain = 0.02
            }
        },
        
        summer = {
            name = "Sommer",
            months = {6, 7, 8},
            baseTemperature = 25,
            weatherProbabilities = {
                clear = 0.5,
                cloudy = 0.2,
                overcast = 0.1,
                light_rain = 0.1,
                rain = 0.05,
                storm = 0.05
            }
        },
        
        autumn = {
            name = "Herbst",
            months = {9, 10, 11},
            baseTemperature = 12,
            weatherProbabilities = {
                clear = 0.2,
                cloudy = 0.3,
                overcast = 0.2,
                light_rain = 0.15,
                rain = 0.1,
                fog = 0.05
            }
        },
        
        winter = {
            name = "Winter",
            months = {12, 1, 2},
            baseTemperature = 2,
            weatherProbabilities = {
                clear = 0.25,
                cloudy = 0.25,
                overcast = 0.2,
                snow = 0.2,
                blizzard = 0.05,
                fog = 0.05
            }
        }
    }
    
    -- Weather transition system
    self.weatherTransition = {
        active = false,
        fromWeather = nil,
        toWeather = nil,
        progress = 0,
        duration = 0
    }
    
    -- Weather forecast (next 24 hours)
    self.weatherForecast = {}
    
    -- Weather events
    self.weatherEvents = {}
    self.nextWeatherChange = 0
    
    -- Lightning system
    self.lightningSystem = {
        active = false,
        nextStrike = 0,
        strikeInterval = {5, 30}, -- seconds
        flashDuration = 0.2
    }
    
    -- Climate zones (different weather patterns by region)
    self.climateZones = {
        temperate = {
            name = "Gemäßigt",
            temperatureModifier = 0,
            humidityModifier = 0,
            region = {x = {-500, 500}, z = {-500, 500}}
        },
        
        continental = {
            name = "Kontinental",
            temperatureModifier = -5,
            humidityModifier = -10,
            region = {x = {500, 1000}, z = {-500, 500}}
        },
        
        coastal = {
            name = "Küsten",
            temperatureModifier = 3,
            humidityModifier = 15,
            region = {x = {-1000, -500}, z = {-500, 500}}
        }
    }
    
    return self
end

-- Initialize weather system
function WeatherSystem:Initialize()
    -- Set initial time
    self.gameTime = 8.0 -- Start at 8 AM
    
    -- Generate initial weather forecast
    self:GenerateWeatherForecast()
    
    -- Set initial weather
    self:SetWeather("clear", 0)
    
    -- Start weather update loop
    self:StartWeatherLoop()
    
    print("🌤️ Weather system initialized")
end

-- Start weather update loop
function WeatherSystem:StartWeatherLoop()
    spawn(function()
        while true do
            self:UpdateWeather()
            wait(1) -- Update every second
        end
    end)
end

-- Update weather system
function WeatherSystem:UpdateWeather()
    local deltaTime = 1 / 60 -- Assuming 60 FPS
    
    -- Update game time
    self:UpdateGameTime(deltaTime)
    
    -- Update weather transition
    if self.weatherTransition.active then
        self:UpdateWeatherTransition(deltaTime)
    end
    
    -- Check for weather changes
    if tick() >= self.nextWeatherChange and not self.weatherTransition.active then
        self:TriggerWeatherChange()
    end
    
    -- Update lightning system
    if self.lightningSystem.active then
        self:UpdateLightning()
    end
    
    -- Update atmospheric effects
    self:UpdateAtmosphericEffects()
end

-- Update game time
function WeatherSystem:UpdateGameTime(deltaTime)
    -- Convert real time to game time
    local gameMinutesPerSecond = self.timeScale
    local gameMinutes = deltaTime * gameMinutesPerSecond * 60
    
    self.gameTime = self.gameTime + (gameMinutes / 60)
    
    -- Handle day rollover
    if self.gameTime >= 24 then
        self.gameTime = self.gameTime - 24
        self:AdvanceDate()
    end
    
    -- Update lighting based on time of day
    self:UpdateTimeOfDayLighting()
end

-- Advance date
function WeatherSystem:AdvanceDate()
    self.gameDate.day = self.gameDate.day + 1
    
    -- Handle month rollover
    local daysInMonth = self:GetDaysInMonth(self.gameDate.month, self.gameDate.year)
    if self.gameDate.day > daysInMonth then
        self.gameDate.day = 1
        self.gameDate.month = self.gameDate.month + 1
        
        -- Handle year rollover
        if self.gameDate.month > 12 then
            self.gameDate.month = 1
            self.gameDate.year = self.gameDate.year + 1
        end
    end
    
    -- Generate new weather forecast for new day
    self:GenerateWeatherForecast()
end

-- Get days in month
function WeatherSystem:GetDaysInMonth(month, year)
    local daysInMonth = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}
    
    -- Check for leap year
    if month == 2 and self:IsLeapYear(year) then
        return 29
    end
    
    return daysInMonth[month]
end

-- Check if year is leap year
function WeatherSystem:IsLeapYear(year)
    return (year % 4 == 0 and year % 100 ~= 0) or (year % 400 == 0)
end

-- Get current season
function WeatherSystem:GetCurrentSeason()
    for seasonName, season in pairs(self.seasons) do
        for _, month in pairs(season.months) do
            if month == self.gameDate.month then
                return seasonName, season
            end
        end
    end
    return "spring", self.seasons.spring
end

-- Generate weather forecast
function WeatherSystem:GenerateWeatherForecast()
    self.weatherForecast = {}
    local currentSeason, seasonData = self:GetCurrentSeason()
    
    -- Generate 24 hours of weather
    for hour = 0, 23 do
        local weather = self:SelectRandomWeather(seasonData.weatherProbabilities)
        local intensity = math.random() * 0.8 + 0.2 -- 0.2 to 1.0
        
        table.insert(self.weatherForecast, {
            hour = hour,
            weather = weather,
            intensity = intensity,
            temperature = self:CalculateTemperature(hour, seasonData.baseTemperature, weather)
        })
    end
end

-- Select random weather based on probabilities
function WeatherSystem:SelectRandomWeather(probabilities)
    local random = math.random()
    local cumulative = 0
    
    for weatherType, probability in pairs(probabilities) do
        cumulative = cumulative + probability
        if random <= cumulative then
            return weatherType
        end
    end
    
    return "clear" -- Fallback
end

-- Calculate temperature based on time and weather
function WeatherSystem:CalculateTemperature(hour, baseTemp, weatherType)
    -- Daily temperature variation (cooler at night)
    local timeModifier = math.sin((hour - 6) * math.pi / 12) * 8
    
    -- Weather modifier
    local weatherData = self.weatherTypes[weatherType]
    local weatherModifier = weatherData and weatherData.temperatureModifier or 0
    
    return baseTemp + timeModifier + weatherModifier
end

-- Set weather
function WeatherSystem:SetWeather(weatherType, intensity, immediate)
    local weatherData = self.weatherTypes[weatherType]
    if not weatherData then
        warn("Unknown weather type:", weatherType)
        return
    end
    
    if immediate or not self.currentWeather.type then
        -- Immediate change
        self.currentWeather.type = weatherType
        self.currentWeather.intensity = intensity or 1.0
        self:ApplyWeatherEffects()
    else
        -- Start transition
        self:StartWeatherTransition(weatherType, intensity or 1.0)
    end
    
    -- Schedule next weather change
    local duration = weatherData.duration
    local nextChangeMinutes = math.random(duration[1], duration[2])
    self.nextWeatherChange = tick() + (nextChangeMinutes * 60 / self.timeScale)
    
    print("🌦️ Weather changed to:", weatherData.name, "intensity:", intensity or 1.0)
end

-- Start weather transition
function WeatherSystem:StartWeatherTransition(toWeather, intensity)
    local weatherData = self.weatherTypes[toWeather]
    
    self.weatherTransition = {
        active = true,
        fromWeather = self.currentWeather.type,
        toWeather = toWeather,
        toIntensity = intensity,
        progress = 0,
        duration = weatherData.transitionTime or 10
    }
end

-- Update weather transition
function WeatherSystem:UpdateWeatherTransition(deltaTime)
    if not self.weatherTransition.active then return end
    
    local transition = self.weatherTransition
    transition.progress = transition.progress + (deltaTime * self.timeScale / transition.duration)
    
    if transition.progress >= 1 then
        -- Transition complete
        self.currentWeather.type = transition.toWeather
        self.currentWeather.intensity = transition.toIntensity
        self.weatherTransition.active = false
        self:ApplyWeatherEffects()
    else
        -- Interpolate weather effects
        self:InterpolateWeatherEffects(transition.progress)
    end
end

-- Apply weather effects
function WeatherSystem:ApplyWeatherEffects()
    local weatherData = self.weatherTypes[self.currentWeather.type]
    if not weatherData then return end
    
    -- Update atmospheric properties
    local atmosphere = Lighting:FindFirstChild("Atmosphere")
    if atmosphere then
        atmosphere.Density = (1 - weatherData.visibility) * 0.5
        atmosphere.Haze = (1 - weatherData.visibility) * 2
    end
    
    -- Update lighting
    local lightingMultiplier = weatherData.lightingModifier
    if self.gameTime >= 6 and self.gameTime <= 18 then
        Lighting.Brightness = 2.5 * lightingMultiplier
    else
        Lighting.Brightness = 0.8 * lightingMultiplier
    end
    
    -- Enable/disable particle effects
    if weatherData.hasParticles then
        self:EnableWeatherParticles(self.currentWeather.type, weatherData.particleIntensity * self.currentWeather.intensity)
    else
        self:DisableWeatherParticles()
    end
    
    -- Enable/disable lightning
    if weatherData.hasLightning then
        self:EnableLightning()
    else
        self:DisableLightning()
    end
    
    -- Update wind effects
    self:UpdateWindEffects(weatherData)
end

-- Interpolate weather effects during transition
function WeatherSystem:InterpolateWeatherEffects(progress)
    local fromData = self.weatherTypes[self.weatherTransition.fromWeather]
    local toData = self.weatherTypes[self.weatherTransition.toWeather]
    
    if not fromData or not toData then return end
    
    -- Interpolate visibility
    local visibility = fromData.visibility + (toData.visibility - fromData.visibility) * progress
    
    -- Update atmosphere
    local atmosphere = Lighting:FindFirstChild("Atmosphere")
    if atmosphere then
        atmosphere.Density = (1 - visibility) * 0.5
        atmosphere.Haze = (1 - visibility) * 2
    end
    
    -- Interpolate lighting
    local lightingMultiplier = fromData.lightingModifier + (toData.lightingModifier - fromData.lightingModifier) * progress
    if self.gameTime >= 6 and self.gameTime <= 18 then
        Lighting.Brightness = 2.5 * lightingMultiplier
    else
        Lighting.Brightness = 0.8 * lightingMultiplier
    end
end

-- Update time of day lighting
function WeatherSystem:UpdateTimeOfDayLighting()
    local hour = self.gameTime
    local weatherData = self.weatherTypes[self.currentWeather.type]
    local lightingModifier = weatherData and weatherData.lightingModifier or 1.0
    
    -- Calculate sun position
    local sunAngle = (hour - 6) * 15 -- Degrees from horizon
    Lighting.ClockTime = hour
    
    -- Update lighting colors based on time
    if hour >= 5 and hour < 7 then
        -- Dawn
        Lighting.Ambient = Color3.fromRGB(100, 120, 150)
        Lighting.ColorShift_Bottom = Color3.fromRGB(255, 200, 150)
        Lighting.ColorShift_Top = Color3.fromRGB(200, 220, 255)
        Lighting.Brightness = 1.5 * lightingModifier
    elseif hour >= 7 and hour < 17 then
        -- Day
        Lighting.Ambient = Color3.fromRGB(140, 140, 140)
        Lighting.ColorShift_Bottom = Color3.fromRGB(255, 255, 255)
        Lighting.ColorShift_Top = Color3.fromRGB(200, 220, 255)
        Lighting.Brightness = 2.5 * lightingModifier
    elseif hour >= 17 and hour < 19 then
        -- Dusk
        Lighting.Ambient = Color3.fromRGB(120, 100, 80)
        Lighting.ColorShift_Bottom = Color3.fromRGB(255, 150, 100)
        Lighting.ColorShift_Top = Color3.fromRGB(150, 100, 200)
        Lighting.Brightness = 1.2 * lightingModifier
    else
        -- Night
        Lighting.Ambient = Color3.fromRGB(50, 60, 80)
        Lighting.ColorShift_Bottom = Color3.fromRGB(100, 120, 150)
        Lighting.ColorShift_Top = Color3.fromRGB(50, 80, 120)
        Lighting.Brightness = 0.8 * lightingModifier
    end
end

-- Enable weather particles
function WeatherSystem:EnableWeatherParticles(weatherType, intensity)
    -- This would interface with VisualEffectsManager
    print("🌧️ Enabling weather particles:", weatherType, "intensity:", intensity)
end

-- Disable weather particles
function WeatherSystem:DisableWeatherParticles()
    print("☀️ Disabling weather particles")
end

-- Enable lightning system
function WeatherSystem:EnableLightning()
    self.lightningSystem.active = true
    self.lightningSystem.nextStrike = tick() + math.random(self.lightningSystem.strikeInterval[1], self.lightningSystem.strikeInterval[2])
end

-- Disable lightning system
function WeatherSystem:DisableLightning()
    self.lightningSystem.active = false
end

-- Update lightning system
function WeatherSystem:UpdateLightning()
    if not self.lightningSystem.active then return end
    
    if tick() >= self.lightningSystem.nextStrike then
        self:TriggerLightningStrike()
        self.lightningSystem.nextStrike = tick() + math.random(self.lightningSystem.strikeInterval[1], self.lightningSystem.strikeInterval[2])
    end
end

-- Trigger lightning strike
function WeatherSystem:TriggerLightningStrike()
    -- Flash effect
    local originalBrightness = Lighting.Brightness
    Lighting.Brightness = 5
    
    spawn(function()
        wait(self.lightningSystem.flashDuration)
        Lighting.Brightness = originalBrightness
    end)
    
    -- Thunder sound would be triggered here
    print("⚡ Lightning strike!")
end

-- Update wind effects
function WeatherSystem:UpdateWindEffects(weatherData)
    local windSpeed = math.random(weatherData.windSpeedRange[1], weatherData.windSpeedRange[2])
    self.currentWeather.windSpeed = windSpeed
    self.currentWeather.windDirection = math.random(0, 360)
    
    -- Wind effects on particles and objects would be applied here
end

-- Update atmospheric effects
function WeatherSystem:UpdateAtmosphericEffects()
    -- Update fog, haze, and other atmospheric effects based on current weather
    local weatherData = self.weatherTypes[self.currentWeather.type]
    if not weatherData then return end
    
    -- This would interface with VisualEffectsManager for atmospheric effects
end

-- Trigger weather change
function WeatherSystem:TriggerWeatherChange()
    local currentSeason, seasonData = self:GetCurrentSeason()
    local newWeather = self:SelectRandomWeather(seasonData.weatherProbabilities)
    local intensity = math.random() * 0.8 + 0.2
    
    self:SetWeather(newWeather, intensity, false)
end

-- Get weather info
function WeatherSystem:GetWeatherInfo()
    local weatherData = self.weatherTypes[self.currentWeather.type]
    local seasonName, seasonData = self:GetCurrentSeason()
    
    return {
        type = self.currentWeather.type,
        name = weatherData and weatherData.name or "Unknown",
        icon = weatherData and weatherData.icon or "❓",
        intensity = self.currentWeather.intensity,
        temperature = self:CalculateTemperature(self.gameTime, seasonData.baseTemperature, self.currentWeather.type),
        humidity = self.currentWeather.humidity,
        windSpeed = self.currentWeather.windSpeed,
        windDirection = self.currentWeather.windDirection,
        visibility = weatherData and weatherData.visibility or 1.0,
        season = seasonName,
        time = self.gameTime,
        date = self.gameDate
    }
end

-- Get weather forecast
function WeatherSystem:GetWeatherForecast()
    return self.weatherForecast
end

-- Set time scale
function WeatherSystem:SetTimeScale(scale)
    self.timeScale = math.max(0.1, math.min(100, scale))
end

-- Get time scale
function WeatherSystem:GetTimeScale()
    return self.timeScale
end

return WeatherSystem
