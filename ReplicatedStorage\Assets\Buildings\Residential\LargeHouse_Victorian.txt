# Großes Viktorianisches Haus
# ROBLOX SCRIPT TYPE: Asset Data File

[BASIC_INFO]
ID = "LARGE_HOUSE_VICTORIAN"
Name = "Großes Viktorianisches Haus"
Category = "Residential"
Size = "Large"
Era = "Victorian"
Era_Years = {1870, 1900}

[MODEL_DATA]
ModelId = "rbxassetid://LARGE_HOUSE_VICTORIAN"
Scale = Vector3(18, 12, 20)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.8, 0.7, 0.6)      # Heller Sandstein
Secondary = Color3(0.2, 0.4, 0.2)    # <PERSON><PERSON><PERSON><PERSON> Ku<PERSON>er-Dach
Accent = Color3(0.9, 0.8, 0.3)       # Goldene Verzierungen
Trim = Color3(0.1, 0.1, 0.1)         # Schwarze Details

[GAMEPLAY_STATS]
Population = 15
BuildCost = 12000
MaintenanceCost = 120
BuildTime = 90
PowerConsumption = 0
WaterConsumption = 8
LandSize = Vector2(4, 4)  # 4x4 Felder

[REQUIREMENTS]
MinPopulation = 1000
MinYear = 1870
RequiredTech = {"Advanced_Construction", "Ornamental_Ironwork"}
RequiredResources = {"Stone", "Iron", "Glass", "Luxury_Materials"}
UnlockCost = 2000

[FEATURES]
ArchitecturalStyle = "Victorian_Mansion"
HasGarden = true
HasChimney = true
HasBasement = true
Floors = 3
WindowStyle = "Ornate_Bay_Windows"
RoofStyle = "Turret_Complex"
HasServantQuarters = true
HasStables = true

[UPGRADE_PATH]
CanUpgrade = false  # Höchste Stufe für Victorian Era

[ECONOMIC_DATA]
TaxRevenue = 100
PropertyValue = 12000
MaintenanceJobs = 3
ConstructionJobs = 15
AttractsWealthyPopulation = true

[DESCRIPTION]
ShortDesc = "Prachtvolle viktorianische Villa mit Türmen"
LongDesc = "Eine imposante viktorianische Villa mit mehreren Türmen, aufwendigen Verzierungen und großzügigen Räumlichkeiten. Symbol für den Reichtum der industriellen Elite mit Dienerquartieren und Stallungen."
