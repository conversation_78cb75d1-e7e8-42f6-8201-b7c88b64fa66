-- ServerScriptService/Managers/FinanceManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vollständiges Finanz- und Kreditsystem

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local HttpService = game:GetService("HttpService")

local FinanceManager = {}
FinanceManager.__index = FinanceManager

function FinanceManager.new()
    local self = setmetatable({}, FinanceManager)
    
    -- Spieler-Finanzen
    self.playerFinances = {}
    
    -- Marktbedingungen
    self.marketConditions = {
        economicGrowth = 1.0,
        inflation = 0.02,
        interestRate = 0.05,
        stockMarketIndex = 1000,
        recession = false,
        boom = false
    }
    
    -- Kredit-System
    self.loanSystem = {
        maxLoanAmount = 10000000, -- 10 Millionen
        baseInterestRate = 0.06,
        creditRatingMultiplier = {
            excellent = 0.8,
            good = 1.0,
            fair = 1.3,
            poor = 1.8,
            bad = 2.5
        }
    }
    
    -- Aktienmarkt
    self.stockMarket = {
        companies = {},
        playerShares = {},
        marketVolatility = 0.1
    }
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function FinanceManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    -- Finanz-Events erstellen falls nicht vorhanden
    if not Events:FindFirstChild("GetFinanceDataFunction") then
        local getFinanceDataFunction = Instance.new("RemoteFunction")
        getFinanceDataFunction.Name = "GetFinanceDataFunction"
        getFinanceDataFunction.Parent = Events
        
        getFinanceDataFunction.OnServerInvoke = function(player)
            return self:GetPlayerFinanceData(player.UserId)
        end
    end
    
    if not Events:FindFirstChild("TakeLoanEvent") then
        local takeLoanEvent = Instance.new("RemoteEvent")
        takeLoanEvent.Name = "TakeLoanEvent"
        takeLoanEvent.Parent = Events
        
        takeLoanEvent.OnServerEvent:Connect(function(player, amount, duration)
            self:ProcessLoan(player.UserId, amount, duration)
        end)
    end
end

-- Spieler-Finanzen initialisieren
function FinanceManager:InitializePlayerFinances(playerId)
    self.playerFinances[playerId] = {
        cash = 2000000, -- 2 Millionen Startkapital
        totalRevenue = 0,
        totalExpenses = 0,
        monthlyRevenue = 0,
        monthlyExpenses = 0,
        netWorth = 2000000,
        
        -- Kredite
        loans = {},
        totalDebt = 0,
        creditRating = "good",
        
        -- Aktien
        shares = {},
        portfolioValue = 0,
        
        -- Statistiken
        profitHistory = {},
        revenueHistory = {},
        expenseHistory = {}
    }
    
    print("💰 Finanzen für Spieler initialisiert:", playerId)
end

-- Kredit beantragen
function FinanceManager:ProcessLoan(playerId, amount, duration)
    local playerFinance = self.playerFinances[playerId]
    if not playerFinance then return false end
    
    -- Kredit-Limit prüfen
    if amount > self.loanSystem.maxLoanAmount then
        return false, "Kreditbetrag zu hoch"
    end
    
    -- Kreditwürdigkeit prüfen
    local creditMultiplier = self.loanSystem.creditRatingMultiplier[playerFinance.creditRating] or 1.0
    local interestRate = self.loanSystem.baseInterestRate * creditMultiplier
    
    -- Kredit erstellen
    local loanId = HttpService:GenerateGUID(false)
    local loan = {
        id = loanId,
        amount = amount,
        remainingAmount = amount,
        interestRate = interestRate,
        monthlyPayment = math.floor(amount * (interestRate / 12) / (1 - (1 + interestRate / 12)^(-duration))),
        remainingMonths = duration,
        startDate = os.time()
    }
    
    -- Kredit hinzufügen
    playerFinance.loans[loanId] = loan
    playerFinance.totalDebt = playerFinance.totalDebt + amount
    playerFinance.cash = playerFinance.cash + amount
    
    print("💳 Kredit gewährt:", amount, "€ für Spieler:", playerId)
    return true, loanId
end

-- Monatliche Finanz-Updates
function FinanceManager:ProcessMonthlyFinances(playerId)
    local playerFinance = self.playerFinances[playerId]
    if not playerFinance then return end
    
    -- Kredit-Rückzahlungen
    for loanId, loan in pairs(playerFinance.loans) do
        if loan.remainingMonths > 0 then
            -- Monatliche Zahlung abziehen
            playerFinance.cash = playerFinance.cash - loan.monthlyPayment
            playerFinance.monthlyExpenses = playerFinance.monthlyExpenses + loan.monthlyPayment
            
            -- Kredit reduzieren
            local principalPayment = loan.monthlyPayment - (loan.remainingAmount * loan.interestRate / 12)
            loan.remainingAmount = loan.remainingAmount - principalPayment
            loan.remainingMonths = loan.remainingMonths - 1
            
            -- Kredit abbezahlt?
            if loan.remainingMonths <= 0 then
                playerFinance.totalDebt = playerFinance.totalDebt - loan.amount
                playerFinance.loans[loanId] = nil
                print("✅ Kredit abbezahlt:", loanId)
            end
        end
    end
    
    -- Kreditwürdigkeit aktualisieren
    self:UpdateCreditRating(playerId)
    
    -- Statistiken aktualisieren
    table.insert(playerFinance.profitHistory, playerFinance.monthlyRevenue - playerFinance.monthlyExpenses)
    table.insert(playerFinance.revenueHistory, playerFinance.monthlyRevenue)
    table.insert(playerFinance.expenseHistory, playerFinance.monthlyExpenses)
    
    -- Nur letzte 24 Monate behalten
    if #playerFinance.profitHistory > 24 then
        table.remove(playerFinance.profitHistory, 1)
        table.remove(playerFinance.revenueHistory, 1)
        table.remove(playerFinance.expenseHistory, 1)
    end
    
    -- Monatliche Werte zurücksetzen
    playerFinance.monthlyRevenue = 0
    playerFinance.monthlyExpenses = 0
end

-- Kreditwürdigkeit aktualisieren
function FinanceManager:UpdateCreditRating(playerId)
    local playerFinance = self.playerFinances[playerId]
    if not playerFinance then return end
    
    local debtToIncomeRatio = playerFinance.totalDebt / math.max(playerFinance.monthlyRevenue * 12, 1)
    local profitability = 0
    
    -- Durchschnittliche Profitabilität der letzten 6 Monate
    if #playerFinance.profitHistory >= 6 then
        local recentProfits = {}
        for i = #playerFinance.profitHistory - 5, #playerFinance.profitHistory do
            table.insert(recentProfits, playerFinance.profitHistory[i])
        end
        
        local sum = 0
        for _, profit in pairs(recentProfits) do
            sum = sum + profit
        end
        profitability = sum / #recentProfits
    end
    
    -- Kreditwürdigkeit bestimmen
    if debtToIncomeRatio < 0.3 and profitability > 100000 then
        playerFinance.creditRating = "excellent"
    elseif debtToIncomeRatio < 0.5 and profitability > 50000 then
        playerFinance.creditRating = "good"
    elseif debtToIncomeRatio < 0.7 and profitability > 0 then
        playerFinance.creditRating = "fair"
    elseif debtToIncomeRatio < 1.0 then
        playerFinance.creditRating = "poor"
    else
        playerFinance.creditRating = "bad"
    end
end

-- Einnahmen hinzufügen
function FinanceManager:AddRevenue(playerId, amount, source)
    local playerFinance = self.playerFinances[playerId]
    if not playerFinance then return end
    
    playerFinance.cash = playerFinance.cash + amount
    playerFinance.totalRevenue = playerFinance.totalRevenue + amount
    playerFinance.monthlyRevenue = playerFinance.monthlyRevenue + amount
    playerFinance.netWorth = playerFinance.netWorth + amount
end

-- Ausgaben hinzufügen
function FinanceManager:AddExpense(playerId, amount, source)
    local playerFinance = self.playerFinances[playerId]
    if not playerFinance then return end
    
    playerFinance.cash = playerFinance.cash - amount
    playerFinance.totalExpenses = playerFinance.totalExpenses + amount
    playerFinance.monthlyExpenses = playerFinance.monthlyExpenses + amount
    playerFinance.netWorth = playerFinance.netWorth - amount
end

-- Spieler-Finanzdaten abrufen
function FinanceManager:GetPlayerFinanceData(playerId)
    return self.playerFinances[playerId]
end

-- Marktbedingungen aktualisieren
function FinanceManager:UpdateMarketConditions()
    -- Zufällige Marktveränderungen
    self.marketConditions.economicGrowth = math.max(0.5, self.marketConditions.economicGrowth + (math.random() - 0.5) * 0.1)
    self.marketConditions.inflation = math.max(0, self.marketConditions.inflation + (math.random() - 0.5) * 0.01)
    self.marketConditions.interestRate = math.max(0.01, self.marketConditions.interestRate + (math.random() - 0.5) * 0.005)
    
    -- Rezession/Boom erkennen
    if self.marketConditions.economicGrowth < 0.8 then
        self.marketConditions.recession = true
        self.marketConditions.boom = false
    elseif self.marketConditions.economicGrowth > 1.3 then
        self.marketConditions.boom = true
        self.marketConditions.recession = false
    else
        self.marketConditions.recession = false
        self.marketConditions.boom = false
    end
end

return FinanceManager
