-- ServerScriptService/CooperationManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Kooperations-System für Spieler-Allianzen und gemeinsame Projekte

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local CooperationManager = {}
CooperationManager.__index = CooperationManager

function CooperationManager.new()
    local self = setmetatable({}, CooperationManager)
    
    -- Alliance system
    self.alliances = {}
    self.allianceInvites = {}
    self.maxAllianceSize = 4
    self.allianceCreationCost = 100000
    
    -- Cooperation projects
    self.cooperationProjects = {}
    self.projectTypes = {
        shared_infrastructure = {
            name = "Geteilte Infrastruktur",
            description = "Gemeinsamer Bau und Nutzung von Schienen/Straßen",
            minParticipants = 2,
            maxParticipants = 4,
            costSharing = true,
            revenueSharing = true
        },
        joint_venture = {
            name = "Joint Venture",
            description = "Gemeinsame Geschäftsprojekte mit geteilten Kosten und Gewinnen",
            minParticipants = 2,
            maxParticipants = 3,
            costSharing = true,
            revenueSharing = true
        },
        research_cooperation = {
            name = "Forschungskooperation",
            description = "Gemeinsame Technologie-Entwicklung",
            minParticipants = 2,
            maxParticipants = 6,
            costSharing = true,
            revenueSharing = false
        },
        market_agreement = {
            name = "Marktabkommen",
            description = "Aufteilung von Märkten und Routen",
            minParticipants = 2,
            maxParticipants = 8,
            costSharing = false,
            revenueSharing = false
        }
    }
    
    -- Trade system
    self.tradeOffers = {}
    self.tradeHistory = {}
    self.tradeTypes = {
        money = {name = "Geld", transferable = true},
        vehicles = {name = "Fahrzeuge", transferable = true},
        resources = {name = "Ressourcen", transferable = true},
        infrastructure = {name = "Infrastruktur", transferable = false}, -- Can only share usage rights
        technology = {name = "Technologie", transferable = true},
        routes = {name = "Routen", transferable = false} -- Can only share usage rights
    }
    
    -- Reputation system
    self.playerReputations = {}
    self.reputationFactors = {
        trade_completion = 5,
        alliance_loyalty = 10,
        project_contribution = 8,
        promise_keeping = 12,
        fair_dealing = 6
    }
    
    return self
end

-- Initialize player cooperation data
function CooperationManager:InitializePlayer(playerId)
    self.playerReputations[playerId] = {
        overall = 50, -- 0-100 scale
        tradeReliability = 50,
        allianceLoyalty = 50,
        cooperationWillingness = 50,
        
        -- History tracking
        tradesCompleted = 0,
        tradesCancelled = 0,
        alliancesJoined = 0,
        alliancesLeft = 0,
        projectsCompleted = 0,
        projectsAbandoned = 0,
        
        -- Relationships with other players
        relationships = {}
    }
end

-- Create alliance
function CooperationManager:CreateAlliance(creatorId, allianceName, settings)
    if not self.playerReputations[creatorId] then
        self:InitializePlayer(creatorId)
    end
    
    -- Check if player is already in alliance
    for _, alliance in pairs(self.alliances) do
        for _, memberId in pairs(alliance.members) do
            if memberId == creatorId then
                return false, "Already in alliance"
            end
        end
    end
    
    local allianceId = HttpService:GenerateGUID(false)
    self.alliances[allianceId] = {
        id = allianceId,
        name = allianceName,
        creator = creatorId,
        members = {creatorId},
        created = os.time(),
        
        -- Alliance settings
        settings = {
            maxMembers = math.min(settings.maxMembers or 4, self.maxAllianceSize),
            allowInvites = settings.allowInvites ~= false,
            shareRevenue = settings.shareRevenue or false,
            shareInfrastructure = settings.shareInfrastructure or false,
            democraticVoting = settings.democraticVoting or false,
            requireApproval = settings.requireApproval or true,
            kickThreshold = settings.kickThreshold or 0.6 -- Percentage of members needed to kick
        },
        
        -- Alliance data
        treasury = 0,
        sharedAssets = {
            infrastructure = {},
            vehicles = {},
            technology = {}
        },
        
        -- Alliance stats
        stats = {
            totalRevenue = 0,
            sharedProjects = 0,
            memberSatisfaction = 100,
            efficiency = 1.0
        },
        
        -- Governance
        votes = {},
        decisions = {}
    }
    
    print("🤝 Alliance created:", allianceName, "by player", creatorId)
    return true, allianceId
end

-- Invite player to alliance
function CooperationManager:InviteToAlliance(inviterId, targetId, allianceId)
    local alliance = self.alliances[allianceId]
    if not alliance then
        return false, "Alliance not found"
    end
    
    -- Check permissions
    local canInvite = false
    if alliance.creator == inviterId then
        canInvite = true
    elseif alliance.settings.allowInvites then
        for _, memberId in pairs(alliance.members) do
            if memberId == inviterId then
                canInvite = true
                break
            end
        end
    end
    
    if not canInvite then
        return false, "No permission to invite"
    end
    
    -- Check if target is already in an alliance
    for _, otherAlliance in pairs(self.alliances) do
        for _, memberId in pairs(otherAlliance.members) do
            if memberId == targetId then
                return false, "Player already in alliance"
            end
        end
    end
    
    -- Check max members
    if #alliance.members >= alliance.settings.maxMembers then
        return false, "Alliance full"
    end
    
    -- Create invite
    local inviteId = HttpService:GenerateGUID(false)
    self.allianceInvites[inviteId] = {
        id = inviteId,
        allianceId = allianceId,
        inviterId = inviterId,
        targetId = targetId,
        created = os.time(),
        expires = os.time() + 600, -- 10 minutes
        message = "Join our alliance: " .. alliance.name
    }
    
    return true, inviteId
end

-- Accept alliance invite
function CooperationManager:AcceptAllianceInvite(playerId, inviteId)
    local invite = self.allianceInvites[inviteId]
    if not invite then
        return false, "Invite not found or expired"
    end
    
    if invite.targetId ~= playerId then
        return false, "Not your invite"
    end
    
    if os.time() > invite.expires then
        self.allianceInvites[inviteId] = nil
        return false, "Invite expired"
    end
    
    local alliance = self.alliances[invite.allianceId]
    if not alliance then
        return false, "Alliance no longer exists"
    end
    
    -- Add to alliance
    table.insert(alliance.members, playerId)
    
    -- Update reputation
    if not self.playerReputations[playerId] then
        self:InitializePlayer(playerId)
    end
    self.playerReputations[playerId].alliancesJoined = self.playerReputations[playerId].alliancesJoined + 1
    
    -- Clean up invite
    self.allianceInvites[inviteId] = nil
    
    print("🤝 Player", playerId, "joined alliance:", alliance.name)
    return true, alliance.name
end

-- Leave alliance
function CooperationManager:LeaveAlliance(playerId, allianceId)
    local alliance = self.alliances[allianceId]
    if not alliance then
        return false, "Alliance not found"
    end
    
    -- Remove from members
    local memberIndex = nil
    for i, memberId in pairs(alliance.members) do
        if memberId == playerId then
            memberIndex = i
            break
        end
    end
    
    if not memberIndex then
        return false, "Not a member of this alliance"
    end
    
    table.remove(alliance.members, memberIndex)
    
    -- Update reputation (leaving alliance has small negative impact)
    if self.playerReputations[playerId] then
        self.playerReputations[playerId].alliancesLeft = self.playerReputations[playerId].alliancesLeft + 1
        self.playerReputations[playerId].allianceLoyalty = math.max(0, self.playerReputations[playerId].allianceLoyalty - 5)
    end
    
    -- If creator left, transfer leadership or dissolve
    if alliance.creator == playerId then
        if #alliance.members > 0 then
            alliance.creator = alliance.members[1] -- Transfer to first remaining member
            print("🤝 Alliance leadership transferred to:", alliance.creator)
        else
            self.alliances[allianceId] = nil -- Dissolve empty alliance
            print("🤝 Alliance dissolved:", alliance.name)
        end
    end
    
    return true, "Left alliance"
end

-- Create cooperation project
function CooperationManager:CreateCooperationProject(creatorId, projectType, projectData)
    local projectTypeData = self.projectTypes[projectType]
    if not projectTypeData then
        return false, "Invalid project type"
    end
    
    local projectId = HttpService:GenerateGUID(false)
    self.cooperationProjects[projectId] = {
        id = projectId,
        type = projectType,
        name = projectData.name or (projectTypeData.name .. " Project"),
        description = projectData.description or projectTypeData.description,
        creator = creatorId,
        participants = {creatorId},
        created = os.time(),
        
        -- Project settings
        settings = {
            minParticipants = projectTypeData.minParticipants,
            maxParticipants = projectTypeData.maxParticipants,
            costSharing = projectTypeData.costSharing,
            revenueSharing = projectTypeData.revenueSharing,
            autoAccept = projectData.autoAccept or false,
            requireApproval = projectData.requireApproval ~= false
        },
        
        -- Project data
        totalCost = projectData.totalCost or 0,
        currentFunding = 0,
        expectedRevenue = projectData.expectedRevenue or 0,
        
        -- Participant contributions
        contributions = {
            [creatorId] = {
                money = 0,
                resources = {},
                commitment = 100
            }
        },
        
        -- Project status
        status = "planning", -- planning, funding, active, completed, cancelled
        progress = 0,
        
        -- Invites
        pendingInvites = {}
    }
    
    print("🏗️ Cooperation project created:", projectData.name, "by player", creatorId)
    return true, projectId
end

-- Invite to cooperation project
function CooperationManager:InviteToProject(inviterId, targetId, projectId)
    local project = self.cooperationProjects[projectId]
    if not project then
        return false, "Project not found"
    end
    
    -- Check permissions
    local canInvite = false
    if project.creator == inviterId then
        canInvite = true
    else
        for _, participantId in pairs(project.participants) do
            if participantId == inviterId then
                canInvite = true
                break
            end
        end
    end
    
    if not canInvite then
        return false, "No permission to invite"
    end
    
    -- Check max participants
    if #project.participants >= project.settings.maxParticipants then
        return false, "Project full"
    end
    
    -- Check if already participant
    for _, participantId in pairs(project.participants) do
        if participantId == targetId then
            return false, "Already participant"
        end
    end
    
    -- Create invite
    local inviteId = HttpService:GenerateGUID(false)
    project.pendingInvites[inviteId] = {
        id = inviteId,
        inviterId = inviterId,
        targetId = targetId,
        created = os.time(),
        expires = os.time() + 600
    }
    
    return true, inviteId
end

-- Create trade offer
function CooperationManager:CreateTradeOffer(offererId, targetId, offer, request)
    local tradeId = HttpService:GenerateGUID(false)
    self.tradeOffers[tradeId] = {
        id = tradeId,
        offererId = offererId,
        targetId = targetId,
        created = os.time(),
        expires = os.time() + 1800, -- 30 minutes
        status = "pending",
        
        offer = offer, -- {money = 100000, vehicles = {...}, resources = {...}}
        request = request, -- What the offerer wants in return
        
        -- Trade metadata
        estimatedValue = self:CalculateTradeValue(offer),
        requestedValue = self:CalculateTradeValue(request),
        fairnessRating = 0 -- Calculated based on value difference
    }
    
    -- Calculate fairness rating
    local trade = self.tradeOffers[tradeId]
    if trade.requestedValue > 0 then
        trade.fairnessRating = trade.estimatedValue / trade.requestedValue
    end
    
    print("💰 Trade offer created by player", offererId, "to player", targetId)
    return true, tradeId
end

-- Accept trade offer
function CooperationManager:AcceptTradeOffer(playerId, tradeId)
    local trade = self.tradeOffers[tradeId]
    if not trade then
        return false, "Trade not found"
    end
    
    if trade.targetId ~= playerId then
        return false, "Not your trade"
    end
    
    if os.time() > trade.expires then
        self.tradeOffers[tradeId] = nil
        return false, "Trade expired"
    end
    
    if trade.status ~= "pending" then
        return false, "Trade no longer available"
    end
    
    -- Execute trade (this would integrate with other systems)
    trade.status = "completed"
    trade.completedAt = os.time()
    
    -- Update reputations
    self:UpdateTradeReputation(trade.offererId, true)
    self:UpdateTradeReputation(trade.targetId, true)
    
    -- Move to history
    table.insert(self.tradeHistory, trade)
    self.tradeOffers[tradeId] = nil
    
    print("💰 Trade completed between players", trade.offererId, "and", trade.targetId)
    return true, "Trade completed"
end

-- Calculate trade value (simplified)
function CooperationManager:CalculateTradeValue(tradeItems)
    local totalValue = 0
    
    if tradeItems.money then
        totalValue = totalValue + tradeItems.money
    end
    
    if tradeItems.vehicles then
        for _, vehicle in pairs(tradeItems.vehicles) do
            totalValue = totalValue + (vehicle.value or 100000)
        end
    end
    
    if tradeItems.resources then
        for resourceType, amount in pairs(tradeItems.resources) do
            local basePrice = 100 -- Base price per unit
            totalValue = totalValue + (amount * basePrice)
        end
    end
    
    return totalValue
end

-- Update trade reputation
function CooperationManager:UpdateTradeReputation(playerId, successful)
    if not self.playerReputations[playerId] then
        self:InitializePlayer(playerId)
    end
    
    local reputation = self.playerReputations[playerId]
    
    if successful then
        reputation.tradesCompleted = reputation.tradesCompleted + 1
        reputation.tradeReliability = math.min(100, reputation.tradeReliability + self.reputationFactors.trade_completion)
    else
        reputation.tradesCancelled = reputation.tradesCancelled + 1
        reputation.tradeReliability = math.max(0, reputation.tradeReliability - self.reputationFactors.trade_completion)
    end
    
    -- Update overall reputation
    self:UpdateOverallReputation(playerId)
end

-- Update overall reputation
function CooperationManager:UpdateOverallReputation(playerId)
    local reputation = self.playerReputations[playerId]
    if not reputation then return end
    
    -- Calculate weighted average of all reputation factors
    local totalWeight = 0
    local weightedSum = 0
    
    local factors = {
        {value = reputation.tradeReliability, weight = 0.3},
        {value = reputation.allianceLoyalty, weight = 0.4},
        {value = reputation.cooperationWillingness, weight = 0.3}
    }
    
    for _, factor in pairs(factors) do
        weightedSum = weightedSum + (factor.value * factor.weight)
        totalWeight = totalWeight + factor.weight
    end
    
    reputation.overall = weightedSum / totalWeight
end

-- Get player reputation
function CooperationManager:GetPlayerReputation(playerId)
    return self.playerReputations[playerId]
end

-- Get alliance info
function CooperationManager:GetAlliance(allianceId)
    return self.alliances[allianceId]
end

-- Get player's alliance
function CooperationManager:GetPlayerAlliance(playerId)
    for _, alliance in pairs(self.alliances) do
        for _, memberId in pairs(alliance.members) do
            if memberId == playerId then
                return alliance
            end
        end
    end
    return nil
end

-- Get cooperation projects
function CooperationManager:GetCooperationProjects()
    return self.cooperationProjects
end

-- Get trade offers for player
function CooperationManager:GetPlayerTradeOffers(playerId)
    local offers = {}
    
    for tradeId, trade in pairs(self.tradeOffers) do
        if trade.targetId == playerId or trade.offererId == playerId then
            table.insert(offers, trade)
        end
    end
    
    return offers
end

-- Clean up expired data
function CooperationManager:CleanupExpiredData()
    local currentTime = os.time()
    
    -- Clean expired invites
    for inviteId, invite in pairs(self.allianceInvites) do
        if currentTime > invite.expires then
            self.allianceInvites[inviteId] = nil
        end
    end
    
    -- Clean expired trades
    for tradeId, trade in pairs(self.tradeOffers) do
        if currentTime > trade.expires then
            -- Mark as expired and update reputation negatively
            self:UpdateTradeReputation(trade.offererId, false)
            self.tradeOffers[tradeId] = nil
        end
    end
    
    -- Clean expired project invites
    for projectId, project in pairs(self.cooperationProjects) do
        for inviteId, invite in pairs(project.pendingInvites) do
            if currentTime > invite.expires then
                project.pendingInvites[inviteId] = nil
            end
        end
    end
end

return CooperationManager
