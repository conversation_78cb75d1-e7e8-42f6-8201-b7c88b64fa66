-- ServerScriptService/Managers/VisualEffectsManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Visueller Effekte-Manager für Partikel und Beleuchtung

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Lighting = game:GetService("Lighting")
local TweenService = game:GetService("TweenService")

local VisualEffectsManager = {}
VisualEffectsManager.__index = VisualEffectsManager

function VisualEffectsManager.new()
    local self = setmetatable({}, VisualEffectsManager)
    
    -- Partikel-Effekte
    self.particleEffects = {
        smoke = {
            texture = "rbxasset://textures/particles/smoke_main.dds",
            lifetime = NumberRange.new(1.0, 3.0),
            rate = 50,
            color = ColorSequence.new(Color3.new(0.5, 0.5, 0.5))
        },
        
        sparks = {
            texture = "rbxasset://textures/particles/sparkles_main.dds",
            lifetime = NumberRange.new(0.5, 1.5),
            rate = 100,
            color = ColorSequence.new(Color3.new(1, 0.8, 0))
        },
        
        dust = {
            texture = "rbxasset://textures/particles/smoke_main.dds",
            lifetime = NumberRange.new(2.0, 4.0),
            rate = 25,
            color = ColorSequence.new(Color3.new(0.8, 0.7, 0.6))
        }
    }
    
    -- Beleuchtungs-Presets
    self.lightingPresets = {
        dawn = {
            brightness = 1,
            ambient = Color3.new(0.5, 0.5, 0.7),
            colorShift_Top = Color3.new(1, 0.9, 0.8),
            colorShift_Bottom = Color3.new(0.8, 0.8, 1),
            shadowSoftness = 0.2
        },
        
        day = {
            brightness = 2,
            ambient = Color3.new(0.7, 0.7, 0.7),
            colorShift_Top = Color3.new(1, 1, 1),
            colorShift_Bottom = Color3.new(0.9, 0.9, 1),
            shadowSoftness = 0.1
        },
        
        dusk = {
            brightness = 0.5,
            ambient = Color3.new(0.4, 0.4, 0.6),
            colorShift_Top = Color3.new(1, 0.7, 0.5),
            colorShift_Bottom = Color3.new(0.6, 0.6, 0.8),
            shadowSoftness = 0.3
        },
        
        night = {
            brightness = 0.1,
            ambient = Color3.new(0.2, 0.2, 0.4),
            colorShift_Top = Color3.new(0.5, 0.5, 0.8),
            colorShift_Bottom = Color3.new(0.3, 0.3, 0.6),
            shadowSoftness = 0.5
        }
    }
    
    -- Aktive Effekte
    self.activeEffects = {}
    
    -- Tageszeit-System
    self.timeOfDay = 12 -- 12:00 Uhr
    self.dayDuration = 1200 -- 20 Minuten pro Tag
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function VisualEffectsManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("CreateParticleEffectEvent") then
        local createParticleEvent = Instance.new("RemoteEvent")
        createParticleEvent.Name = "CreateParticleEffectEvent"
        createParticleEvent.Parent = Events
        
        createParticleEvent.OnServerEvent:Connect(function(player, effectType, position, duration)
            self:CreateParticleEffect(effectType, position, duration)
        end)
    end
end

-- Partikel-Effekt erstellen
function VisualEffectsManager:CreateParticleEffect(effectType, position, duration)
    local effectData = self.particleEffects[effectType]
    if not effectData then return end
    
    -- Attachment für Partikel erstellen
    local part = Instance.new("Part")
    part.Anchored = true
    part.CanCollide = false
    part.Transparency = 1
    part.Size = Vector3.new(1, 1, 1)
    part.Position = position
    part.Parent = workspace
    
    local attachment = Instance.new("Attachment")
    attachment.Parent = part
    
    -- Partikel-Emitter erstellen
    local emitter = Instance.new("ParticleEmitter")
    emitter.Texture = effectData.texture
    emitter.Lifetime = effectData.lifetime
    emitter.Rate = effectData.rate
    emitter.Color = effectData.color
    emitter.Parent = attachment
    
    -- Effekt zu aktiven Effekten hinzufügen
    local effectId = #self.activeEffects + 1
    self.activeEffects[effectId] = {
        part = part,
        emitter = emitter,
        startTime = tick(),
        duration = duration or 5
    }
    
    -- Effekt nach Ablauf entfernen
    spawn(function()
        wait(duration or 5)
        emitter.Enabled = false
        wait(effectData.lifetime.Max)
        part:Destroy()
        self.activeEffects[effectId] = nil
    end)
    
    return effectId
end

-- Fahrzeug-Rauch-Effekt
function VisualEffectsManager:CreateVehicleSmokeEffect(vehicle)
    if not vehicle or not vehicle:FindFirstChild("PrimaryPart") then return end
    
    local attachment = vehicle.PrimaryPart:FindFirstChild("SmokeAttachment")
    if not attachment then
        attachment = Instance.new("Attachment")
        attachment.Name = "SmokeAttachment"
        attachment.Position = Vector3.new(0, 2, -3) -- Hinten am Fahrzeug
        attachment.Parent = vehicle.PrimaryPart
    end
    
    local emitter = attachment:FindFirstChild("SmokeEmitter")
    if not emitter then
        emitter = Instance.new("ParticleEmitter")
        emitter.Name = "SmokeEmitter"
        emitter.Texture = self.particleEffects.smoke.texture
        emitter.Lifetime = self.particleEffects.smoke.lifetime
        emitter.Rate = self.particleEffects.smoke.rate
        emitter.Color = self.particleEffects.smoke.color
        emitter.VelocityInheritance = 0.5
        emitter.Parent = attachment
    end
    
    return emitter
end

-- Bau-Staub-Effekt
function VisualEffectsManager:CreateConstructionDustEffect(position, duration)
    return self:CreateParticleEffect("dust", position, duration or 10)
end

-- Beleuchtung ändern
function VisualEffectsManager:SetLightingPreset(presetName, transitionTime)
    local preset = self.lightingPresets[presetName]
    if not preset then return end
    
    transitionTime = transitionTime or 2
    
    -- Tween-Informationen
    local tweenInfo = TweenInfo.new(
        transitionTime,
        Enum.EasingStyle.Sine,
        Enum.EasingDirection.InOut
    )
    
    -- Beleuchtungs-Eigenschaften tweenen
    local lightingTween = TweenService:Create(Lighting, tweenInfo, preset)
    lightingTween:Play()
    
    print("💡 Beleuchtung geändert zu:", presetName)
end

-- Tageszeit aktualisieren
function VisualEffectsManager:UpdateTimeOfDay(deltaTime)
    -- Zeit voranschreiten
    self.timeOfDay = self.timeOfDay + (deltaTime * 24 / self.dayDuration)
    
    -- 24-Stunden-Zyklus
    if self.timeOfDay >= 24 then
        self.timeOfDay = self.timeOfDay - 24
    end
    
    -- Beleuchtung basierend auf Tageszeit
    local preset = self:GetLightingPresetForTime(self.timeOfDay)
    if preset then
        self:SetLightingPreset(preset, 1)
    end
    
    -- Roblox Lighting ClockTime aktualisieren
    Lighting.ClockTime = self.timeOfDay
end

-- Beleuchtungs-Preset für Tageszeit ermitteln
function VisualEffectsManager:GetLightingPresetForTime(timeOfDay)
    if timeOfDay >= 5 and timeOfDay < 7 then
        return "dawn"
    elseif timeOfDay >= 7 and timeOfDay < 18 then
        return "day"
    elseif timeOfDay >= 18 and timeOfDay < 20 then
        return "dusk"
    else
        return "night"
    end
end

-- Wetter-Effekte
function VisualEffectsManager:CreateWeatherEffect(weatherType, intensity)
    intensity = intensity or 0.5
    
    if weatherType == "rain" then
        self:CreateRainEffect(intensity)
    elseif weatherType == "snow" then
        self:CreateSnowEffect(intensity)
    elseif weatherType == "fog" then
        self:CreateFogEffect(intensity)
    end
end

-- Regen-Effekt
function VisualEffectsManager:CreateRainEffect(intensity)
    -- Globaler Regen-Emitter
    local rainPart = workspace:FindFirstChild("RainPart")
    if not rainPart then
        rainPart = Instance.new("Part")
        rainPart.Name = "RainPart"
        rainPart.Anchored = true
        rainPart.CanCollide = false
        rainPart.Transparency = 1
        rainPart.Size = Vector3.new(1000, 1, 1000)
        rainPart.Position = Vector3.new(0, 200, 0)
        rainPart.Parent = workspace
        
        local attachment = Instance.new("Attachment")
        attachment.Parent = rainPart
        
        local rainEmitter = Instance.new("ParticleEmitter")
        rainEmitter.Name = "RainEmitter"
        rainEmitter.Texture = "rbxasset://textures/particles/smoke_main.dds"
        rainEmitter.Lifetime = NumberRange.new(2, 4)
        rainEmitter.Rate = 1000 * intensity
        rainEmitter.Color = ColorSequence.new(Color3.new(0.7, 0.8, 1))
        rainEmitter.Size = NumberSequence.new(0.1)
        rainEmitter.Acceleration = Vector3.new(0, -50, 0)
        rainEmitter.Parent = attachment
    end
    
    -- Beleuchtung für Regen anpassen
    Lighting.Brightness = 1 * (1 - intensity * 0.5)
    Lighting.Ambient = Color3.new(0.4, 0.4, 0.5)
end

-- Schnee-Effekt
function VisualEffectsManager:CreateSnowEffect(intensity)
    local snowPart = workspace:FindFirstChild("SnowPart")
    if not snowPart then
        snowPart = Instance.new("Part")
        snowPart.Name = "SnowPart"
        snowPart.Anchored = true
        snowPart.CanCollide = false
        snowPart.Transparency = 1
        snowPart.Size = Vector3.new(1000, 1, 1000)
        snowPart.Position = Vector3.new(0, 200, 0)
        snowPart.Parent = workspace
        
        local attachment = Instance.new("Attachment")
        attachment.Parent = snowPart
        
        local snowEmitter = Instance.new("ParticleEmitter")
        snowEmitter.Name = "SnowEmitter"
        snowEmitter.Texture = "rbxasset://textures/particles/sparkles_main.dds"
        snowEmitter.Lifetime = NumberRange.new(5, 8)
        snowEmitter.Rate = 500 * intensity
        snowEmitter.Color = ColorSequence.new(Color3.new(1, 1, 1))
        snowEmitter.Size = NumberSequence.new(0.2)
        snowEmitter.Acceleration = Vector3.new(0, -10, 0)
        snowEmitter.Parent = attachment
    end
end

-- Nebel-Effekt
function VisualEffectsManager:CreateFogEffect(intensity)
    Lighting.FogEnd = 500 * (1 - intensity)
    Lighting.FogStart = 0
    Lighting.FogColor = Color3.new(0.8, 0.8, 0.9)
end

-- Wetter-Effekte entfernen
function VisualEffectsManager:ClearWeatherEffects()
    -- Regen entfernen
    local rainPart = workspace:FindFirstChild("RainPart")
    if rainPart then rainPart:Destroy() end
    
    -- Schnee entfernen
    local snowPart = workspace:FindFirstChild("SnowPart")
    if snowPart then snowPart:Destroy() end
    
    -- Nebel entfernen
    Lighting.FogEnd = 100000
    Lighting.FogStart = 0
    
    -- Standard-Beleuchtung wiederherstellen
    self:SetLightingPreset("day", 2)
end

-- Update-Funktion
function VisualEffectsManager:Update(deltaTime, gameState)
    -- Tageszeit aktualisieren
    self:UpdateTimeOfDay(deltaTime)
    
    -- Aktive Effekte bereinigen
    for effectId, effect in pairs(self.activeEffects) do
        if tick() - effect.startTime > effect.duration then
            if effect.part and effect.part.Parent then
                effect.part:Destroy()
            end
            self.activeEffects[effectId] = nil
        end
    end
end

return VisualEffectsManager
