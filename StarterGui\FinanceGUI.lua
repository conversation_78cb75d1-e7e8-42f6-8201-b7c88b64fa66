-- StarterGui/FinanceGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Vollständiges Finanz-Management GUI

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetFinancialSummaryEvent = Events:WaitForChild("GetFinancialSummaryEvent")
local FinancialSummaryResponseEvent = Events:WaitForChild("FinancialSummaryResponseEvent")
local ApplyForLoanEvent = Events:WaitForChild("ApplyForLoanEvent")
local LoanApplicationResponseEvent = Events:WaitForChild("LoanApplicationResponseEvent")
local GetAICompetitorsEvent = Events:WaitForChild("GetAICompetitorsEvent")
local AICompetitorsResponseEvent = Events:WaitForChild("AICompetitorsResponseEvent")
local GetMarketOverviewEvent = Events:WaitForChild("GetMarketOverviewEvent")
local MarketOverviewResponseEvent = Events:WaitForChild("MarketOverviewResponseEvent")
local NotificationEvent = Events:WaitForChild("NotificationEvent")

local FinanceGUI = {}

-- Current data
local currentFinancialData = nil
local currentCompetitorData = nil
local currentMarketData = nil

-- Create main GUI
function FinanceGUI.CreateGUI()
    -- Main ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "FinanceGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Main Frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 1000, 0, 700)
    mainFrame.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    -- Corner rounding
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame
    
    -- Title Bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 50)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = mainFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleBar
    
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, -100, 1, 0)
    title.Position = UDim2.new(0, 20, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "💰 Finanz-Management"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.GothamBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Close Button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- Tab System
    local tabFrame = Instance.new("Frame")
    tabFrame.Name = "TabFrame"
    tabFrame.Size = UDim2.new(1, 0, 0, 40)
    tabFrame.Position = UDim2.new(0, 0, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    -- Content Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Name = "ContentFrame"
    contentFrame.Size = UDim2.new(1, -20, 1, -120)
    contentFrame.Position = UDim2.new(0, 10, 0, 110)
    contentFrame.BackgroundTransparency = 1
    contentFrame.Parent = mainFrame
    
    -- Create tabs
    FinanceGUI.CreateTabs(tabFrame, contentFrame)
    
    -- Event connections
    closeButton.MouseButton1Click:Connect(function()
        FinanceGUI.HideGUI()
    end)
    
    return screenGui
end

-- Create tab system
function FinanceGUI.CreateTabs(tabFrame, contentFrame)
    local tabs = {
        {name = "Übersicht", icon = "📊"},
        {name = "Kredite", icon = "🏦"},
        {name = "Konkurrenz", icon = "🤖"},
        {name = "Markt", icon = "📈"}
    }
    
    local tabButtons = {}
    local tabContents = {}
    
    for i, tab in pairs(tabs) do
        -- Tab Button
        local tabButton = Instance.new("TextButton")
        tabButton.Name = tab.name .. "Tab"
        tabButton.Size = UDim2.new(0.25, -5, 1, 0)
        tabButton.Position = UDim2.new((i-1) * 0.25, 2.5, 0, 0)
        tabButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
        tabButton.BorderSizePixel = 0
        tabButton.Text = tab.icon .. " " .. tab.name
        tabButton.TextColor3 = Color3.fromRGB(200, 200, 200)
        tabButton.TextScaled = true
        tabButton.Font = Enum.Font.Gotham
        tabButton.Parent = tabFrame
        
        local tabCorner = Instance.new("UICorner")
        tabCorner.CornerRadius = UDim.new(0, 6)
        tabCorner.Parent = tabButton
        
        -- Tab Content
        local tabContent = Instance.new("ScrollingFrame")
        tabContent.Name = tab.name .. "Content"
        tabContent.Size = UDim2.new(1, 0, 1, 0)
        tabContent.Position = UDim2.new(0, 0, 0, 0)
        tabContent.BackgroundTransparency = 1
        tabContent.BorderSizePixel = 0
        tabContent.ScrollBarThickness = 8
        tabContent.ScrollBarImageColor3 = Color3.fromRGB(100, 100, 100)
        tabContent.CanvasSize = UDim2.new(0, 0, 0, 0)
        tabContent.Visible = (i == 1) -- Show first tab by default
        tabContent.Parent = contentFrame
        
        local listLayout = Instance.new("UIListLayout")
        listLayout.SortOrder = Enum.SortOrder.LayoutOrder
        listLayout.Padding = UDim.new(0, 10)
        listLayout.Parent = tabContent
        
        tabButtons[i] = tabButton
        tabContents[i] = tabContent
        
        -- Tab click handler
        tabButton.MouseButton1Click:Connect(function()
            FinanceGUI.SwitchTab(i, tabButtons, tabContents)
            
            -- Load data for specific tabs
            if tab.name == "Übersicht" then
                FinanceGUI.LoadFinancialData()
            elseif tab.name == "Konkurrenz" then
                FinanceGUI.LoadCompetitorData()
            elseif tab.name == "Markt" then
                FinanceGUI.LoadMarketData()
            end
        end)
    end
    
    -- Create tab content
    FinanceGUI.CreateOverviewTab(tabContents[1])
    FinanceGUI.CreateLoanTab(tabContents[2])
    FinanceGUI.CreateCompetitorTab(tabContents[3])
    FinanceGUI.CreateMarketTab(tabContents[4])
    
    -- Load initial data
    FinanceGUI.LoadFinancialData()
end

-- Switch between tabs
function FinanceGUI.SwitchTab(activeIndex, tabButtons, tabContents)
    for i, button in pairs(tabButtons) do
        if i == activeIndex then
            button.BackgroundColor3 = Color3.fromRGB(80, 120, 200)
            button.TextColor3 = Color3.fromRGB(255, 255, 255)
            tabContents[i].Visible = true
        else
            button.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
            button.TextColor3 = Color3.fromRGB(200, 200, 200)
            tabContents[i].Visible = false
        end
    end
end

-- Create overview tab
function FinanceGUI.CreateOverviewTab(parent)
    -- Financial summary will be populated dynamically
    local summaryLabel = Instance.new("TextLabel")
    summaryLabel.Name = "SummaryLabel"
    summaryLabel.Size = UDim2.new(1, 0, 0, 50)
    summaryLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    summaryLabel.BorderSizePixel = 0
    summaryLabel.Text = "Lade Finanzdaten..."
    summaryLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    summaryLabel.TextScaled = true
    summaryLabel.Font = Enum.Font.Gotham
    summaryLabel.Parent = parent
    
    local summaryCorner = Instance.new("UICorner")
    summaryCorner.CornerRadius = UDim.new(0, 8)
    summaryCorner.Parent = summaryLabel
end

-- Create loan tab
function FinanceGUI.CreateLoanTab(parent)
    local loanLabel = Instance.new("TextLabel")
    loanLabel.Name = "LoanLabel"
    loanLabel.Size = UDim2.new(1, 0, 0, 50)
    loanLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    loanLabel.BorderSizePixel = 0
    loanLabel.Text = "🏦 Verfügbare Kredite"
    loanLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    loanLabel.TextScaled = true
    loanLabel.Font = Enum.Font.GothamBold
    loanLabel.Parent = parent
    
    local loanCorner = Instance.new("UICorner")
    loanCorner.CornerRadius = UDim.new(0, 8)
    loanCorner.Parent = loanLabel
end

-- Create competitor tab
function FinanceGUI.CreateCompetitorTab(parent)
    local competitorLabel = Instance.new("TextLabel")
    competitorLabel.Name = "CompetitorLabel"
    competitorLabel.Size = UDim2.new(1, 0, 0, 50)
    competitorLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    competitorLabel.BorderSizePixel = 0
    competitorLabel.Text = "🤖 KI-Konkurrenten"
    competitorLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    competitorLabel.TextScaled = true
    competitorLabel.Font = Enum.Font.GothamBold
    competitorLabel.Parent = parent
end

-- Create market tab
function FinanceGUI.CreateMarketTab(parent)
    local marketLabel = Instance.new("TextLabel")
    marketLabel.Name = "MarketLabel"
    marketLabel.Size = UDim2.new(1, 0, 0, 50)
    marketLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    marketLabel.BorderSizePixel = 0
    marketLabel.Text = "📈 Markt-Übersicht"
    marketLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    marketLabel.TextScaled = true
    marketLabel.Font = Enum.Font.GothamBold
    marketLabel.Parent = parent
end

-- Load financial data
function FinanceGUI.LoadFinancialData()
    GetFinancialSummaryEvent:FireServer()
end

-- Load competitor data
function FinanceGUI.LoadCompetitorData()
    GetAICompetitorsEvent:FireServer()
end

-- Load market data
function FinanceGUI.LoadMarketData()
    GetMarketOverviewEvent:FireServer()
end

-- Show GUI
function FinanceGUI.ShowGUI()
    local gui = playerGui:FindFirstChild("FinanceGUI")
    if not gui then
        gui = FinanceGUI.CreateGUI()
    end
    
    local mainFrame = gui:FindFirstChild("MainFrame")
    if mainFrame then
        mainFrame.Visible = true
        FinanceGUI.LoadFinancialData()
        
        -- Animate in
        mainFrame.Size = UDim2.new(0, 0, 0, 0)
        local tween = TweenService:Create(mainFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
            Size = UDim2.new(0, 1000, 0, 700)
        })
        tween:Play()
    end
end

-- Hide GUI
function FinanceGUI.HideGUI()
    local gui = playerGui:FindFirstChild("FinanceGUI")
    if gui then
        local mainFrame = gui:FindFirstChild("MainFrame")
        if mainFrame then
            local tween = TweenService:Create(mainFrame, TweenInfo.new(0.2, Enum.EasingStyle.Quad), {
                Size = UDim2.new(0, 0, 0, 0)
            })
            tween:Play()
            tween.Completed:Connect(function()
                mainFrame.Visible = false
            end)
        end
    end
end

-- Event handlers
FinancialSummaryResponseEvent.OnClientEvent:Connect(function(financialData)
    currentFinancialData = financialData
    FinanceGUI.UpdateFinancialDisplay()
end)

AICompetitorsResponseEvent.OnClientEvent:Connect(function(competitorData)
    currentCompetitorData = competitorData
    FinanceGUI.UpdateCompetitorDisplay()
end)

MarketOverviewResponseEvent.OnClientEvent:Connect(function(marketData)
    currentMarketData = marketData
    FinanceGUI.UpdateMarketDisplay()
end)

-- Update displays (placeholder functions)
function FinanceGUI.UpdateFinancialDisplay()
    if not currentFinancialData then return end
    
    local gui = playerGui:FindFirstChild("FinanceGUI")
    if not gui then return end
    
    local summaryLabel = gui:FindFirstChild("MainFrame"):FindFirstChild("ContentFrame"):FindFirstChild("ÜbersichtContent"):FindFirstChild("SummaryLabel")
    if summaryLabel then
        summaryLabel.Text = string.format("💰 Bargeld: %s | 📈 Monatsumsatz: %s | 💸 Ausgaben: %s | 📊 Gewinn: %s", 
            FinanceGUI.FormatCurrency(currentFinancialData.cash or 0),
            FinanceGUI.FormatCurrency(currentFinancialData.monthlyRevenue or 0),
            FinanceGUI.FormatCurrency(currentFinancialData.monthlyExpenses or 0),
            FinanceGUI.FormatCurrency(currentFinancialData.monthlyProfit or 0)
        )
    end
end

function FinanceGUI.UpdateCompetitorDisplay()
    -- Placeholder
end

function FinanceGUI.UpdateMarketDisplay()
    -- Placeholder
end

-- Format currency
function FinanceGUI.FormatCurrency(amount)
    if amount >= 1000000 then
        return string.format("%.1fM €", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.0fk €", amount / 1000)
    else
        return string.format("%.0f €", amount)
    end
end

-- Hotkey to toggle GUI
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.F then -- F key for Finance
        local gui = playerGui:FindFirstChild("FinanceGUI")
        if gui and gui:FindFirstChild("MainFrame").Visible then
            FinanceGUI.HideGUI()
        else
            FinanceGUI.ShowGUI()
        end
    end
end)

return FinanceGUI
