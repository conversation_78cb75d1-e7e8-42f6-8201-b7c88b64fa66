-- StarterPlayerScripts/GUI/FinanceGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- Finanz-Übersicht GUI wie in Transport Fever 2

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local GetPlayerDataFunction = Events:WaitForChild("GetPlayerDataFunction")

local FinanceGUI = {}
FinanceGUI.IsOpen = false
FinanceGUI.CurrentTab = "OVERVIEW" -- OVERVIEW, INCOME, EXPENSES, LOANS
FinanceGUI.FinanceData = {}

-- GUI erstellen
function FinanceGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "FinanceGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1000, 0, 700)
    mainFrame.Position = UDim2.new(0.5, -500, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "💰 FINANZ-ÜBERSICHT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Geld-Anzeige (oben rechts)
    local moneyDisplay = Instance.new("Frame")
    moneyDisplay.Size = UDim2.new(0, 200, 0, 40)
    moneyDisplay.Position = UDim2.new(1, -250, 0, 10)
    moneyDisplay.BackgroundColor3 = Color3.fromRGB(0, 100, 0)
    moneyDisplay.BorderSizePixel = 0
    moneyDisplay.Parent = mainFrame
    
    local moneyCorner = Instance.new("UICorner")
    moneyCorner.CornerRadius = UDim.new(0, 8)
    moneyCorner.Parent = moneyDisplay
    
    local moneyLabel = Instance.new("TextLabel")
    moneyLabel.Size = UDim2.new(1, 0, 1, 0)
    moneyLabel.BackgroundTransparency = 1
    moneyLabel.Text = "💰 $1,000,000"
    moneyLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    moneyLabel.TextScaled = true
    moneyLabel.Font = Enum.Font.SourceSansBold
    moneyLabel.Parent = moneyDisplay
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 50)
    tabFrame.Position = UDim2.new(0, 10, 0, 60)
    tabFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 8)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "OVERVIEW", text = "📊 Übersicht", icon = "📊"},
        {name = "INCOME", text = "📈 Einnahmen", icon = "📈"},
        {name = "EXPENSES", text = "📉 Ausgaben", icon = "📉"},
        {name = "LOANS", text = "🏦 Kredite", icon = "🏦"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -5, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(40, 45, 50)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 570)
    contentFrame.Position = UDim2.new(0, 10, 0, 120)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 8)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    self.MoneyLabel = moneyLabel
    
    return screenGui
end

-- Tab wechseln
function FinanceGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function FinanceGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
        end
    end
end

-- Content aktualisieren
function FinanceGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "OVERVIEW" then
        self:CreateOverviewContent()
    elseif self.CurrentTab == "INCOME" then
        self:CreateIncomeContent()
    elseif self.CurrentTab == "EXPENSES" then
        self:CreateExpensesContent()
    elseif self.CurrentTab == "LOANS" then
        self:CreateLoansContent()
    end
end

-- Übersicht-Content erstellen
function FinanceGUI:CreateOverviewContent()
    -- Finanz-Karten
    local cards = {
        {title = "💰 Aktuelles Geld", value = "$1,234,567", color = Color3.fromRGB(0, 150, 0)},
        {title = "📈 Monatliche Einnahmen", value = "$45,678", color = Color3.fromRGB(0, 100, 200)},
        {title = "📉 Monatliche Ausgaben", value = "$23,456", color = Color3.fromRGB(200, 100, 0)},
        {title = "💹 Monatlicher Gewinn", value = "$22,222", color = Color3.fromRGB(100, 200, 100)}
    }
    
    for i, card in ipairs(cards) do
        local cardFrame = Instance.new("Frame")
        cardFrame.Size = UDim2.new(0.48, 0, 0.2, 0)
        cardFrame.Position = UDim2.new(((i-1) % 2) * 0.52, 0, math.floor((i-1) / 2) * 0.25, 10)
        cardFrame.BackgroundColor3 = card.color
        cardFrame.BorderSizePixel = 0
        cardFrame.Parent = self.ContentFrame
        
        local cardCorner = Instance.new("UICorner")
        cardCorner.CornerRadius = UDim.new(0, 10)
        cardCorner.Parent = cardFrame
        
        local titleLabel = Instance.new("TextLabel")
        titleLabel.Size = UDim2.new(1, -20, 0.5, 0)
        titleLabel.Position = UDim2.new(0, 10, 0, 10)
        titleLabel.BackgroundTransparency = 1
        titleLabel.Text = card.title
        titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        titleLabel.TextScaled = true
        titleLabel.Font = Enum.Font.SourceSans
        titleLabel.TextXAlignment = Enum.TextXAlignment.Left
        titleLabel.Parent = cardFrame
        
        local valueLabel = Instance.new("TextLabel")
        valueLabel.Size = UDim2.new(1, -20, 0.5, 0)
        valueLabel.Position = UDim2.new(0, 10, 0.5, 0)
        valueLabel.BackgroundTransparency = 1
        valueLabel.Text = card.value
        valueLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        valueLabel.TextScaled = true
        valueLabel.Font = Enum.Font.SourceSansBold
        valueLabel.TextXAlignment = Enum.TextXAlignment.Left
        valueLabel.Parent = cardFrame
    end
    
    -- Finanz-Verlauf (vereinfacht)
    local historyFrame = Instance.new("Frame")
    historyFrame.Size = UDim2.new(1, -20, 0.45, 0)
    historyFrame.Position = UDim2.new(0, 10, 0.55, 0)
    historyFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    historyFrame.BorderSizePixel = 0
    historyFrame.Parent = self.ContentFrame
    
    local historyCorner = Instance.new("UICorner")
    historyCorner.CornerRadius = UDim.new(0, 8)
    historyCorner.Parent = historyFrame
    
    local historyTitle = Instance.new("TextLabel")
    historyTitle.Size = UDim2.new(1, 0, 0, 40)
    historyTitle.BackgroundTransparency = 1
    historyTitle.Text = "📊 FINANZ-VERLAUF (LETZTE 12 MONATE)"
    historyTitle.TextColor3 = Color3.fromRGB(255, 255, 255)
    historyTitle.TextScaled = true
    historyTitle.Font = Enum.Font.SourceSansBold
    historyTitle.Parent = historyFrame
    
    -- Vereinfachtes Diagramm
    local chartFrame = Instance.new("Frame")
    chartFrame.Size = UDim2.new(1, -20, 1, -50)
    chartFrame.Position = UDim2.new(0, 10, 0, 45)
    chartFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    chartFrame.BorderSizePixel = 0
    chartFrame.Parent = historyFrame
    
    local chartCorner = Instance.new("UICorner")
    chartCorner.CornerRadius = UDim.new(0, 5)
    chartCorner.Parent = chartFrame
    
    -- Balken für jeden Monat
    for i = 1, 12 do
        local bar = Instance.new("Frame")
        bar.Size = UDim2.new(1/12, -2, math.random(20, 80)/100, 0)
        bar.Position = UDim2.new((i-1)/12, 1, 1, 0)
        bar.AnchorPoint = Vector2.new(0, 1)
        bar.BackgroundColor3 = i % 2 == 0 and Color3.fromRGB(0, 150, 0) or Color3.fromRGB(0, 100, 200)
        bar.BorderSizePixel = 0
        bar.Parent = chartFrame
        
        local barCorner = Instance.new("UICorner")
        barCorner.CornerRadius = UDim.new(0, 2)
        barCorner.Parent = bar
    end
end

-- Einnahmen-Content erstellen
function FinanceGUI:CreateIncomeContent()
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = self.ContentFrame
    
    -- Header
    local header = Instance.new("Frame")
    header.Size = UDim2.new(1, -10, 0, 40)
    header.Position = UDim2.new(0, 5, 0, 10)
    header.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    header.BorderSizePixel = 0
    header.Parent = scrollFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 5)
    headerCorner.Parent = header
    
    -- Header-Labels
    local headerLabels = {"Quelle", "Betrag", "Typ", "Trend"}
    for i, label in ipairs(headerLabels) do
        local headerLabel = Instance.new("TextLabel")
        headerLabel.Size = UDim2.new(0.25, 0, 1, 0)
        headerLabel.Position = UDim2.new((i-1) * 0.25, 0, 0, 0)
        headerLabel.BackgroundTransparency = 1
        headerLabel.Text = label
        headerLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        headerLabel.TextScaled = true
        headerLabel.Font = Enum.Font.SourceSansBold
        headerLabel.Parent = header
    end
    
    -- Einnahmen-Einträge
    local incomeEntries = {
        {source = "🚂 Passagier-Transport", amount = "$15,234", type = "Transport", trend = "+5%"},
        {source = "📦 Cargo-Transport", amount = "$12,456", type = "Transport", trend = "+8%"},
        {source = "🏭 Industrie-Verträge", amount = "$8,789", type = "Verträge", trend = "+2%"},
        {source = "🏘️ Stadt-Subventionen", amount = "$5,432", type = "Subventionen", trend = "-1%"},
        {source = "💰 Investitionen", amount = "$3,567", type = "Zinsen", trend = "+12%"}
    }
    
    local yPos = 60
    for _, entry in ipairs(incomeEntries) do
        local entryFrame = self:CreateIncomeEntry(scrollFrame, entry, yPos)
        yPos = yPos + 50
    end
    
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos + 20)
end

-- Einnahmen-Eintrag erstellen
function FinanceGUI:CreateIncomeEntry(parent, entry, yPos)
    local entryFrame = Instance.new("Frame")
    entryFrame.Size = UDim2.new(1, -10, 0, 40)
    entryFrame.Position = UDim2.new(0, 5, 0, yPos)
    entryFrame.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    entryFrame.BorderSizePixel = 0
    entryFrame.Parent = parent
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 5)
    entryCorner.Parent = entryFrame
    
    -- Quelle
    local sourceLabel = Instance.new("TextLabel")
    sourceLabel.Size = UDim2.new(0.25, 0, 1, 0)
    sourceLabel.Position = UDim2.new(0, 0, 0, 0)
    sourceLabel.BackgroundTransparency = 1
    sourceLabel.Text = entry.source
    sourceLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    sourceLabel.TextScaled = true
    sourceLabel.Font = Enum.Font.SourceSans
    sourceLabel.TextXAlignment = Enum.TextXAlignment.Left
    sourceLabel.Parent = entryFrame
    
    -- Betrag
    local amountLabel = Instance.new("TextLabel")
    amountLabel.Size = UDim2.new(0.25, 0, 1, 0)
    amountLabel.Position = UDim2.new(0.25, 0, 0, 0)
    amountLabel.BackgroundTransparency = 1
    amountLabel.Text = entry.amount
    amountLabel.TextColor3 = Color3.fromRGB(0, 255, 0)
    amountLabel.TextScaled = true
    amountLabel.Font = Enum.Font.SourceSansBold
    amountLabel.Parent = entryFrame
    
    -- Typ
    local typeLabel = Instance.new("TextLabel")
    typeLabel.Size = UDim2.new(0.25, 0, 1, 0)
    typeLabel.Position = UDim2.new(0.5, 0, 0, 0)
    typeLabel.BackgroundTransparency = 1
    typeLabel.Text = entry.type
    typeLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    typeLabel.TextScaled = true
    typeLabel.Font = Enum.Font.SourceSans
    typeLabel.Parent = entryFrame
    
    -- Trend
    local trendLabel = Instance.new("TextLabel")
    trendLabel.Size = UDim2.new(0.25, 0, 1, 0)
    trendLabel.Position = UDim2.new(0.75, 0, 0, 0)
    trendLabel.BackgroundTransparency = 1
    trendLabel.Text = entry.trend
    trendLabel.TextColor3 = entry.trend:find("+") and Color3.fromRGB(0, 255, 0) or Color3.fromRGB(255, 0, 0)
    trendLabel.TextScaled = true
    trendLabel.Font = Enum.Font.SourceSansBold
    trendLabel.Parent = entryFrame
    
    return entryFrame
end

-- Ausgaben-Content erstellen
function FinanceGUI:CreateExpensesContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "📉 AUSGABEN\n\nDetaillierte Aufschlüsselung\naller monatlichen Ausgaben."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Kredite-Content erstellen
function FinanceGUI:CreateLoansContent()
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🏦 KREDITE\n\nKredit-Management\nund Finanzierungsoptionen."
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = self.ContentFrame
end

-- Finanz-Daten laden
function FinanceGUI:LoadFinanceData()
    local success, data = pcall(function()
        return GetPlayerDataFunction:InvokeServer()
    end)
    
    if success and data then
        self.FinanceData = data
        
        -- Geld-Anzeige aktualisieren
        if self.MoneyLabel and data.money then
            self.MoneyLabel.Text = "💰 $" .. string.format("%,d", data.money):gsub(",", ".")
        end
        
        self:UpdateContent()
    else
        warn("Fehler beim Laden der Finanz-Daten")
    end
end

-- GUI öffnen
function FinanceGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end
    
    self:LoadFinanceData()
    self.MainFrame.Visible = true
    self.IsOpen = true
    
    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function FinanceGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()
        
        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.G then -- Geändert von F zu G (F ist für Terraforming)
        if FinanceGUI.IsOpen then
            FinanceGUI:CloseGUI()
        else
            FinanceGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function FinanceGUI:Initialize()
    print("💰 FinanceGUI initialisiert - Drücke 'G' zum Öffnen")
end

-- Auto-Start
FinanceGUI:Initialize()

return FinanceGUI
