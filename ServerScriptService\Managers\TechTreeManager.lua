-- ServerScriptService/Managers/TechTreeManager.lua
-- Technologie-Baum und Forschungs-System
-- ROBLOX SCRIPT TYPE: ModuleScript

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local TechTreeManager = {}
TechTreeManager.__index = TechTreeManager

-- Initialisierung
function TechTreeManager.new()
    local self = setmetatable({}, TechTreeManager)
    
    self.Technologies = {}
    self.PlayerTech = {}  -- Gespeicherte Technologien pro Spieler
    self.ResearchQueue = {}  -- Aktuelle Forschung pro Spieler
    
    self:InitializeTechTree()
    
    return self
end

-- Technologie-Baum initialisieren
function TechTreeManager:InitializeTechTree()
    self.Technologies = {
        -- Grundlegende Technologien (1850)
        ["Basic_Mining"] = {
            Name = "Grundlegender Bergbau",
            Description = "Einfache Bergbautechniken mit Handwerkzeugen",
            Year = 1850,
            Cost = 0,
            ResearchTime = 0,
            Prerequisites = {},
            Unlocks = {"COAL_MINE_BASIC", "IRON_MINE_BASIC"},
            Category = "Mining"
        },
        
        ["Brick_Construction"] = {
            Name = "Backsteinbau",
            Description = "Fortgeschrittene Bautechniken mit gebrannten Ziegeln",
            Year = 1860,
            Cost = 500,
            ResearchTime = 30,
            Prerequisites = {},
            Unlocks = {"MEDIUM_HOUSE_VICTORIAN", "BRICK_BUILDINGS"},
            Category = "Construction"
        },
        
        ["Steel_Production"] = {
            Name = "Stahlproduktion",
            Description = "Hochofentechnologie zur Stahlherstellung",
            Year = 1870,
            Cost = 2000,
            ResearchTime = 60,
            Prerequisites = {"Basic_Mining"},
            Unlocks = {"STEEL_MILL_BASIC"},
            Category = "Industry"
        },
        
        -- Verbesserte Technologien (1880-1920)
        ["Improved_Mining"] = {
            Name = "Verbesserter Bergbau",
            Description = "Stahlverstärkte Strukturen und bessere Werkzeuge",
            Year = 1880,
            Cost = 3000,
            ResearchTime = 45,
            Prerequisites = {"Basic_Mining", "Steel_Production"},
            Unlocks = {"COAL_MINE_IMPROVED", "IRON_MINE_IMPROVED"},
            Category = "Mining"
        },
        
        ["Improved_Steel_Production"] = {
            Name = "Verbesserte Stahlproduktion",
            Description = "Effizientere Hochöfen und bessere Legierungen",
            Year = 1900,
            Cost = 5000,
            ResearchTime = 90,
            Prerequisites = {"Steel_Production"},
            Unlocks = {"STEEL_MILL_IMPROVED"},
            Category = "Industry"
        },
        
        ["Electricity"] = {
            Name = "Elektrizität",
            Description = "Elektrische Energie für Industrie und Haushalte",
            Year = 1890,
            Cost = 4000,
            ResearchTime = 120,
            Prerequisites = {},
            Unlocks = {"POWER_PLANT", "ELECTRIC_LIGHTING"},
            Category = "Power"
        },
        
        -- Mechanisierung (1920-1950)
        ["Mechanized_Mining"] = {
            Name = "Mechanisierter Bergbau",
            Description = "Maschinelle Förderung und Transportbänder",
            Year = 1920,
            Cost = 8000,
            ResearchTime = 60,
            Prerequisites = {"Improved_Mining", "Electricity"},
            Unlocks = {"COAL_MINE_MECHANIZED", "IRON_MINE_MECHANIZED"},
            Category = "Mining"
        },
        
        ["Electric_Steel_Production"] = {
            Name = "Elektrische Stahlproduktion",
            Description = "Lichtbogenöfen für effiziente Stahlherstellung",
            Year = 1950,
            Cost = 15000,
            ResearchTime = 120,
            Prerequisites = {"Improved_Steel_Production", "Electricity"},
            Unlocks = {"STEEL_MILL_ELECTRIC"},
            Category = "Industry"
        },
        
        ["Modern_Construction"] = {
            Name = "Moderne Bautechnik",
            Description = "Stahlbeton und moderne Architektur",
            Year = 1945,
            Cost = 10000,
            ResearchTime = 90,
            Prerequisites = {"Steel_Production", "Electricity"},
            Unlocks = {"SMALL_HOUSE_MODERN", "MEDIUM_HOUSE_MODERN"},
            Category = "Construction"
        },
        
        -- Automatisierung (1960-2000)
        ["Automated_Mining"] = {
            Name = "Automatisierter Bergbau",
            Description = "Vollautomatische Förderanlagen",
            Year = 1960,
            Cost = 20000,
            ResearchTime = 180,
            Prerequisites = {"Mechanized_Mining"},
            Unlocks = {"COAL_MINE_MODERN", "IRON_MINE_MODERN"},
            Category = "Mining"
        },
        
        ["Automated_Steel_Production"] = {
            Name = "Automatisierte Stahlproduktion",
            Description = "Computergesteuerte Stahlwerke",
            Year = 1980,
            Cost = 35000,
            ResearchTime = 240,
            Prerequisites = {"Electric_Steel_Production"},
            Unlocks = {"STEEL_MILL_AUTOMATED"},
            Category = "Industry"
        }
    }
    
    print("🔬 Technologie-Baum initialisiert:", #self.Technologies, "Technologien")
end

-- Spieler-Technologien initialisieren
function TechTreeManager:InitializePlayerTech(playerId)
    if not self.PlayerTech[playerId] then
        self.PlayerTech[playerId] = {
            UnlockedTech = {"Basic_Mining"},  -- Starttechnologie
            ResearchPoints = 0,
            CurrentResearch = nil,
            ResearchProgress = 0
        }
    end
end

-- Forschung starten
function TechTreeManager:StartResearch(playerId, techId)
    local playerTech = self.PlayerTech[playerId]
    local tech = self.Technologies[techId]
    
    if not playerTech or not tech then
        return false, "Spieler oder Technologie nicht gefunden"
    end
    
    -- Prüfen ob bereits erforscht
    if table.find(playerTech.UnlockedTech, techId) then
        return false, "Technologie bereits erforscht"
    end
    
    -- Prüfen ob Voraussetzungen erfüllt sind
    for _, prereq in pairs(tech.Prerequisites) do
        if not table.find(playerTech.UnlockedTech, prereq) then
            return false, "Voraussetzungen nicht erfüllt: " .. prereq
        end
    end
    
    -- Prüfen ob genug Forschungspunkte vorhanden
    if playerTech.ResearchPoints < tech.Cost then
        return false, "Nicht genug Forschungspunkte"
    end
    
    -- Forschung starten
    playerTech.ResearchPoints = playerTech.ResearchPoints - tech.Cost
    playerTech.CurrentResearch = techId
    playerTech.ResearchProgress = 0
    
    print("🔬 Forschung gestartet:", tech.Name, "für Spieler", playerId)
    return true, "Forschung gestartet"
end

-- Forschungsfortschritt aktualisieren
function TechTreeManager:UpdateResearch(playerId, deltaTime)
    local playerTech = self.PlayerTech[playerId]
    if not playerTech or not playerTech.CurrentResearch then
        return
    end
    
    local tech = self.Technologies[playerTech.CurrentResearch]
    if not tech then
        return
    end
    
    -- Fortschritt erhöhen (deltaTime in Sekunden, ResearchTime in Tagen)
    local progressPerSecond = 1 / (tech.ResearchTime * 24 * 60 * 60)  -- Umrechnung Tage zu Sekunden
    playerTech.ResearchProgress = playerTech.ResearchProgress + (deltaTime * progressPerSecond)
    
    -- Forschung abgeschlossen?
    if playerTech.ResearchProgress >= 1.0 then
        self:CompleteResearch(playerId)
    end
end

-- Forschung abschließen
function TechTreeManager:CompleteResearch(playerId)
    local playerTech = self.PlayerTech[playerId]
    if not playerTech or not playerTech.CurrentResearch then
        return
    end
    
    local techId = playerTech.CurrentResearch
    local tech = self.Technologies[techId]
    
    -- Technologie freischalten
    table.insert(playerTech.UnlockedTech, techId)
    playerTech.CurrentResearch = nil
    playerTech.ResearchProgress = 0
    
    print("🔬 Forschung abgeschlossen:", tech.Name, "für Spieler", playerId)
    
    -- Event an Client senden
    local player = Players:GetPlayerByUserId(playerId)
    if player then
        local researchCompleteEvent = ReplicatedStorage.Events:FindFirstChild("ResearchCompleteEvent")
        if researchCompleteEvent then
            researchCompleteEvent:FireClient(player, techId, tech)
        end
    end
end

-- Verfügbare Technologien für Spieler abrufen
function TechTreeManager:GetAvailableTech(playerId, currentYear)
    local playerTech = self.PlayerTech[playerId]
    if not playerTech then
        return {}
    end
    
    local availableTech = {}
    
    for techId, tech in pairs(self.Technologies) do
        -- Bereits erforscht?
        if not table.find(playerTech.UnlockedTech, techId) then
            -- Jahr erreicht?
            if currentYear >= tech.Year then
                -- Voraussetzungen erfüllt?
                local canResearch = true
                for _, prereq in pairs(tech.Prerequisites) do
                    if not table.find(playerTech.UnlockedTech, prereq) then
                        canResearch = false
                        break
                    end
                end
                
                if canResearch then
                    availableTech[techId] = tech
                end
            end
        end
    end
    
    return availableTech
end

-- Forschungspunkte hinzufügen
function TechTreeManager:AddResearchPoints(playerId, points)
    local playerTech = self.PlayerTech[playerId]
    if playerTech then
        playerTech.ResearchPoints = playerTech.ResearchPoints + points
    end
end

-- Technologie freigeschaltet prüfen
function TechTreeManager:IsTechUnlocked(playerId, techId)
    local playerTech = self.PlayerTech[playerId]
    if not playerTech then
        return false
    end
    
    return table.find(playerTech.UnlockedTech, techId) ~= nil
end

return TechTreeManager
