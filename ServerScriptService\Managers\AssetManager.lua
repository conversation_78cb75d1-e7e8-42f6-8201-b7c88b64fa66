-- ServerScriptService/Managers/AssetManager.lua
-- Asset-Management für 3D-Modelle und Ressourcen

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ContentProvider = game:GetService("ContentProvider")
local HttpService = game:GetService("HttpService")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local AssetManager = {}
AssetManager.LoadedAssets = {}
AssetManager.AssetCache = {}
AssetManager.LoadingQueue = {}
AssetManager.IsInitialized = false

-- Asset-Konfigurationen laden
function AssetManager:LoadAssetConfigurations()
    local assetConfigs = {
        Vehicles = self:ParseAssetFile("VehicleAssets"),
        Infrastructure = self:ParseAssetFile("InfrastructureAssets"),
        Terrain = self:ParseAssetFile("TerrainAssets"),
        Economy = self:ParseAssetFile("EconomyAssets")
    }
    
    self.AssetConfigurations = assetConfigs
    print("📦 Asset-Konfigurationen geladen")
    
    return assetConfigs
end

-- Asset-Datei parsen (vereinfacht)
function AssetManager:ParseAssetFile(fileName)
    local assetsFolder = ReplicatedStorage:FindFirstChild("Assets")
    if not assetsFolder then
        warn("📦 Assets-Ordner nicht gefunden")
        return {}
    end
    
    local assetFile = assetsFolder:FindFirstChild(fileName .. ".txt")
    if not assetFile then
        warn("📦 Asset-Datei nicht gefunden:", fileName)
        return {}
    end
    
    -- Vereinfachte Parsing-Logik
    -- In einer echten Implementierung würde hier die .txt-Datei geparst werden
    local assets = {}
    
    -- Beispiel-Assets basierend auf dem Dateinamen
    if fileName == "VehicleAssets" then
        assets = self:GetDefaultVehicleAssets()
    elseif fileName == "InfrastructureAssets" then
        assets = self:GetDefaultInfrastructureAssets()
    elseif fileName == "TerrainAssets" then
        assets = self:GetDefaultTerrainAssets()
    elseif fileName == "EconomyAssets" then
        assets = self:GetDefaultEconomyAssets()
    end
    
    print("📦 " .. fileName .. " geparst:", #assets, "Assets")
    return assets
end

-- Standard-Fahrzeug-Assets
function AssetManager:GetDefaultVehicleAssets()
    return {
        {
            id = "TRAIN_STEAM_BASIC",
            name = "Grundlegende Dampflok",
            modelId = "rbxassetid://0", -- Platzhalter
            scale = Vector3.new(4, 3, 12),
            cost = 15000,
            era = {1850, 1880},
            vehicleType = "Train",
            colors = {
                primary = Color3.new(0.2, 0.2, 0.2),
                secondary = Color3.new(0.8, 0.1, 0.1),
                accent = Color3.new(0.9, 0.8, 0.3)
            }
        },
        {
            id = "TRUCK_EARLY_SMALL",
            name = "Kleiner Lieferwagen",
            modelId = "rbxassetid://0",
            scale = Vector3.new(2, 1.8, 4),
            cost = 8000,
            era = {1920, 1950},
            vehicleType = "Truck"
        },
        {
            id = "SHIP_STEAM_SMALL",
            name = "Kleines Dampfschiff",
            modelId = "rbxassetid://0",
            scale = Vector3.new(6, 4, 20),
            cost = 50000,
            era = {1850, 1920},
            vehicleType = "Ship"
        }
    }
end

-- Standard-Infrastruktur-Assets
function AssetManager:GetDefaultInfrastructureAssets()
    return {
        {
            id = "RAIL_STRAIGHT",
            name = "Gerade Schiene",
            modelId = "rbxassetid://0",
            scale = Vector3.new(1, 0.2, 8),
            cost = 500,
            infrastructureType = "Rail"
        },
        {
            id = "ROAD_ASPHALT",
            name = "Asphaltstraße",
            modelId = "rbxassetid://0",
            scale = Vector3.new(6, 0.1, 8),
            cost = 800,
            infrastructureType = "Road"
        },
        {
            id = "STATION_SMALL",
            name = "Kleiner Bahnhof",
            modelId = "rbxassetid://0",
            scale = Vector3.new(12, 6, 20),
            cost = 25000,
            infrastructureType = "Station"
        }
    }
end

-- Standard-Terrain-Assets
function AssetManager:GetDefaultTerrainAssets()
    return {
        {
            id = "TERRAIN_GRASS_BASIC",
            name = "Grundgras",
            textureId = "rbxassetid://0",
            material = Enum.Material.Grass,
            color = Color3.new(0.2, 0.6, 0.2)
        },
        {
            id = "TERRAIN_WATER",
            name = "Wasser",
            textureId = "rbxassetid://0",
            material = Enum.Material.Water,
            color = Color3.new(0.2, 0.4, 0.6)
        }
    }
end

-- Standard-Wirtschafts-Assets
function AssetManager:GetDefaultEconomyAssets()
    return {
        {
            id = "CITY_SMALL",
            name = "Kleine Stadt",
            modelId = "rbxassetid://0",
            scale = Vector3.new(20, 8, 20),
            population = 1000
        },
        {
            id = "INDUSTRY_COAL_MINE",
            name = "Kohlemine",
            modelId = "rbxassetid://0",
            scale = Vector3.new(15, 10, 15),
            production = "Coal"
        }
    }
end

-- Asset erstellen
function AssetManager:CreateAsset(assetId, position, rotation)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        warn("📦 Asset-Konfiguration nicht gefunden:", assetId)
        return nil
    end
    
    -- Model erstellen
    local model = Instance.new("Model")
    model.Name = assetConfig.name or assetId
    
    -- Haupt-Part erstellen
    local mainPart = Instance.new("Part")
    mainPart.Name = "MainPart"
    mainPart.Size = assetConfig.scale or Vector3.new(4, 4, 4)
    mainPart.Position = position or Vector3.new(0, 0, 0)
    mainPart.Rotation = rotation or Vector3.new(0, 0, 0)
    mainPart.Anchored = true
    mainPart.Parent = model
    
    -- Material und Farbe setzen
    if assetConfig.material then
        mainPart.Material = assetConfig.material
    end
    
    if assetConfig.color then
        mainPart.Color = assetConfig.color
    elseif assetConfig.colors and assetConfig.colors.primary then
        mainPart.Color = assetConfig.colors.primary
    end
    
    -- Mesh laden (falls verfügbar)
    if assetConfig.modelId and assetConfig.modelId ~= "rbxassetid://0" then
        local mesh = Instance.new("SpecialMesh")
        mesh.MeshType = Enum.MeshType.FileMesh
        mesh.MeshId = assetConfig.modelId
        mesh.Parent = mainPart
    end
    
    -- Asset-Daten als Attribute speichern
    model:SetAttribute("AssetId", assetId)
    model:SetAttribute("AssetType", assetConfig.vehicleType or assetConfig.infrastructureType or "Unknown")
    model:SetAttribute("Cost", assetConfig.cost or 0)
    
    -- In Cache speichern
    self.AssetCache[assetId] = assetConfig
    
    print("📦 Asset erstellt:", assetId, "an Position", position)
    return model
end

-- Asset-Konfiguration abrufen
function AssetManager:GetAssetConfig(assetId)
    -- Zuerst im Cache suchen
    if self.AssetCache[assetId] then
        return self.AssetCache[assetId]
    end
    
    -- In allen Konfigurationen suchen
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.id == assetId then
                    self.AssetCache[assetId] = asset
                    return asset
                end
            end
        end
    end
    
    return nil
end

-- Assets nach Kategorie abrufen
function AssetManager:GetAssetsByCategory(category)
    if not self.AssetConfigurations then
        return {}
    end
    
    return self.AssetConfigurations[category] or {}
end

-- Assets nach Typ filtern
function AssetManager:GetAssetsByType(assetType)
    local filteredAssets = {}
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.vehicleType == assetType or 
                   asset.infrastructureType == assetType or
                   asset.terrainType == assetType then
                    table.insert(filteredAssets, asset)
                end
            end
        end
    end
    
    return filteredAssets
end

-- Assets nach Ära filtern
function AssetManager:GetAssetsByEra(year)
    local filteredAssets = {}
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.era then
                    local startYear = asset.era[1] or 1850
                    local endYear = asset.era[2] or 2000
                    
                    if year >= startYear and year <= endYear then
                        table.insert(filteredAssets, asset)
                    end
                end
            end
        end
    end
    
    return filteredAssets
end

-- Asset vorladen
function AssetManager:PreloadAsset(assetId)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        return false
    end
    
    -- Model-ID vorladen (falls vorhanden)
    if assetConfig.modelId and assetConfig.modelId ~= "rbxassetid://0" then
        table.insert(self.LoadingQueue, assetConfig.modelId)
    end
    
    -- Texture-ID vorladen (falls vorhanden)
    if assetConfig.textureId and assetConfig.textureId ~= "rbxassetid://0" then
        table.insert(self.LoadingQueue, assetConfig.textureId)
    end
    
    return true
end

-- Alle Assets vorladen
function AssetManager:PreloadAllAssets()
    print("📦 Starte Asset-Vorladung...")
    
    local totalAssets = 0
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                self:PreloadAsset(asset.id)
                totalAssets = totalAssets + 1
            end
        end
    end
    
    -- ContentProvider verwenden
    if #self.LoadingQueue > 0 then
        spawn(function()
            local success, result = pcall(function()
                ContentProvider:PreloadAsync(self.LoadingQueue)
            end)
            
            if success then
                print("📦 Asset-Vorladung abgeschlossen:", totalAssets, "Assets")
            else
                warn("📦 Asset-Vorladung fehlgeschlagen:", result)
            end
        end)
    end
end

-- Asset-Statistiken
function AssetManager:GetAssetStatistics()
    local stats = {
        totalAssets = 0,
        categories = {},
        loadedAssets = 0,
        cacheSize = 0
    }
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            stats.categories[category] = #assets
            stats.totalAssets = stats.totalAssets + #assets
        end
    end
    
    stats.cacheSize = 0
    for _ in pairs(self.AssetCache) do
        stats.cacheSize = stats.cacheSize + 1
    end
    
    return stats
end

-- Cache leeren
function AssetManager:ClearCache()
    self.AssetCache = {}
    self.LoadingQueue = {}
    print("📦 Asset-Cache geleert")
end

-- Asset validieren
function AssetManager:ValidateAsset(assetId)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        return false, "Asset-Konfiguration nicht gefunden"
    end
    
    -- Grundlegende Validierung
    if not assetConfig.name or assetConfig.name == "" then
        return false, "Asset-Name fehlt"
    end
    
    if not assetConfig.scale then
        return false, "Asset-Größe fehlt"
    end
    
    return true, "Asset ist gültig"
end

-- Initialisierung
function AssetManager:Initialize()
    print("📦 AssetManager wird initialisiert...")
    
    -- Asset-Konfigurationen laden
    self:LoadAssetConfigurations()
    
    -- Assets vorladen (optional)
    if GameConfig.Assets and GameConfig.Assets.PreloadOnStart then
        self:PreloadAllAssets()
    end
    
    self.IsInitialized = true
    print("📦 AssetManager erfolgreich initialisiert")
    
    -- Statistiken ausgeben
    local stats = self:GetAssetStatistics()
    print("📦 Verfügbare Assets:", stats.totalAssets)
    for category, count in pairs(stats.categories) do
        print("  " .. category .. ":", count)
    end
end

return AssetManager
