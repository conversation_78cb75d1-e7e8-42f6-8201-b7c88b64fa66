-- ServerScriptService/Managers/AssetManager.lua
-- Asset-Management für 3D-Modelle und Ressourcen

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ContentProvider = game:GetService("ContentProvider")
local HttpService = game:GetService("HttpService")

local GameConfig = require(ReplicatedStorage.Modules.GameConfig)

local AssetManager = {}
AssetManager.LoadedAssets = {}
AssetManager.AssetCache = {}
AssetManager.LoadingQueue = {}
AssetManager.IsInitialized = false

-- Asset-Konfigurationen laden (neue strukturierte Version)
function AssetManager:LoadAssetConfigurations()
    local assetConfigs = {
        Buildings = self:LoadBuildingAssets(),
        Vehicles = self:ParseAssetFile("VehicleAssets"),
        Infrastructure = self:ParseAssetFile("InfrastructureAssets"),
        Terrain = self:ParseAssetFile("TerrainAssets")
    }

    self.AssetConfigurations = assetConfigs
    print("📦 Asset-Konfigurationen geladen")

    return assetConfigs
end

-- Gebäude-Assets aus strukturierten Ordnern laden
function AssetManager:LoadBuildingAssets()
    local buildings = {
        Residential = {},
        Commercial = {},
        Industry = {},
        Public = {}
    }

    local assetsFolder = ReplicatedStorage:FindFirstChild("Assets")
    if not assetsFolder then
        warn("📦 Assets-Ordner nicht gefunden")
        return buildings
    end

    local buildingsFolder = assetsFolder:FindFirstChild("Buildings")
    if not buildingsFolder then
        warn("📦 Buildings-Ordner nicht gefunden")
        return buildings
    end

    -- Residential Buildings laden
    buildings.Residential = self:LoadBuildingCategory(buildingsFolder, "Residential")
    buildings.Commercial = self:LoadBuildingCategory(buildingsFolder, "Commercial")
    buildings.Industry = self:LoadBuildingCategory(buildingsFolder, "Industry")
    buildings.Public = self:LoadBuildingCategory(buildingsFolder, "Public")

    local totalBuildings = 0
    for category, categoryBuildings in pairs(buildings) do
        local count = 0
        for _ in pairs(categoryBuildings) do count = count + 1 end
        totalBuildings = totalBuildings + count
        print("📦 " .. category .. ":", count, "Gebäude")
    end

    print("📦 Gesamt Gebäude geladen:", totalBuildings)
    return buildings
end

-- Gebäude-Kategorie laden
function AssetManager:LoadBuildingCategory(buildingsFolder, category)
    local categoryBuildings = {}
    local categoryFolder = buildingsFolder:FindFirstChild(category)

    if not categoryFolder then
        warn("📦 Kategorie-Ordner nicht gefunden:", category)
        return categoryBuildings
    end

    -- Durch alle Epochen-Unterordner gehen
    for _, eraFolder in pairs(categoryFolder:GetChildren()) do
        if eraFolder:IsA("Folder") then
            -- Durch alle Gebäude-Dateien in der Epoche gehen
            for _, buildingFile in pairs(eraFolder:GetChildren()) do
                if buildingFile.Name:match("%.txt$") then
                    local buildingData = self:ParseBuildingFile(buildingFile)
                    if buildingData then
                        categoryBuildings[buildingData.ID] = buildingData
                    end
                end
            end
        end
    end

    return categoryBuildings
end

-- Einzelne Gebäude-Datei parsen
function AssetManager:ParseBuildingFile(buildingFile)
    -- Vereinfachte Parsing-Logik für .txt Dateien
    -- In einer echten Implementierung würde hier der Dateiinhalt geparst werden

    local buildingId = buildingFile.Name:gsub("%.txt$", "")

    -- Beispiel-Daten basierend auf Dateinamen
    local buildingData = {
        ID = buildingId,
        Name = buildingId:gsub("_", " "),
        Category = "Unknown",
        Era_Start = 1850,
        Era_End = 2000,
        ModelId = "rbxassetid://0",
        Scale = Vector3.new(10, 6, 10),
        BuildCost = 5000,
        MaintenanceCost = 50
    }

    -- Spezifische Daten je nach Gebäudetyp
    if buildingId:find("HOUSE") then
        buildingData.Category = "Residential"
        buildingData.Population = 6
        buildingData.BuildingType = "Residential"
    elseif buildingId:find("SHOP") or buildingId:find("MALL") then
        buildingData.Category = "Commercial"
        buildingData.Workers = 10
        buildingData.BuildingType = "Commercial"
    elseif buildingId:find("MINE") or buildingId:find("FACTORY") then
        buildingData.Category = "Industry"
        buildingData.Workers = 50
        buildingData.BuildingType = "Industry"
        buildingData.Production = {"Generic"}
        buildingData.ProductionRate = 100
    end

    return buildingData
end

-- Asset-Datei parsen (vereinfacht)
function AssetManager:ParseAssetFile(fileName)
    local assetsFolder = ReplicatedStorage:FindFirstChild("Assets")
    if not assetsFolder then
        warn("📦 Assets-Ordner nicht gefunden")
        return {}
    end
    
    local assetFile = assetsFolder:FindFirstChild(fileName .. ".txt")
    if not assetFile then
        warn("📦 Asset-Datei nicht gefunden:", fileName)
        return {}
    end
    
    -- Vereinfachte Parsing-Logik
    -- In einer echten Implementierung würde hier die .txt-Datei geparst werden
    local assets = {}
    
    -- Beispiel-Assets basierend auf dem Dateinamen
    if fileName == "VehicleAssets" then
        assets = self:GetDefaultVehicleAssets()
    elseif fileName == "InfrastructureAssets" then
        assets = self:GetDefaultInfrastructureAssets()
    elseif fileName == "TerrainAssets" then
        assets = self:GetDefaultTerrainAssets()
    elseif fileName == "EconomyAssets" then
        assets = self:GetDefaultEconomyAssets()
    end
    
    print("📦 " .. fileName .. " geparst:", #assets, "Assets")
    return assets
end

-- Standard-Fahrzeug-Assets
function AssetManager:GetDefaultVehicleAssets()
    return {
        {
            id = "TRAIN_STEAM_BASIC",
            name = "Grundlegende Dampflok",
            modelId = "rbxassetid://0", -- Platzhalter
            scale = Vector3.new(4, 3, 12),
            cost = 15000,
            era = {1850, 1880},
            vehicleType = "Train",
            colors = {
                primary = Color3.new(0.2, 0.2, 0.2),
                secondary = Color3.new(0.8, 0.1, 0.1),
                accent = Color3.new(0.9, 0.8, 0.3)
            }
        },
        {
            id = "TRUCK_EARLY_SMALL",
            name = "Kleiner Lieferwagen",
            modelId = "rbxassetid://0",
            scale = Vector3.new(2, 1.8, 4),
            cost = 8000,
            era = {1920, 1950},
            vehicleType = "Truck"
        },
        {
            id = "SHIP_STEAM_SMALL",
            name = "Kleines Dampfschiff",
            modelId = "rbxassetid://0",
            scale = Vector3.new(6, 4, 20),
            cost = 50000,
            era = {1850, 1920},
            vehicleType = "Ship"
        }
    }
end

-- Standard-Infrastruktur-Assets
function AssetManager:GetDefaultInfrastructureAssets()
    return {
        {
            id = "RAIL_STRAIGHT",
            name = "Gerade Schiene",
            modelId = "rbxassetid://0",
            scale = Vector3.new(1, 0.2, 8),
            cost = 500,
            infrastructureType = "Rail"
        },
        {
            id = "ROAD_ASPHALT",
            name = "Asphaltstraße",
            modelId = "rbxassetid://0",
            scale = Vector3.new(6, 0.1, 8),
            cost = 800,
            infrastructureType = "Road"
        },
        {
            id = "STATION_SMALL",
            name = "Kleiner Bahnhof",
            modelId = "rbxassetid://0",
            scale = Vector3.new(12, 6, 20),
            cost = 25000,
            infrastructureType = "Station"
        }
    }
end

-- Standard-Terrain-Assets
function AssetManager:GetDefaultTerrainAssets()
    return {
        {
            id = "TERRAIN_GRASS_BASIC",
            name = "Grundgras",
            textureId = "rbxassetid://0",
            material = Enum.Material.Grass,
            color = Color3.new(0.2, 0.6, 0.2)
        },
        {
            id = "TERRAIN_WATER",
            name = "Wasser",
            textureId = "rbxassetid://0",
            material = Enum.Material.Water,
            color = Color3.new(0.2, 0.4, 0.6)
        }
    }
end

-- Standard-Wirtschafts-Assets
function AssetManager:GetDefaultEconomyAssets()
    return {
        {
            id = "CITY_SMALL",
            name = "Kleine Stadt",
            modelId = "rbxassetid://0",
            scale = Vector3.new(20, 8, 20),
            population = 1000
        },
        {
            id = "INDUSTRY_COAL_MINE",
            name = "Kohlemine",
            modelId = "rbxassetid://0",
            scale = Vector3.new(15, 10, 15),
            production = "Coal"
        }
    }
end

-- Asset erstellen
function AssetManager:CreateAsset(assetId, position, rotation)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        warn("📦 Asset-Konfiguration nicht gefunden:", assetId)
        return nil
    end
    
    -- Model erstellen
    local model = Instance.new("Model")
    model.Name = assetConfig.name or assetId
    
    -- Haupt-Part erstellen
    local mainPart = Instance.new("Part")
    mainPart.Name = "MainPart"
    mainPart.Size = assetConfig.scale or Vector3.new(4, 4, 4)
    mainPart.Position = position or Vector3.new(0, 0, 0)
    mainPart.Rotation = rotation or Vector3.new(0, 0, 0)
    mainPart.Anchored = true
    mainPart.Parent = model
    
    -- Material und Farbe setzen
    if assetConfig.material then
        mainPart.Material = assetConfig.material
    end
    
    if assetConfig.color then
        mainPart.Color = assetConfig.color
    elseif assetConfig.colors and assetConfig.colors.primary then
        mainPart.Color = assetConfig.colors.primary
    end
    
    -- Mesh laden (falls verfügbar)
    if assetConfig.modelId and assetConfig.modelId ~= "rbxassetid://0" then
        local mesh = Instance.new("SpecialMesh")
        mesh.MeshType = Enum.MeshType.FileMesh
        mesh.MeshId = assetConfig.modelId
        mesh.Parent = mainPart
    end
    
    -- Asset-Daten als Attribute speichern
    model:SetAttribute("AssetId", assetId)
    model:SetAttribute("AssetType", assetConfig.vehicleType or assetConfig.infrastructureType or "Unknown")
    model:SetAttribute("Cost", assetConfig.cost or 0)
    
    -- In Cache speichern
    self.AssetCache[assetId] = assetConfig
    
    print("📦 Asset erstellt:", assetId, "an Position", position)
    return model
end

-- Asset-Konfiguration abrufen
function AssetManager:GetAssetConfig(assetId)
    -- Zuerst im Cache suchen
    if self.AssetCache[assetId] then
        return self.AssetCache[assetId]
    end
    
    -- In allen Konfigurationen suchen
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.id == assetId then
                    self.AssetCache[assetId] = asset
                    return asset
                end
            end
        end
    end
    
    return nil
end

-- Assets nach Kategorie abrufen
function AssetManager:GetAssetsByCategory(category)
    if not self.AssetConfigurations then
        return {}
    end
    
    return self.AssetConfigurations[category] or {}
end

-- Assets nach Typ filtern
function AssetManager:GetAssetsByType(assetType)
    local filteredAssets = {}
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.vehicleType == assetType or 
                   asset.infrastructureType == assetType or
                   asset.terrainType == assetType then
                    table.insert(filteredAssets, asset)
                end
            end
        end
    end
    
    return filteredAssets
end

-- Assets nach Ära filtern
function AssetManager:GetAssetsByEra(year)
    local filteredAssets = {}
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                if asset.era then
                    local startYear = asset.era[1] or 1850
                    local endYear = asset.era[2] or 2000
                    
                    if year >= startYear and year <= endYear then
                        table.insert(filteredAssets, asset)
                    end
                end
            end
        end
    end
    
    return filteredAssets
end

-- Asset vorladen
function AssetManager:PreloadAsset(assetId)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        return false
    end
    
    -- Model-ID vorladen (falls vorhanden)
    if assetConfig.modelId and assetConfig.modelId ~= "rbxassetid://0" then
        table.insert(self.LoadingQueue, assetConfig.modelId)
    end
    
    -- Texture-ID vorladen (falls vorhanden)
    if assetConfig.textureId and assetConfig.textureId ~= "rbxassetid://0" then
        table.insert(self.LoadingQueue, assetConfig.textureId)
    end
    
    return true
end

-- Alle Assets vorladen
function AssetManager:PreloadAllAssets()
    print("📦 Starte Asset-Vorladung...")
    
    local totalAssets = 0
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            for _, asset in pairs(assets) do
                self:PreloadAsset(asset.id)
                totalAssets = totalAssets + 1
            end
        end
    end
    
    -- ContentProvider verwenden
    if #self.LoadingQueue > 0 then
        spawn(function()
            local success, result = pcall(function()
                ContentProvider:PreloadAsync(self.LoadingQueue)
            end)
            
            if success then
                print("📦 Asset-Vorladung abgeschlossen:", totalAssets, "Assets")
            else
                warn("📦 Asset-Vorladung fehlgeschlagen:", result)
            end
        end)
    end
end

-- Asset-Statistiken
function AssetManager:GetAssetStatistics()
    local stats = {
        totalAssets = 0,
        categories = {},
        loadedAssets = 0,
        cacheSize = 0
    }
    
    if self.AssetConfigurations then
        for category, assets in pairs(self.AssetConfigurations) do
            stats.categories[category] = #assets
            stats.totalAssets = stats.totalAssets + #assets
        end
    end
    
    stats.cacheSize = 0
    for _ in pairs(self.AssetCache) do
        stats.cacheSize = stats.cacheSize + 1
    end
    
    return stats
end

-- Cache leeren
function AssetManager:ClearCache()
    self.AssetCache = {}
    self.LoadingQueue = {}
    print("📦 Asset-Cache geleert")
end

-- Asset validieren
function AssetManager:ValidateAsset(assetId)
    local assetConfig = self:GetAssetConfig(assetId)
    if not assetConfig then
        return false, "Asset-Konfiguration nicht gefunden"
    end
    
    -- Grundlegende Validierung
    if not assetConfig.name or assetConfig.name == "" then
        return false, "Asset-Name fehlt"
    end
    
    if not assetConfig.scale then
        return false, "Asset-Größe fehlt"
    end
    
    return true, "Asset ist gültig"
end

-- Initialisierung
function AssetManager:Initialize()
    print("📦 AssetManager wird initialisiert...")
    
    -- Asset-Konfigurationen laden
    self:LoadAssetConfigurations()
    
    -- Assets vorladen (optional)
    if GameConfig.Assets and GameConfig.Assets.PreloadOnStart then
        self:PreloadAllAssets()
    end
    
    self.IsInitialized = true
    print("📦 AssetManager erfolgreich initialisiert")
    
    -- Statistiken ausgeben
    local stats = self:GetAssetStatistics()
    print("📦 Verfügbare Assets:", stats.totalAssets)
    for category, count in pairs(stats.categories) do
        print("  " .. category .. ":", count)
    end
end

-- Erweiterte Asset-Funktionen für strukturierte Gebäude

-- Gebäude nach Epoche filtern
function AssetManager:GetBuildingsByEra(startYear, endYear)
    local filteredBuildings = {}

    if not self.AssetConfigurations.Buildings then
        return filteredBuildings
    end

    for category, buildings in pairs(self.AssetConfigurations.Buildings) do
        filteredBuildings[category] = {}

        for buildingId, building in pairs(buildings) do
            local eraStart = building.Era_Start or 1850
            local eraEnd = building.Era_End or 2000

            -- Prüfen ob Gebäude in der gewünschten Epoche verfügbar ist
            if (eraStart <= endYear) and (eraEnd >= startYear) then
                filteredBuildings[category][buildingId] = building
            end
        end
    end

    return filteredBuildings
end

-- Gebäude nach Kategorie und Epoche
function AssetManager:GetBuildingsByCategory(category, era)
    local buildings = {}

    if not self.AssetConfigurations.Buildings or not self.AssetConfigurations.Buildings[category] then
        return buildings
    end

    for buildingId, building in pairs(self.AssetConfigurations.Buildings[category]) do
        if era then
            local eraStart = building.Era_Start or 1850
            local eraEnd = building.Era_End or 2000

            if (eraStart <= era) and (eraEnd >= era) then
                buildings[buildingId] = building
            end
        else
            buildings[buildingId] = building
        end
    end

    return buildings
end

-- Verfügbare Epochen ermitteln
function AssetManager:GetAvailableEras()
    local eras = {}

    if not self.AssetConfigurations.Buildings then
        return eras
    end

    for category, buildings in pairs(self.AssetConfigurations.Buildings) do
        for buildingId, building in pairs(buildings) do
            local eraStart = building.Era_Start or 1850
            local eraEnd = building.Era_End or 2000

            local eraKey = tostring(eraStart) .. "-" .. tostring(eraEnd)
            if not eras[eraKey] then
                eras[eraKey] = {
                    Start = eraStart,
                    End = eraEnd,
                    Buildings = {},
                    Name = self:GetEraName(eraStart)
                }
            end

            table.insert(eras[eraKey].Buildings, buildingId)
        end
    end

    return eras
end

-- Epochen-Namen ermitteln
function AssetManager:GetEraName(year)
    if year >= 1850 and year < 1900 then
        return "Victorian"
    elseif year >= 1900 and year < 1920 then
        return "Edwardian"
    elseif year >= 1920 and year < 1940 then
        return "Art_Deco"
    elseif year >= 1940 and year < 1960 then
        return "Mid_Century"
    elseif year >= 1960 and year < 1980 then
        return "Late_Modern"
    elseif year >= 1980 and year <= 2000 then
        return "Postmodern"
    else
        return "Unknown"
    end
end

-- Produktionsketten-Analyse
function AssetManager:GetProductionChain(resourceType)
    local chain = {
        Producers = {},
        Consumers = {},
        Chain = {}
    }

    if not self.AssetConfigurations.Buildings or not self.AssetConfigurations.Buildings.Industry then
        return chain
    end

    for buildingId, building in pairs(self.AssetConfigurations.Buildings.Industry) do
        -- Produzenten finden
        if building.Production and table.find(building.Production, resourceType) then
            table.insert(chain.Producers, buildingId)
        end

        -- Konsumenten finden
        if building.InputResources and table.find(building.InputResources, resourceType) then
            table.insert(chain.Consumers, buildingId)
        end
    end

    return chain
end

return AssetManager
