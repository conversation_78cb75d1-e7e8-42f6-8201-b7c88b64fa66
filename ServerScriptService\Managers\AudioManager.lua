-- ServerScriptService/Managers/AudioManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Vollständiges Audio-System mit dynamischer Musik und 3D-Audio

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local SoundService = game:GetService("SoundService")
local Players = game:GetService("Players")

local AudioManager = {}
AudioManager.__index = AudioManager

function AudioManager.new()
    local self = setmetatable({}, AudioManager)
    
    -- Sound-Gruppen
    self.soundGroups = {
        music = SoundService:FindFirstChild("Music") or Instance.new("SoundGroup"),
        effects = SoundService:FindFirstChild("Effects") or Instance.new("SoundGroup"),
        ambient = SoundService:FindFirstChild("Ambient") or Instance.new("SoundGroup"),
        ui = SoundService:FindFirstChild("UI") or Instance.new("SoundGroup")
    }
    
    -- Sound-Gruppen zu SoundService hinzufügen
    for name, group in pairs(self.soundGroups) do
        group.Name = name:sub(1,1):upper() .. name:sub(2)
        group.Parent = SoundService
    end
    
    -- Musik-Tracks
    self.musicTracks = {
        menu = {id = "rbxassetid://1234567890", volume = 0.5, looped = true},
        gameplay = {id = "rbxassetid://1234567891", volume = 0.3, looped = true},
        construction = {id = "rbxassetid://1234567892", volume = 0.4, looped = true},
        victory = {id = "rbxassetid://1234567893", volume = 0.6, looped = false}
    }
    
    -- Sound-Effekte
    self.soundEffects = {
        vehicle_horn = {id = "rbxassetid://131961136", volume = 0.5},
        train_whistle = {id = "rbxassetid://131961136", volume = 0.7},
        construction_sound = {id = "rbxassetid://131961136", volume = 0.4},
        money_sound = {id = "rbxassetid://131961136", volume = 0.3},
        notification = {id = "rbxassetid://131961136", volume = 0.4},
        button_click = {id = "rbxassetid://131961136", volume = 0.2}
    }
    
    -- Aktuelle Musik
    self.currentMusic = nil
    self.musicVolume = 0.5
    self.effectsVolume = 0.7
    self.masterVolume = 1.0
    
    -- 3D-Audio-System
    self.spatialSounds = {}
    
    -- Spieler-Audio-Einstellungen
    self.playerSettings = {}
    
    self:InitializeEvents()
    
    return self
end

-- Events initialisieren
function AudioManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("PlaySoundEvent") then
        local playSoundEvent = Instance.new("RemoteEvent")
        playSoundEvent.Name = "PlaySoundEvent"
        playSoundEvent.Parent = Events
        
        playSoundEvent.OnServerEvent:Connect(function(player, soundName, position, volume)
            self:PlaySoundEffect(soundName, position, volume)
        end)
    end
    
    if not Events:FindFirstChild("SetAudioSettingsEvent") then
        local setAudioEvent = Instance.new("RemoteEvent")
        setAudioEvent.Name = "SetAudioSettingsEvent"
        setAudioEvent.Parent = Events
        
        setAudioEvent.OnServerEvent:Connect(function(player, settings)
            self:SetPlayerAudioSettings(player.UserId, settings)
        end)
    end
end

-- Audio-System initialisieren
function AudioManager:Initialize()
    -- Standard-Lautstärken setzen
    self.soundGroups.music.Volume = self.musicVolume
    self.soundGroups.effects.Volume = self.effectsVolume
    self.soundGroups.ambient.Volume = 0.3
    self.soundGroups.ui.Volume = 0.4
    
    print("🎵 AudioManager initialisiert")
end

-- Musik abspielen
function AudioManager:PlayMusic(trackName, fadeIn)
    local track = self.musicTracks[trackName]
    if not track then return end
    
    -- Aktuelle Musik stoppen
    if self.currentMusic then
        if fadeIn then
            self:FadeOutMusic(self.currentMusic, 1.0)
        else
            self.currentMusic:Stop()
        end
    end
    
    -- Neue Musik erstellen
    local sound = Instance.new("Sound")
    sound.SoundId = track.id
    sound.Volume = track.volume * self.musicVolume * self.masterVolume
    sound.Looped = track.looped
    sound.SoundGroup = self.soundGroups.music
    sound.Parent = SoundService
    
    -- Musik starten
    if fadeIn then
        sound.Volume = 0
        sound:Play()
        self:FadeInMusic(sound, track.volume * self.musicVolume * self.masterVolume, 1.0)
    else
        sound:Play()
    end
    
    self.currentMusic = sound
    print("🎵 Musik gestartet:", trackName)
end

-- Musik ein-/ausblenden
function AudioManager:FadeInMusic(sound, targetVolume, duration)
    local startVolume = 0
    local startTime = tick()
    
    spawn(function()
        while tick() - startTime < duration and sound.Parent do
            local progress = (tick() - startTime) / duration
            sound.Volume = startVolume + (targetVolume - startVolume) * progress
            wait(0.1)
        end
        if sound.Parent then
            sound.Volume = targetVolume
        end
    end)
end

function AudioManager:FadeOutMusic(sound, duration)
    local startVolume = sound.Volume
    local startTime = tick()
    
    spawn(function()
        while tick() - startTime < duration and sound.Parent do
            local progress = (tick() - startTime) / duration
            sound.Volume = startVolume * (1 - progress)
            wait(0.1)
        end
        if sound.Parent then
            sound:Stop()
            sound:Destroy()
        end
    end)
end

-- Sound-Effekt abspielen
function AudioManager:PlaySoundEffect(soundName, position, volume)
    local effect = self.soundEffects[soundName]
    if not effect then return end
    
    local sound = Instance.new("Sound")
    sound.SoundId = effect.id
    sound.Volume = (volume or effect.volume) * self.effectsVolume * self.masterVolume
    sound.SoundGroup = self.soundGroups.effects
    
    if position then
        -- 3D-Audio
        local part = Instance.new("Part")
        part.Anchored = true
        part.CanCollide = false
        part.Transparency = 1
        part.Size = Vector3.new(1, 1, 1)
        part.Position = position
        part.Parent = workspace
        
        sound.Parent = part
        
        -- Sound nach Wiedergabe löschen
        spawn(function()
            sound:Play()
            sound.Ended:Wait()
            part:Destroy()
        end)
        
        -- Zu räumlichen Sounds hinzufügen
        table.insert(self.spatialSounds, {sound = sound, part = part, created = tick()})
    else
        -- Globaler Sound
        sound.Parent = SoundService
        sound:Play()
        
        -- Sound nach Wiedergabe löschen
        spawn(function()
            sound.Ended:Wait()
            sound:Destroy()
        end)
    end
end

-- Spieler-Audio-Einstellungen setzen
function AudioManager:SetPlayerAudioSettings(playerId, settings)
    self.playerSettings[playerId] = settings
    
    -- Globale Lautstärken anpassen (für alle Spieler)
    if settings.masterVolume then
        self.masterVolume = settings.masterVolume
    end
    if settings.musicVolume then
        self.musicVolume = settings.musicVolume
        self.soundGroups.music.Volume = self.musicVolume * self.masterVolume
    end
    if settings.effectsVolume then
        self.effectsVolume = settings.effectsVolume
        self.soundGroups.effects.Volume = self.effectsVolume * self.masterVolume
    end
    
    print("🔊 Audio-Einstellungen aktualisiert für Spieler:", playerId)
end

-- Dynamische Musik basierend auf Spielzustand
function AudioManager:UpdateDynamicMusic(gameState)
    local currentTrack = nil
    
    if gameState.isInMenu then
        currentTrack = "menu"
    elseif gameState.isBuilding then
        currentTrack = "construction"
    elseif gameState.hasWon then
        currentTrack = "victory"
    else
        currentTrack = "gameplay"
    end
    
    -- Musik wechseln wenn nötig
    if not self.currentMusic or self.currentMusic.Name ~= currentTrack then
        self:PlayMusic(currentTrack, true)
    end
end

-- Fahrzeug-Sounds verwalten
function AudioManager:CreateVehicleSound(vehicle, soundType)
    local soundData = self.soundEffects[soundType]
    if not soundData then return end
    
    local sound = Instance.new("Sound")
    sound.SoundId = soundData.id
    sound.Volume = soundData.volume * self.effectsVolume * self.masterVolume
    sound.Looped = true
    sound.SoundGroup = self.soundGroups.effects
    sound.Parent = vehicle
    
    sound:Play()
    return sound
end

-- Update-Funktion
function AudioManager:Update(deltaTime, gameState, playerData)
    -- Dynamische Musik aktualisieren
    self:UpdateDynamicMusic(gameState)
    
    -- Alte räumliche Sounds bereinigen
    for i = #self.spatialSounds, 1, -1 do
        local spatialSound = self.spatialSounds[i]
        if not spatialSound.sound.Parent or tick() - spatialSound.created > 60 then
            if spatialSound.part and spatialSound.part.Parent then
                spatialSound.part:Destroy()
            end
            table.remove(self.spatialSounds, i)
        end
    end
end

-- Audio-Einstellungen abrufen
function AudioManager:GetPlayerAudioSettings(playerId)
    return self.playerSettings[playerId] or {
        masterVolume = 1.0,
        musicVolume = 0.5,
        effectsVolume = 0.7,
        ambientVolume = 0.3,
        uiVolume = 0.4
    }
end

return AudioManager
