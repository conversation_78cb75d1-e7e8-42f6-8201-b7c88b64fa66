-- ServerScriptService/VehicleScript.lua
-- ROBLOX SCRIPT TYPE: Script
-- Individual vehicle movement and physics script

local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Get vehicle ID from StringValue
local vehicleIdValue = script:WaitForChild("VehicleId")
local vehicleId = vehicleIdValue.Value

-- Get vehicle model
local model = script.Parent
local primaryPart = model.PrimaryPart

if not primaryPart then
    warn("❌ Vehicle model has no PrimaryPart:", vehicleId)
    return
end

-- Vehicle physics components
local bodyVelocity = primaryPart:FindFirstChild("BodyVelocity")
local bodyAngularVelocity = primaryPart:FindFirstChild("BodyAngularVelocity")

if not bodyVelocity or not bodyAngularVelocity then
    warn("❌ Vehicle missing physics components:", vehicleId)
    return
end

-- Vehicle state
local vehicleState = {
    id = vehicleId,
    position = primaryPart.Position,
    rotation = primaryPart.Rotation,
    velocity = Vector3.new(0, 0, 0),
    targetPosition = nil,
    targetRotation = nil,
    speed = 0,
    maxSpeed = 50, -- Will be updated from server
    acceleration = 5,
    deceleration = 10,
    turnSpeed = 2,
    isMoving = false,
    route = {},
    routeIndex = 1,
    lastUpdate = tick()
}

-- Physics constants
local GRAVITY = Vector3.new(0, -196.2, 0) -- Roblox gravity
local AIR_RESISTANCE = 0.95
local GROUND_FRICTION = 0.8

-- Movement functions
local function updatePhysics(deltaTime)
    if not primaryPart or not primaryPart.Parent then return end
    
    local currentPosition = primaryPart.Position
    local currentVelocity = bodyVelocity.Velocity
    
    -- Apply gravity if not on ground
    local raycast = workspace:Raycast(currentPosition, Vector3.new(0, -10, 0))
    local onGround = raycast and raycast.Distance < 5
    
    if not onGround then
        vehicleState.velocity = vehicleState.velocity + (GRAVITY * deltaTime)
    else
        -- Ground friction
        vehicleState.velocity = vehicleState.velocity * GROUND_FRICTION
    end
    
    -- Air resistance
    vehicleState.velocity = vehicleState.velocity * AIR_RESISTANCE
    
    -- Update body velocity
    bodyVelocity.Velocity = vehicleState.velocity
    
    -- Update position tracking
    vehicleState.position = currentPosition
end

local function moveToTarget(targetPos, deltaTime)
    if not targetPos or not primaryPart then return end
    
    local currentPos = primaryPart.Position
    local direction = (targetPos - currentPos)
    local distance = direction.Magnitude
    
    if distance < 2 then
        -- Reached target
        vehicleState.isMoving = false
        vehicleState.velocity = Vector3.new(0, 0, 0)
        return true
    end
    
    -- Calculate movement
    direction = direction.Unit
    local targetSpeed = math.min(vehicleState.maxSpeed, distance * 2) -- Slow down when approaching
    
    -- Accelerate or decelerate
    if vehicleState.speed < targetSpeed then
        vehicleState.speed = math.min(targetSpeed, vehicleState.speed + vehicleState.acceleration * deltaTime)
    else
        vehicleState.speed = math.max(targetSpeed, vehicleState.speed - vehicleState.deceleration * deltaTime)
    end
    
    -- Apply movement
    vehicleState.velocity = direction * vehicleState.speed
    vehicleState.isMoving = true
    
    -- Rotate towards movement direction
    if vehicleState.speed > 1 then
        local targetRotation = math.atan2(direction.X, direction.Z)
        local currentRotation = math.rad(primaryPart.Rotation.Y)
        local rotationDiff = targetRotation - currentRotation
        
        -- Normalize rotation difference
        if rotationDiff > math.pi then
            rotationDiff = rotationDiff - 2 * math.pi
        elseif rotationDiff < -math.pi then
            rotationDiff = rotationDiff + 2 * math.pi
        end
        
        -- Apply rotation
        local rotationSpeed = vehicleState.turnSpeed * deltaTime
        if math.abs(rotationDiff) > rotationSpeed then
            rotationDiff = rotationDiff > 0 and rotationSpeed or -rotationSpeed
        end
        
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, rotationDiff * 10, 0)
    else
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
    end
    
    return false
end

local function followRoute()
    if not vehicleState.route or #vehicleState.route == 0 then return end
    
    local currentTarget = vehicleState.route[vehicleState.routeIndex]
    if not currentTarget then return end
    
    local targetPos = Vector3.new(currentTarget.x, currentTarget.y or vehicleState.position.Y, currentTarget.z)
    local reachedTarget = moveToTarget(targetPos, tick() - vehicleState.lastUpdate)
    
    if reachedTarget then
        vehicleState.routeIndex = vehicleState.routeIndex + 1
        if vehicleState.routeIndex > #vehicleState.route then
            vehicleState.routeIndex = 1 -- Loop route
        end
    end
end

-- Wheel animation (for trains and trucks)
local function animateWheels()
    if vehicleState.speed < 1 then return end
    
    for _, part in pairs(model:GetChildren()) do
        if part.Name:find("Wheel") and part:IsA("BasePart") then
            local rotationSpeed = vehicleState.speed * 2
            part.CFrame = part.CFrame * CFrame.Angles(math.rad(rotationSpeed), 0, 0)
        end
    end
end

-- Particle effects
local function updateEffects()
    if vehicleState.speed > 10 then
        -- Add smoke/steam effects for trains
        if model.Name:find("train") or model.Name:find("locomotive") then
            -- Create smoke particle effect
            local smoke = primaryPart:FindFirstChild("Smoke")
            if not smoke then
                smoke = Instance.new("Smoke")
                smoke.Size = 5
                smoke.Opacity = 0.3
                smoke.RiseVelocity = 10
                smoke.Parent = primaryPart
            end
            smoke.Enabled = true
        end
    else
        -- Disable effects when not moving
        local smoke = primaryPart:FindFirstChild("Smoke")
        if smoke then
            smoke.Enabled = false
        end
    end
end

-- Sound effects
local function updateSounds()
    local engineSound = primaryPart:FindFirstChild("EngineSound")
    if not engineSound then
        engineSound = Instance.new("Sound")
        engineSound.Name = "EngineSound"
        engineSound.SoundId = "rbxasset://sounds/electronicpingshort.wav" -- Placeholder
        engineSound.Looped = true
        engineSound.Volume = 0.5
        engineSound.Parent = primaryPart
    end
    
    if vehicleState.speed > 1 then
        if not engineSound.IsPlaying then
            engineSound:Play()
        end
        engineSound.Pitch = 0.5 + (vehicleState.speed / vehicleState.maxSpeed) * 1.5
    else
        if engineSound.IsPlaying then
            engineSound:Stop()
        end
    end
end

-- Network communication
local function sendPositionUpdate()
    -- Send position to server every 0.1 seconds
    if tick() - vehicleState.lastUpdate > 0.1 then
        ReplicatedStorage.Events.VehiclePositionUpdateEvent:FireServer(vehicleId, {
            position = vehicleState.position,
            rotation = primaryPart.Rotation,
            speed = vehicleState.speed,
            isMoving = vehicleState.isMoving
        })
        vehicleState.lastUpdate = tick()
    end
end

-- Remote event handlers
ReplicatedStorage.Events.VehicleMoveCommandEvent.OnClientEvent:Connect(function(commandVehicleId, command)
    if commandVehicleId ~= vehicleId then return end
    
    if command.type == "moveTo" then
        vehicleState.targetPosition = command.position
        vehicleState.maxSpeed = command.speed or vehicleState.maxSpeed
    elseif command.type == "setRoute" then
        vehicleState.route = command.route
        vehicleState.routeIndex = 1
    elseif command.type == "stop" then
        vehicleState.targetPosition = nil
        vehicleState.route = {}
        vehicleState.velocity = Vector3.new(0, 0, 0)
        vehicleState.speed = 0
    elseif command.type == "setSpeed" then
        vehicleState.maxSpeed = command.speed
    end
end)

-- Main update loop
local connection
connection = RunService.Heartbeat:Connect(function()
    local deltaTime = RunService.Heartbeat:Wait()
    
    -- Check if model still exists
    if not model or not model.Parent or not primaryPart or not primaryPart.Parent then
        connection:Disconnect()
        return
    end
    
    -- Update physics
    updatePhysics(deltaTime)
    
    -- Handle movement
    if vehicleState.targetPosition then
        local reached = moveToTarget(vehicleState.targetPosition, deltaTime)
        if reached then
            vehicleState.targetPosition = nil
        end
    elseif #vehicleState.route > 0 then
        followRoute()
    end
    
    -- Update visual effects
    animateWheels()
    updateEffects()
    updateSounds()
    
    -- Send network updates
    sendPositionUpdate()
end)

-- Cleanup on removal
model.AncestryChanged:Connect(function()
    if not model.Parent then
        if connection then
            connection:Disconnect()
        end
        
        -- Stop sounds
        local engineSound = primaryPart:FindFirstChild("EngineSound")
        if engineSound then
            engineSound:Stop()
        end
        
        print("🚗 Vehicle script cleaned up:", vehicleId)
    end
end)

-- Initialize
print("🚗 Vehicle script initialized:", vehicleId)

-- Create StringValue for vehicle ID reference
if not script:FindFirstChild("VehicleId") then
    local idValue = Instance.new("StringValue")
    idValue.Name = "VehicleId"
    idValue.Value = vehicleId
    idValue.Parent = script
end
