-- ReplicatedStorage/Modules/GameConfig.lua
-- Zentrale Konfiguration für das gesamte Spiel

local GameConfig = {}

-- Spiel-Grundeinstellungen
GameConfig.Game = {
    Name = "Transport Empire",
    Version = "1.0.0",
    StartMoney = 100000, -- Startkapital
    TimeScale = 1, -- 1 Sekunde = 1 Tag
    AutoSaveInterval = 300, -- Auto-Save alle 5 Minuten
    MaxSaveSlots = 10
}

-- Karten-Konfiguration
GameConfig.Map = {
    Sizes = {
        Small = {x = 64, z = 64, name = "<PERSON> (64x64)"},
        Medium = {x = 128, z = 128, name = "<PERSON><PERSON><PERSON> (128x128)"},
        Large = {x = 256, z = 256, name = "<PERSON><PERSON><PERSON> (256x256)"}
    },
    
    Types = {
        European = {
            name = "Europäisch",
            description = "<PERSON><PERSON><PERSON>ig mit Wäldern und Flüssen",
            hilliness = {min = 0.4, max = 0.8, default = 0.6},
            water = {min = 0.2, max = 0.5, default = 0.3},
            forests = 0.4,
            cities = {min = 8, max = 15}
        },
        American = {
            name = "Amerikanisch", 
            description = "Canyons und Wüsten",
            hilliness = {min = 0.3, max = 0.9, default = 0.7},
            water = {min = 0.1, max = 0.3, default = 0.2},
            deserts = 0.3,
            canyons = 0.2,
            cities = {min = 6, max = 12}
        },
        Asian = {
            name = "Asiatisch",
            description = "Inseln und Meer",
            hilliness = {min = 0.2, max = 0.6, default = 0.4},
            water = {min = 0.4, max = 0.7, default = 0.6},
            islands = 0.8,
            cities = {min = 10, max = 20}
        }
    }
}

-- Wirtschafts-Konfiguration
GameConfig.Economy = {
    -- Waren-Typen
    Goods = {
        Passengers = {name = "Passagiere", color = Color3.fromRGB(100, 150, 255)},
        Mail = {name = "Post", color = Color3.fromRGB(255, 255, 100)},
        Coal = {name = "Kohle", color = Color3.fromRGB(50, 50, 50)},
        Iron = {name = "Eisenerz", color = Color3.fromRGB(150, 100, 50)},
        Steel = {name = "Stahl", color = Color3.fromRGB(200, 200, 200)},
        Wood = {name = "Holz", color = Color3.fromRGB(139, 69, 19)},
        Planks = {name = "Bretter", color = Color3.fromRGB(205, 133, 63)},
        Food = {name = "Nahrung", color = Color3.fromRGB(255, 165, 0)},
        Goods = {name = "Waren", color = Color3.fromRGB(255, 192, 203)}
    },
    
    -- Industrie-Ketten
    Industries = {
        CoalMine = {
            name = "Kohlemine",
            produces = {"Coal"},
            consumes = {},
            productionRate = 10,
            workers = 50
        },
        IronMine = {
            name = "Eisenmine", 
            produces = {"Iron"},
            consumes = {},
            productionRate = 8,
            workers = 40
        },
        SteelMill = {
            name = "Stahlwerk",
            produces = {"Steel"},
            consumes = {"Coal", "Iron"},
            productionRate = 6,
            workers = 80
        },
        Sawmill = {
            name = "Sägewerk",
            produces = {"Planks"},
            consumes = {"Wood"},
            productionRate = 12,
            workers = 30
        },
        Farm = {
            name = "Farm",
            produces = {"Food"},
            consumes = {},
            productionRate = 15,
            workers = 20
        }
    },
    
    -- Stadt-Bedürfnisse
    CityDemands = {
        Small = {
            population = {min = 500, max = 2000},
            demands = {"Passengers", "Mail", "Food"},
            growth = 0.02 -- 2% pro Monat bei guter Versorgung
        },
        Medium = {
            population = {min = 2000, max = 8000},
            demands = {"Passengers", "Mail", "Food", "Goods"},
            growth = 0.015
        },
        Large = {
            population = {min = 8000, max = 25000},
            demands = {"Passengers", "Mail", "Food", "Goods", "Steel"},
            growth = 0.01
        }
    }
}

-- Transport-Konfiguration
GameConfig.Transport = {
    -- Fahrzeug-Typen
    Vehicles = {
        -- Züge
        SteamTrain = {
            name = "Dampflok",
            type = "Train",
            speed = 60, -- km/h
            capacity = 100,
            cost = 25000,
            maintenance = 500, -- pro Monat
            availableFrom = 1850,
            fuelType = "Coal"
        },
        ElectricTrain = {
            name = "E-Lok",
            type = "Train", 
            speed = 120,
            capacity = 150,
            cost = 75000,
            maintenance = 800,
            availableFrom = 1920,
            fuelType = "Electric"
        },
        
        -- LKWs
        OldTruck = {
            name = "Alter LKW",
            type = "Truck",
            speed = 40,
            capacity = 20,
            cost = 8000,
            maintenance = 200,
            availableFrom = 1900,
            fuelType = "Diesel"
        },
        ModernTruck = {
            name = "Moderner LKW",
            type = "Truck",
            speed = 80,
            capacity = 40,
            cost = 25000,
            maintenance = 400,
            availableFrom = 1960,
            fuelType = "Diesel"
        },
        
        -- Schiffe
        SteamShip = {
            name = "Dampfschiff",
            type = "Ship",
            speed = 25,
            capacity = 200,
            cost = 50000,
            maintenance = 1000,
            availableFrom = 1850,
            fuelType = "Coal"
        }
    },
    
    -- Infrastruktur-Kosten
    Infrastructure = {
        Railway = {
            costPerTile = 1000,
            maintenancePerTile = 10 -- pro Monat
        },
        Road = {
            costPerTile = 500,
            maintenancePerTile = 5
        },
        Station = {
            Railway = {cost = 15000, maintenance = 200},
            Road = {cost = 8000, maintenance = 100},
            Harbor = {cost = 30000, maintenance = 500}
        }
    }
}

-- GUI-Konfiguration
GameConfig.GUI = {
    Colors = {
        Primary = Color3.fromRGB(41, 128, 185),
        Secondary = Color3.fromRGB(52, 152, 219),
        Success = Color3.fromRGB(39, 174, 96),
        Warning = Color3.fromRGB(241, 196, 15),
        Danger = Color3.fromRGB(231, 76, 60),
        Dark = Color3.fromRGB(44, 62, 80),
        Light = Color3.fromRGB(236, 240, 241)
    },
    
    Fonts = {
        Title = Enum.Font.GothamBold,
        Subtitle = Enum.Font.GothamMedium,
        Body = Enum.Font.Gotham,
        UI = Enum.Font.RobotoMono
    },
    
    Sizes = {
        ButtonHeight = 40,
        InputHeight = 35,
        HeaderHeight = 60,
        SidebarWidth = 250
    }
}

return GameConfig
