# Elektronikfabrik - High-Tech Industrie
# Era: 1950-2000 | Type: High Tech Manufacturing

[ASSET_INFO]
ID = "ELECTRONICS_FACTORY"
Name = "Elektronikfabrik"
Category = "Industry"
Era_Start = 1950
Era_End = 2000
IndustryType = "High_Tech"
IndustryEra = "Modern"

[MODEL_DATA]
ModelId = "rbxassetid://ELECTRONICS_FACTORY"
Scale = Vector3(80, 6, 100)
Rotation = Vector3(0, 0, 0)
Anchor = true

[COLORS]
Primary = Color3(0.9, 0.9, 0.9)      # Weiße Reinraumwände
Secondary = Color3(0.7, 0.7, 0.8)    # Metallisches Dach
Accent = Color3(0.2, 0.6, 1)         # Blaue Akzente
Trim = Color3(0.3, 0.3, 0.3)         # Dunkle Fensterrahmen

[GAMEPLAY_DATA]
Workers = 300
BuildingType = "Industry"
MaintenanceCost = 800
BuildCost = 120000
BuildTime = 150
PowerConsumption = 200
WaterConsumption = 30

[PRODUCTION_DATA]
Production = {"Electronics"}
ProductionRate = 200  # Einheiten/Monat
InputResources = {"Metals", "Plastics", "Rare_Earth"}
OutputResources = {"Electronics"}
Input_Ratios = {
    Metals = 50,      # Tonnen/Monat
    Plastics = 30,    # Tonnen/Monat
    Rare_Earth = 5    # Tonnen/Monat
}
Storage_Capacity = 500
Production_Cycle = 8  # Stunden

[FEATURES]
Features = {
    "Clean_Rooms",
    "Assembly_Lines",
    "Quality_Control",
    "Testing_Labs",
    "Automated_Systems",
    "Climate_Control",
    "Security_Systems"
}

[REQUIREMENTS]
RequiredTech = "Electronics_Technology"
RequiredPopulation = 10000
RequiredYear = 1950
UnlockCost = 15000
RequiredResources = {"Electricity", "Skilled_Workers"}

[VISUAL_EFFECTS]
HasSmoke = false
HasLights = true
NightLighting = true
LightColor = Color3(0.9, 0.9, 1)
HasNeonSigns = true
NeonColor = Color3(0.2, 0.6, 1)

[SOUND_EFFECTS]
AmbientSound = "rbxassetid://ELECTRONICS_HUM"
BuildSound = "rbxassetid://MODERN_CONSTRUCTION"
DestroySound = "rbxassetid://MODERN_DEMOLITION"
ProductionSound = "rbxassetid://ASSEMBLY_LINE"

[UPGRADE_PATH]
CanUpgrade = true
UpgradeTo = "COMPUTER_FACTORY"
UpgradeCost = 50000
UpgradeTime = 90

[ECONOMIC_DATA]
Employment_Provided = 300
Tax_Revenue = 600
Resource_Value = 100  # Per Einheit Electronics
Export_Potential = true
Maintenance_Jobs = 30
High_Skill_Jobs = 150

[ENVIRONMENTAL_DATA]
Pollution_Level = 3
Noise_Level = 4
Water_Pollution = 2
Air_Pollution = 1
Land_Usage = "Light_Industrial"

[SAFETY_DATA]
Accident_Risk = 2
Safety_Measures = {
    "Clean_Room_Protocols",
    "Chemical_Safety",
    "Fire_Suppression",
    "Emergency_Systems"
}
Insurance_Cost = 200

[TRANSPORT_DATA]
Requires_Rail_Access = false
Truck_Access = true
Ship_Access = false
Air_Access = true
Daily_Shipments = 10
Loading_Time = 1  # Stunden

[SUPPLY_CHAIN]
Suppliers = {
    "CHEMICAL_PLANT",
    "METAL_REFINERY",
    "PLASTICS_FACTORY"
}
Customers = {
    "CONSUMER_MARKET",
    "AUTOMOTIVE_INDUSTRY",
    "AEROSPACE_INDUSTRY"
}

[WORKFORCE_DATA]
Skill_Requirements = {
    Engineers = 50,
    Technicians = 100,
    Assembly_Workers = 120,
    Quality_Control = 30
}
Education_Level = "High_School_Plus"
Training_Required = true
Training_Cost = 2000

[TECHNOLOGY_DATA]
Research_Capability = true
Innovation_Points = 10
Patent_Generation = 5
Technology_Level = 8

[DESCRIPTION]
ShortDesc = "Moderne Elektronikfabrik mit Reinräumen"
LongDesc = "Eine hochmoderne Fabrik zur Herstellung elektronischer Komponenten mit Reinräumen, automatisierten Fertigungslinien und strengen Qualitätskontrollen."
HistoricalNote = "Elektronikfabriken entstanden nach dem 2. Weltkrieg und wurden zum Symbol des technologischen Fortschritts."
