-- ServerScriptService/DeploymentValidator.lua
-- ROBLOX SCRIPT TYPE: Script
-- <PERSON><PERSON><PERSON><PERSON> die vollständige Installation und Funktionsfähigkeit des Spiels

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")
local StarterGui = game:GetService("StarterGui")
local StarterPlayer = game:GetService("StarterPlayer")

print("🔍 Transport Fever 2 Clone - Deployment Validation gestartet")

local DeploymentValidator = {}

-- Erforderliche Scripts und ihre Typen
local requiredScripts = {
    -- ServerScriptService Scripts
    ["ServerScriptService/GameInitializer"] = "Script",
    ["ServerScriptService/GameManager"] = "ModuleScript",
    ["ServerScriptService/Managers/EconomyManager"] = "ModuleScript",
    ["ServerScriptService/Managers/TransportManager"] = "ModuleScript",
    ["ServerScriptService/Managers/CityManager"] = "ModuleScript",
    ["ServerScriptService/Managers/VehicleManager"] = "ModuleScript",
    ["ServerScriptService/Managers/TerrainManager"] = "ModuleScript",
    ["ServerScriptService/Managers/AIManager"] = "ModuleScript",
    ["ServerScriptService/Managers/FinanceManager"] = "ModuleScript",
    ["ServerScriptService/Managers/AICompetitorManager"] = "ModuleScript",
    ["ServerScriptService/Managers/CampaignManager"] = "ModuleScript",
    ["ServerScriptService/Managers/MultiplayerManager"] = "ModuleScript",
    ["ServerScriptService/Managers/AudioManager"] = "ModuleScript",
    ["ServerScriptService/Managers/VisualEffectsManager"] = "ModuleScript",
    ["ServerScriptService/Managers/WeatherManager"] = "ModuleScript",
    ["ServerScriptService/Managers/PerformanceManager"] = "ModuleScript",
    ["ServerScriptService/Managers/GameTester"] = "ModuleScript",
    ["ServerScriptService/Managers/AchievementManager"] = "ModuleScript",
    ["ServerScriptService/Managers/AdvancedAIManager"] = "ModuleScript",
    ["ServerScriptService/Managers/CooperationManager"] = "ModuleScript",

    -- ReplicatedStorage Scripts
    ["ReplicatedStorage/Events/CreateRemoteEvents"] = "Script",

    -- StarterPlayerScripts
    ["StarterPlayer/StarterPlayerScripts/ClientManager"] = "LocalScript"
}

-- Erforderliche Ordner
local requiredFolders = {
    "ReplicatedStorage/Events",
    "ReplicatedStorage/Assets",
    "ReplicatedStorage/Assets/Buildings",
    "ReplicatedStorage/Assets/Vehicles",
    "ReplicatedStorage/Assets/Infrastructure",
    "ReplicatedStorage/Modules",
    "StarterGui/MainMenu",
    "StarterGui/GameUI",
    "StarterPlayerScripts/GUI"
}

-- Validiere Ordnerstruktur
function DeploymentValidator.ValidateFolders()
    print("📁 Validiere Ordnerstruktur...")
    local errors = {}
    
    for _, folderPath in pairs(requiredFolders) do
        local parts = string.split(folderPath, "/")
        local current = game
        
        for _, part in pairs(parts) do
            current = current:FindFirstChild(part)
            if not current then
                table.insert(errors, "❌ Fehlender Ordner: " .. folderPath)
                break
            end
        end
    end
    
    if #errors == 0 then
        print("✅ Alle erforderlichen Ordner vorhanden")
        return true
    else
        for _, error in pairs(errors) do
            warn(error)
        end
        return false
    end
end

-- Validiere Scripts
function DeploymentValidator.ValidateScripts()
    print("📜 Validiere Scripts...")
    local errors = {}
    
    for scriptPath, expectedType in pairs(requiredScripts) do
        local parts = string.split(scriptPath, "/")
        local current = game
        
        for _, part in pairs(parts) do
            current = current:FindFirstChild(part)
            if not current then
                table.insert(errors, "❌ Fehlendes Script: " .. scriptPath)
                break
            end
        end
        
        if current then
            local actualType = current.ClassName
            if actualType ~= expectedType then
                table.insert(errors, string.format("❌ Falscher Script-Typ: %s (erwartet: %s, gefunden: %s)", 
                    scriptPath, expectedType, actualType))
            end
        end
    end
    
    if #errors == 0 then
        print("✅ Alle erforderlichen Scripts vorhanden und korrekt typisiert")
        return true
    else
        for _, error in pairs(errors) do
            warn(error)
        end
        return false
    end
end

-- Validiere RemoteEvents
function DeploymentValidator.ValidateRemoteEvents()
    print("📡 Validiere RemoteEvents...")
    
    local Events = ReplicatedStorage:FindFirstChild("Events")
    if not Events then
        warn("❌ Events-Ordner nicht gefunden!")
        return false
    end
    
    local essentialEvents = {
        "GetPlayerDataFunction",
        "SavePlayerDataEvent",
        "CreateVehicleEvent",
        "UpdateGameStateEvent",
        "MapGeneratedEvent",
        "NotificationEvent"
    }
    
    local errors = {}
    for _, eventName in pairs(essentialEvents) do
        local event = Events:FindFirstChild(eventName)
        if not event then
            table.insert(errors, "❌ Fehlendes Event: " .. eventName)
        end
    end
    
    if #errors == 0 then
        print("✅ Alle essentiellen RemoteEvents vorhanden")
        return true
    else
        for _, error in pairs(errors) do
            warn(error)
        end
        return false
    end
end

-- Teste Manager-Initialisierung
function DeploymentValidator.TestManagerInitialization()
    print("🧪 Teste Manager-Initialisierung...")
    
    local managers = {
        "EconomyManager",
        "TransportManager", 
        "CityManager",
        "VehicleManager",
        "FinanceManager",
        "AudioManager",
        "PerformanceManager"
    }
    
    local errors = {}
    for _, managerName in pairs(managers) do
        local success, result = pcall(function()
            local Manager = require(ServerScriptService:FindFirstChild(managerName))
            local instance = Manager.new()
            return instance ~= nil
        end)
        
        if not success then
            table.insert(errors, "❌ Manager-Initialisierung fehlgeschlagen: " .. managerName .. " - " .. tostring(result))
        end
    end
    
    if #errors == 0 then
        print("✅ Alle Manager können erfolgreich initialisiert werden")
        return true
    else
        for _, error in pairs(errors) do
            warn(error)
        end
        return false
    end
end

-- Teste GameManager Singleton
function DeploymentValidator.TestGameManager()
    print("🎮 Teste GameManager...")
    
    local success, result = pcall(function()
        local GameManager = require(ServerScriptService.GameManager)
        local instance = GameManager.GetInstance()
        return instance ~= nil
    end)
    
    if success and result then
        print("✅ GameManager Singleton funktioniert")
        return true
    else
        warn("❌ GameManager-Test fehlgeschlagen:", tostring(result))
        return false
    end
end

-- Vollständige Validierung
function DeploymentValidator.RunFullValidation()
    print("🚀 Starte vollständige Deployment-Validierung...")
    print("=" .. string.rep("=", 50))
    
    local results = {
        folders = DeploymentValidator.ValidateFolders(),
        scripts = DeploymentValidator.ValidateScripts(),
        events = DeploymentValidator.ValidateRemoteEvents(),
        managers = DeploymentValidator.TestManagerInitialization(),
        gameManager = DeploymentValidator.TestGameManager()
    }
    
    print("=" .. string.rep("=", 50))
    print("📊 VALIDIERUNGS-ERGEBNISSE:")
    
    local allPassed = true
    for testName, passed in pairs(results) do
        local status = passed and "✅ BESTANDEN" or "❌ FEHLGESCHLAGEN"
        print(string.format("  %s: %s", testName:upper(), status))
        if not passed then
            allPassed = false
        end
    end
    
    print("=" .. string.rep("=", 50))
    
    if allPassed then
        print("🎉 DEPLOYMENT ERFOLGREICH!")
        print("✅ Das Spiel ist vollständig installiert und bereit!")
        print("🎮 Spieler können jetzt beitreten und spielen.")
        print("")
        print("🚀 VERFÜGBARE FEATURES:")
        print("  🗺️  Prozedurale Kartengenerierung")
        print("  🏙️  Dynamische Städte und Industrien")
        print("  🚂  Vollständiges Transportsystem")
        print("  💰  Komplexe Wirtschaftssimulation")
        print("  🤖  KI-Konkurrenten")
        print("  👥  Multiplayer-Unterstützung")
        print("  🎵  Audio und Visual Effects")
        print("  ⚡  Performance-Optimierung")
        print("  🧪  Automatische Tests")
        print("  💾  Speicher-/Ladesystem")
        print("  🏆  Kampagnen und Achievements")
        print("  🌦️  Wetter und Tag/Nacht-Zyklen")
        
        return true
    else
        warn("❌ DEPLOYMENT FEHLGESCHLAGEN!")
        warn("🔧 Bitte behebe die oben genannten Probleme und führe die Validierung erneut aus.")
        return false
    end
end

-- Automatische Validierung beim Server-Start
spawn(function()
    wait(2) -- Kurz warten bis alle Scripts geladen sind
    DeploymentValidator.RunFullValidation()
end)

return DeploymentValidator
