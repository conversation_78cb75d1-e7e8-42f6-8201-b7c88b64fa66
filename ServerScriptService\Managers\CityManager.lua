-- ServerScriptService/Managers/CityManager.lua
-- RO<PERSON>OX SCRIPT TYPE: ModuleScript
-- Erweiterte Stadt-Simulation mit Entwicklung, Zufriedenheit und Umwelt

local CityManager = {}
CityManager.__index = CityManager

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Stadt-Typen
local CITY_TYPES = {
    RESIDENTIAL = {
        name = "Wohnstadt",
        populationGrowth = 1.2,
        economicFocus = "housing",
        pollutionTolerance = 0.3,
        description = "Fokus auf Wohnen und Lebensqualität"
    },
    INDUSTRIAL = {
        name = "Industriestadt",
        populationGrowth = 0.8,
        economicFocus = "production",
        pollutionTolerance = 0.7,
        description = "Fokus auf Produktion und Arbeitsplätze"
    },
    COMMERCIAL = {
        name = "Handelsstadt",
        populationGrowth = 1.0,
        economicFocus = "trade",
        pollutionTolerance = 0.4,
        description = "Fokus auf Handel und Dienstleistungen"
    },
    TOURIST = {
        name = "Tourismusstadt",
        populationGrowth = 0.9,
        economicFocus = "tourism",
        pollutionTolerance = 0.2,
        description = "Fokus auf Tourismus und Attraktionen"
    }
}

-- Stadt-Probleme
local CITY_PROBLEMS = {
    HIGH_POLLUTION = {
        name = "Hohe Umweltverschmutzung",
        impact = {happiness = -15, health = -20, tourism = -25},
        threshold = 60,
        solutions = {"Umweltschutz", "Öffentlicher Verkehr", "Grünflächen"}
    },
    HIGH_CRIME = {
        name = "Hohe Kriminalität",
        impact = {happiness = -20, safety = -30, business = -15},
        threshold = 40,
        solutions = {"Polizei", "Bildung", "Arbeitsplätze"}
    },
    TRAFFIC_CONGESTION = {
        name = "Verkehrsstau",
        impact = {satisfaction = -10, economy = -15, pollution = 10},
        threshold = 70,
        solutions = {"Öffentlicher Verkehr", "Straßenausbau", "Verkehrsplanung"}
    },
    UNEMPLOYMENT = {
        name = "Hohe Arbeitslosigkeit",
        impact = {happiness = -25, economy = -30, crime = 15},
        threshold = 12,
        solutions = {"Industrie ansiedeln", "Bildung", "Infrastruktur"}
    }
}

-- Konstruktor
function CityManager.new()
    local self = setmetatable({}, CityManager)
    
    self.cities = {}
    self.nextCityId = 1
    self.updateTimer = 0
    
    self:InitializeEvents()
    
    -- Stadt-Update-Loop
    self.updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
        self:UpdateCities(deltaTime)
    end)
    
    return self
end

-- Events initialisieren
function CityManager:InitializeEvents()
    local Events = ReplicatedStorage:WaitForChild("Events")
    
    if not Events:FindFirstChild("GetCityDataFunction") then
        local getCityDataFunction = Instance.new("RemoteFunction")
        getCityDataFunction.Name = "GetCityDataFunction"
        getCityDataFunction.Parent = Events
    end
    
    if not Events:FindFirstChild("RenameCityEvent") then
        local renameCityEvent = Instance.new("RemoteEvent")
        renameCityEvent.Name = "RenameCityEvent"
        renameCityEvent.Parent = Events
    end
    
    Events.GetCityDataFunction.OnServerInvoke = function(player)
        return self:GetCityData()
    end
    
    Events.RenameCityEvent.OnServerEvent:Connect(function(player, cityId, newName)
        self:RenameCity(cityId, newName)
    end)
end

-- Stadt erstellen
function CityManager:CreateCity(name, position, size, cityType)
    local cityId = "city_" .. self.nextCityId
    self.nextCityId = self.nextCityId + 1
    
    local basePopulation = {SMALL = 2000, MEDIUM = 8000, LARGE = 25000}
    local population = basePopulation[size] or 5000
    
    local city = {
        id = cityId,
        name = name,
        position = position,
        size = size, -- SMALL, MEDIUM, LARGE
        cityType = cityType or "RESIDENTIAL",
        
        -- Grundlegende Eigenschaften
        population = population + math.random(-500, 500),
        founded = tick(),
        mayorRating = math.random(40, 80),
        
        -- Demografie
        demographics = {
            children = math.random(15, 25), -- Prozent
            adults = math.random(60, 70),
            seniors = math.random(10, 20),
            averageAge = math.random(35, 45),
            birthRate = math.random(8, 15), -- pro 1000 Einwohner
            deathRate = math.random(6, 12)
        },
        
        -- Lebensqualität
        quality = {
            happiness = math.random(50, 85),
            satisfaction = math.random(60, 90),
            healthLevel = math.random(60, 95),
            educationLevel = math.random(40, 80),
            safetyLevel = math.random(60, 90),
            culturalLevel = math.random(30, 70)
        },
        
        -- Wirtschaft
        economy = {
            averageIncome = math.random(25000, 75000),
            unemployment = math.random(2, 15),
            businessCount = math.random(10, 100),
            taxRate = 0.15,
            budget = math.random(50000, 500000),
            gdpPerCapita = math.random(30000, 80000)
        },
        
        -- Infrastruktur
        infrastructure = {
            roads = math.random(30, 70),
            utilities = math.random(40, 80),
            publicTransport = math.random(10, 50),
            healthcare = math.random(30, 70),
            education = math.random(25, 65),
            internet = math.random(60, 95),
            waterSupply = math.random(70, 95),
            powerGrid = math.random(65, 90)
        },
        
        -- Umwelt
        environment = {
            airQuality = math.random(40, 90),
            waterQuality = math.random(50, 95),
            noiseLevel = math.random(20, 80),
            greenSpaces = math.random(10, 40),
            recyclingRate = math.random(20, 70),
            pollution = math.random(0, 30),
            carbonFootprint = math.random(5, 25)
        },
        
        -- Tourismus
        tourism = {
            attractiveness = math.random(0, 50),
            visitors = 0,
            hotels = math.random(0, 10),
            attractions = {},
            seasonalFactor = 1.0
        },
        
        -- Transport
        transport = {
            demand = {
                passengers = math.random(50, 200),
                mail = math.random(10, 50),
                goods = math.random(20, 100)
            },
            supply = {
                passengers = 0,
                workers = math.random(100, 500)
            },
            connections = {},
            satisfaction = math.random(40, 80)
        },
        
        -- Stadtentwicklung
        development = {
            level = 1, -- 1-10
            growthRate = math.random(1, 5) / 100,
            plannedProjects = {},
            completedProjects = {},
            developmentFocus = "balanced" -- balanced, economic, environmental, social
        },
        
        -- Probleme und Events
        problems = {},
        activeEvents = {},
        history = {},
        
        -- Gebäude
        buildings = {
            residential = math.random(100, 500),
            commercial = math.random(20, 100),
            industrial = math.random(10, 50),
            public = math.random(5, 25)
        },
        
        lastUpdate = tick()
    }
    
    self.cities[cityId] = city
    self:InitializeCityDevelopment(city)
    self:CheckCityProblems(city)
    
    print("🏙️ Stadt erstellt:", name, "(" .. cityType .. ")", "Einwohner:", population)
    return city
end

-- Stadt-Entwicklung initialisieren
function CityManager:InitializeCityDevelopment(city)
    local cityTypeData = CITY_TYPES[city.cityType]
    
    -- Entwicklungsziele basierend auf Stadt-Typ setzen
    if city.cityType == "RESIDENTIAL" then
        city.development.plannedProjects = {"Park", "Schule", "Krankenhaus"}
    elseif city.cityType == "INDUSTRIAL" then
        city.development.plannedProjects = {"Fabrik", "Logistikzentrum", "Kraftwerk"}
    elseif city.cityType == "COMMERCIAL" then
        city.development.plannedProjects = {"Einkaufszentrum", "Bürogebäude", "Hotel"}
    elseif city.cityType == "TOURIST" then
        city.development.plannedProjects = {"Museum", "Theater", "Freizeitpark"}
    end
    
    -- Erste Attraktionen für Tourismusstädte
    if city.cityType == "TOURIST" then
        city.tourism.attractions = {"Historisches Zentrum", "Naturpark"}
        city.tourism.attractiveness = math.random(30, 70)
    end
end

-- Städte aktualisieren
function CityManager:UpdateCities(deltaTime)
    self.updateTimer = self.updateTimer + deltaTime
    
    -- Alle 5 Sekunden aktualisieren
    if self.updateTimer >= 5 then
        for cityId, city in pairs(self.cities) do
            self:UpdateCity(city)
        end
        self.updateTimer = 0
    end
end

-- Einzelne Stadt aktualisieren
function CityManager:UpdateCity(city)
    -- Bevölkerungswachstum
    self:UpdatePopulation(city)
    
    -- Wirtschaft aktualisieren
    self:UpdateCityEconomy(city)
    
    -- Umwelt aktualisieren
    self:UpdateEnvironment(city)
    
    -- Probleme prüfen
    self:CheckCityProblems(city)
    
    -- Tourismus aktualisieren
    self:UpdateTourism(city)
    
    -- Infrastruktur-Verschleiß
    self:UpdateInfrastructure(city)
    
    city.lastUpdate = tick()
end

-- Bevölkerungswachstum
function CityManager:UpdatePopulation(city)
    local cityTypeData = CITY_TYPES[city.cityType]
    local baseGrowthRate = city.development.growthRate
    
    -- Wachstumsfaktoren
    local happinessFactor = city.quality.happiness / 100
    local economyFactor = (100 - city.economy.unemployment) / 100
    local environmentFactor = city.environment.airQuality / 100
    local infrastructureFactor = (city.infrastructure.roads + city.infrastructure.utilities) / 200
    
    local totalGrowthRate = baseGrowthRate * cityTypeData.populationGrowth * 
                           happinessFactor * economyFactor * environmentFactor * infrastructureFactor
    
    -- Bevölkerung aktualisieren (vereinfacht - pro Update-Zyklus)
    local populationChange = city.population * totalGrowthRate * 0.01 -- 1% des Wachstums pro Update
    city.population = math.max(100, city.population + populationChange)
    
    -- Demografie aktualisieren
    if math.random() < 0.1 then -- 10% Chance auf Demografie-Update
        city.demographics.averageAge = city.demographics.averageAge + math.random(-1, 1) * 0.1
        city.demographics.birthRate = math.max(5, city.demographics.birthRate + math.random(-2, 2))
    end
end

-- Stadt-Wirtschaft aktualisieren
function CityManager:UpdateCityEconomy(city)
    -- Arbeitslosigkeit basierend auf Wirtschaftslage
    local businessFactor = city.economy.businessCount / 100
    local infrastructureFactor = city.infrastructure.education / 100
    
    local targetUnemployment = math.max(2, 15 - (businessFactor * 8) - (infrastructureFactor * 5))
    city.economy.unemployment = city.economy.unemployment + (targetUnemployment - city.economy.unemployment) * 0.1
    
    -- Budget basierend auf Steuern und Bevölkerung
    local taxIncome = city.population * city.economy.averageIncome * city.economy.taxRate * 0.001 -- Vereinfacht
    city.economy.budget = city.economy.budget + taxIncome
    
    -- GDP pro Kopf aktualisieren
    city.economy.gdpPerCapita = city.economy.averageIncome * (1 + (city.economy.businessCount / 200))
end

-- Umwelt aktualisieren
function CityManager:UpdateEnvironment(city)
    -- Verschmutzung basierend auf Industrie und Verkehr
    local industrialPollution = city.buildings.industrial * 0.5
    local trafficPollution = (100 - city.infrastructure.publicTransport) * 0.3
    
    city.environment.pollution = math.min(100, industrialPollution + trafficPollution)
    city.environment.airQuality = math.max(0, 100 - city.environment.pollution)
    
    -- Grünflächen verbessern Luftqualität
    local greenBonus = city.environment.greenSpaces * 0.5
    city.environment.airQuality = math.min(100, city.environment.airQuality + greenBonus)
    
    -- Lärmpegel basierend auf Verkehr und Industrie
    city.environment.noiseLevel = math.min(100, trafficPollution + industrialPollution * 0.8)
end

-- Stadt-Probleme prüfen
function CityManager:CheckCityProblems(city)
    city.problems = {}
    
    for problemType, problemData in pairs(CITY_PROBLEMS) do
        local shouldHaveProblem = false
        
        if problemType == "HIGH_POLLUTION" and city.environment.pollution > problemData.threshold then
            shouldHaveProblem = true
        elseif problemType == "HIGH_CRIME" and city.quality.safetyLevel < (100 - problemData.threshold) then
            shouldHaveProblem = true
        elseif problemType == "TRAFFIC_CONGESTION" and city.environment.noiseLevel > problemData.threshold then
            shouldHaveProblem = true
        elseif problemType == "UNEMPLOYMENT" and city.economy.unemployment > problemData.threshold then
            shouldHaveProblem = true
        end
        
        if shouldHaveProblem then
            table.insert(city.problems, {
                type = problemType,
                name = problemData.name,
                impact = problemData.impact,
                solutions = problemData.solutions,
                severity = math.random(1, 5)
            })
            
            -- Auswirkungen anwenden
            for stat, impact in pairs(problemData.impact) do
                if stat == "happiness" then
                    city.quality.happiness = math.max(0, city.quality.happiness + impact)
                elseif stat == "health" then
                    city.quality.healthLevel = math.max(0, city.quality.healthLevel + impact)
                end
            end
        end
    end
end

-- Tourismus aktualisieren
function CityManager:UpdateTourism(city)
    if city.cityType == "TOURIST" then
        -- Touristen basierend auf Attraktivität
        local baseVisitors = city.tourism.attractiveness * 100
        local environmentBonus = city.environment.airQuality / 100
        local safetyBonus = city.quality.safetyLevel / 100
        
        city.tourism.visitors = math.floor(baseVisitors * environmentBonus * safetyBonus * city.tourism.seasonalFactor)
        
        -- Tourismus bringt Einkommen
        local tourismIncome = city.tourism.visitors * 50 -- $50 pro Tourist
        city.economy.budget = city.economy.budget + tourismIncome
    end
end

-- Stadt umbenennen
function CityManager:RenameCity(cityId, newName)
    local city = self.cities[cityId]
    if city then
        local oldName = city.name
        city.name = newName
        
        table.insert(city.history, {
            timestamp = tick(),
            event = "RENAMED",
            description = "Stadt umbenannt von '" .. oldName .. "' zu '" .. newName .. "'"
        })
        
        print("🏙️ Stadt umbenannt:", oldName, "→", newName)
        return true
    end
    return false
end

-- Stadt-Daten abrufen
function CityManager:GetCityData()
    local cityData = {}
    
    for cityId, city in pairs(self.cities) do
        cityData[cityId] = {
            id = city.id,
            name = city.name,
            position = city.position,
            size = city.size,
            cityType = city.cityType,
            population = math.floor(city.population),
            
            -- Zusammengefasste Statistiken
            happiness = math.floor(city.quality.happiness),
            satisfaction = math.floor(city.quality.satisfaction),
            healthLevel = math.floor(city.quality.healthLevel),
            unemployment = math.floor(city.economy.unemployment * 10) / 10,
            pollution = math.floor(city.environment.pollution),
            
            -- Transport
            transportDemand = city.transport.demand,
            transportSupply = city.transport.supply,
            
            -- Probleme
            problems = city.problems,
            
            -- Entwicklung
            developmentLevel = city.development.level,
            growthRate = math.floor(city.development.growthRate * 1000) / 10 -- In Promille
        }
    end
    
    return cityData
end

return CityManager
