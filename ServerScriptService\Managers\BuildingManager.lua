-- ServerScriptService/Managers/BuildingManager.lua
-- Gebäude-Management und Requirements-System
-- ROBLOX SCRIPT TYPE: ModuleScript

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local BuildingManager = {}
BuildingManager.__index = BuildingManager

-- Initialisierung
function BuildingManager.new(assetManager, techTreeManager, economyManager)
    local self = setmetatable({}, BuildingManager)
    
    self.AssetManager = assetManager
    self.TechTreeManager = techTreeManager
    self.EconomyManager = economyManager
    
    self.PlayerBuildings = {}  -- Gebäude pro Spieler
    self.BuildingAssets = {}   -- Geladene Asset-Daten
    
    self:LoadBuildingAssets()
    
    return self
end

-- Gebäude-Assets laden
function BuildingManager:LoadBuildingAssets()
    local assetsFolder = ReplicatedStorage:FindFirstChild("Assets")
    if not assetsFolder then
        warn("📦 Assets-Ordner nicht gefunden")
        return
    end
    
    local buildingsFolder = assetsFolder:FindFirstChild("Buildings")
    if not buildingsFolder then
        warn("📦 Buildings-Ordner nicht gefunden")
        return
    end
    
    -- Residential Buildings laden
    self:LoadBuildingCategory(buildingsFolder, "Residential")
    self:LoadBuildingCategory(buildingsFolder, "Industry")
    self:LoadBuildingCategory(buildingsFolder, "Commercial")
    
    local totalBuildings = 0
    for _ in pairs(self.BuildingAssets) do
        totalBuildings = totalBuildings + 1
    end
    
    print("🏗️ Gebäude-Assets geladen:", totalBuildings)
end

-- Gebäude-Kategorie laden
function BuildingManager:LoadBuildingCategory(buildingsFolder, category)
    local categoryFolder = buildingsFolder:FindFirstChild(category)
    if not categoryFolder then
        warn("📦 Kategorie nicht gefunden:", category)
        return
    end
    
    -- Alle .txt Dateien in der Kategorie laden
    for _, file in pairs(categoryFolder:GetChildren()) do
        if file.Name:match("%.txt$") then
            local buildingData = self:ParseBuildingFile(file)
            if buildingData then
                self.BuildingAssets[buildingData.ID] = buildingData
            end
        end
    end
end

-- Gebäude-Datei parsen (vereinfacht für Demo)
function BuildingManager:ParseBuildingFile(file)
    local buildingId = file.Name:gsub("%.txt$", "")
    
    -- Beispiel-Parsing basierend auf Dateinamen
    local buildingData = {
        ID = buildingId,
        Name = buildingId:gsub("_", " "),
        Category = "Unknown",
        Size = "Small",
        Era = "Victorian",
        BuildCost = 5000,
        MaintenanceCost = 50,
        Population = 0,
        Workers = 0,
        RequiredTech = {},
        MinYear = 1850,
        MinPopulation = 0
    }
    
    -- Kategorie aus Dateinamen ableiten
    if buildingId:find("HOUSE") then
        buildingData.Category = "Residential"
        buildingData.Population = 6
        if buildingId:find("SMALL") then
            buildingData.Size = "Small"
            buildingData.Population = 4
        elseif buildingId:find("MEDIUM") then
            buildingData.Size = "Medium"
            buildingData.Population = 8
        elseif buildingId:find("LARGE") then
            buildingData.Size = "Large"
            buildingData.Population = 15
        end
    elseif buildingId:find("MINE") or buildingId:find("MILL") then
        buildingData.Category = "Industry"
        buildingData.Workers = 50
        buildingData.Production = {"Generic"}
        buildingData.ProductionRate = 100
    end
    
    -- Era aus Dateinamen ableiten
    if buildingId:find("VICTORIAN") then
        buildingData.Era = "Victorian"
        buildingData.MinYear = 1850
    elseif buildingId:find("MODERN") then
        buildingData.Era = "Modern"
        buildingData.MinYear = 1945
        buildingData.RequiredTech = {"Modern_Construction", "Electricity"}
    end
    
    return buildingData
end

-- Spieler-Gebäude initialisieren
function BuildingManager:InitializePlayerBuildings(playerId)
    if not self.PlayerBuildings[playerId] then
        self.PlayerBuildings[playerId] = {
            Buildings = {},
            TotalPopulation = 0,
            TotalWorkers = 0,
            MaintenanceCosts = 0
        }
    end
end

-- Gebäude bauen
function BuildingManager:BuildBuilding(playerId, buildingId, position)
    local buildingData = self.BuildingAssets[buildingId]
    if not buildingData then
        return false, "Gebäude nicht gefunden: " .. buildingId
    end
    
    -- Requirements prüfen
    local canBuild, reason = self:CheckBuildingRequirements(playerId, buildingData)
    if not canBuild then
        return false, reason
    end
    
    -- Kosten abziehen
    local playerData = self.EconomyManager:GetPlayerData(playerId)
    if playerData.Money < buildingData.BuildCost then
        return false, "Nicht genug Geld"
    end
    
    self.EconomyManager:SpendMoney(playerId, buildingData.BuildCost)
    
    -- Gebäude erstellen
    local building = {
        ID = buildingId,
        Data = buildingData,
        Position = position,
        BuildTime = buildingData.BuildTime or 30,
        ConstructionProgress = 0,
        IsCompleted = false,
        Level = 1
    }
    
    -- Zu Spieler-Gebäuden hinzufügen
    local playerBuildings = self.PlayerBuildings[playerId]
    table.insert(playerBuildings.Buildings, building)
    
    print("🏗️ Gebäude-Bau gestartet:", buildingData.Name, "für Spieler", playerId)
    return true, "Gebäude-Bau gestartet"
end

-- Gebäude-Requirements prüfen
function BuildingManager:CheckBuildingRequirements(playerId, buildingData)
    -- Jahr prüfen
    local currentYear = self.EconomyManager:GetCurrentYear()
    if currentYear < buildingData.MinYear then
        return false, "Gebäude erst ab " .. buildingData.MinYear .. " verfügbar"
    end
    
    -- Bevölkerung prüfen
    local playerData = self.EconomyManager:GetPlayerData(playerId)
    if playerData.Population < buildingData.MinPopulation then
        return false, "Mindestbevölkerung erforderlich: " .. buildingData.MinPopulation
    end
    
    -- Technologie prüfen
    if buildingData.RequiredTech then
        for _, techId in pairs(buildingData.RequiredTech) do
            if not self.TechTreeManager:IsTechUnlocked(playerId, techId) then
                return false, "Technologie erforderlich: " .. techId
            end
        end
    end
    
    return true, "Requirements erfüllt"
end

-- Verfügbare Gebäude für Spieler abrufen
function BuildingManager:GetAvailableBuildings(playerId, category)
    local availableBuildings = {}
    local currentYear = self.EconomyManager:GetCurrentYear()
    
    for buildingId, buildingData in pairs(self.BuildingAssets) do
        if not category or buildingData.Category == category then
            local canBuild, reason = self:CheckBuildingRequirements(playerId, buildingData)
            if canBuild then
                availableBuildings[buildingId] = buildingData
            end
        end
    end
    
    return availableBuildings
end

-- Gebäude upgraden
function BuildingManager:UpgradeBuilding(playerId, buildingIndex)
    local playerBuildings = self.PlayerBuildings[playerId]
    if not playerBuildings then
        return false, "Spieler-Gebäude nicht gefunden"
    end
    
    local building = playerBuildings.Buildings[buildingIndex]
    if not building then
        return false, "Gebäude nicht gefunden"
    end
    
    -- Upgrade-Möglichkeiten prüfen (für Industrie-Gebäude)
    if building.Data.Category == "Industry" and building.Data.TechLevel then
        local nextLevel = building.Level + 1
        local upgradePath = building.Data.TechUpgrades["Level_" .. nextLevel]
        
        if upgradePath then
            -- Technologie-Requirements prüfen
            if upgradePath.RequiredTech then
                if not self.TechTreeManager:IsTechUnlocked(playerId, upgradePath.RequiredTech) then
                    return false, "Technologie erforderlich: " .. upgradePath.RequiredTech
                end
            end
            
            -- Jahr prüfen
            local currentYear = self.EconomyManager:GetCurrentYear()
            if currentYear < upgradePath.UnlockYear then
                return false, "Upgrade erst ab " .. upgradePath.UnlockYear .. " verfügbar"
            end
            
            -- Kosten prüfen
            local playerData = self.EconomyManager:GetPlayerData(playerId)
            if playerData.Money < upgradePath.UpgradeCost then
                return false, "Nicht genug Geld für Upgrade"
            end
            
            -- Upgrade durchführen
            self.EconomyManager:SpendMoney(playerId, upgradePath.UpgradeCost)
            building.Level = nextLevel
            building.Data.ProductionRate = upgradePath.ProductionRate
            building.Data.Workers = upgradePath.Workers
            
            print("🔧 Gebäude upgraded:", building.Data.Name, "auf Level", nextLevel)
            return true, "Gebäude erfolgreich upgraded"
        end
    end
    
    return false, "Kein Upgrade verfügbar"
end

-- Bau-Fortschritt aktualisieren
function BuildingManager:UpdateConstruction(playerId, deltaTime)
    local playerBuildings = self.PlayerBuildings[playerId]
    if not playerBuildings then
        return
    end
    
    for _, building in pairs(playerBuildings.Buildings) do
        if not building.IsCompleted then
            -- Baufortschritt erhöhen (deltaTime in Sekunden, BuildTime in Tagen)
            local progressPerSecond = 1 / (building.BuildTime * 24 * 60 * 60)
            building.ConstructionProgress = building.ConstructionProgress + (deltaTime * progressPerSecond)
            
            -- Bau abgeschlossen?
            if building.ConstructionProgress >= 1.0 then
                building.IsCompleted = true
                self:CompleteBuildingConstruction(playerId, building)
            end
        end
    end
end

-- Gebäude-Bau abschließen
function BuildingManager:CompleteBuildingConstruction(playerId, building)
    local playerBuildings = self.PlayerBuildings[playerId]
    
    -- Statistiken aktualisieren
    if building.Data.Population then
        playerBuildings.TotalPopulation = playerBuildings.TotalPopulation + building.Data.Population
        self.EconomyManager:UpdatePopulation(playerId, building.Data.Population)
    end
    
    if building.Data.Workers then
        playerBuildings.TotalWorkers = playerBuildings.TotalWorkers + building.Data.Workers
    end
    
    playerBuildings.MaintenanceCosts = playerBuildings.MaintenanceCosts + building.Data.MaintenanceCost
    
    print("🏗️ Gebäude-Bau abgeschlossen:", building.Data.Name)
    
    -- Event an Client senden
    local player = Players:GetPlayerByUserId(playerId)
    if player then
        local buildingCompleteEvent = ReplicatedStorage.Events:FindFirstChild("BuildingCompleteEvent")
        if buildingCompleteEvent then
            buildingCompleteEvent:FireClient(player, building)
        end
    end
end

-- Spieler-Gebäude-Statistiken abrufen
function BuildingManager:GetPlayerBuildingStats(playerId)
    local playerBuildings = self.PlayerBuildings[playerId]
    if not playerBuildings then
        return {
            TotalBuildings = 0,
            TotalPopulation = 0,
            TotalWorkers = 0,
            MaintenanceCosts = 0
        }
    end
    
    return {
        TotalBuildings = #playerBuildings.Buildings,
        TotalPopulation = playerBuildings.TotalPopulation,
        TotalWorkers = playerBuildings.TotalWorkers,
        MaintenanceCosts = playerBuildings.MaintenanceCosts
    }
end

return BuildingManager
