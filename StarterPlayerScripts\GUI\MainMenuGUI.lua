-- StarterPlayerScripts/GUI/MainMenuGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- <PERSON><PERSON><PERSON>tertes Main Menu mit vollständiger Spielkonfiguration

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local StartNewGameEvent = Events:WaitForChild("StartNewGameEvent")
local LoadGameEvent = Events:WaitForChild("LoadGameEvent")
local GetSaveGamesFunction = Events:WaitForChild("GetSaveGamesFunction")

local MainMenuGUI = {}
MainMenuGUI.IsOpen = true
MainMenuGUI.CurrentSeed = ""
MainMenuGUI.SelectedMapSize = "MEDIUM"
MainMenuGUI.SelectedStartYear = 1950
MainMenuGUI.SelectedClimate = "TEMPERATE"
MainMenuGUI.SelectedDifficulty = "NORMAL"
MainMenuGUI.CurrentTab = "NEW_GAME" -- NEW_GAME, LOAD_GAME, SETTINGS, CAMPAIGNS

-- Erweiterte Spielkonfiguration
MainMenuGUI.GameConfig = {
    -- KI & Konkurrenz
    aiCompetitors = true,
    aiCompetitorCount = 2,
    aiDifficulty = "NORMAL", -- EASY, NORMAL, HARD, EXPERT
    
    -- Wirtschafts-Events
    economicCrises = true,
    marketVolatility = "NORMAL", -- LOW, NORMAL, HIGH, EXTREME
    seasonalEffects = true,
    
    -- Zufalls-Events
    randomEvents = true,
    naturalDisasters = true,
    disasterFrequency = "NORMAL", -- RARE, NORMAL, FREQUENT
    
    -- Gameplay-Modifikatoren
    vehicleBreakdowns = true,
    maintenanceCosts = true,
    environmentalImpact = true,
    
    -- Finanz-Optionen
    startingMoney = 1000000,
    loansAvailable = true,
    bankruptcyEnabled = true,
    
    -- Technologie
    techResearch = true,
    techProgression = "HISTORICAL", -- HISTORICAL, ACCELERATED, SANDBOX
    
    -- Sonstiges
    pauseOnEvents = false,
    autoSave = true,
    autoSaveInterval = 5 -- Minuten
}

-- Karten-Größen
local MAP_SIZES = {
    SMALL = {name = "Klein (512x512)", size = 512, cost = 0},
    MEDIUM = {name = "Mittel (1024x1024)", size = 1024, cost = 0},
    LARGE = {name = "Groß (2048x2048)", size = 2048, cost = 0},
    HUGE = {name = "Riesig (4096x4096)", size = 4096, cost = 0}
}

-- Klima-Typen
local CLIMATES = {
    TEMPERATE = {name = "Gemäßigt", desc = "Ausgewogenes Klima"},
    TROPICAL = {name = "Tropisch", desc = "Heiß und feucht"},
    ARCTIC = {name = "Arktisch", desc = "Kalt und schneereich"},
    DESERT = {name = "Wüste", desc = "Heiß und trocken"}
}

-- Schwierigkeitsgrade
local DIFFICULTIES = {
    SANDBOX = {name = "Sandbox", desc = "Unbegrenzte Ressourcen", startMoney = *********},
    EASY = {name = "Einfach", desc = "Entspanntes Spiel", startMoney = 5000000},
    NORMAL = {name = "Normal", desc = "Ausgewogene Herausforderung", startMoney = 1000000},
    HARD = {name = "Schwer", desc = "Echte Herausforderung", startMoney = 500000},
    EXPERT = {name = "Experte", desc = "Nur für Profis", startMoney = 200000}
}

-- GUI erstellen
function MainMenuGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "MainMenuGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hintergrund
    local background = Instance.new("Frame")
    background.Size = UDim2.new(1, 0, 1, 0)
    background.BackgroundColor3 = Color3.fromRGB(10, 15, 20)
    background.BorderSizePixel = 0
    background.Parent = screenGui
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(0, 600, 0, 100)
    title.Position = UDim2.new(0.5, -300, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🚂 TRANSPORT FEVER 2 CLONE"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = background
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 1400, 0, 800)
    mainFrame.Position = UDim2.new(0.5, -700, 0.5, -350)
    mainFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = background
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Tab-Navigation
    local tabFrame = Instance.new("Frame")
    tabFrame.Size = UDim2.new(1, -20, 0, 60)
    tabFrame.Position = UDim2.new(0, 10, 0, 10)
    tabFrame.BackgroundColor3 = Color3.fromRGB(30, 35, 40)
    tabFrame.BorderSizePixel = 0
    tabFrame.Parent = mainFrame
    
    local tabCorner = Instance.new("UICorner")
    tabCorner.CornerRadius = UDim.new(0, 10)
    tabCorner.Parent = tabFrame
    
    -- Tab-Buttons
    local tabs = {
        {name = "NEW_GAME", text = "🆕 Neues Spiel", icon = "🆕"},
        {name = "LOAD_GAME", text = "📁 Spiel laden", icon = "📁"},
        {name = "CAMPAIGNS", text = "🎯 Kampagnen", icon = "🎯"},
        {name = "SETTINGS", text = "⚙️ Einstellungen", icon = "⚙️"}
    }
    
    local tabButtons = {}
    for i, tab in ipairs(tabs) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(1/#tabs, -10, 1, -10)
        button.Position = UDim2.new((i-1)/#tabs, 5, 0, 5)
        button.BackgroundColor3 = tab.name == self.CurrentTab and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(50, 55, 60)
        button.Text = tab.text
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = tabFrame
        
        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 8)
        buttonCorner.Parent = button
        
        button.MouseButton1Click:Connect(function()
            self:SwitchTab(tab.name)
            self:UpdateTabButtons(tabButtons)
        end)
        
        tabButtons[tab.name] = button
    end
    
    -- Content-Frame
    local contentFrame = Instance.new("Frame")
    contentFrame.Size = UDim2.new(1, -20, 0, 710)
    contentFrame.Position = UDim2.new(0, 10, 0, 80)
    contentFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    contentFrame.BorderSizePixel = 0
    contentFrame.Parent = mainFrame
    
    local contentCorner = Instance.new("UICorner")
    contentCorner.CornerRadius = UDim.new(0, 10)
    contentCorner.Parent = contentFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.ContentFrame = contentFrame
    self.TabButtons = tabButtons
    
    return screenGui
end

-- Tab wechseln
function MainMenuGUI:SwitchTab(tabName)
    self.CurrentTab = tabName
    self:UpdateContent()
end

-- Tab-Buttons aktualisieren
function MainMenuGUI:UpdateTabButtons(tabButtons)
    for tabName, button in pairs(tabButtons) do
        if tabName == self.CurrentTab then
            button.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        else
            button.BackgroundColor3 = Color3.fromRGB(50, 55, 60)
        end
    end
end

-- Content aktualisieren
function MainMenuGUI:UpdateContent()
    -- Alten Content löschen
    for _, child in pairs(self.ContentFrame:GetChildren()) do
        if not child:IsA("UICorner") then
            child:Destroy()
        end
    end
    
    if self.CurrentTab == "NEW_GAME" then
        self:CreateNewGameContent()
    elseif self.CurrentTab == "LOAD_GAME" then
        self:CreateLoadGameContent()
    elseif self.CurrentTab == "CAMPAIGNS" then
        self:CreateCampaignsContent()
    elseif self.CurrentTab == "SETTINGS" then
        self:CreateSettingsContent()
    end
end

-- Neues Spiel Content erstellen
function MainMenuGUI:CreateNewGameContent()
    -- Scroll-Container
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.ScrollBarThickness = 12
    scrollFrame.Parent = self.ContentFrame
    
    local yPos = 10
    
    -- Basis-Konfiguration
    self:CreateSection(scrollFrame, "🗺️ KARTEN-KONFIGURATION", yPos)
    yPos = yPos + 50
    
    -- Seed-Eingabe
    yPos = self:CreateSeedInput(scrollFrame, yPos)
    
    -- Kartengröße
    yPos = self:CreateMapSizeSelection(scrollFrame, yPos)
    
    -- Klima
    yPos = self:CreateClimateSelection(scrollFrame, yPos)
    
    -- Startjahr
    yPos = self:CreateStartYearSelection(scrollFrame, yPos)
    
    -- Schwierigkeit
    yPos = self:CreateDifficultySelection(scrollFrame, yPos)
    
    -- Erweiterte Optionen
    self:CreateSection(scrollFrame, "🎮 GAMEPLAY-OPTIONEN", yPos)
    yPos = yPos + 50
    
    -- KI-Konkurrenten
    yPos = self:CreateAIOptions(scrollFrame, yPos)
    
    -- Wirtschafts-Events
    yPos = self:CreateEconomicOptions(scrollFrame, yPos)
    
    -- Zufalls-Events
    yPos = self:CreateRandomEventOptions(scrollFrame, yPos)
    
    -- Start-Button
    local startButton = Instance.new("TextButton")
    startButton.Size = UDim2.new(0, 300, 0, 60)
    startButton.Position = UDim2.new(0.5, -150, 0, yPos + 20)
    startButton.BackgroundColor3 = Color3.fromRGB(0, 150, 0)
    startButton.Text = "🚀 SPIEL STARTEN"
    startButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    startButton.TextScaled = true
    startButton.Font = Enum.Font.SourceSansBold
    startButton.BorderSizePixel = 0
    startButton.Parent = scrollFrame
    
    local startCorner = Instance.new("UICorner")
    startCorner.CornerRadius = UDim.new(0, 10)
    startCorner.Parent = startButton
    
    startButton.MouseButton1Click:Connect(function()
        self:StartNewGame()
    end)
    
    yPos = yPos + 100
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yPos)
end

-- Sektion erstellen
function MainMenuGUI:CreateSection(parent, title, yPos)
    local section = Instance.new("TextLabel")
    section.Size = UDim2.new(1, -20, 0, 40)
    section.Position = UDim2.new(0, 10, 0, yPos)
    section.BackgroundColor3 = Color3.fromRGB(80, 120, 160)
    section.Text = title
    section.TextColor3 = Color3.fromRGB(255, 255, 255)
    section.TextScaled = true
    section.Font = Enum.Font.SourceSansBold
    section.BorderSizePixel = 0
    section.Parent = parent
    
    local sectionCorner = Instance.new("UICorner")
    sectionCorner.CornerRadius = UDim.new(0, 8)
    sectionCorner.Parent = section
    
    return yPos + 50
end

-- Seed-Eingabe erstellen
function MainMenuGUI:CreateSeedInput(parent, yPos)
    local seedFrame = Instance.new("Frame")
    seedFrame.Size = UDim2.new(1, -20, 0, 50)
    seedFrame.Position = UDim2.new(0, 10, 0, yPos)
    seedFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    seedFrame.BorderSizePixel = 0
    seedFrame.Parent = parent
    
    local seedCorner = Instance.new("UICorner")
    seedCorner.CornerRadius = UDim.new(0, 8)
    seedCorner.Parent = seedFrame
    
    local seedLabel = Instance.new("TextLabel")
    seedLabel.Size = UDim2.new(0, 150, 1, 0)
    seedLabel.Position = UDim2.new(0, 10, 0, 0)
    seedLabel.BackgroundTransparency = 1
    seedLabel.Text = "🌱 Seed:"
    seedLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    seedLabel.TextScaled = true
    seedLabel.Font = Enum.Font.SourceSans
    seedLabel.TextXAlignment = Enum.TextXAlignment.Left
    seedLabel.Parent = seedFrame
    
    local seedInput = Instance.new("TextBox")
    seedInput.Size = UDim2.new(1, -170, 0, 30)
    seedInput.Position = UDim2.new(0, 160, 0, 10)
    seedInput.BackgroundColor3 = Color3.fromRGB(60, 65, 70)
    seedInput.Text = self.CurrentSeed
    seedInput.TextColor3 = Color3.fromRGB(255, 255, 255)
    seedInput.TextScaled = true
    seedInput.Font = Enum.Font.SourceSans
    seedInput.BorderSizePixel = 0
    seedInput.Parent = seedFrame
    
    local inputCorner = Instance.new("UICorner")
    inputCorner.CornerRadius = UDim.new(0, 5)
    inputCorner.Parent = seedInput
    
    seedInput.FocusLost:Connect(function()
        self.CurrentSeed = seedInput.Text
    end)
    
    return yPos + 60
end

-- Kartengröße-Auswahl erstellen
function MainMenuGUI:CreateMapSizeSelection(parent, yPos)
    local sizeFrame = Instance.new("Frame")
    sizeFrame.Size = UDim2.new(1, -20, 0, 80)
    sizeFrame.Position = UDim2.new(0, 10, 0, yPos)
    sizeFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    sizeFrame.BorderSizePixel = 0
    sizeFrame.Parent = parent

    local sizeCorner = Instance.new("UICorner")
    sizeCorner.CornerRadius = UDim.new(0, 8)
    sizeCorner.Parent = sizeFrame

    local sizeLabel = Instance.new("TextLabel")
    sizeLabel.Size = UDim2.new(1, 0, 0, 25)
    sizeLabel.Position = UDim2.new(0, 10, 0, 5)
    sizeLabel.BackgroundTransparency = 1
    sizeLabel.Text = "🗺️ Kartengröße:"
    sizeLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    sizeLabel.TextScaled = true
    sizeLabel.Font = Enum.Font.SourceSansBold
    sizeLabel.TextXAlignment = Enum.TextXAlignment.Left
    sizeLabel.Parent = sizeFrame

    local buttonWidth = 0.24
    local i = 0
    for sizeKey, sizeData in pairs(MAP_SIZES) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(buttonWidth, 0, 0, 40)
        button.Position = UDim2.new(i * (buttonWidth + 0.01), 10, 0, 35)
        button.BackgroundColor3 = sizeKey == self.SelectedMapSize and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 65, 70)
        button.Text = sizeData.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.BorderSizePixel = 0
        button.Parent = sizeFrame

        local buttonCorner = Instance.new("UICorner")
        buttonCorner.CornerRadius = UDim.new(0, 5)
        buttonCorner.Parent = button

        button.MouseButton1Click:Connect(function()
            self.SelectedMapSize = sizeKey
            self:UpdateContent()
        end)

        i = i + 1
    end

    return yPos + 90
end

-- KI-Optionen erstellen
function MainMenuGUI:CreateAIOptions(parent, yPos)
    local aiFrame = Instance.new("Frame")
    aiFrame.Size = UDim2.new(1, -20, 0, 120)
    aiFrame.Position = UDim2.new(0, 10, 0, yPos)
    aiFrame.BackgroundColor3 = Color3.fromRGB(40, 45, 50)
    aiFrame.BorderSizePixel = 0
    aiFrame.Parent = parent

    local aiCorner = Instance.new("UICorner")
    aiCorner.CornerRadius = UDim.new(0, 8)
    aiCorner.Parent = aiFrame

    -- KI-Konkurrenten Toggle
    local aiToggle = Instance.new("TextButton")
    aiToggle.Size = UDim2.new(0, 300, 0, 30)
    aiToggle.Position = UDim2.new(0, 10, 0, 10)
    aiToggle.BackgroundColor3 = self.GameConfig.aiCompetitors and Color3.fromRGB(0, 150, 0) or Color3.fromRGB(150, 0, 0)
    aiToggle.Text = "🤖 KI-Konkurrenten: " .. (self.GameConfig.aiCompetitors and "AN" or "AUS")
    aiToggle.TextColor3 = Color3.fromRGB(255, 255, 255)
    aiToggle.TextScaled = true
    aiToggle.Font = Enum.Font.SourceSansBold
    aiToggle.BorderSizePixel = 0
    aiToggle.Parent = aiFrame

    local toggleCorner = Instance.new("UICorner")
    toggleCorner.CornerRadius = UDim.new(0, 5)
    toggleCorner.Parent = aiToggle

    aiToggle.MouseButton1Click:Connect(function()
        self.GameConfig.aiCompetitors = not self.GameConfig.aiCompetitors
        self:UpdateContent()
    end)

    -- KI-Anzahl Slider
    if self.GameConfig.aiCompetitors then
        local countLabel = Instance.new("TextLabel")
        countLabel.Size = UDim2.new(0, 200, 0, 25)
        countLabel.Position = UDim2.new(0, 10, 0, 50)
        countLabel.BackgroundTransparency = 1
        countLabel.Text = "Anzahl KI-Gegner: " .. self.GameConfig.aiCompetitorCount
        countLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        countLabel.TextScaled = true
        countLabel.Font = Enum.Font.SourceSans
        countLabel.TextXAlignment = Enum.TextXAlignment.Left
        countLabel.Parent = aiFrame

        -- Vereinfachter Slider mit Buttons
        for i = 1, 5 do
            local countButton = Instance.new("TextButton")
            countButton.Size = UDim2.new(0, 30, 0, 25)
            countButton.Position = UDim2.new(0, 220 + (i-1) * 35, 0, 50)
            countButton.BackgroundColor3 = i == self.GameConfig.aiCompetitorCount and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 65, 70)
            countButton.Text = tostring(i)
            countButton.TextColor3 = Color3.fromRGB(255, 255, 255)
            countButton.TextScaled = true
            countButton.Font = Enum.Font.SourceSansBold
            countButton.BorderSizePixel = 0
            countButton.Parent = aiFrame

            local countCorner = Instance.new("UICorner")
            countCorner.CornerRadius = UDim.new(0, 3)
            countCorner.Parent = countButton

            countButton.MouseButton1Click:Connect(function()
                self.GameConfig.aiCompetitorCount = i
                self:UpdateContent()
            end)
        end

        -- KI-Schwierigkeit
        local diffLabel = Instance.new("TextLabel")
        diffLabel.Size = UDim2.new(0, 200, 0, 25)
        diffLabel.Position = UDim2.new(0, 10, 0, 85)
        diffLabel.BackgroundTransparency = 1
        diffLabel.Text = "KI-Schwierigkeit:"
        diffLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        diffLabel.TextScaled = true
        diffLabel.Font = Enum.Font.SourceSans
        diffLabel.TextXAlignment = Enum.TextXAlignment.Left
        diffLabel.Parent = aiFrame

        local aiDifficulties = {"EASY", "NORMAL", "HARD", "EXPERT"}
        for i, diff in ipairs(aiDifficulties) do
            local diffButton = Instance.new("TextButton")
            diffButton.Size = UDim2.new(0, 80, 0, 25)
            diffButton.Position = UDim2.new(0, 220 + (i-1) * 85, 0, 85)
            diffButton.BackgroundColor3 = diff == self.GameConfig.aiDifficulty and Color3.fromRGB(100, 150, 255) or Color3.fromRGB(60, 65, 70)
            diffButton.Text = diff
            diffButton.TextColor3 = Color3.fromRGB(255, 255, 255)
            diffButton.TextScaled = true
            diffButton.Font = Enum.Font.SourceSans
            diffButton.BorderSizePixel = 0
            diffButton.Parent = aiFrame

            local diffCorner = Instance.new("UICorner")
            diffCorner.CornerRadius = UDim.new(0, 3)
            diffCorner.Parent = diffButton

            diffButton.MouseButton1Click:Connect(function()
                self.GameConfig.aiDifficulty = diff
                self:UpdateContent()
            end)
        end
    end

    return yPos + 130
end

-- Spiel starten
function MainMenuGUI:StartNewGame()
    -- Konfiguration zusammenstellen
    local gameConfig = {
        seed = self.CurrentSeed,
        mapSize = self.SelectedMapSize,
        startYear = self.SelectedStartYear,
        climate = self.SelectedClimate,
        difficulty = self.SelectedDifficulty,
        gameConfig = self.GameConfig
    }

    -- Event senden
    StartNewGameEvent:FireServer(gameConfig)

    -- Menu schließen
    self:CloseGUI()
end

-- GUI schließen
function MainMenuGUI:CloseGUI()
    if self.ScreenGui then
        self.ScreenGui:Destroy()
        self.IsOpen = false
    end
end

-- Initialisierung
function MainMenuGUI:Initialize()
    self:CreateGUI()
    self:UpdateContent()
    print("🎮 MainMenuGUI initialisiert")
end

-- Auto-Start
MainMenuGUI:Initialize()

return MainMenuGUI
