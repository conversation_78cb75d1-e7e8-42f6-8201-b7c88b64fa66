-- ServerScriptService/CityManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Stadt- und Industrie-Management System

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")
local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")

local CityManager = {}
CityManager.__index = CityManager

function CityManager.new()
    local self = setmetatable({}, CityManager)
    
    -- City data
    self.cities = {}
    self.industries = {}
    self.cityBuildings = {}
    
    -- Industry types and their properties
    self.industryTypes = {
        residential = {
            name = "Wohngebiet",
            produces = {"passengers"},
            consumes = {"goods", "mail"},
            baseProduction = 100,
            baseConsumption = 50,
            growthRate = 0.02,
            maxLevel = 5
        },
        commercial = {
            name = "Gewerbegebiet",
            produces = {"goods", "mail"},
            consumes = {"passengers"},
            baseProduction = 80,
            baseConsumption = 60,
            growthRate = 0.015,
            maxLevel = 4
        },
        industrial = {
            name = "Industriegebiet",
            produces = {"goods"},
            consumes = {"raw_materials", "passengers"},
            baseProduction = 150,
            baseConsumption = 100,
            growthRate = 0.01,
            maxLevel = 6
        },
        farm = {
            name = "Bauernhof",
            produces = {"food", "raw_materials"},
            consumes = {},
            baseProduction = 120,
            baseConsumption = 20,
            growthRate = 0.005,
            maxLevel = 3
        },
        lumber_mill = {
            name = "Sägewerk",
            produces = {"wood", "raw_materials"},
            consumes = {"passengers"},
            baseProduction = 100,
            baseConsumption = 30,
            growthRate = 0.008,
            maxLevel = 4
        },
        quarry = {
            name = "Steinbruch",
            produces = {"stone", "raw_materials"},
            consumes = {"passengers"},
            baseProduction = 90,
            baseConsumption = 40,
            growthRate = 0.006,
            maxLevel = 4
        },
        iron_mine = {
            name = "Eisenmine",
            produces = {"iron", "raw_materials"},
            consumes = {"passengers"},
            baseProduction = 80,
            baseConsumption = 50,
            growthRate = 0.004,
            maxLevel = 5
        },
        coal_mine = {
            name = "Kohlemine",
            produces = {"coal", "raw_materials"},
            consumes = {"passengers"},
            baseProduction = 110,
            baseConsumption = 45,
            growthRate = 0.003,
            maxLevel = 5
        },
        oil_well = {
            name = "Ölquelle",
            produces = {"oil", "raw_materials"},
            consumes = {"passengers"},
            baseProduction = 200,
            baseConsumption = 60,
            growthRate = 0.002,
            maxLevel = 6
        },
        power_plant = {
            name = "Kraftwerk",
            produces = {"electricity"},
            consumes = {"coal", "oil", "passengers"},
            baseProduction = 300,
            baseConsumption = 150,
            growthRate = 0.001,
            maxLevel = 4
        }
    }
    
    -- City growth factors
    self.growthFactors = {
        transport_connection = 1.5, -- Cities with good transport grow faster
        industry_diversity = 1.3, -- Cities with varied industries grow faster
        resource_access = 1.2, -- Cities near resources grow faster
        population_size = 0.8 -- Larger cities grow slower (diminishing returns)
    }
    
    -- Setup city folder in workspace
    self.cityFolder = Workspace:FindFirstChild("Cities") or Instance.new("Folder")
    self.cityFolder.Name = "Cities"
    self.cityFolder.Parent = Workspace
    
    return self
end

-- Initialize cities from TerrainManager
function CityManager:InitializeCities(terrainManager)
    if not terrainManager or not terrainManager.cities then
        print("❌ No terrain manager or cities data provided")
        return
    end
    
    -- Import cities from terrain generation
    for cityId, cityData in pairs(terrainManager.cities) do
        self.cities[cityId] = cityData
        
        -- Create 3D city model
        self:CreateCityModel(cityData)
        
        -- Initialize industries
        for _, industryData in pairs(cityData.industries) do
            local industryId = HttpService:GenerateGUID(false)
            industryData.id = industryId
            industryData.cityId = cityId
            industryData.lastUpdate = os.time()
            industryData.efficiency = 1.0
            industryData.satisfaction = 100
            
            self.industries[industryId] = industryData
        end
    end
    
    print("🏙️ Initialized", self:CountCities(), "cities with", self:CountIndustries(), "industries")
end

-- Create 3D city model
function CityManager:CreateCityModel(city)
    local cityModel = Instance.new("Model")
    cityModel.Name = "City_" .. city.name
    cityModel.Parent = self.cityFolder
    
    -- Calculate world position
    local worldX = (city.x - 128) * 8 -- Scale factor from terrain
    local worldZ = (city.z - 128) * 8
    local worldY = city.height + 2
    
    -- Create city center
    local cityCenter = Instance.new("Part")
    cityCenter.Name = "CityCenter"
    cityCenter.Size = Vector3.new(10, 5, 10)
    cityCenter.Position = Vector3.new(worldX, worldY, worldZ)
    cityCenter.Material = Enum.Material.Concrete
    cityCenter.BrickColor = BrickColor.new("Light stone grey")
    cityCenter.Anchored = true
    cityCenter.CanCollide = true
    cityCenter.Parent = cityModel
    
    -- Add city name label
    local nameGui = Instance.new("BillboardGui")
    nameGui.Size = UDim2.new(0, 200, 0, 50)
    nameGui.StudsOffset = Vector3.new(0, 8, 0)
    nameGui.Parent = cityCenter
    
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Size = UDim2.new(1, 0, 1, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = city.name
    nameLabel.TextColor3 = Color3.new(1, 1, 1)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.Parent = nameGui
    
    -- Create buildings based on city size and population
    local buildingCount = math.min(20, math.floor(city.population / 2000) + city.size * 3)
    
    for i = 1, buildingCount do
        local angle = (i / buildingCount) * math.pi * 2
        local distance = math.random(15, 30 + city.size * 10)
        local buildingX = worldX + math.cos(angle) * distance
        local buildingZ = worldZ + math.sin(angle) * distance
        
        self:CreateBuilding(Vector3.new(buildingX, worldY, buildingZ), city.size, cityModel)
    end
    
    -- Create industry buildings
    for _, industry in pairs(city.industries) do
        if industry.type ~= "residential" and industry.type ~= "commercial" then
            local angle = math.random() * math.pi * 2
            local distance = math.random(40, 80)
            local industryX = worldX + math.cos(angle) * distance
            local industryZ = worldZ + math.sin(angle) * distance
            
            self:CreateIndustryBuilding(Vector3.new(industryX, worldY, industryZ), industry, cityModel)
        end
    end
    
    cityModel.PrimaryPart = cityCenter
    print("🏗️ City model created:", city.name)
end

-- Create individual building
function CityManager:CreateBuilding(position, citySize, parent)
    local building = Instance.new("Part")
    building.Name = "Building"
    
    -- Building size based on city size
    local width = math.random(4, 6 + citySize)
    local depth = math.random(4, 6 + citySize)
    local height = math.random(8, 15 + citySize * 5)
    
    building.Size = Vector3.new(width, height, depth)
    building.Position = position + Vector3.new(0, height/2, 0)
    building.Material = Enum.Material.Concrete
    
    -- Random building colors
    local colors = {"Light stone grey", "Medium stone grey", "Brick yellow", "Reddish brown"}
    building.BrickColor = BrickColor.new(colors[math.random(#colors)])
    
    building.Anchored = true
    building.CanCollide = true
    building.Parent = parent
    
    -- Add windows
    for face = 1, 4 do
        local window = Instance.new("SurfaceGui")
        window.Face = face == 1 and Enum.NormalId.Front or 
                     face == 2 and Enum.NormalId.Back or
                     face == 3 and Enum.NormalId.Left or Enum.NormalId.Right
        window.Parent = building
        
        -- Create window pattern
        for row = 1, math.floor(height / 3) do
            for col = 1, math.floor(width / 2) do
                local windowFrame = Instance.new("Frame")
                windowFrame.Size = UDim2.new(0, 20, 0, 15)
                windowFrame.Position = UDim2.new(0, col * 25, 0, row * 20)
                windowFrame.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
                windowFrame.BorderSizePixel = 1
                windowFrame.Parent = window
            end
        end
    end
end

-- Create industry building
function CityManager:CreateIndustryBuilding(position, industry, parent)
    local industryType = self.industryTypes[industry.type]
    if not industryType then return end
    
    local building = Instance.new("Part")
    building.Name = "Industry_" .. industry.type
    
    -- Industry-specific building design
    local size = Vector3.new(15, 8, 20)
    if industry.type == "farm" then
        size = Vector3.new(25, 6, 15)
        building.Material = Enum.Material.Wood
        building.BrickColor = BrickColor.new("Brown")
    elseif industry.type == "lumber_mill" then
        size = Vector3.new(20, 10, 15)
        building.Material = Enum.Material.Wood
        building.BrickColor = BrickColor.new("Reddish brown")
    elseif industry.type == "quarry" or industry.type == "iron_mine" or industry.type == "coal_mine" then
        size = Vector3.new(12, 15, 12)
        building.Material = Enum.Material.Metal
        building.BrickColor = BrickColor.new("Dark stone grey")
    elseif industry.type == "power_plant" then
        size = Vector3.new(30, 20, 25)
        building.Material = Enum.Material.Concrete
        building.BrickColor = BrickColor.new("Medium stone grey")
    else
        building.Material = Enum.Material.Concrete
        building.BrickColor = BrickColor.new("Light stone grey")
    end
    
    building.Size = size
    building.Position = position + Vector3.new(0, size.Y/2, 0)
    building.Anchored = true
    building.CanCollide = true
    building.Parent = parent
    
    -- Add industry label
    local labelGui = Instance.new("BillboardGui")
    labelGui.Size = UDim2.new(0, 150, 0, 30)
    labelGui.StudsOffset = Vector3.new(0, size.Y/2 + 3, 0)
    labelGui.Parent = building
    
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = industryType.name .. " Lv." .. industry.level
    label.TextColor3 = Color3.new(1, 1, 1)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.Parent = labelGui
    
    -- Add smoke effect for certain industries
    if industry.type == "power_plant" or industry.type == "lumber_mill" then
        local smoke = Instance.new("Smoke")
        smoke.Size = 10
        smoke.Opacity = 0.5
        smoke.RiseVelocity = 15
        smoke.Parent = building
    end
end

-- Update city growth and industry production
function CityManager:UpdateCities(deltaTime)
    for cityId, city in pairs(self.cities) do
        -- Update city population growth
        self:UpdateCityGrowth(city, deltaTime)
        
        -- Update city industries
        self:UpdateCityIndustries(city, deltaTime)
        
        -- Update city demands
        self:UpdateCityDemands(city)
        
        -- Check for new industry opportunities
        if math.random() < 0.001 then -- 0.1% chance per update
            self:CheckNewIndustryOpportunity(city)
        end
    end
end

-- Update city population growth
function CityManager:UpdateCityGrowth(city, deltaTime)
    local baseGrowthRate = 0.001 -- 0.1% per update
    local growthMultiplier = 1.0
    
    -- Apply growth factors
    if self:HasGoodTransportConnection(city) then
        growthMultiplier = growthMultiplier * self.growthFactors.transport_connection
    end
    
    if self:HasIndustryDiversity(city) then
        growthMultiplier = growthMultiplier * self.growthFactors.industry_diversity
    end
    
    if self:HasResourceAccess(city) then
        growthMultiplier = growthMultiplier * self.growthFactors.resource_access
    end
    
    -- Larger cities grow slower
    if city.population > 50000 then
        growthMultiplier = growthMultiplier * self.growthFactors.population_size
    end
    
    -- Apply growth
    local growthRate = baseGrowthRate * growthMultiplier
    local populationGrowth = city.population * growthRate * deltaTime
    city.population = math.floor(city.population + populationGrowth)
    
    -- Update city size based on population
    local newSize = math.min(3, math.floor(city.population / 20000) + 1)
    if newSize > city.size then
        city.size = newSize
        print("📈 City grew:", city.name, "Population:", city.population, "Size:", city.size)
    end
end

-- Update city industries
function CityManager:UpdateCityIndustries(city, deltaTime)
    for _, industryData in pairs(city.industries) do
        local industry = self.industries[industryData.id]
        if industry then
            local industryType = self.industryTypes[industry.type]
            if industryType then
                -- Calculate production
                local baseProduction = industryType.baseProduction * industry.level
                local efficiency = industry.efficiency or 1.0
                local actualProduction = baseProduction * efficiency * deltaTime
                
                industry.production = (industry.production or 0) + actualProduction
                
                -- Calculate consumption
                local baseConsumption = industryType.baseConsumption * industry.level
                industry.consumption = (industry.consumption or 0) + (baseConsumption * deltaTime)
                
                -- Industry growth
                if math.random() < industryType.growthRate * deltaTime then
                    if industry.level < industryType.maxLevel then
                        industry.level = industry.level + 1
                        print("🏭 Industry upgraded:", industryType.name, "in", city.name, "to level", industry.level)
                    end
                end
                
                -- Update satisfaction based on supply/demand
                industry.satisfaction = math.max(0, math.min(100, industry.satisfaction + math.random(-2, 2)))
            end
        end
    end
end

-- Update city demands
function CityManager:UpdateCityDemands(city)
    -- Base demands scale with population
    city.demands.passengers = math.floor(city.population * 0.1)
    city.demands.mail = math.floor(city.population * 0.05)
    city.demands.goods = math.floor(city.population * 0.2)
    
    -- Add industry-specific demands
    for _, industryData in pairs(city.industries) do
        local industry = self.industries[industryData.id]
        if industry then
            local industryType = self.industryTypes[industry.type]
            if industryType then
                for _, resource in pairs(industryType.consumes) do
                    city.demands[resource] = (city.demands[resource] or 0) + (industryType.baseConsumption * industry.level)
                end
            end
        end
    end
end

-- Check for new industry opportunities
function CityManager:CheckNewIndustryOpportunity(city)
    -- Cities can develop new industries based on conditions
    local availableIndustries = {}
    
    -- Check what industries are missing
    local existingTypes = {}
    for _, industryData in pairs(city.industries) do
        existingTypes[industryData.type] = true
    end
    
    -- Add potential new industries
    if not existingTypes["industrial"] and city.population > 10000 then
        table.insert(availableIndustries, "industrial")
    end
    
    if not existingTypes["power_plant"] and city.population > 25000 then
        table.insert(availableIndustries, "power_plant")
    end
    
    -- Resource-based industries
    if self:HasNearbyResource(city, "coal") and not existingTypes["coal_mine"] then
        table.insert(availableIndustries, "coal_mine")
    end
    
    if self:HasNearbyResource(city, "iron") and not existingTypes["iron_mine"] then
        table.insert(availableIndustries, "iron_mine")
    end
    
    if self:HasNearbyResource(city, "oil") and not existingTypes["oil_well"] then
        table.insert(availableIndustries, "oil_well")
    end
    
    -- Create new industry
    if #availableIndustries > 0 then
        local newIndustryType = availableIndustries[math.random(#availableIndustries)]
        local industryId = HttpService:GenerateGUID(false)
        
        local newIndustry = {
            id = industryId,
            type = newIndustryType,
            level = 1,
            cityId = city.id,
            production = 0,
            consumption = 0,
            demand = 0,
            efficiency = 1.0,
            satisfaction = 100,
            lastUpdate = os.time()
        }
        
        table.insert(city.industries, newIndustry)
        self.industries[industryId] = newIndustry
        
        print("🏭 New industry developed:", self.industryTypes[newIndustryType].name, "in", city.name)
    end
end

-- Helper functions for city growth factors
function CityManager:HasGoodTransportConnection(city)
    -- Check if city has transport lines (simplified)
    local transportManager = self:GetTransportManager()
    if not transportManager then return false end
    
    local playerLines = transportManager.lines or {}
    for _, line in pairs(playerLines) do
        if line.route then
            for _, waypoint in pairs(line.route) do
                local distance = math.sqrt((waypoint.x - city.x)^2 + (waypoint.z - city.z)^2)
                if distance < 20 then -- Within 20 units of city
                    return true
                end
            end
        end
    end
    return false
end

function CityManager:HasIndustryDiversity(city)
    local industryTypes = {}
    for _, industryData in pairs(city.industries) do
        industryTypes[industryData.type] = true
    end
    return self:CountTable(industryTypes) >= 3
end

function CityManager:HasResourceAccess(city)
    return self:HasNearbyResource(city, "coal") or 
           self:HasNearbyResource(city, "iron") or 
           self:HasNearbyResource(city, "oil")
end

function CityManager:HasNearbyResource(city, resourceType)
    local terrainManager = self:GetTerrainManager()
    if not terrainManager or not terrainManager.resources then return false end
    
    for _, resource in pairs(terrainManager.resources) do
        if resource.type == resourceType then
            local distance = math.sqrt((resource.x - city.x)^2 + (resource.z - city.z)^2)
            if distance < 50 then -- Within 50 units
                return true
            end
        end
    end
    return false
end

-- Get city statistics
function CityManager:GetCityStats(cityId)
    local city = self.cities[cityId]
    if not city then return nil end
    
    local stats = {
        name = city.name,
        population = city.population,
        size = city.size,
        totalIndustries = #city.industries,
        totalProduction = 0,
        totalConsumption = 0,
        averageSatisfaction = 0,
        demands = city.demands,
        growth = {
            hasTransport = self:HasGoodTransportConnection(city),
            hasDiversity = self:HasIndustryDiversity(city),
            hasResources = self:HasResourceAccess(city)
        }
    }
    
    -- Calculate industry totals
    local satisfactionSum = 0
    for _, industryData in pairs(city.industries) do
        local industry = self.industries[industryData.id]
        if industry then
            stats.totalProduction = stats.totalProduction + (industry.production or 0)
            stats.totalConsumption = stats.totalConsumption + (industry.consumption or 0)
            satisfactionSum = satisfactionSum + (industry.satisfaction or 100)
        end
    end
    
    if #city.industries > 0 then
        stats.averageSatisfaction = satisfactionSum / #city.industries
    end
    
    return stats
end

-- Helper functions
function CityManager:CountCities()
    local count = 0
    for _ in pairs(self.cities) do
        count = count + 1
    end
    return count
end

function CityManager:CountIndustries()
    local count = 0
    for _ in pairs(self.industries) do
        count = count + 1
    end
    return count
end

function CityManager:CountTable(t)
    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end
    return count
end

function CityManager:GetTransportManager()
    local success, TransportManager = pcall(require, script.Parent.TransportManager)
    if success then
        return TransportManager.new()
    end
    return nil
end

function CityManager:GetTerrainManager()
    local success, TerrainManager = pcall(require, script.Parent.TerrainManager)
    if success then
        return TerrainManager.new()
    end
    return nil
end

return CityManager
