-- StarterPlayerScripts/GUI/GUIManager.lua
-- R<PERSON><PERSON>OX SCRIPT TYPE: LocalScript
-- <PERSON><PERSON><PERSON> GUI-Manager für modernes Screen-Layout, Docking-System und responsive Design

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")
local GuiService = game:GetService("GuiService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local GUIManager = {}
GUIManager.__index = GUIManager

-- Konfiguration
local DOCK_ZONES = {
    LEFT = {position = UDim2.new(0, 10, 0, 100), size = UDim2.new(0, 300, 1, -200)},
    RIGHT = {position = UDim2.new(1, -310, 0, 100), size = UDim2.new(0, 300, 1, -200)},
    TOP = {position = UDim2.new(0, 320, 0, 10), size = UDim2.new(1, -640, 0, 80)},
    BOTTOM = {position = UDim2.new(0, 320, 1, -90), size = UDim2.new(1, -640, 0, 80)},
    CENTER = {position = UDim2.new(0.5, -400, 0.5, -300), size = UDim2.new(0, 800, 0, 600)}
}

local ANIMATION_TIME = 0.3
local ANIMATION_STYLE = Enum.EasingStyle.Quart
local ANIMATION_DIRECTION = Enum.EasingDirection.Out

-- Konstruktor
function GUIManager.new()
    local self = setmetatable({}, GUIManager)
    
    self.dockedWindows = {} -- [windowId] = {frame, zone, isVisible}
    self.floatingWindows = {} -- [windowId] = {frame, isDragging, dragOffset}
    self.windowOrder = {} -- Z-Index Management
    self.isInitialized = false
    
    self:Initialize()
    
    return self
end

-- Initialisierung
function GUIManager:Initialize()
    if self.isInitialized then return end
    
    -- Haupt-ScreenGui erstellen
    self.screenGui = Instance.new("ScreenGui")
    self.screenGui.Name = "ModernGUISystem"
    self.screenGui.ResetOnSpawn = false
    self.screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
    self.screenGui.Parent = playerGui
    
    -- Dock-Zonen erstellen
    self:CreateDockZones()
    
    -- Taskbar erstellen
    self:CreateTaskbar()
    
    -- Responsive Design Setup
    self:SetupResponsiveDesign()
    
    -- Hotkeys Setup
    self:SetupHotkeys()
    
    self.isInitialized = true
    print("🖥️ Modernes GUI-System initialisiert")
end

-- Dock-Zonen erstellen
function GUIManager:CreateDockZones()
    self.dockZones = {}
    
    for zoneName, config in pairs(DOCK_ZONES) do
        local dockZone = Instance.new("Frame")
        dockZone.Name = "DockZone_" .. zoneName
        dockZone.Position = config.position
        dockZone.Size = config.size
        dockZone.BackgroundTransparency = 1
        dockZone.BorderSizePixel = 0
        dockZone.Parent = self.screenGui
        
        -- Dock-Indikator (unsichtbar, wird bei Drag angezeigt)
        local dockIndicator = Instance.new("Frame")
        dockIndicator.Name = "DockIndicator"
        dockIndicator.Size = UDim2.new(1, 0, 1, 0)
        dockIndicator.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
        dockIndicator.BackgroundTransparency = 0.7
        dockIndicator.BorderSizePixel = 0
        dockIndicator.Visible = false
        dockIndicator.Parent = dockZone
        
        local indicatorCorner = Instance.new("UICorner")
        indicatorCorner.CornerRadius = UDim.new(0, 8)
        indicatorCorner.Parent = dockIndicator
        
        self.dockZones[zoneName] = {
            frame = dockZone,
            indicator = dockIndicator,
            windows = {}
        }
    end
end

-- Taskbar erstellen
function GUIManager:CreateTaskbar()
    local taskbar = Instance.new("Frame")
    taskbar.Name = "Taskbar"
    taskbar.Size = UDim2.new(1, 0, 0, 60)
    taskbar.Position = UDim2.new(0, 0, 1, -60)
    taskbar.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    taskbar.BorderSizePixel = 0
    taskbar.Parent = self.screenGui
    
    -- Taskbar-Gradient
    local gradient = Instance.new("UIGradient")
    gradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 30, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 20, 25))
    }
    gradient.Rotation = 90
    gradient.Parent = taskbar
    
    -- Taskbar-Layout
    local layout = Instance.new("UIListLayout")
    layout.FillDirection = Enum.FillDirection.Horizontal
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    layout.VerticalAlignment = Enum.VerticalAlignment.Center
    layout.Padding = UDim.new(0, 5)
    layout.Parent = taskbar
    
    local padding = Instance.new("UIPadding")
    padding.PaddingAll = UDim.new(0, 10)
    padding.Parent = taskbar
    
    -- Logo/Titel
    local logo = Instance.new("TextLabel")
    logo.Size = UDim2.new(0, 200, 1, -20)
    logo.BackgroundTransparency = 1
    logo.Text = "🚂 TRANSPORT FEVER"
    logo.TextColor3 = Color3.fromRGB(100, 150, 255)
    logo.TextScaled = true
    logo.Font = Enum.Font.SourceSansBold
    logo.Parent = taskbar
    
    -- Taskbar-Buttons Container
    local buttonsContainer = Instance.new("Frame")
    buttonsContainer.Size = UDim2.new(1, -220, 1, -20)
    buttonsContainer.BackgroundTransparency = 1
    buttonsContainer.Parent = taskbar
    
    local buttonsLayout = Instance.new("UIListLayout")
    buttonsLayout.FillDirection = Enum.FillDirection.Horizontal
    buttonsLayout.HorizontalAlignment = Enum.HorizontalAlignment.Left
    buttonsLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    buttonsLayout.Padding = UDim.new(0, 5)
    buttonsLayout.Parent = buttonsContainer
    
    self.taskbar = taskbar
    self.taskbarButtons = buttonsContainer
    
    -- Standard-Taskbar-Buttons erstellen
    self:CreateTaskbarButtons()
end

-- Taskbar-Buttons erstellen
function GUIManager:CreateTaskbarButtons()
    local buttons = {
        {name = "📂 LADEN", id = "load", color = Color3.fromRGB(100, 150, 255)},
        {name = "💾 SPEICHERN", id = "save", color = Color3.fromRGB(100, 255, 100)},
        {name = "🚂 LINIEN", id = "lines", color = Color3.fromRGB(255, 150, 100)},
        {name = "🏭 WIRTSCHAFT", id = "economy", color = Color3.fromRGB(255, 200, 100)},
        {name = "🏗️ BAUEN", id = "build", color = Color3.fromRGB(150, 255, 150)},
        {name = "📊 STATISTIKEN", id = "stats", color = Color3.fromRGB(255, 150, 255)},
        {name = "⚙️ EINSTELLUNGEN", id = "settings", color = Color3.fromRGB(150, 150, 255)}
    }
    
    for _, buttonData in ipairs(buttons) do
        local button = Instance.new("TextButton")
        button.Size = UDim2.new(0, 120, 0, 40)
        button.BackgroundColor3 = buttonData.color
        button.Text = buttonData.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSansBold
        button.BorderSizePixel = 0
        button.Parent = self.taskbarButtons
        
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 8)
        corner.Parent = button
        
        -- Hover-Effekt
        button.MouseEnter:Connect(function()
            TweenService:Create(button, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.new(
                    math.min(buttonData.color.R + 0.1, 1),
                    math.min(buttonData.color.G + 0.1, 1),
                    math.min(buttonData.color.B + 0.1, 1)
                )
            }):Play()
        end)
        
        button.MouseLeave:Connect(function()
            TweenService:Create(button, TweenInfo.new(0.2), {
                BackgroundColor3 = buttonData.color
            }):Play()
        end)
        
        -- Click-Handler
        button.MouseButton1Click:Connect(function()
            self:HandleTaskbarClick(buttonData.id)
        end)
    end
end

-- Responsive Design Setup
function GUIManager:SetupResponsiveDesign()
    local function updateLayout()
        local screenSize = workspace.CurrentCamera.ViewportSize
        local isSmallScreen = screenSize.X < 1200 or screenSize.Y < 800
        
        if isSmallScreen then
            -- Kompaktes Layout für kleine Bildschirme
            DOCK_ZONES.LEFT.size = UDim2.new(0, 250, 1, -200)
            DOCK_ZONES.RIGHT.size = UDim2.new(0, 250, 1, -200)
            self.taskbar.Size = UDim2.new(1, 0, 0, 50)
        else
            -- Standard-Layout für große Bildschirme
            DOCK_ZONES.LEFT.size = UDim2.new(0, 300, 1, -200)
            DOCK_ZONES.RIGHT.size = UDim2.new(0, 300, 1, -200)
            self.taskbar.Size = UDim2.new(1, 0, 0, 60)
        end
        
        -- Dock-Zonen aktualisieren
        for zoneName, config in pairs(DOCK_ZONES) do
            if self.dockZones[zoneName] then
                self.dockZones[zoneName].frame.Size = config.size
                self.dockZones[zoneName].frame.Position = config.position
            end
        end
    end
    
    -- Initial update
    updateLayout()
    
    -- Update bei Größenänderung
    workspace.CurrentCamera:GetPropertyChangedSignal("ViewportSize"):Connect(updateLayout)
end

-- Hotkeys Setup
function GUIManager:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        local keyCode = input.KeyCode
        
        -- F-Tasten für schnelle GUI-Öffnung
        if keyCode == Enum.KeyCode.F1 then
            self:ToggleWindow("lines")
        elseif keyCode == Enum.KeyCode.F2 then
            self:ToggleWindow("economy")
        elseif keyCode == Enum.KeyCode.F3 then
            self:ToggleWindow("build")
        elseif keyCode == Enum.KeyCode.F4 then
            self:ToggleWindow("stats")
        elseif keyCode == Enum.KeyCode.F5 then
            self:HandleTaskbarClick("save")
        elseif keyCode == Enum.KeyCode.F9 then
            self:HandleTaskbarClick("load")
        elseif keyCode == Enum.KeyCode.F10 then
            self:ToggleWindow("settings")
        elseif keyCode == Enum.KeyCode.Escape then
            self:CloseAllFloatingWindows()
        end
    end)
end

-- Taskbar-Click Handler
function GUIManager:HandleTaskbarClick(buttonId)
    if buttonId == "load" then
        -- SaveGameGUI öffnen im Load-Modus
        local SaveGameGUI = require(script.Parent.SaveGameGUI)
        SaveGameGUI:OpenGUI("LOAD")
    elseif buttonId == "save" then
        -- SaveGameGUI öffnen im Save-Modus
        local SaveGameGUI = require(script.Parent.SaveGameGUI)
        SaveGameGUI:OpenGUI("SAVE")
    elseif buttonId == "lines" then
        -- Modernes Linien-GUI öffnen
        local ModernLinesGUI = require(script.Parent.ModernLinesGUI)
        ModernLinesGUI:OpenGUI()
    elseif buttonId == "economy" then
        -- Modernes Finanz-GUI öffnen
        local ModernFinanceGUI = require(script.Parent.ModernFinanceGUI)
        ModernFinanceGUI:OpenGUI()
    elseif buttonId == "build" then
        -- Modernes Bau-GUI öffnen
        local ModernBuildGUI = require(script.Parent.ModernBuildGUI)
        ModernBuildGUI:OpenGUI()
    elseif buttonId == "stats" then
        -- Modernes Statistiken-GUI öffnen
        local ModernStatsGUI = require(script.Parent.ModernStatsGUI)
        ModernStatsGUI:OpenGUI()
    elseif buttonId == "settings" then
        -- Modernes Einstellungen-GUI öffnen
        local ModernSettingsGUI = require(script.Parent.ModernSettingsGUI)
        ModernSettingsGUI:OpenGUI()
    else
        -- Andere Fenster öffnen/schließen
        self:ToggleWindow(buttonId)
    end
end

-- Fenster öffnen/schließen
function GUIManager:ToggleWindow(windowId)
    if self.dockedWindows[windowId] and self.dockedWindows[windowId].isVisible then
        self:HideWindow(windowId)
    else
        self:ShowWindow(windowId)
    end
end

-- Fenster anzeigen
function GUIManager:ShowWindow(windowId)
    -- Hier würden die spezifischen Fenster-GUIs geladen
    print("🖥️ Öffne Fenster:", windowId)
    
    -- Placeholder für jetzt
    local window = self:CreatePlaceholderWindow(windowId)
    self:DockWindow(windowId, window, "CENTER")
end

-- Fenster verstecken
function GUIManager:HideWindow(windowId)
    if self.dockedWindows[windowId] then
        local windowData = self.dockedWindows[windowId]
        
        TweenService:Create(windowData.frame, TweenInfo.new(ANIMATION_TIME, ANIMATION_STYLE), {
            Size = UDim2.new(0, 0, 0, 0),
            BackgroundTransparency = 1
        }):Play()
        
        wait(ANIMATION_TIME)
        windowData.frame:Destroy()
        self.dockedWindows[windowId] = nil
        
        print("🖥️ Fenster geschlossen:", windowId)
    end
end

-- Placeholder-Fenster erstellen (für Demonstration)
function GUIManager:CreatePlaceholderWindow(windowId)
    local window = Instance.new("Frame")
    window.Size = UDim2.new(0, 600, 0, 400)
    window.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    window.BorderSizePixel = 0
    window.Parent = self.screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = window
    
    -- Titel-Bar
    local titleBar = Instance.new("Frame")
    titleBar.Size = UDim2.new(1, 0, 0, 40)
    titleBar.BackgroundColor3 = Color3.fromRGB(35, 40, 45)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = window
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 12)
    titleCorner.Parent = titleBar
    
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, -100, 1, 0)
    title.Position = UDim2.new(0, 10, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "📋 " .. string.upper(windowId)
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(220, 60, 60)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 15)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:HideWindow(windowId)
    end)
    
    -- Content-Bereich
    local content = Instance.new("TextLabel")
    content.Size = UDim2.new(1, -20, 1, -60)
    content.Position = UDim2.new(0, 10, 0, 50)
    content.BackgroundTransparency = 1
    content.Text = "🚧 " .. windowId:upper() .. " GUI\n\nDieses Fenster wird bald mit\nder vollständigen Funktionalität\nimplementiert."
    content.TextColor3 = Color3.fromRGB(180, 180, 180)
    content.TextScaled = true
    content.Font = Enum.Font.SourceSans
    content.Parent = window
    
    return window
end

-- Fenster andocken
function GUIManager:DockWindow(windowId, window, zone)
    local zoneConfig = DOCK_ZONES[zone]
    if not zoneConfig then return end
    
    window.Position = zoneConfig.position
    window.Size = zoneConfig.size
    
    self.dockedWindows[windowId] = {
        frame = window,
        zone = zone,
        isVisible = true
    }
    
    -- Smooth fade-in
    window.BackgroundTransparency = 1
    window.Size = UDim2.new(0, 0, 0, 0)
    
    local sizeTween = TweenService:Create(window, TweenInfo.new(ANIMATION_TIME, ANIMATION_STYLE), {
        Size = zoneConfig.size
    })
    
    local fadeTween = TweenService:Create(window, TweenInfo.new(ANIMATION_TIME), {
        BackgroundTransparency = 0
    })
    
    sizeTween:Play()
    fadeTween:Play()
end

-- Alle schwebenden Fenster schließen
function GUIManager:CloseAllFloatingWindows()
    for windowId, _ in pairs(self.floatingWindows) do
        self:HideWindow(windowId)
    end
end

-- Singleton-Instanz
local GUIManagerInstance = GUIManager.new()

return GUIManagerInstance
