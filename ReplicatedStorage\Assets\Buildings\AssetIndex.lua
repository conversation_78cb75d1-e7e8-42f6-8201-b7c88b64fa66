-- Asset-Index für Transport Empire
-- Zentrale Übersicht aller verfügbaren Gebäude-Assets

local AssetIndex = {}

-- Gebäude-Kategorien und ihre Strukturen
AssetIndex.Categories = {
    Residential = {
        Victorian = {
            "HOUSE_VICTORIAN_COTTAGE",
            "HOUSE_VICTORIAN_TOWNHOUSE"
        },
        <PERSON><PERSON> = {
            "HOUSE_EDWARDIAN_VILLA",
            "APARTMENT_EDWARDIAN"
        },
        Modern = {
            "HOUSE_RANCH_STYLE",
            "APARTMENT_MODERNIST"
        },
        Contemporary = {
            "APARTMENT_HIGH_RISE",
            "APARTMENT_LUXURY"
        }
    },
    
    Commercial = {
        Victorian = {
            "SHOP_VICTORIAN_GENERAL",
            "BANK_VICTORIAN"
        },
        <PERSON><PERSON> = {
            "DEPARTMENT_STORE_EARLY",
            "HOTEL_GRAND"
        },
        Art_Deco = {
            "CINEMA_ART_DECO",
            "OFFICE_ART_DECO"
        },
        Modern = {
            "SHOPPING_CENTER_EARLY",
            "MALL_ENCLOSED",
            "MEGAMALL"
        }
    },
    
    Industry = {
        Early_Industrial = {
            "COAL_MINE",
            "IRON_MINE",
            "STEEL_MILL",
            "TEXTILE_MILL"
        },
        Chemical = {
            "CHEMICAL_PLANT",
            "OIL_REFINERY",
            "PLASTICS_FACTORY"
        },
        Modern = {
            "ELECTRONICS_FACTORY",
            "COMPUTER_FACTORY",
            "AUTOMOTIVE_PLANT"
        },
        Power = {
            "COAL_POWER_PLANT",
            "NUCLEAR_POWER_PLANT",
            "SOLAR_FARM"
        }
    },
    
    Public = {
        Government = {
            "CITY_HALL_VICTORIAN",
            "CITY_HALL_MODERN",
            "COURTHOUSE"
        },
        Education = {
            "SCHOOL_VICTORIAN",
            "HIGH_SCHOOL",
            "UNIVERSITY"
        },
        Healthcare = {
            "HOSPITAL_SMALL",
            "HOSPITAL_LARGE",
            "MEDICAL_CENTER"
        },
        Safety = {
            "FIRE_STATION",
            "POLICE_STATION",
            "PRISON"
        }
    }
}

-- Epochen-Definitionen
AssetIndex.Eras = {
    Victorian = {
        Name = "Viktorianische Ära",
        Years = {1850, 1900},
        Style = "Ornate, Gothic Revival, Bay Windows",
        Materials = {"Wood", "Brick", "Stone"},
        Colors = {"Dark_Wood", "Red_Brick", "White_Trim"}
    },
    
    Edwardian = {
        Name = "Jahrhundertwende",
        Years = {1900, 1920},
        Style = "Symmetrical, Classical Elements, Electric Lighting",
        Materials = {"Brick", "Stone", "Steel"},
        Colors = {"Red_Brick", "White_Stone", "Green_Copper"}
    },
    
    Art_Deco = {
        Name = "Zwischenkriegszeit",
        Years = {1920, 1940},
        Style = "Geometric, Streamlined, Vertical Emphasis",
        Materials = {"Concrete", "Steel", "Glass"},
        Colors = {"Cream", "Gold", "Black"}
    },
    
    Mid_Century = {
        Name = "Nachkriegszeit",
        Years = {1945, 1960},
        Style = "Clean Lines, Glass, Horizontal Emphasis",
        Materials = {"Steel", "Glass", "Concrete"},
        Colors = {"White", "Beige", "Pastels"}
    },
    
    Late_Modern = {
        Name = "Moderne Ära",
        Years = {1960, 1980},
        Style = "Brutalist, Concrete, Minimal Decoration",
        Materials = {"Concrete", "Steel", "Glass"},
        Colors = {"Gray", "Brown", "Orange"}
    },
    
    Postmodern = {
        Name = "Postmoderne",
        Years = {1980, 2000},
        Style = "Eclectic, Colorful, Historical References",
        Materials = {"Mixed", "Synthetic", "Glass"},
        Colors = {"Varied", "Bright", "Metallic"}
    }
}

-- Produktionsketten
AssetIndex.ProductionChains = {
    Steel = {
        Inputs = {"Iron_Ore", "Coal"},
        Producer = "STEEL_MILL",
        Consumers = {"MACHINE_FACTORY", "CONSTRUCTION_INDUSTRY", "AUTOMOTIVE_PLANT"}
    },
    
    Electronics = {
        Inputs = {"Metals", "Plastics", "Rare_Earth"},
        Producer = "ELECTRONICS_FACTORY",
        Consumers = {"CONSUMER_MARKET", "AUTOMOTIVE_INDUSTRY", "AEROSPACE_INDUSTRY"}
    },
    
    Coal = {
        Inputs = {},
        Producer = "COAL_MINE",
        Consumers = {"STEEL_MILL", "COAL_POWER_PLANT", "CHEMICAL_PLANT"}
    },
    
    Electricity = {
        Inputs = {"Coal", "Nuclear_Fuel", "Solar"},
        Producer = {"COAL_POWER_PLANT", "NUCLEAR_POWER_PLANT", "SOLAR_FARM"},
        Consumers = {"ALL_MODERN_BUILDINGS"}
    }
}

-- Stadtentwicklungs-Stufen
AssetIndex.CityStages = {
    Village = {
        Population = {0, 500},
        Era = "Victorian",
        Buildings = {"HOUSE_VICTORIAN_COTTAGE", "SHOP_VICTORIAN_GENERAL"}
    },
    
    Town = {
        Population = {500, 2000},
        Era = "Edwardian", 
        Buildings = {"HOUSE_EDWARDIAN_VILLA", "DEPARTMENT_STORE_EARLY", "CITY_HALL_VICTORIAN"}
    },
    
    City = {
        Population = {2000, 10000},
        Era = "Art_Deco",
        Buildings = {"APARTMENT_ART_DECO", "CINEMA_ART_DECO", "OFFICE_ART_DECO"}
    },
    
    Metropolis = {
        Population = {10000, 50000},
        Era = "Late_Modern",
        Buildings = {"APARTMENT_HIGH_RISE", "MALL_ENCLOSED", "OFFICE_TOWER"}
    },
    
    Megacity = {
        Population = {50000, 999999},
        Era = "Postmodern",
        Buildings = {"APARTMENT_LUXURY", "MEGAMALL", "OFFICE_POSTMODERN"}
    }
}

-- Hilfsfunktionen
function AssetIndex:GetBuildingsByEra(eraName)
    local buildings = {}
    
    for category, eras in pairs(self.Categories) do
        if eras[eraName] then
            for _, buildingId in pairs(eras[eraName]) do
                table.insert(buildings, buildingId)
            end
        end
    end
    
    return buildings
end

function AssetIndex:GetEraByYear(year)
    for eraName, eraData in pairs(self.Eras) do
        if year >= eraData.Years[1] and year <= eraData.Years[2] then
            return eraName
        end
    end
    return "Unknown"
end

function AssetIndex:GetCityStageByPopulation(population)
    for stageName, stageData in pairs(self.CityStages) do
        if population >= stageData.Population[1] and population <= stageData.Population[2] then
            return stageName
        end
    end
    return "Megacity"
end

function AssetIndex:GetAvailableBuildings(year, population)
    local era = self:GetEraByYear(year)
    local stage = self:GetCityStageByPopulation(population)
    
    local availableBuildings = {}
    
    -- Gebäude nach Epoche filtern
    for category, eras in pairs(self.Categories) do
        availableBuildings[category] = {}
        
        for eraName, buildings in pairs(eras) do
            local eraData = self.Eras[eraName]
            if eraData and year >= eraData.Years[1] and year <= eraData.Years[2] then
                for _, buildingId in pairs(buildings) do
                    table.insert(availableBuildings[category], buildingId)
                end
            end
        end
    end
    
    return availableBuildings
end

return AssetIndex
