-- ServerScriptService/QuickManagerTest.lua
-- ROBLOX SCRIPT TYPE: Script
-- Schneller Test aller Manager-Module

local ServerScriptService = game:GetService("ServerScriptService")

print("🧪 SCHNELLER MANAGER-TEST GESTARTET")

-- Managers-Ordner prüfen
local managersFolder = ServerScriptService:FindFirstChild("Managers")
if not managersFolder then
    warn("❌ FEHLER: Managers-Ordner nicht gefunden!")
    warn("   Stelle sicher, dass alle Manager in ServerScriptService/Managers/ liegen")
    return
end

print("📁 Managers-Ordner gefunden:", managersFolder:GetFullName())

-- Alle Manager auflisten
local allManagers = {}
for _, child in pairs(managersFolder:GetChildren()) do
    if child:IsA("ModuleScript") then
        table.insert(allManagers, child.Name)
    end
end

print("📊 Gefundene Manager:", #allManagers)
for i, managerName in pairs(allManagers) do
    print("  " .. i .. ". " .. managerName)
end

-- Wichtigste Manager testen
local criticalManagers = {
    "EconomyManager",
    "VehicleManager", 
    "CityManager",
    "TransportManager",
    "GameStateManager",
    "SaveManager"
}

print("")
print("🔍 TESTE KRITISCHE MANAGER:")

local workingManagers = 0
local totalManagers = #criticalManagers

for _, managerName in pairs(criticalManagers) do
    local managerScript = managersFolder:FindFirstChild(managerName)
    
    if managerScript then
        local success, result = pcall(function()
            local Manager = require(managerScript)
            return Manager ~= nil and type(Manager) == "table"
        end)
        
        if success and result then
            print("✅", managerName, "- OK")
            workingManagers = workingManagers + 1
        else
            warn("❌", managerName, "- FEHLER:", tostring(result))
        end
    else
        warn("❌", managerName, "- NICHT GEFUNDEN")
    end
end

print("")
print("📊 ERGEBNIS:")
print("  ✅ Funktionierende Manager:", workingManagers)
print("  📦 Getestete Manager:", totalManagers)
print("  📈 Erfolgsrate:", math.floor((workingManagers/totalManagers)*100) .. "%")

if workingManagers == totalManagers then
    print("🎉 ALLE KRITISCHEN MANAGER FUNKTIONIEREN!")
    print("✅ Das Spiel sollte problemlos starten können")
elseif workingManagers >= (totalManagers * 0.8) then
    print("⚠️ MEISTE MANAGER FUNKTIONIEREN")
    print("🎮 Das Spiel sollte grundsätzlich funktionieren")
else
    warn("❌ ZU VIELE MANAGER-FEHLER!")
    warn("🔧 Überprüfe die Manager-Implementierung")
end

-- GameManager-Test
print("")
print("🎮 TESTE GAMEMANAGER:")

local gameManagerScript = ServerScriptService:FindFirstChild("GameManager")
if gameManagerScript then
    local success, result = pcall(function()
        local GameManager = require(gameManagerScript)
        return GameManager ~= nil and type(GameManager) == "table"
    end)
    
    if success and result then
        print("✅ GameManager - OK")
        print("🎯 BEREIT FÜR SPIELSTART!")
    else
        warn("❌ GameManager - FEHLER:", tostring(result))
        warn("🔧 GameManager muss repariert werden")
    end
else
    warn("❌ GameManager nicht gefunden!")
end

print("")
print("🏁 MANAGER-TEST ABGESCHLOSSEN")

-- Empfehlungen ausgeben
print("")
print("💡 EMPFEHLUNGEN:")
if workingManagers == totalManagers then
    print("  🚀 Führe GameInitializer.lua aus um das Spiel zu starten")
    print("  🎮 Alle Systeme sind bereit!")
else
    print("  🔧 Repariere defekte Manager vor dem Spielstart")
    print("  📋 Überprüfe Script-Typen (alle Manager = ModuleScript)")
    print("  📁 Stelle sicher, dass alle Manager im Managers/ Ordner liegen")
end
