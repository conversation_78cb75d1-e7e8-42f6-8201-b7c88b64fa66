-- ServerScriptService/MultiplayerManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Erweiterte Multiplayer-Funktionen mit Kooperation und Konkurrenz

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")
local MessagingService = game:GetService("MessagingService")

local MultiplayerManager = {}
MultiplayerManager.__index = MultiplayerManager

function MultiplayerManager.new()
    local self = setmetatable({}, MultiplayerManager)
    
    -- Player data
    self.connectedPlayers = {}
    self.playerCompanies = {}
    
    -- Game modes
    self.gameMode = "competitive" -- competitive, cooperative, sandbox
    self.maxPlayers = 8
    self.allowSpectators = true
    
    -- Multiplayer features
    self.sharedInfrastructure = false -- Can players share tracks/roads?
    self.tradingEnabled = true -- Can players trade resources/money?
    self.allianceSystem = true -- Can players form alliances?
    
    -- Server synchronization
    self.syncInterval = 1.0 -- seconds
    self.lastSync = 0
    
    -- Chat system
    self.chatHistory = {}
    self.maxChatMessages = 100
    
    -- Alliance system
    self.alliances = {}
    self.allianceInvites = {}
    
    -- Trading system
    self.tradeOffers = {}
    self.tradeHistory = {}
    
    -- Spectator system
    self.spectators = {}
    
    return self
end

-- Initialize multiplayer for player
function MultiplayerManager:InitializePlayer(player)
    local playerId = tostring(player.UserId)
    
    self.connectedPlayers[playerId] = {
        player = player,
        joinTime = os.time(),
        isActive = true,
        isSpectator = false,
        company = {
            name = player.Name .. "'s Transport Co.",
            color = self:GenerateCompanyColor(),
            founded = os.time(),
            reputation = 50, -- 0-100
            publicRating = 3.0 -- 1-5 stars
        },
        
        -- Multiplayer stats
        stats = {
            gamesPlayed = 0,
            gamesWon = 0,
            totalRevenue = 0,
            bestCompanyValue = 0,
            cooperationScore = 50, -- How well they cooperate
            competitionScore = 50 -- How competitive they are
        },
        
        -- Social features
        friends = {},
        blockedPlayers = {},
        currentAlliance = nil,
        
        -- Communication
        lastChatTime = 0,
        chatCooldown = 1.0, -- seconds between messages
        
        -- Permissions
        permissions = {
            canTrade = true,
            canFormAlliances = true,
            canUseSharedInfrastructure = self.sharedInfrastructure,
            canSpectate = true
        }
    }
    
    self.playerCompanies[playerId] = self.connectedPlayers[playerId].company
    
    print("🌐 Multiplayer initialized for player:", player.Name)
    
    -- Notify other players
    self:BroadcastPlayerJoined(player)
end

-- Generate unique company color
function MultiplayerManager:GenerateCompanyColor()
    local colors = {
        Color3.fromRGB(255, 100, 100), -- Red
        Color3.fromRGB(100, 255, 100), -- Green
        Color3.fromRGB(100, 100, 255), -- Blue
        Color3.fromRGB(255, 255, 100), -- Yellow
        Color3.fromRGB(255, 100, 255), -- Magenta
        Color3.fromRGB(100, 255, 255), -- Cyan
        Color3.fromRGB(255, 150, 100), -- Orange
        Color3.fromRGB(150, 100, 255), -- Purple
        Color3.fromRGB(100, 255, 150), -- Light Green
        Color3.fromRGB(255, 100, 150)  -- Pink
    }
    
    -- Find unused color
    local usedColors = {}
    for _, playerData in pairs(self.connectedPlayers) do
        if playerData.company and playerData.company.color then
            usedColors[tostring(playerData.company.color)] = true
        end
    end
    
    for _, color in pairs(colors) do
        if not usedColors[tostring(color)] then
            return color
        end
    end
    
    -- If all colors used, generate random
    return Color3.fromRGB(
        math.random(100, 255),
        math.random(100, 255),
        math.random(100, 255)
    )
end

-- Player disconnect handling
function MultiplayerManager:PlayerDisconnected(player)
    local playerId = tostring(player.UserId)
    local playerData = self.connectedPlayers[playerId]
    
    if playerData then
        playerData.isActive = false
        playerData.disconnectTime = os.time()
        
        -- Handle alliance membership
        if playerData.currentAlliance then
            self:RemoveFromAlliance(playerId, playerData.currentAlliance)
        end
        
        -- Cancel pending trades
        self:CancelPlayerTrades(playerId)
        
        print("🌐 Player disconnected:", player.Name)
        
        -- Notify other players
        self:BroadcastPlayerLeft(player)
        
        -- Keep data for potential reconnection (5 minutes)
        wait(300)
        if not playerData.isActive then
            self.connectedPlayers[playerId] = nil
            self.playerCompanies[playerId] = nil
        end
    end
end

-- Chat system
function MultiplayerManager:SendChatMessage(player, message, channel)
    local playerId = tostring(player.UserId)
    local playerData = self.connectedPlayers[playerId]
    
    if not playerData then return false, "Player not found" end
    
    -- Check cooldown
    local currentTime = tick()
    if currentTime - playerData.lastChatTime < playerData.chatCooldown then
        return false, "Chat cooldown active"
    end
    
    -- Filter message (basic)
    message = self:FilterMessage(message)
    if not message or message == "" then
        return false, "Message filtered"
    end
    
    -- Create chat message
    local chatMessage = {
        id = HttpService:GenerateGUID(false),
        playerId = playerId,
        playerName = player.Name,
        message = message,
        channel = channel or "global",
        timestamp = os.time(),
        companyColor = playerData.company.color
    }
    
    -- Add to history
    table.insert(self.chatHistory, chatMessage)
    if #self.chatHistory > self.maxChatMessages then
        table.remove(self.chatHistory, 1)
    end
    
    playerData.lastChatTime = currentTime
    
    -- Broadcast message
    self:BroadcastChatMessage(chatMessage)
    
    return true, "Message sent"
end

-- Basic message filtering
function MultiplayerManager:FilterMessage(message)
    if not message or type(message) ~= "string" then return nil end
    
    -- Length check
    if #message > 200 then
        message = string.sub(message, 1, 200)
    end
    
    -- Remove excessive whitespace
    message = string.gsub(message, "%s+", " ")
    message = string.gsub(message, "^%s*(.-)%s*$", "%1")
    
    return message
end

-- Alliance system
function MultiplayerManager:CreateAlliance(creatorId, allianceName)
    if not self.allianceSystem then
        return false, "Alliances disabled"
    end
    
    local playerData = self.connectedPlayers[creatorId]
    if not playerData then
        return false, "Player not found"
    end
    
    if playerData.currentAlliance then
        return false, "Already in alliance"
    end
    
    -- Create alliance
    local allianceId = HttpService:GenerateGUID(false)
    self.alliances[allianceId] = {
        id = allianceId,
        name = allianceName,
        creator = creatorId,
        members = {creatorId},
        created = os.time(),
        
        -- Alliance settings
        settings = {
            maxMembers = 4,
            allowInvites = true,
            shareRevenue = false,
            shareInfrastructure = true,
            democraticVoting = false
        },
        
        -- Alliance stats
        stats = {
            totalRevenue = 0,
            sharedProjects = 0,
            memberCount = 1
        }
    }
    
    playerData.currentAlliance = allianceId
    
    print("🤝 Alliance created:", allianceName, "by", playerData.player.Name)
    return true, "Alliance created: " .. allianceName
end

-- Invite player to alliance
function MultiplayerManager:InviteToAlliance(inviterId, targetId, allianceId)
    local alliance = self.alliances[allianceId]
    if not alliance then
        return false, "Alliance not found"
    end
    
    -- Check permissions
    if alliance.creator ~= inviterId and not alliance.settings.allowInvites then
        return false, "No permission to invite"
    end
    
    local targetData = self.connectedPlayers[targetId]
    if not targetData then
        return false, "Target player not found"
    end
    
    if targetData.currentAlliance then
        return false, "Player already in alliance"
    end
    
    -- Check max members
    if #alliance.members >= alliance.settings.maxMembers then
        return false, "Alliance full"
    end
    
    -- Create invite
    local inviteId = HttpService:GenerateGUID(false)
    self.allianceInvites[inviteId] = {
        id = inviteId,
        allianceId = allianceId,
        inviterId = inviterId,
        targetId = targetId,
        created = os.time(),
        expires = os.time() + 300 -- 5 minutes
    }
    
    -- Notify target player
    self:NotifyAllianceInvite(targetId, inviteId)
    
    return true, "Invitation sent"
end

-- Accept alliance invite
function MultiplayerManager:AcceptAllianceInvite(playerId, inviteId)
    local invite = self.allianceInvites[inviteId]
    if not invite then
        return false, "Invite not found"
    end
    
    if invite.targetId ~= playerId then
        return false, "Not your invite"
    end
    
    if os.time() > invite.expires then
        self.allianceInvites[inviteId] = nil
        return false, "Invite expired"
    end
    
    local alliance = self.alliances[invite.allianceId]
    if not alliance then
        self.allianceInvites[inviteId] = nil
        return false, "Alliance no longer exists"
    end
    
    -- Add to alliance
    table.insert(alliance.members, playerId)
    alliance.stats.memberCount = #alliance.members
    
    local playerData = self.connectedPlayers[playerId]
    playerData.currentAlliance = invite.allianceId
    
    -- Clean up invite
    self.allianceInvites[inviteId] = nil
    
    -- Notify alliance members
    self:NotifyAllianceJoined(invite.allianceId, playerId)
    
    return true, "Joined alliance: " .. alliance.name
end

-- Trading system
function MultiplayerManager:CreateTradeOffer(offererId, targetId, offerData)
    if not self.tradingEnabled then
        return false, "Trading disabled"
    end
    
    local offererData = self.connectedPlayers[offererId]
    local targetData = self.connectedPlayers[targetId]
    
    if not offererData or not targetData then
        return false, "Player not found"
    end
    
    if not offererData.permissions.canTrade or not targetData.permissions.canTrade then
        return false, "Trading not allowed"
    end
    
    -- Create trade offer
    local tradeId = HttpService:GenerateGUID(false)
    self.tradeOffers[tradeId] = {
        id = tradeId,
        offererId = offererId,
        targetId = targetId,
        created = os.time(),
        expires = os.time() + 600, -- 10 minutes
        status = "pending",
        
        offer = {
            money = offerData.money or 0,
            resources = offerData.resources or {},
            vehicles = offerData.vehicles or {},
            infrastructure = offerData.infrastructure or {}
        },
        
        request = {
            money = offerData.requestMoney or 0,
            resources = offerData.requestResources or {},
            vehicles = offerData.requestVehicles or {},
            infrastructure = offerData.requestInfrastructure or {}
        }
    }
    
    -- Notify target player
    self:NotifyTradeOffer(targetId, tradeId)
    
    return true, "Trade offer sent"
end

-- Get connected players info
function MultiplayerManager:GetConnectedPlayers()
    local players = {}
    
    for playerId, playerData in pairs(self.connectedPlayers) do
        if playerData.isActive then
            table.insert(players, {
                id = playerId,
                name = playerData.player.Name,
                company = playerData.company,
                joinTime = playerData.joinTime,
                isSpectator = playerData.isSpectator,
                alliance = playerData.currentAlliance,
                stats = playerData.stats
            })
        end
    end
    
    return players
end

-- Get player company info
function MultiplayerManager:GetPlayerCompany(playerId)
    return self.playerCompanies[playerId]
end

-- Update player reputation
function MultiplayerManager:UpdatePlayerReputation(playerId, change, reason)
    local playerData = self.connectedPlayers[playerId]
    if not playerData then return end
    
    playerData.company.reputation = math.max(0, math.min(100, playerData.company.reputation + change))
    
    print("📊 Reputation update for", playerData.player.Name, ":", change, "(" .. reason .. ")")
end

-- Broadcast functions (placeholder - would use RemoteEvents)
function MultiplayerManager:BroadcastPlayerJoined(player)
    print("📢 Broadcasting: Player joined -", player.Name)
end

function MultiplayerManager:BroadcastPlayerLeft(player)
    print("📢 Broadcasting: Player left -", player.Name)
end

function MultiplayerManager:BroadcastChatMessage(message)
    print("💬 Broadcasting chat:", message.playerName, "-", message.message)
end

function MultiplayerManager:NotifyAllianceInvite(playerId, inviteId)
    print("🤝 Notifying alliance invite to player:", playerId)
end

function MultiplayerManager:NotifyAllianceJoined(allianceId, playerId)
    print("🤝 Notifying alliance joined:", allianceId, playerId)
end

function MultiplayerManager:NotifyTradeOffer(playerId, tradeId)
    print("💰 Notifying trade offer to player:", playerId)
end

-- Remove from alliance
function MultiplayerManager:RemoveFromAlliance(playerId, allianceId)
    local alliance = self.alliances[allianceId]
    if not alliance then return end
    
    -- Remove from members
    for i, memberId in pairs(alliance.members) do
        if memberId == playerId then
            table.remove(alliance.members, i)
            break
        end
    end
    
    alliance.stats.memberCount = #alliance.members
    
    -- If no members left, delete alliance
    if #alliance.members == 0 then
        self.alliances[allianceId] = nil
    end
    
    local playerData = self.connectedPlayers[playerId]
    if playerData then
        playerData.currentAlliance = nil
    end
end

-- Cancel player trades
function MultiplayerManager:CancelPlayerTrades(playerId)
    for tradeId, trade in pairs(self.tradeOffers) do
        if trade.offererId == playerId or trade.targetId == playerId then
            trade.status = "cancelled"
            self.tradeOffers[tradeId] = nil
        end
    end
end

-- Update multiplayer systems
function MultiplayerManager:Update(deltaTime)
    self.lastSync = self.lastSync + deltaTime
    
    if self.lastSync >= self.syncInterval then
        self:SyncPlayerData()
        self:CleanupExpiredData()
        self.lastSync = 0
    end
end

-- Sync player data
function MultiplayerManager:SyncPlayerData()
    -- Sync company data, positions, etc.
    for playerId, playerData in pairs(self.connectedPlayers) do
        if playerData.isActive then
            -- Update company stats, sync with other systems
        end
    end
end

-- Cleanup expired data
function MultiplayerManager:CleanupExpiredData()
    local currentTime = os.time()
    
    -- Clean expired invites
    for inviteId, invite in pairs(self.allianceInvites) do
        if currentTime > invite.expires then
            self.allianceInvites[inviteId] = nil
        end
    end
    
    -- Clean expired trades
    for tradeId, trade in pairs(self.tradeOffers) do
        if currentTime > trade.expires then
            self.tradeOffers[tradeId] = nil
        end
    end
end

return MultiplayerManager
