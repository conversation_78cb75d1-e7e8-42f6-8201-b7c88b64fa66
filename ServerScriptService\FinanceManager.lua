-- ServerScriptService/FinanceManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Vollständiges Finanz- und Wirtschafts-System

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local FinanceManager = {}
FinanceManager.__index = FinanceManager

function FinanceManager.new()
    local self = setmetatable({}, FinanceManager)
    
    -- Player financial data
    self.playerFinances = {}
    
    -- Loan system
    self.loanOffers = {
        {
            id = "small_loan",
            name = "Kleiner Kredit",
            amount = 100000,
            interestRate = 0.05, -- 5% per year
            duration = 5, -- years
            requirements = {minRevenue = 0}
        },
        {
            id = "medium_loan",
            name = "Mittlerer Kredit",
            amount = 500000,
            interestRate = 0.04,
            duration = 7,
            requirements = {minRevenue = 50000}
        },
        {
            id = "large_loan",
            name = "Großer Kredit",
            amount = 2000000,
            interestRate = 0.035,
            duration = 10,
            requirements = {minRevenue = 200000}
        },
        {
            id = "mega_loan",
            name = "Mega-Kredit",
            amount = 10000000,
            interestRate = 0.03,
            duration = 15,
            requirements = {minRevenue = 1000000}
        }
    }
    
    -- Market conditions
    self.marketConditions = {
        economicCycle = "normal", -- boom, normal, recession
        inflation = 0.02, -- 2% per year
        fuelPrices = 1.0, -- multiplier
        laborCosts = 1.0, -- multiplier
        materialCosts = 1.0, -- multiplier
        demandMultiplier = 1.0
    }
    
    -- Revenue categories
    self.revenueCategories = {
        passenger_transport = "Passagier-Transport",
        cargo_transport = "Fracht-Transport",
        mail_transport = "Post-Transport",
        industry_contracts = "Industrie-Verträge",
        government_subsidies = "Staatliche Zuschüsse"
    }
    
    -- Expense categories
    self.expenseCategories = {
        vehicle_maintenance = "Fahrzeug-Wartung",
        fuel_costs = "Treibstoff-Kosten",
        infrastructure_maintenance = "Infrastruktur-Wartung",
        staff_salaries = "Personal-Gehälter",
        loan_payments = "Kredit-Rückzahlungen",
        insurance = "Versicherung",
        taxes = "Steuern"
    }
    
    return self
end

-- Initialize player finances
function FinanceManager:InitializePlayer(playerId)
    self.playerFinances[playerId] = {
        cash = 2000000, -- Starting money
        totalRevenue = 0,
        totalExpenses = 0,
        monthlyRevenue = 0,
        monthlyExpenses = 0,
        yearlyRevenue = 0,
        yearlyExpenses = 0,
        
        -- Revenue breakdown
        revenueBreakdown = {
            passenger_transport = 0,
            cargo_transport = 0,
            mail_transport = 0,
            industry_contracts = 0,
            government_subsidies = 0
        },
        
        -- Expense breakdown
        expenseBreakdown = {
            vehicle_maintenance = 0,
            fuel_costs = 0,
            infrastructure_maintenance = 0,
            staff_salaries = 0,
            loan_payments = 0,
            insurance = 0,
            taxes = 0
        },
        
        -- Loans
        activeLoans = {},
        creditRating = 100, -- 0-100, affects loan availability
        
        -- Financial history
        monthlyHistory = {},
        yearlyHistory = {},
        
        -- Company valuation
        companyValue = 2000000,
        assets = {},
        
        -- Performance metrics
        profitMargin = 0,
        returnOnInvestment = 0,
        debtToEquityRatio = 0
    }
end

-- Add revenue
function FinanceManager:AddRevenue(playerId, amount, category, description)
    local finances = self.playerFinances[playerId]
    if not finances then return false end
    
    finances.cash = finances.cash + amount
    finances.totalRevenue = finances.totalRevenue + amount
    finances.monthlyRevenue = finances.monthlyRevenue + amount
    finances.yearlyRevenue = finances.yearlyRevenue + amount
    
    if finances.revenueBreakdown[category] then
        finances.revenueBreakdown[category] = finances.revenueBreakdown[category] + amount
    end
    
    -- Log transaction
    self:LogTransaction(playerId, "revenue", amount, category, description)
    
    return true
end

-- Add expense
function FinanceManager:AddExpense(playerId, amount, category, description)
    local finances = self.playerFinances[playerId]
    if not finances then return false end
    
    finances.cash = finances.cash - amount
    finances.totalExpenses = finances.totalExpenses + amount
    finances.monthlyExpenses = finances.monthlyExpenses + amount
    finances.yearlyExpenses = finances.yearlyExpenses + amount
    
    if finances.expenseBreakdown[category] then
        finances.expenseBreakdown[category] = finances.expenseBreakdown[category] + amount
    end
    
    -- Log transaction
    self:LogTransaction(playerId, "expense", amount, category, description)
    
    return true
end

-- Calculate automatic expenses (called monthly)
function FinanceManager:CalculateMonthlyExpenses(playerId, gameData)
    local finances = self.playerFinances[playerId]
    if not finances then return end
    
    local totalExpenses = 0
    
    -- Vehicle maintenance costs
    if gameData.vehicles then
        local maintenanceCost = 0
        for _, vehicle in pairs(gameData.vehicles) do
            local baseCost = vehicle.purchasePrice * 0.02 -- 2% of purchase price per month
            local ageFactor = 1 + (vehicle.age or 0) * 0.1 -- Older vehicles cost more
            maintenanceCost = maintenanceCost + (baseCost * ageFactor)
        end
        self:AddExpense(playerId, maintenanceCost, "vehicle_maintenance", "Monatliche Fahrzeug-Wartung")
        totalExpenses = totalExpenses + maintenanceCost
    end
    
    -- Infrastructure maintenance
    if gameData.infrastructure then
        local infraCost = 0
        for _, infra in pairs(gameData.infrastructure) do
            infraCost = infraCost + (infra.maintenanceCost or 1000)
        end
        self:AddExpense(playerId, infraCost, "infrastructure_maintenance", "Infrastruktur-Wartung")
        totalExpenses = totalExpenses + infraCost
    end
    
    -- Staff salaries (based on company size)
    local staffCost = math.max(50000, finances.companyValue * 0.001) -- Min 50k, or 0.1% of company value
    self:AddExpense(playerId, staffCost, "staff_salaries", "Personal-Gehälter")
    totalExpenses = totalExpenses + staffCost
    
    -- Insurance (based on assets)
    local insuranceCost = finances.companyValue * 0.0005 -- 0.05% of company value
    self:AddExpense(playerId, insuranceCost, "insurance", "Versicherung")
    totalExpenses = totalExpenses + insuranceCost
    
    -- Loan payments
    local loanPayments = self:CalculateLoanPayments(playerId)
    totalExpenses = totalExpenses + loanPayments
    
    return totalExpenses
end

-- Calculate loan payments
function FinanceManager:CalculateLoanPayments(playerId)
    local finances = self.playerFinances[playerId]
    if not finances then return 0 end
    
    local totalPayment = 0
    
    for loanId, loan in pairs(finances.activeLoans) do
        if loan.remainingAmount > 0 then
            local monthlyPayment = loan.monthlyPayment
            
            -- Make payment
            if finances.cash >= monthlyPayment then
                self:AddExpense(playerId, monthlyPayment, "loan_payments", "Kredit-Rückzahlung: " .. loan.name)
                loan.remainingAmount = loan.remainingAmount - (monthlyPayment - loan.monthlyInterest)
                totalPayment = totalPayment + monthlyPayment
                
                -- Check if loan is paid off
                if loan.remainingAmount <= 0 then
                    finances.activeLoans[loanId] = nil
                    print("💰 Loan paid off:", loan.name, "for player", playerId)
                end
            else
                -- Missed payment - affect credit rating
                finances.creditRating = math.max(0, finances.creditRating - 5)
                print("⚠️ Missed loan payment for player", playerId, "- Credit rating decreased")
            end
        end
    end
    
    return totalPayment
end

-- Apply for loan
function FinanceManager:ApplyForLoan(playerId, loanId)
    local finances = self.playerFinances[playerId]
    if not finances then return false, "Spieler nicht gefunden" end
    
    local loanOffer = nil
    for _, offer in pairs(self.loanOffers) do
        if offer.id == loanId then
            loanOffer = offer
            break
        end
    end
    
    if not loanOffer then
        return false, "Kredit-Angebot nicht gefunden"
    end
    
    -- Check requirements
    if finances.yearlyRevenue < loanOffer.requirements.minRevenue then
        return false, "Unzureichender Jahresumsatz für diesen Kredit"
    end
    
    -- Check credit rating
    local requiredRating = 50 + (loanOffer.amount / 100000) -- Higher amounts need better rating
    if finances.creditRating < requiredRating then
        return false, "Unzureichende Kreditwürdigkeit"
    end
    
    -- Check existing debt
    local totalDebt = 0
    for _, loan in pairs(finances.activeLoans) do
        totalDebt = totalDebt + loan.remainingAmount
    end
    
    if totalDebt + loanOffer.amount > finances.companyValue * 2 then
        return false, "Zu hohe Verschuldung im Verhältnis zum Unternehmenswert"
    end
    
    -- Approve loan
    local monthlyPayment = self:CalculateMonthlyLoanPayment(loanOffer.amount, loanOffer.interestRate, loanOffer.duration)
    local monthlyInterest = (loanOffer.amount * loanOffer.interestRate) / 12
    
    local newLoan = {
        id = HttpService:GenerateGUID(false),
        name = loanOffer.name,
        originalAmount = loanOffer.amount,
        remainingAmount = loanOffer.amount,
        interestRate = loanOffer.interestRate,
        duration = loanOffer.duration,
        monthlyPayment = monthlyPayment,
        monthlyInterest = monthlyInterest,
        startDate = os.time()
    }
    
    finances.activeLoans[newLoan.id] = newLoan
    finances.cash = finances.cash + loanOffer.amount
    
    -- Slightly decrease credit rating for taking on debt
    finances.creditRating = math.max(0, finances.creditRating - 2)
    
    return true, "Kredit erfolgreich bewilligt: " .. self:FormatCurrency(loanOffer.amount)
end

-- Calculate monthly loan payment
function FinanceManager:CalculateMonthlyLoanPayment(principal, annualRate, years)
    local monthlyRate = annualRate / 12
    local numPayments = years * 12
    
    if monthlyRate == 0 then
        return principal / numPayments
    end
    
    local monthlyPayment = principal * (monthlyRate * (1 + monthlyRate)^numPayments) / ((1 + monthlyRate)^numPayments - 1)
    return monthlyPayment
end

-- Update market conditions
function FinanceManager:UpdateMarketConditions(deltaTime)
    -- Economic cycle changes
    if math.random() < 0.001 * deltaTime then -- Small chance each update
        local cycles = {"boom", "normal", "recession"}
        local currentIndex = 2 -- normal
        for i, cycle in pairs(cycles) do
            if cycle == self.marketConditions.economicCycle then
                currentIndex = i
                break
            end
        end
        
        -- Tend towards normal
        if currentIndex == 1 and math.random() < 0.3 then -- boom -> normal
            self.marketConditions.economicCycle = "normal"
        elseif currentIndex == 3 and math.random() < 0.3 then -- recession -> normal
            self.marketConditions.economicCycle = "normal"
        elseif currentIndex == 2 then -- normal -> boom/recession
            if math.random() < 0.1 then
                self.marketConditions.economicCycle = math.random() < 0.5 and "boom" or "recession"
            end
        end
    end
    
    -- Update multipliers based on economic cycle
    if self.marketConditions.economicCycle == "boom" then
        self.marketConditions.demandMultiplier = 1.3
        self.marketConditions.fuelPrices = 1.2
        self.marketConditions.laborCosts = 1.1
    elseif self.marketConditions.economicCycle == "recession" then
        self.marketConditions.demandMultiplier = 0.7
        self.marketConditions.fuelPrices = 0.9
        self.marketConditions.laborCosts = 0.95
    else
        self.marketConditions.demandMultiplier = 1.0
        self.marketConditions.fuelPrices = 1.0
        self.marketConditions.laborCosts = 1.0
    end
    
    -- Random price fluctuations
    self.marketConditions.fuelPrices = self.marketConditions.fuelPrices * (0.95 + math.random() * 0.1)
    self.marketConditions.materialCosts = self.marketConditions.materialCosts * (0.98 + math.random() * 0.04)
end

-- Calculate company valuation
function FinanceManager:CalculateCompanyValue(playerId, gameData)
    local finances = self.playerFinances[playerId]
    if not finances then return 0 end
    
    local value = finances.cash
    
    -- Add vehicle values
    if gameData.vehicles then
        for _, vehicle in pairs(gameData.vehicles) do
            local depreciation = 1 - ((vehicle.age or 0) * 0.1) -- 10% per year
            value = value + (vehicle.purchasePrice * math.max(0.1, depreciation))
        end
    end
    
    -- Add infrastructure value
    if gameData.infrastructure then
        for _, infra in pairs(gameData.infrastructure) do
            value = value + (infra.buildCost or 0) * 0.8 -- 80% of build cost
        end
    end
    
    -- Subtract debt
    for _, loan in pairs(finances.activeLoans) do
        value = value - loan.remainingAmount
    end
    
    -- Add revenue multiple (companies are valued at multiple of annual revenue)
    local revenueMultiple = 3 -- Conservative multiple
    value = value + (finances.yearlyRevenue * revenueMultiple)
    
    finances.companyValue = math.max(0, value)
    return finances.companyValue
end

-- Log financial transaction
function FinanceManager:LogTransaction(playerId, type, amount, category, description)
    local finances = self.playerFinances[playerId]
    if not finances then return end
    
    if not finances.transactionLog then
        finances.transactionLog = {}
    end
    
    table.insert(finances.transactionLog, {
        timestamp = os.time(),
        type = type,
        amount = amount,
        category = category,
        description = description,
        balance = finances.cash
    })
    
    -- Keep only last 1000 transactions
    if #finances.transactionLog > 1000 then
        table.remove(finances.transactionLog, 1)
    end
end

-- Get financial summary
function FinanceManager:GetFinancialSummary(playerId)
    local finances = self.playerFinances[playerId]
    if not finances then return nil end
    
    local profit = finances.monthlyRevenue - finances.monthlyExpenses
    local profitMargin = finances.monthlyRevenue > 0 and (profit / finances.monthlyRevenue) * 100 or 0
    
    return {
        cash = finances.cash,
        monthlyRevenue = finances.monthlyRevenue,
        monthlyExpenses = finances.monthlyExpenses,
        monthlyProfit = profit,
        profitMargin = profitMargin,
        companyValue = finances.companyValue,
        creditRating = finances.creditRating,
        totalDebt = self:GetTotalDebt(playerId),
        revenueBreakdown = finances.revenueBreakdown,
        expenseBreakdown = finances.expenseBreakdown,
        activeLoans = finances.activeLoans,
        marketConditions = self.marketConditions
    }
end

-- Get total debt
function FinanceManager:GetTotalDebt(playerId)
    local finances = self.playerFinances[playerId]
    if not finances then return 0 end
    
    local totalDebt = 0
    for _, loan in pairs(finances.activeLoans) do
        totalDebt = totalDebt + loan.remainingAmount
    end
    
    return totalDebt
end

-- Format currency
function FinanceManager:FormatCurrency(amount)
    if amount >= 1000000 then
        return string.format("%.1fM €", amount / 1000000)
    elseif amount >= 1000 then
        return string.format("%.0fk €", amount / 1000)
    else
        return string.format("%.0f €", amount)
    end
end

-- Reset monthly/yearly counters
function FinanceManager:ResetPeriodCounters(playerId, period)
    local finances = self.playerFinances[playerId]
    if not finances then return end
    
    if period == "monthly" then
        -- Save to history
        table.insert(finances.monthlyHistory, {
            month = os.date("%Y-%m"),
            revenue = finances.monthlyRevenue,
            expenses = finances.monthlyExpenses,
            profit = finances.monthlyRevenue - finances.monthlyExpenses
        })
        
        -- Reset counters
        finances.monthlyRevenue = 0
        finances.monthlyExpenses = 0
        
        -- Reset breakdown
        for category, _ in pairs(finances.revenueBreakdown) do
            finances.revenueBreakdown[category] = 0
        end
        for category, _ in pairs(finances.expenseBreakdown) do
            finances.expenseBreakdown[category] = 0
        end
        
    elseif period == "yearly" then
        -- Save to history
        table.insert(finances.yearlyHistory, {
            year = os.date("%Y"),
            revenue = finances.yearlyRevenue,
            expenses = finances.yearlyExpenses,
            profit = finances.yearlyRevenue - finances.yearlyExpenses
        })
        
        -- Reset counters
        finances.yearlyRevenue = 0
        finances.yearlyExpenses = 0
    end
end

return FinanceManager
