-- ServerScriptService/IndustryManager.lua
-- ROBLOX SCRIPT TYPE: ModuleScript
-- Erweiterte Industrie-Verwaltung und Produktionsketten

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local HttpService = game:GetService("HttpService")

local IndustryManager = {}
IndustryManager.__index = IndustryManager

function IndustryManager.new()
    local self = setmetatable({}, IndustryManager)
    
    -- Production chains and resource flows
    self.productionChains = {
        -- Basic resource extraction
        coal_mine = {
            inputs = {},
            outputs = {"coal"},
            processingTime = 60, -- seconds
            efficiency = 1.0
        },
        iron_mine = {
            inputs = {},
            outputs = {"iron_ore"},
            processingTime = 80,
            efficiency = 1.0
        },
        oil_well = {
            inputs = {},
            outputs = {"crude_oil"},
            processingTime = 45,
            efficiency = 1.0
        },
        quarry = {
            inputs = {},
            outputs = {"stone"},
            processingTime = 50,
            efficiency = 1.0
        },
        lumber_mill = {
            inputs = {},
            outputs = {"wood"},
            processingTime = 40,
            efficiency = 1.0
        },
        farm = {
            inputs = {},
            outputs = {"food", "grain"},
            processingTime = 120,
            efficiency = 1.0
        },
        
        -- Processing industries
        steel_mill = {
            inputs = {"iron_ore", "coal"},
            outputs = {"steel"},
            processingTime = 90,
            efficiency = 0.8,
            inputRatios = {iron_ore = 2, coal = 1}
        },
        oil_refinery = {
            inputs = {"crude_oil"},
            outputs = {"fuel", "plastic"},
            processingTime = 70,
            efficiency = 0.9,
            inputRatios = {crude_oil = 1}
        },
        cement_plant = {
            inputs = {"stone", "coal"},
            outputs = {"cement"},
            processingTime = 85,
            efficiency = 0.7,
            inputRatios = {stone = 3, coal = 1}
        },
        food_processing = {
            inputs = {"grain", "food"},
            outputs = {"processed_food"},
            processingTime = 60,
            efficiency = 0.8,
            inputRatios = {grain = 2, food = 1}
        },
        
        -- Manufacturing
        machinery_factory = {
            inputs = {"steel", "plastic"},
            outputs = {"machinery"},
            processingTime = 120,
            efficiency = 0.6,
            inputRatios = {steel = 2, plastic = 1}
        },
        electronics_factory = {
            inputs = {"plastic", "steel"},
            outputs = {"electronics"},
            processingTime = 100,
            efficiency = 0.7,
            inputRatios = {plastic = 1, steel = 1}
        },
        vehicle_factory = {
            inputs = {"steel", "machinery", "electronics"},
            outputs = {"vehicles"},
            processingTime = 180,
            efficiency = 0.5,
            inputRatios = {steel = 3, machinery = 2, electronics = 1}
        },
        
        -- Consumer goods
        textile_mill = {
            inputs = {"grain"}, -- simplified
            outputs = {"textiles"},
            processingTime = 80,
            efficiency = 0.8,
            inputRatios = {grain = 1}
        },
        furniture_factory = {
            inputs = {"wood", "textiles"},
            outputs = {"furniture"},
            processingTime = 90,
            efficiency = 0.7,
            inputRatios = {wood = 2, textiles = 1}
        },
        
        -- Services and utilities
        power_plant = {
            inputs = {"coal", "fuel"},
            outputs = {"electricity"},
            processingTime = 30,
            efficiency = 0.9,
            inputRatios = {coal = 2, fuel = 1}
        },
        water_treatment = {
            inputs = {"electricity"},
            outputs = {"clean_water"},
            processingTime = 40,
            efficiency = 0.95,
            inputRatios = {electricity = 1}
        }
    }
    
    -- Resource market values (base prices)
    self.resourceValues = {
        -- Raw materials
        coal = 10,
        iron_ore = 15,
        crude_oil = 20,
        stone = 8,
        wood = 12,
        food = 18,
        grain = 14,
        
        -- Processed materials
        steel = 45,
        fuel = 35,
        plastic = 30,
        cement = 25,
        processed_food = 40,
        
        -- Manufactured goods
        machinery = 120,
        electronics = 150,
        vehicles = 500,
        textiles = 60,
        furniture = 180,
        
        -- Utilities
        electricity = 5,
        clean_water = 3
    }
    
    -- Industry upgrade costs and benefits
    self.upgradeSystem = {
        level_2 = {
            cost = 50000,
            productionBonus = 1.5,
            efficiencyBonus = 1.2,
            newInputs = {},
            newOutputs = {}
        },
        level_3 = {
            cost = 150000,
            productionBonus = 2.0,
            efficiencyBonus = 1.4,
            newInputs = {},
            newOutputs = {}
        },
        level_4 = {
            cost = 400000,
            productionBonus = 2.8,
            efficiencyBonus = 1.6,
            newInputs = {},
            newOutputs = {}
        },
        level_5 = {
            cost = 1000000,
            productionBonus = 4.0,
            efficiencyBonus = 1.8,
            newInputs = {},
            newOutputs = {}
        }
    }
    
    return self
end

-- Calculate industry production for a given time period
function IndustryManager:CalculateProduction(industryType, level, deltaTime, inputAvailability)
    local chain = self.productionChains[industryType]
    if not chain then return {} end
    
    -- Base production rate
    local baseRate = 1.0
    local levelMultiplier = level or 1
    
    -- Apply upgrade bonuses
    if level > 1 then
        local upgradeKey = "level_" .. level
        local upgrade = self.upgradeSystem[upgradeKey]
        if upgrade then
            baseRate = baseRate * upgrade.productionBonus
        end
    end
    
    -- Check input availability
    local productionMultiplier = 1.0
    if chain.inputs and #chain.inputs > 0 then
        local minAvailability = 1.0
        for _, input in pairs(chain.inputs) do
            local available = inputAvailability[input] or 0
            local required = chain.inputRatios[input] or 1
            local availability = available / required
            minAvailability = math.min(minAvailability, availability)
        end
        productionMultiplier = math.min(1.0, minAvailability)
    end
    
    -- Calculate actual production
    local actualRate = baseRate * levelMultiplier * productionMultiplier * chain.efficiency
    local productionAmount = actualRate * deltaTime / chain.processingTime
    
    -- Generate outputs
    local outputs = {}
    for _, output in pairs(chain.outputs) do
        outputs[output] = productionAmount
    end
    
    return outputs
end

-- Calculate resource consumption
function IndustryManager:CalculateConsumption(industryType, level, production)
    local chain = self.productionChains[industryType]
    if not chain or not chain.inputs then return {} end
    
    local consumption = {}
    local totalProduction = 0
    
    -- Calculate total production amount
    for _, amount in pairs(production) do
        totalProduction = totalProduction + amount
    end
    
    -- Calculate input consumption based on production
    for _, input in pairs(chain.inputs) do
        local ratio = chain.inputRatios[input] or 1
        consumption[input] = totalProduction * ratio
    end
    
    return consumption
end

-- Get industry efficiency based on various factors
function IndustryManager:CalculateEfficiency(industryData, cityData, transportConnections)
    local baseEfficiency = 1.0
    local efficiency = baseEfficiency
    
    -- Transport connection bonus
    if transportConnections and transportConnections > 0 then
        efficiency = efficiency * (1.0 + (transportConnections * 0.1)) -- 10% per connection
    end
    
    -- City size bonus
    if cityData and cityData.size then
        efficiency = efficiency * (1.0 + (cityData.size * 0.05)) -- 5% per city size level
    end
    
    -- Industry level bonus
    if industryData.level and industryData.level > 1 then
        local upgradeKey = "level_" .. industryData.level
        local upgrade = self.upgradeSystem[upgradeKey]
        if upgrade then
            efficiency = efficiency * upgrade.efficiencyBonus
        end
    end
    
    -- Random variation (±10%)
    local variation = (math.random() - 0.5) * 0.2
    efficiency = efficiency * (1.0 + variation)
    
    -- Clamp efficiency between 0.1 and 3.0
    efficiency = math.max(0.1, math.min(3.0, efficiency))
    
    return efficiency
end

-- Calculate market price for a resource
function IndustryManager:CalculateMarketPrice(resourceType, supply, demand, marketConditions)
    local basePrice = self.resourceValues[resourceType] or 10
    
    -- Supply and demand adjustment
    local supplyDemandRatio = 1.0
    if demand > 0 then
        supplyDemandRatio = supply / demand
    end
    
    -- Price adjustment based on supply/demand
    local priceMultiplier = 1.0
    if supplyDemandRatio < 0.5 then
        priceMultiplier = 2.0 -- High demand, low supply
    elseif supplyDemandRatio < 0.8 then
        priceMultiplier = 1.5
    elseif supplyDemandRatio > 2.0 then
        priceMultiplier = 0.5 -- High supply, low demand
    elseif supplyDemandRatio > 1.5 then
        priceMultiplier = 0.7
    end
    
    -- Market conditions (economic cycles, events, etc.)
    if marketConditions then
        if marketConditions.economicBoom then
            priceMultiplier = priceMultiplier * 1.3
        elseif marketConditions.recession then
            priceMultiplier = priceMultiplier * 0.7
        end
        
        if marketConditions.resourceShortage and marketConditions.resourceShortage[resourceType] then
            priceMultiplier = priceMultiplier * 2.0
        end
    end
    
    -- Random market fluctuation (±15%)
    local fluctuation = (math.random() - 0.5) * 0.3
    priceMultiplier = priceMultiplier * (1.0 + fluctuation)
    
    local finalPrice = basePrice * priceMultiplier
    return math.max(1, math.floor(finalPrice))
end

-- Get upgrade cost for industry
function IndustryManager:GetUpgradeCost(industryType, currentLevel)
    local nextLevel = currentLevel + 1
    local upgradeKey = "level_" .. nextLevel
    local upgrade = self.upgradeSystem[upgradeKey]
    
    if upgrade then
        return upgrade.cost
    end
    
    return nil -- Max level reached
end

-- Check if industry can be upgraded
function IndustryManager:CanUpgrade(industryData, playerMoney)
    if not industryData or not industryData.level then return false end
    
    local upgradeCost = self:GetUpgradeCost(industryData.type, industryData.level)
    if not upgradeCost then return false end -- Max level
    
    return playerMoney >= upgradeCost
end

-- Perform industry upgrade
function IndustryManager:UpgradeIndustry(industryData, playerData)
    if not self:CanUpgrade(industryData, playerData.money) then
        return false, "Nicht genügend Geld oder maximales Level erreicht"
    end
    
    local upgradeCost = self:GetUpgradeCost(industryData.type, industryData.level)
    
    -- Deduct cost
    playerData.money = playerData.money - upgradeCost
    
    -- Upgrade industry
    industryData.level = industryData.level + 1
    industryData.lastUpgrade = os.time()
    
    return true, "Industrie erfolgreich auf Level " .. industryData.level .. " aufgerüstet"
end

-- Get industry statistics
function IndustryManager:GetIndustryStats(industryData, cityData)
    local chain = self.productionChains[industryData.type]
    if not chain then return nil end
    
    local stats = {
        type = industryData.type,
        level = industryData.level,
        efficiency = industryData.efficiency or 1.0,
        production = industryData.production or {},
        consumption = industryData.consumption or {},
        inputs = chain.inputs,
        outputs = chain.outputs,
        processingTime = chain.processingTime,
        upgradeCost = self:GetUpgradeCost(industryData.type, industryData.level),
        canUpgrade = industryData.level < 5,
        marketValue = 0
    }
    
    -- Calculate total market value of production
    for resource, amount in pairs(stats.production) do
        local price = self.resourceValues[resource] or 0
        stats.marketValue = stats.marketValue + (amount * price)
    end
    
    return stats
end

-- Get all available industry types
function IndustryManager:GetAvailableIndustryTypes()
    local types = {}
    for industryType, chain in pairs(self.productionChains) do
        table.insert(types, {
            type = industryType,
            name = self:GetIndustryDisplayName(industryType),
            inputs = chain.inputs,
            outputs = chain.outputs,
            baseValue = self.resourceValues[chain.outputs[1]] or 0
        })
    end
    return types
end

-- Get display name for industry type
function IndustryManager:GetIndustryDisplayName(industryType)
    local displayNames = {
        coal_mine = "Kohlemine",
        iron_mine = "Eisenmine",
        oil_well = "Ölquelle",
        quarry = "Steinbruch",
        lumber_mill = "Sägewerk",
        farm = "Bauernhof",
        steel_mill = "Stahlwerk",
        oil_refinery = "Ölraffinerie",
        cement_plant = "Zementwerk",
        food_processing = "Lebensmittelverarbeitung",
        machinery_factory = "Maschinenfabrik",
        electronics_factory = "Elektronikfabrik",
        vehicle_factory = "Fahrzeugfabrik",
        textile_mill = "Textilfabrik",
        furniture_factory = "Möbelfabrik",
        power_plant = "Kraftwerk",
        water_treatment = "Wasseraufbereitung"
    }
    
    return displayNames[industryType] or industryType
end

return IndustryManager
