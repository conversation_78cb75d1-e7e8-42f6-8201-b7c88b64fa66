-- StarterPlayerScripts/GUI/VehicleGUI.lua
-- ROBLOX SCRIPT TYPE: LocalScript
-- GUI für Fahrzeug-Management

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Events
local Events = ReplicatedStorage:WaitForChild("Events")
local CreateVehicleEvent = Events:WaitForChild("CreateVehicleEvent")
local SetVehicleRouteEvent = Events:WaitForChild("SetVehicleRouteEvent")

local VehicleGUI = {}
VehicleGUI.IsOpen = false
VehicleGUI.SelectedVehicleType = "STEAM_TRAIN"
VehicleGUI.RouteWaypoints = {}

-- Fahrzeug-Typen
local VehicleTypes = {
    STEAM_TRAIN = {
        Name = "Dampflokomotive",
        Icon = "🚂",
        Cost = 15000,
        Speed = "40 km/h",
        Capacity = "200 Tonnen",
        UnlockYear = 1850
    },
    TRUCK_EARLY = {
        Name = "Früher LKW",
        Icon = "🚚",
        Cost = 3000,
        Speed = "25 km/h",
        Capacity = "5 Tonnen",
        UnlockYear = 1920
    },
    STEAMSHIP = {
        Name = "Dampfschiff",
        Icon = "🚢",
        Cost = 25000,
        Speed = "15 km/h",
        Capacity = "500 Tonnen",
        UnlockYear = 1850
    }
}

-- GUI erstellen
function VehicleGUI:CreateGUI()
    -- ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "VehicleGUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = playerGui
    
    -- Hauptframe
    local mainFrame = Instance.new("Frame")
    mainFrame.Size = UDim2.new(0, 600, 0, 400)
    mainFrame.Position = UDim2.new(0.5, -300, 0.5, -200)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    mainFrame.BorderSizePixel = 0
    mainFrame.Visible = false
    mainFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = mainFrame
    
    -- Titel
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🚂 FAHRZEUG-MANAGEMENT"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = mainFrame
    
    -- Schließen-Button
    local closeButton = Instance.new("TextButton")
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -40, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.BorderSizePixel = 0
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 5)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        self:CloseGUI()
    end)
    
    -- Fahrzeug-Liste
    local vehicleFrame = Instance.new("ScrollingFrame")
    vehicleFrame.Size = UDim2.new(0.6, 0, 0.8, 0)
    vehicleFrame.Position = UDim2.new(0.05, 0, 0.15, 0)
    vehicleFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    vehicleFrame.BorderSizePixel = 0
    vehicleFrame.ScrollBarThickness = 8
    vehicleFrame.Parent = mainFrame
    
    local vehicleCorner = Instance.new("UICorner")
    vehicleCorner.CornerRadius = UDim.new(0, 8)
    vehicleCorner.Parent = vehicleFrame
    
    -- Info-Panel
    local infoFrame = Instance.new("Frame")
    infoFrame.Size = UDim2.new(0.3, 0, 0.8, 0)
    infoFrame.Position = UDim2.new(0.67, 0, 0.15, 0)
    infoFrame.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    infoFrame.BorderSizePixel = 0
    infoFrame.Parent = mainFrame
    
    local infoCorner = Instance.new("UICorner")
    infoCorner.CornerRadius = UDim.new(0, 8)
    infoCorner.Parent = infoFrame
    
    self.ScreenGui = screenGui
    self.MainFrame = mainFrame
    self.VehicleFrame = vehicleFrame
    self.InfoFrame = infoFrame
    
    -- Fahrzeug-Liste erstellen
    self:CreateVehicleList()
    self:CreateInfoPanel()
    
    return screenGui
end

-- Fahrzeug-Liste erstellen
function VehicleGUI:CreateVehicleList()
    local yPosition = 0
    local entryHeight = 80
    
    for vehicleType, data in pairs(VehicleTypes) do
        local entry = Instance.new("Frame")
        entry.Size = UDim2.new(1, -10, 0, entryHeight)
        entry.Position = UDim2.new(0, 5, 0, yPosition)
        entry.BackgroundColor3 = Color3.fromRGB(50, 60, 70)
        entry.BorderSizePixel = 0
        entry.Parent = self.VehicleFrame
        
        local entryCorner = Instance.new("UICorner")
        entryCorner.CornerRadius = UDim.new(0, 5)
        entryCorner.Parent = entry
        
        -- Icon
        local icon = Instance.new("TextLabel")
        icon.Size = UDim2.new(0, 60, 0, 60)
        icon.Position = UDim2.new(0, 10, 0, 10)
        icon.BackgroundTransparency = 1
        icon.Text = data.Icon
        icon.TextScaled = true
        icon.Font = Enum.Font.SourceSans
        icon.Parent = entry
        
        -- Name
        local nameLabel = Instance.new("TextLabel")
        nameLabel.Size = UDim2.new(0.5, 0, 0, 30)
        nameLabel.Position = UDim2.new(0, 80, 0, 10)
        nameLabel.BackgroundTransparency = 1
        nameLabel.Text = data.Name
        nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
        nameLabel.TextScaled = true
        nameLabel.Font = Enum.Font.SourceSansBold
        nameLabel.TextXAlignment = Enum.TextXAlignment.Left
        nameLabel.Parent = entry
        
        -- Kosten
        local costLabel = Instance.new("TextLabel")
        costLabel.Size = UDim2.new(0.5, 0, 0, 20)
        costLabel.Position = UDim2.new(0, 80, 0, 40)
        costLabel.BackgroundTransparency = 1
        costLabel.Text = "💰 " .. data.Cost .. " $"
        costLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
        costLabel.TextScaled = true
        costLabel.Font = Enum.Font.SourceSans
        costLabel.TextXAlignment = Enum.TextXAlignment.Left
        costLabel.Parent = entry
        
        -- Kaufen-Button
        local buyButton = Instance.new("TextButton")
        buyButton.Size = UDim2.new(0, 80, 0, 30)
        buyButton.Position = UDim2.new(1, -90, 0, 25)
        buyButton.BackgroundColor3 = Color3.fromRGB(60, 120, 180)
        buyButton.Text = "KAUFEN"
        buyButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        buyButton.TextScaled = true
        buyButton.Font = Enum.Font.SourceSansBold
        buyButton.BorderSizePixel = 0
        buyButton.Parent = entry
        
        local buyCorner = Instance.new("UICorner")
        buyCorner.CornerRadius = UDim.new(0, 5)
        buyCorner.Parent = buyButton
        
        -- Button-Funktionalität
        buyButton.MouseButton1Click:Connect(function()
            self:BuyVehicle(vehicleType)
        end)
        
        -- Hover-Effekt
        entry.MouseEnter:Connect(function()
            self.SelectedVehicleType = vehicleType
            self:UpdateInfoPanel(data)
            
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(70, 80, 90)
            })
            tween:Play()
        end)
        
        entry.MouseLeave:Connect(function()
            local tween = TweenService:Create(entry, TweenInfo.new(0.2), {
                BackgroundColor3 = Color3.fromRGB(50, 60, 70)
            })
            tween:Play()
        end)
        
        yPosition = yPosition + entryHeight + 10
    end
    
    self.VehicleFrame.CanvasSize = UDim2.new(0, 0, 0, yPosition)
end

-- Info-Panel erstellen
function VehicleGUI:CreateInfoPanel()
    local title = Instance.new("TextLabel")
    title.Size = UDim2.new(1, 0, 0, 30)
    title.Position = UDim2.new(0, 0, 0, 10)
    title.BackgroundTransparency = 1
    title.Text = "FAHRZEUG-INFO"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = self.InfoFrame
    
    self.InfoTitle = title
    
    -- Platzhalter für Details
    local details = Instance.new("TextLabel")
    details.Size = UDim2.new(1, -10, 0.8, 0)
    details.Position = UDim2.new(0, 5, 0, 50)
    details.BackgroundTransparency = 1
    details.Text = "Wähle ein Fahrzeug aus"
    details.TextColor3 = Color3.fromRGB(200, 200, 200)
    details.TextScaled = true
    details.Font = Enum.Font.SourceSans
    details.TextWrapped = true
    details.Parent = self.InfoFrame
    
    self.InfoDetails = details
end

-- Info-Panel aktualisieren
function VehicleGUI:UpdateInfoPanel(vehicleData)
    self.InfoDetails.Text = string.format(
        "%s\n\n💰 Kosten: %d $\n🏃 Geschwindigkeit: %s\n📦 Kapazität: %s\n📅 Verfügbar ab: %d",
        vehicleData.Name,
        vehicleData.Cost,
        vehicleData.Speed,
        vehicleData.Capacity,
        vehicleData.UnlockYear
    )
end

-- Fahrzeug kaufen
function VehicleGUI:BuyVehicle(vehicleType)
    print("🚂 Kaufe Fahrzeug:", vehicleType)

    -- Position vor dem Spieler
    local character = player.Character
    if character and character:FindFirstChild("HumanoidRootPart") then
        local position = character.HumanoidRootPart.Position + character.HumanoidRootPart.CFrame.LookVector * 10
        position = Vector3.new(position.X, position.Y + 5, position.Z)

        -- Event an Server senden
        CreateVehicleEvent:FireServer(vehicleType, position)

        -- GUI schließen
        self:CloseGUI()
    else
        warn("Spieler-Position nicht gefunden!")
    end
end

-- GUI öffnen
function VehicleGUI:OpenGUI()
    if not self.ScreenGui then
        self:CreateGUI()
    end

    self.MainFrame.Visible = true
    self.IsOpen = true

    -- Smooth fade-in
    self.MainFrame.BackgroundTransparency = 1
    local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
        BackgroundTransparency = 0
    })
    tween:Play()
end

-- GUI schließen
function VehicleGUI:CloseGUI()
    if self.MainFrame then
        local tween = TweenService:Create(self.MainFrame, TweenInfo.new(0.3), {
            BackgroundTransparency = 1
        })
        tween:Play()

        tween.Completed:Connect(function()
            self.MainFrame.Visible = false
            self.IsOpen = false
        end)
    end
end

-- Hotkey-Steuerung
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.V then
        if VehicleGUI.IsOpen then
            VehicleGUI:CloseGUI()
        else
            VehicleGUI:OpenGUI()
        end
    end
end)

-- Initialisierung
function VehicleGUI:Initialize()
    print("🚂 VehicleGUI initialisiert - Drücke 'V' zum Öffnen")
end

-- Auto-Start
VehicleGUI:Initialize()

return VehicleGUI
